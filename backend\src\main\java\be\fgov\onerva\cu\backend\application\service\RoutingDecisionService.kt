package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RequestStatus
import be.fgov.onerva.cu.backend.application.domain.RoutingDecision
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.exception.IncompleteRoutingDecisionsException
import be.fgov.onerva.cu.backend.application.port.`in`.RoutingDecisionUseCase
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.RoutingDecisionPort
import be.fgov.onerva.cu.common.utils.logger

@Service
@BusinessTransaction
class RoutingDecisionService(
    private val routingDecisionPort: RoutingDecisionPort,
    private val changePersonalDataPort: PersistChangePersonalDataPort,
) : RoutingDecisionUseCase {
    
    private val log = logger

    override fun getRoutingDecisions(requestId: UUID): RoutingDecision {
        log.info("Retrieving routing decisions for request ID: $requestId")
        
        // Get the request to calculate processInWave status
        val request = changePersonalDataPort.getChangePersonalDataById(requestId)
            ?: throw IllegalArgumentException("Request not found: $requestId")
        
        // Get existing routing decisions from database
        val existingDecisions = routingDecisionPort.getRoutingDecisions(requestId)
        val existingDecisionTypes = existingDecisions.map { it.type }.toSet()
        
        // Create decisions with null values for missing types, keep existing values
        val allDecisions = mutableSetOf<RoutingDecisionItem>()
        
        val allRequiredTypes = ManualVerificationType.entries.toSet()
        
        // Add existing decisions
        allDecisions.addAll(existingDecisions)
        
        // Add missing types with null values
        val missingTypes = allRequiredTypes - existingDecisionTypes
        missingTypes.forEach { type ->
            allDecisions.add(RoutingDecisionItem(type, null))
        }
        
        // Calculate processInWave status - null if no answers, boolean if answered
        val processInWave = if (existingDecisions.isEmpty()) {
            null // No decisions made yet
        } else if (existingDecisionTypes == allRequiredTypes && existingDecisions.all { it.value != null }) {
            // All decisions are answered, calculate based on request's canBeProcessedInWave method
            request.canBeProcessedInWave()
        } else {
            false // Partial answers or null values prevent Wave processing
        }
        
        return RoutingDecision(processInWave, allDecisions)
    }

    override fun updateRoutingDecisions(requestId: UUID, routingDecisions: Set<RoutingDecisionItem>) {
        log.info("Updating routing decisions for request ID: $requestId")
        
        // Validate that request exists and get its status
        val request = changePersonalDataPort.getChangePersonalDataById(requestId)
            ?: throw IllegalArgumentException("Request not found: $requestId")
        
        // Validate request is open for modifications
        if (request.status == RequestStatus.CLOSED) {
            throw IllegalStateException("Cannot update routing decisions for a closed request: $requestId")
        }
        
        // Validate all required types are provided
        validateAllRoutingDecisionTypesPresent(routingDecisions)
        
        // Calculate processInWave based on the routing decisions
        val processInWave = calculateProcessInWave(routingDecisions)

        // Save routing decisions and update processInWave
        routingDecisionPort.saveRoutingDecisions(requestId, routingDecisions, processInWave)

        log.info("Successfully updated routing decisions for request ID: $requestId, processInWave: $processInWave")
    }
    
    private fun validateAllRoutingDecisionTypesPresent(routingDecisions: Set<RoutingDecisionItem>) {
        val allRequiredTypes = ManualVerificationType.entries.toSet()
        val providedTypes = routingDecisions.map { it.type }.toSet()
        
        if (providedTypes != allRequiredTypes) {
            val missingTypes = allRequiredTypes - providedTypes
            val extraTypes = providedTypes - allRequiredTypes
            
            val errorMessage = buildString {
                append("Routing decisions validation failed. ")
                if (missingTypes.isNotEmpty()) {
                    append("Missing types: ${missingTypes.joinToString(", ")}. ")
                }
                if (extraTypes.isNotEmpty()) {
                    append("Extra types: ${extraTypes.joinToString(", ")}. ")
                }
                append("All ManualVerificationType values must be provided exactly once.")
            }
            
            throw IncompleteRoutingDecisionsException(errorMessage)
        }
    }

    /**
     * Calculates whether the request can be processed in Wave based on routing decisions.
     * Returns true ONLY if ALL manual verification types are answered (non-null) AND all answers
     * match expected values. Returns false in all other cases.
     */
    private fun calculateProcessInWave(routingDecisions: Set<RoutingDecisionItem>): Boolean {
        // If no routing decisions exist, cannot be processed in Wave
        if (routingDecisions.isEmpty()) return false

        // Get all manual verification types that need to be answered
        val allRequiredTypes = ManualVerificationType.entries.toSet()
        val answeredTypes = routingDecisions.map { it.type }.toSet()

        // All types must be present
        if (answeredTypes != allRequiredTypes) return false

        // All values must be non-null (answered) and match expected values for Wave processing
        return routingDecisions.all { decision ->
            decision.value != null && decision.type.allowsWaveProcessing(decision.value!!)
        }
    }
}