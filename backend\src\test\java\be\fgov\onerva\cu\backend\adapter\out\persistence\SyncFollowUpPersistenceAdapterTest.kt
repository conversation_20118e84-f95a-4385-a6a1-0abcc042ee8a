package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.Instant
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SyncFollowUpEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.SyncFollowUpRepository
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.SyncFollowUpNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class SyncFollowUpPersistenceAdapterTest {

    @MockK
    lateinit var requestRepository: RequestRepository

    @MockK
    lateinit var syncFollowUpRepository: SyncFollowUpRepository

    @InjectMockKs
    lateinit var syncFollowUpPersistenceAdapter: SyncFollowUpPersistenceAdapter

    @Nested
    inner class PersistSyncFollowUpAsPending {

        @Test
        fun `should persist sync follow up as pending when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val correlationId = "correlation-123"
            val requestEntity = mockk<RequestEntity>()
            val entitySlot = slot<SyncFollowUpEntity>()

            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity
            every { syncFollowUpRepository.save(capture(entitySlot)) } returnsArgument 0

            // When
            syncFollowUpPersistenceAdapter.persistSyncFollowUpAsPending(requestId, correlationId)

            // Then
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) { syncFollowUpRepository.save(any()) }

            val capturedEntity = entitySlot.captured
            assertThat(capturedEntity.request).isEqualTo(requestEntity)
            assertThat(capturedEntity.correlationId).isEqualTo(correlationId)
            assertThat(capturedEntity.status).isEqualTo(SyncFollowUpStatus.PENDING)
            assertThat(capturedEntity.dateMessageSent).isNotNull
            assertThat(capturedEntity.dateResponseReceived).isNull()
        }

        @Test
        fun `should throw RequestIdNotFoundException when request does not exist`() {
            // Given
            val requestId = UUID.randomUUID()
            val correlationId = "correlation-123"

            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThrows<RequestIdNotFoundException> {
                syncFollowUpPersistenceAdapter.persistSyncFollowUpAsPending(requestId, correlationId)
            }

            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) { syncFollowUpRepository.save(any()) }
        }
    }

    @Nested
    inner class UpdateSyncFollowUpAsOk {

        @Test
        fun `should update sync follow up as OK when correlation ID exists`() {
            // Given
            val correlationId = "correlation-123"
            val syncFollowUpEntity = SyncFollowUpEntity(
                request = mockk(),
                correlationId = correlationId,
                status = SyncFollowUpStatus.PENDING,
                dateMessageSent = Instant.now().minusSeconds(60)
            )

            every { syncFollowUpRepository.findByCorrelationId(correlationId) } returns syncFollowUpEntity

            // When
            syncFollowUpPersistenceAdapter.updateSyncFollowUp(correlationId, true, 0)

            // Then
            verify(exactly = 1) { syncFollowUpRepository.findByCorrelationId(correlationId) }

            assertThat(syncFollowUpEntity.status).isEqualTo(SyncFollowUpStatus.OK)
            assertThat(syncFollowUpEntity.dateResponseReceived).isNotNull
        }

        @Test
        fun `should throw SyncFollowUpNotFoundException when correlation ID does not exist`() {
            // Given
            val correlationId = "correlation-123"

            every { syncFollowUpRepository.findByCorrelationId(correlationId) } returns null

            // When/Then
            assertThrows<SyncFollowUpNotFoundException> {
                syncFollowUpPersistenceAdapter.updateSyncFollowUp(correlationId, true, 0)
            }

            verify(exactly = 1) { syncFollowUpRepository.findByCorrelationId(correlationId) }
        }
    }

    @Nested
    inner class GetSyncFollowUpStatusByRequestId {

        @Test
        fun `should return status when sync follow up exists for request id`() {
            // Given
            val requestId = UUID.randomUUID()
            val syncFollowUpEntity = SyncFollowUpEntity(
                request = mockk(),
                correlationId = "test-correlation-id",
                status = SyncFollowUpStatus.PENDING,
                dateMessageSent = Instant.now()
            )

            every { syncFollowUpRepository.findByRequestId(requestId) } returns syncFollowUpEntity

            // When
            val result = syncFollowUpPersistenceAdapter.getSyncFollowUpStatusByRequestId(requestId)

            // Then
            assertThat(result).isEqualTo(SyncFollowUpStatus.PENDING)
            verify(exactly = 1) { syncFollowUpRepository.findByRequestId(requestId) }
        }

        @Test
        fun `should return null when no sync follow up exists for request id`() {
            // Given
            val requestId = UUID.randomUUID()

            every { syncFollowUpRepository.findByRequestId(requestId) } returns null

            // When
            val result = syncFollowUpPersistenceAdapter.getSyncFollowUpStatusByRequestId(requestId)

            // Then
            assertThat(result).isNull()
            verify(exactly = 1) { syncFollowUpRepository.findByRequestId(requestId) }
        }
    }
}
