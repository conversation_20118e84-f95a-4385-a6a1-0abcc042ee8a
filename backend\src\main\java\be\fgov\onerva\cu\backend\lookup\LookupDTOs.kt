package be.fgov.onerva.cu.backend.lookup

import java.time.LocalDate

abstract class BaseLookupDTO(
    open val lookupId: Int,
    open val code: String,
    open val descFr: String,
    open val descNl: String,
)

/**
 * Key class for city cache that combines nisCode and code for proper hash-based lookups.
 * This class ensures correct equals() and hashCode() implementation for Map operations.
 *
 * @property nisCode The NIS (National Institute of Statistics) code
 * @property code The postal code
 */
data class CityKey(
    val nisCode: String,
    val code: String,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is CityKey) return false
        return nisCode == other.nisCode && code == other.code
    }

    override fun hashCode(): Int {
        var result = nisCode.hashCode()
        result = 31 * result + code.hashCode()
        return result
    }

    override fun toString(): String {
        return "CityKey(nisCode='$nisCode', code='$code')"
    }
}

data class CountryDTO(
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class NationalityDTO(
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
    val onemCountryCode: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class CityDTO(
    val beginDate: LocalDate,
    val nisCode: String,
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class PostalCodeLanguageRegimeDTO(
    val beginDate: LocalDate?,
    val endDate: LocalDate?,
    val languageRegimeCode: String,
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class StreetDTO(
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)