package be.fgov.onerva.cu.backend.adapter.out.external.barema

import java.nio.charset.StandardCharsets
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatusCode
import org.springframework.web.client.HttpClientErrorException
import be.fgov.onerva.barema.api.BaremeApi
import be.fgov.onerva.barema.rest.model.ArticleDTO
import be.fgov.onerva.barema.rest.model.ArticleTypeDTO

import be.fgov.onerva.barema.rest.model.BaremeSearchTypeDTO
import be.fgov.onerva.barema.rest.model.BaremeTypeDTO
import be.fgov.onerva.barema.rest.model.BaremeV2DTO
import be.fgov.onerva.barema.rest.model.BaremeV2ItemDTO
import be.fgov.onerva.barema.rest.model.BaremeV2ResponseDTO
import be.fgov.onerva.barema.rest.model.DateCompareOperatorDTO
import be.fgov.onerva.barema.rest.model.SortDirectionDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class BaremaAdapterTest {

    @MockK
    private lateinit var baremaApi: BaremeApi

    @InjectMockKs
    private lateinit var baremaAdapter: BaremaAdapter

    @Nested
    inner class GetLatestBaremaTests {

        @Test
        fun `should return correct barema when valid response with valid code is received`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val expectedBaremaCode = "01-TEST"
            val expectedArticleCode = "ARTICLE-123"

            val baremeV2Response = createBaremaResponse(
                citizenId = citizenId,
                baremaCode = expectedBaremaCode,
                articleCode = expectedArticleCode,
                baremeType = BaremeTypeDTO.COMPLETE_UNEMPLOYMENT
            )

            every {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.barema).isEqualTo(expectedBaremaCode)
            assertThat(result?.article).isEqualTo(expectedArticleCode)

            verify(exactly = 1) {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            }
        }

        @ParameterizedTest
        @ValueSource(strings = ["01-PREFIX", "03-PREFIX", "57-PREFIX", "58-PREFIX", "02-PREFIX", "99-ANYTHING"])
        fun `should return barema regardless of code prefix`(baremaCode: String) {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val articleCode = "ARTICLE-123"

            val baremeV2Response = createBaremaResponse(
                citizenId = citizenId,
                baremaCode = baremaCode,
                articleCode = articleCode,
                baremeType = BaremeTypeDTO.COMPLETE_UNEMPLOYMENT
            )

            every {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.barema).isEqualTo(baremaCode)
            assertThat(result?.article).isEqualTo(articleCode)
        }

        @Test
        fun `should return barema with any code including previously invalid prefixes`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val baremaCode = "99-PREVIOUSLY-INVALID"
            val articleCode = "ARTICLE-123"

            val baremeV2Response = createBaremaResponse(
                citizenId = citizenId,
                baremaCode = baremaCode,
                articleCode = articleCode,
                baremeType = BaremeTypeDTO.COMPLETE_UNEMPLOYMENT
            )

            every {
                baremaApi.getBaremesByCitizenId(any(), any(), any(), any(), any())
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.barema).isEqualTo(baremaCode)
            assertThat(result?.article).isEqualTo(articleCode)
        }

        @Test
        fun `should return null when response contains no baremes`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)

            val baremeV2Response = BaremeV2ResponseDTO().citizenId(citizenId).baremes(emptyList())

            every {
                baremaApi.getBaremesByCitizenId(any(), any(), any(), any(), any())
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `should return null when baremes contain no items`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)

            val baremeV2Response =
                BaremeV2ResponseDTO().citizenId(citizenId).addBaremesItem(
                    BaremeV2DTO().items(emptyList()).type(
                        BaremeTypeDTO.COMPLETE_UNEMPLOYMENT
                    )
                )

            every {
                baremaApi.getBaremesByCitizenId(any(), any(), any(), any(), any())
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `should return barema with null article when no admissibility article exists`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val baremaCode = "01-TEST"

            // Create barema with non-admissibility article
            val baremeItem = BaremeV2ItemDTO().code(baremaCode)
                .addArticlesItem(ArticleDTO().type(ArticleTypeDTO.INDEMNISATION).code("REF-123"))

            val baremeV2Response =
                BaremeV2ResponseDTO().citizenId(citizenId).addBaremesItem(
                    BaremeV2DTO().addItemsItem(baremeItem).type(
                        BaremeTypeDTO.COMPLETE_UNEMPLOYMENT
                    )
                )

            every {
                baremaApi.getBaremesByCitizenId(any(), any(), any(), any(), any())
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.barema).isEqualTo(baremaCode)
            assertThat(result?.article).isNull()
        }

        @Test
        fun `should return barema with null article when articles list is empty`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val baremaCode = "01-TEST"

            // Create barema with empty articles list
            val baremeItem = BaremeV2ItemDTO().code(baremaCode).articles(emptyList())

            val baremeV2Response =
                BaremeV2ResponseDTO().citizenId(citizenId)
                    .addBaremesItem(BaremeV2DTO().addItemsItem(baremeItem).type(BaremeTypeDTO.COMPLETE_UNEMPLOYMENT))

            every {
                baremaApi.getBaremesByCitizenId(any(), any(), any(), any(), any())
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.barema).isEqualTo(baremaCode)
            assertThat(result?.article).isNull()
        }

        @Test
        fun `should return null when API throws NotFound exception`() {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val notFoundException =
                HttpClientErrorException.NotFound.create(
                    HttpStatusCode.valueOf(404),
                    "Not Found",
                    HttpHeaders(),
                    ByteArray(0),
                    StandardCharsets.UTF_8
                )

            every {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            } throws notFoundException

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNull()

            verify(exactly = 1) {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            }
        }

        @ParameterizedTest
        @ValueSource(strings = ["COMPLETE_UNEMPLOYMENT", "TEMPORARY_UNEMPLOYMENT", "OTHER"])
        fun `should return barema regardless of unemployment type`(typeString: String) {
            // Given
            val citizenId = 12345
            val requestDate = LocalDate.of(2025, 3, 15)
            val baremaCode = "01-TEST"
            val articleCode = "ARTICLE-123"
            val baremeType = BaremeTypeDTO.valueOf(typeString)

            val baremeV2Response = createBaremaResponse(
                citizenId = citizenId,
                baremaCode = baremaCode,
                articleCode = articleCode,
                baremeType = baremeType
            )

            every {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            } returns baremeV2Response

            // When
            val result = baremaAdapter.getLatestBarema(citizenId, requestDate)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.barema).isEqualTo(baremaCode)
            assertThat(result?.article).isEqualTo(articleCode)

            verify(exactly = 1) {
                baremaApi.getBaremesByCitizenId(
                    citizenId,
                    BaremeSearchTypeDTO.ALL,
                    requestDate,
                    SortDirectionDTO.DESC,
                    DateCompareOperatorDTO.LESS_OR_EQUAL
                )
            }
        }

    }

    private fun createBaremaResponse(
        citizenId: Int,
        baremaCode: String,
        articleCode: String,
        baremeType: BaremeTypeDTO,
    ): BaremeV2ResponseDTO {
        // Create article with ADMISSIBILITY type
        val article = ArticleDTO().type(ArticleTypeDTO.ADMISSIBILITY).code(articleCode)

        // Create barema item with the article
        val baremeItem = BaremeV2ItemDTO().code(baremaCode).addArticlesItem(article)

        // Create barema with the item
        val baremeV2 = BaremeV2DTO().addItemsItem(baremeItem)

        // Set the type of the barema
        baremeV2.type = baremeType

        // Create response with the barema
        return BaremeV2ResponseDTO().citizenId(citizenId).addBaremesItem(baremeV2)
    }
}