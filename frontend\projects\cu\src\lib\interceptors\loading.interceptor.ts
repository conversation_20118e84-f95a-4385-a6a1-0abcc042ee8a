import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  constructor(readonly loadingService: LoadingService) {}

  // Base path that should show loading indicator
  readonly apiBasePath = 'bff/api';

  // List of endpoint paths (relative to apiBasePath) that should NOT show the loading indicator
  readonly excludedEndpoints: string[] = [
    'lookup',
    'token'
  ];

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const isApiRequest = req.url.includes(this.apiBasePath);

    const isExcluded = isApiRequest && this.excludedEndpoints.some(endpoint => {
      const fullPath = `${this.apiBasePath}/${endpoint}`;
      return req.url.includes(fullPath);
    });

    const shouldShowLoader = isApiRequest && !isExcluded;

    if (shouldShowLoader) {
      this.loadingService.startLoading();
    }

    return next.handle(req).pipe(
      finalize(() => {
        if (shouldShowLoader) {
          this.loadingService.stopLoading();
        }
      })
    );
  }
}
