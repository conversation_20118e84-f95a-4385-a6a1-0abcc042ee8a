<onemrva-mat-panel color="primary" *ngIf="!FormUtilsService.isLogiclyDeleted(task)">
    <onemrva-mat-panel-title>
        {{ 'ERROR_MESSAGE.DEFAULT.TITLE' | translate }}
    </onemrva-mat-panel-title>

    <onemrva-mat-panel-content>
        <div class="panel-content">
            <img [src]="imgUrl" alt="Icon" class="error-icon"/>
            <div class="error-text">
                <h6 class="error-title">{{ 'ERROR_MESSAGE.DEFAULT.TITLE' | translate }}</h6>
                <p>{{ 'ERROR_MESSAGE.DEFAULT.MESSAGE' | translate }}</p>
                <p class="semibold">{{ 'ERROR_MESSAGE.DEFAULT.MAINFRAME_TREATMENT' | translate }}</p>
            </div>
        </div>
    </onemrva-mat-panel-content>
</onemrva-mat-panel>


<onemrva-mat-message-box color="warn" *ngIf="FormUtilsService.isLogiclyDeleted(task)">
        <span [innerHTML]="'ERROR_MESSAGE.TASK_DELETED.TITLE' | translate: {processLink: processLink }"
              class="error-title"
              style="margin: 0 0 0.5rem 0;"></span>
</onemrva-mat-message-box>