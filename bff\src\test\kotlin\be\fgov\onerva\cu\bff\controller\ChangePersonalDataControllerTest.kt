package be.fgov.onerva.cu.bff.controller

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.bff.rest.server.priv.model.Address
import be.fgov.onerva.cu.bff.rest.server.priv.model.AggregatedChangePersonalDataCaptureResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.AggregatedChangePersonalDataValidateResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateAggregatedChangePersonalDataRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateUnionContributionRequest
import be.fgov.onerva.cu.bff.service.ChangePersonalDataAggregationService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension

@ExtendWith(MockKExtension::class)
class ChangePersonalDataControllerTest {

    @MockK
    private lateinit var aggregationService: ChangePersonalDataAggregationService

    @InjectMockKs
    private lateinit var controller: ChangePersonalDataController

    @Nested
    inner class GetAggregatedChangePersonalDataRequest {
        @Test
        fun `getAggregatedChangePersonalDataCaptureRequest should return aggregated data when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val expectedResponse = AggregatedChangePersonalDataCaptureResponse()

            coEvery { aggregationService.getAggregatedRequestDataCapture(requestId) } returns expectedResponse

            // When
            val response = controller.getAggregatedChangePersonalDataCaptureRequest(requestId)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            assertThat(response.body).isEqualTo(expectedResponse)

            coVerify(exactly = 1) { aggregationService.getAggregatedRequestDataCapture(requestId) }
        }

        @Test
        fun `getAggregatedChangePersonalDataCaptureRequest should propagate service exceptions`() {
            // Given
            val requestId = UUID.randomUUID()
            val expectedException = RuntimeException("Service error")

            coEvery { aggregationService.getAggregatedRequestDataCapture(requestId) } throws expectedException

            // When/Then
            org.junit.jupiter.api.assertThrows<RuntimeException> {
                controller.getAggregatedChangePersonalDataCaptureRequest(requestId)
            }.also { exception ->
                assertThat(exception)
                    .isInstanceOf(RuntimeException::class.java)
                    .hasMessage("Service error")
            }

            coVerify(exactly = 1) { aggregationService.getAggregatedRequestDataCapture(requestId) }
        }

        @Test
        fun `getAggregatedChangePersonalDataCaptureRequest should handle empty response from service`() {
            // Given
            val requestId = UUID.randomUUID()
            val emptyResponse = AggregatedChangePersonalDataCaptureResponse()

            coEvery { aggregationService.getAggregatedRequestDataCapture(requestId) } returns emptyResponse

            // When
            val response = controller.getAggregatedChangePersonalDataCaptureRequest(requestId)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            assertThat(response.body)
                .isNotNull
                .isInstanceOf(AggregatedChangePersonalDataCaptureResponse::class.java)

            coVerify(exactly = 1) { aggregationService.getAggregatedRequestDataCapture(requestId) }
        }
    }

    @Nested
    inner class UpdateAggregatedChangePersonalDataRequestTest {
        @Test
        fun `updateAggregatedChangePersonalDataRequest should call service with all fields populated`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateAggregatedChangePersonalDataRequest(
                citizenInformation = UpdateCitizenInformationRequest(
                    birthDate = LocalDate.of(1990, 1, 1),
                    nationalityCode = 111,
                    address = Address(
                        countryCode = 150,
                        street = "Main Street",
                        houseNumber = "42",
                        zipCode = "1000",
                        city = "Brussels",
                        boxNumber = "A",
                        validFrom = LocalDate.of(2022, 1, 1),
                    )
                ),
                modeOfPayment = UpdateModeOfPaymentRequest(
                    otherPersonName = "John Doe",
                    iban = "****************",
                    bic = null,
                ),
                unionContribution = UpdateUnionContributionRequest(
                    authorized = true,
                    effectiveDate = LocalDate.of(2024, 1, 1)
                )
            )

            coEvery { aggregationService.updateAggregateRequest(requestId, request) } returns Unit

            // When
            val response = controller.updateAggregatedChangePersonalDataRequest(requestId, request)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            coVerify(exactly = 1) { aggregationService.updateAggregateRequest(requestId, request) }
        }

        @Test
        fun `updateAggregatedChangePersonalDataRequest should call service with minimal request`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateAggregatedChangePersonalDataRequest(
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null
            )

            coEvery { aggregationService.updateAggregateRequest(requestId, request) } returns Unit

            // When
            val response = controller.updateAggregatedChangePersonalDataRequest(requestId, request)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            coVerify(exactly = 1) { aggregationService.updateAggregateRequest(requestId, request) }
        }

        @Test
        fun `updateAggregatedChangePersonalDataRequest should handle service error`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateAggregatedChangePersonalDataRequest(
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null
            )
            val exception = RuntimeException("Service error")

            coEvery { aggregationService.updateAggregateRequest(requestId, request) } throws exception

            // When/Then
            org.assertj.core.api.Assertions.assertThatThrownBy {
                controller.updateAggregatedChangePersonalDataRequest(requestId, request)
            }.isInstanceOf(RuntimeException::class.java)
                .hasMessage("Service error")

            coVerify(exactly = 1) { aggregationService.updateAggregateRequest(requestId, request) }
        }

        @Test
        fun `updateAggregatedChangePersonalDataRequest should handle only citizen information update`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateAggregatedChangePersonalDataRequest(
                citizenInformation = UpdateCitizenInformationRequest(
                    birthDate = LocalDate.of(1990, 1, 1),
                    nationalityCode = 111,
                    address = Address(
                        countryCode = 150,
                        street = "Main Street",
                        houseNumber = "42",
                        zipCode = "1000",
                        city = "Brussels",
                        validFrom = LocalDate.of(2022, 1, 1),
                    )
                ),
                modeOfPayment = null,
                unionContribution = null
            )

            coEvery { aggregationService.updateAggregateRequest(requestId, request) } returns Unit

            // When
            val response = controller.updateAggregatedChangePersonalDataRequest(requestId, request)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            coVerify(exactly = 1) { aggregationService.updateAggregateRequest(requestId, request) }
        }

        @Test
        fun `updateAggregatedChangePersonalDataRequest should handle only mode of payment update`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateAggregatedChangePersonalDataRequest(
                citizenInformation = null,
                modeOfPayment = UpdateModeOfPaymentRequest(
                    otherPersonName = "John Doe",
                    iban = "****************",
                    bic = null,
                ),
                unionContribution = null
            )

            coEvery { aggregationService.updateAggregateRequest(requestId, request) } returns Unit

            // When
            val response = controller.updateAggregatedChangePersonalDataRequest(requestId, request)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            coVerify(exactly = 1) { aggregationService.updateAggregateRequest(requestId, request) }
        }

        @Test
        fun `updateAggregatedChangePersonalDataRequest should handle only union contribution update`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateAggregatedChangePersonalDataRequest(
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = UpdateUnionContributionRequest(
                    authorized = true,
                    effectiveDate = LocalDate.of(2024, 1, 1),
                )
            )

            coEvery { aggregationService.updateAggregateRequest(requestId, request) } returns Unit

            // When
            val response = controller.updateAggregatedChangePersonalDataRequest(requestId, request)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            coVerify(exactly = 1) { aggregationService.updateAggregateRequest(requestId, request) }
        }
    }

    @Nested
    inner class GetAggregatedChangePersonalDataValidateRequest {
        @Test
        fun `getAggregatedChangePersonalDataValidateRequest should return aggregated data when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val expectedResponse = AggregatedChangePersonalDataValidateResponse()

            coEvery { aggregationService.getAggregatedRequestDataValidate(requestId) } returns expectedResponse

            // When
            val response = controller.getAggregatedChangePersonalDataValidateRequest(requestId)

            // Then
            assertThat(response.statusCode.value()).isEqualTo(200)
            assertThat(response.body).isEqualTo(expectedResponse)

            coVerify(exactly = 1) { aggregationService.getAggregatedRequestDataValidate(requestId) }
        }
    }
}