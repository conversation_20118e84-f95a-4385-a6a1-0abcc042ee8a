package be.fgov.onerva.cu.backend.config

import org.springframework.boot.testcontainers.service.connection.ServiceConnection
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.testcontainers.containers.MSSQLServerContainer
import org.testcontainers.utility.DockerImageName

@Configuration
class TestContainerConfig {
    companion object {
        @JvmStatic
        @ServiceConnection
        val mssqlServerContainer: MSSQLServerContainer<*> = MSSQLServerContainer(
                DockerImageName.parse("docker-release.onemrva.priv/onemrva/mssql-server-linux:2019-6cf9d12")
                        .asCompatibleSubstituteFor("mcr.microsoft.com/mssql/server")
        ).apply {
            withPassword("A_Str0ng_Required_Password")
            withEnv("ACCEPT_EULA", "Y")
            withUrlParam("trustServerCertificate", "true")
            with<PERSON><PERSON><PERSON>(true)
        }.also { container ->
            container.start()
            // Set system properties for database connection
            System.setProperty("spring.datasource.url", container.jdbcUrl)
            System.setProperty("spring.datasource.username", container.username)
            System.setProperty("spring.datasource.password", container.password)
        }
    }

    @Bean
    fun mssqlServer(): MSSQLServerContainer<*> = mssqlServerContainer
}