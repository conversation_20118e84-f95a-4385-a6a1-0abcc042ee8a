package be.fgov.onerva.cu.backend.adapter.out.mapper

import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Citizen
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskStatusException
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO
import be.fgov.onerva.person.rest.model.BankUpdateRequestDTO
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenUpdateRequestDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import be.fgov.onerva.person.rest.model.PaymentTypeDTO
import be.fgov.onerva.person.rest.model.UnionDueUpdateRequestDTO
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress

fun CitizenDTO.toDomainCitizen(): Citizen {
    val firstName = this.firstname
    val lastName = this.lastname
    val numbox = this.numbox

    if (firstName == null || lastName == null) {
        throw InvalidExternalDataException("Invalid name format: $firstName $lastName")
    }
    if (numbox == null) {
        throw InvalidExternalDataException("Invalid numbox format: $numbox")
    }

    return Citizen(
        firstName = firstName,
        lastName = lastName,
        numbox = numbox,
        zipCode = this.zipCode?.toString() ?: ""
    )
}

fun WoTaskDTO.toDomainTaskStatus() = when (this.status) {
    "OPEN" -> if (this.step == "wait") WaveTaskStatus.WAITING else WaveTaskStatus.OPEN
    "CLOSED" -> WaveTaskStatus.CLOSED
    else -> throw WaveTaskStatusException("Unknown task status: ${this.status}")
}

fun CitizenInfoDTO.toCitizenInfoWithAddress(bcssCountryCode: Int?): HistoricalCitizenOnem {
    val address = this.address
    val addressObj = this.addressObj
    val firstName = this.firstName
    val lastName = this.lastName
    val numBox = this.numBox
    val flagNation = this.flagNation

    if (address == null) {
        throw InvalidExternalDataException("Invalid address format: $address")
    }
    if (addressObj == null) {
        throw InvalidExternalDataException("Invalid address format: addressObj is null")
    }
    // For foreign addresses, only street and zip are mandatory
    if (addressObj.street.isBlank() || addressObj.zip.isBlank()) {
        throw InvalidExternalDataException("Invalid address format: missing required fields (street or zip) in $addressObj")
    }
    if (firstName == null || lastName == null) {
        throw InvalidExternalDataException("Invalid name format: $firstName $lastName")
    }
    if (numBox == null) {
        throw InvalidExternalDataException("Invalid numbox format: $numBox")
    }
    if (flagNation == null) {
        throw InvalidExternalDataException("Invalid nationality format: $flagNation")
    }

    return HistoricalCitizenOnem(
        firstName = firstName,
        lastName = lastName,
        numbox = numBox.toInt(),
        nationalityCode = flagNation.toInt(),
        iban = this.bankAccount?.iban,
        bic = this.bankAccount?.bic,
        otherPersonName = if (this.paymentMode == 2) this.bankAccount?.holder ?: "$firstName $lastName" else null,
        birthDate = this.birthDate,
        authorized = this.unionDue?.mandateActive,
        effectiveDate = this.unionDue?.validFrom,
        bankAccountValueDate = this.bankAccount?.validFrom,
        address = AddressNullable(
            street = addressObj.street,
            houseNumber = addressObj.number, // Allow null for foreign addresses
            boxNumber = addressObj.box,
            zipCode = addressObj.zip,
            countryCode = bcssCountryCode,
            city = addressObj.city, // Allow null for foreign addresses
            valueDate = addressObj.validFrom,
        ),
        paymentMode = this.paymentMode,
    )
}

fun UpdateCitizenCommand.toCitizenUpdateRequestDTO(onemCountryCode: Int): CitizenUpdateRequestDTO {
    return CitizenUpdateRequestDTO(
        address = ForeignAddressDTO(
            street = this.address.street,
            zip = this.address.zipCode,
            city = this.address.city,
            validFrom = this.address.validFrom,
            number = this.address.houseNumber,
            box = this.address.boxNumber,
            countryCode = onemCountryCode,
        ),
        validFrom = this.valueDate,
        nationalityCode = this.nationalityCode,
        birthDate = this.birthDate,
        correlationId = this.correlationId,
        unemploymentOffice = this.unemploymentOffice,
        languageCode = null,
        bankInfo = BankUpdateRequestDTO(
            iban = this.modeOfPayment.iban,
            bic = this.modeOfPayment.bic,
            validFrom = this.modeOfPayment.validFrom,
            paymentType = PaymentTypeDTO.BANK_TRANSFER,
            accountHolder = this.modeOfPayment.otherPersonName,
        ),
        unionDueInfo = this.unionContribution.authorized.let { authorized ->
            UnionDueUpdateRequestDTO(
                unionDue = authorized ?: false,
                validFrom = this.unionContribution.effectiveDate,
            )
        }
    )
}

fun RegisterPerson.toCitizenInfoWithAddress(): HistoricalCitizenAuthenticSources {
    val primaryName = this.names.firstOrNull { it.seq == 1 }
        ?: throw InvalidExternalDataException("No name with sequence number 1 found")

    val primaryNationality = this.nationalities.firstOrNull()
        ?: throw InvalidExternalDataException("No nationality found")

    val residentialAddress =
        addresses.firstOrNull { it.atType == RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS && it.radiated == false && it.validityPeriod.beginDate != null } as? ResidentialAddress
            ?: throw InvalidExternalDataException("No residential address found")

    val valueDateTimestamp = residentialAddress.validityPeriod.beginDate!!
    val valueDate = valueDateTimestamp.let {
        java.time.Instant.ofEpochMilli(it)
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDate()
    }

    return HistoricalCitizenAuthenticSources(
        firstName = primaryName.firstName,
        lastName = this.lastName,
        nationalityCode = primaryNationality.nationalityCode,
        birthDate = this.birthdate,
        address = AddressNullable(
            street = "",
            houseNumber = residentialAddress.houseNumber,
            boxNumber = residentialAddress.boxNumber,
            zipCode = residentialAddress.postalCode,
            countryCode = null,
            city = null,
            valueDate = valueDate,
        ),
    )
}

