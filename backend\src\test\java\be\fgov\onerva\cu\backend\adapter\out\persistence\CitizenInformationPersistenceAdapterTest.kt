package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.history.Revision
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.Address
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationRepository
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import be.fgov.onerva.cu.backend.application.domain.Address as DomainAddress

@ExtendWith(MockKExtension::class)
class CitizenInformationPersistenceAdapterTest {
    @MockK
    lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @MockK
    lateinit var citizenInformationRepository: CitizenInformationRepository

    @InjectMockKs
    lateinit var citizenInformationPersistenceAdapter: CitizenInformationPersistenceAdapter

    @Nested
    @DisplayName("Persist Citizen Information Tests")
    inner class PersistCitizenInformation {
        private val requestId = UUID.randomUUID()
        private val employeeInfo = CitizenInformation(
            firstName = "John",
            lastName = "Doe",
            birthDate = LocalDate.of(1990, 1, 1),
            nationalityCode = 111,
            address = DomainAddress(
                street = "Main Street",
                houseNumber = "42",
                zipCode = "1000",
                city = "Brussels",
                countryCode = 150,
                boxNumber = "A",
                validFrom = LocalDate.of(2022, 1, 1),
            ),
        )

        @Test
        fun `should create new employee information when none exists`() {
            // Given
            val changePersonalDataEntity = ChangePersonalDataRequestEntity(
                c9Id = 12345,
                c9Type = "400",
                ssin = "123456789",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan1",
                operatorCode = 123,
                entityCode = "123456",
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1"
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalDataEntity
            every { citizenInformationRepository.save(any()) } answers { firstArg() }

            // When
            citizenInformationPersistenceAdapter.persistCitizenInformation(requestId, employeeInfo)

            // Then
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) {
                citizenInformationRepository.save(match {
                    it.updateStatus == UpdateStatus.EDITED && it.address.street == "Main Street"
                })
            }
        }

        @Test
        fun `should update existing employee information`() {
            // Given
            val changePersonalDataEntity = ChangePersonalDataRequestEntity(
                c9Id = 12345,
                c9Type = "400",
                ssin = "123456789",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan1",
                operatorCode = 123,
                entityCode = "123456",
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            val existingEmployeeInfo = CitizenInformationEntity(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1980, 1, 1),
                nationalityCode = 111,
                address = Address(
                    street = "Old Street",
                    houseNumber = "Old Number",
                    zipCode = "Old Zip",
                    city = "Old City",
                    countryCode = 150,
                    boxNumber = "Old Box",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                updateStatus = UpdateStatus.EDITED,
                request = changePersonalDataEntity,
            )
            changePersonalDataEntity.citizenInformation = existingEmployeeInfo


            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalDataEntity

            // When
            citizenInformationPersistenceAdapter.persistCitizenInformation(requestId, employeeInfo)

            // Then
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            assertThat(existingEmployeeInfo).extracting({ it.nationalityCode }, { it.updateStatus }).containsExactly(
                    111, UpdateStatus.EDITED
                )
        }

        @Test
        fun `should throw exception when request id not found`() {
            // Given
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy {
                citizenInformationPersistenceAdapter.persistCitizenInformation(requestId, employeeInfo)
            }.isInstanceOf(InvalidRequestIdException::class.java).hasMessage("Request ID not found: $requestId")
        }
    }

    @Nested
    @DisplayName("Get Citizen Information Tests")
    inner class GetCitizenInformation {
        private val requestId = UUID.randomUUID()

        @Test
        fun `should return employee information when found`() {
            // Given
            val changePersonalDataEntity = ChangePersonalDataRequestEntity(
                c9Id = 12345,
                c9Type = "400",
                ssin = "123456789",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan1",
                operatorCode = 123,
                entityCode = "123456",
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            val employeeInfoEntity = CitizenInformationEntity(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = Address(
                    street = "Main Street",
                    houseNumber = "42",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    boxNumber = "A",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                updateStatus = UpdateStatus.EDITED,
                request = changePersonalDataEntity,
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns ChangePersonalDataRequestEntity(
                c9Id = 12345,
                c9Type = "400",
                ssin = "123456789",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan1",
                operatorCode = 123,
                entityCode = "123456",
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            every { citizenInformationRepository.findByRequestId(requestId) } returns employeeInfoEntity

            // When
            val result = citizenInformationPersistenceAdapter.getCitizenInformation(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.nationalityCode).isEqualTo(111)
            assertThat(result?.address?.street).isEqualTo("Main Street")
        }

        @Test
        fun `should return null when employee information not found`() {
            // Given
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns ChangePersonalDataRequestEntity(
                c9Id = 12345,
                c9Type = "400",
                ssin = "123456789",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan1",
                operatorCode = 123,
                entityCode = "123456",
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            every { citizenInformationRepository.findByRequestId(requestId) } returns null

            // When
            val result = citizenInformationPersistenceAdapter.getCitizenInformation(requestId)

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `should throw exception when request id not found for get operation`() {
            // Given
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy {
                citizenInformationPersistenceAdapter.getCitizenInformation(requestId)
            }.isInstanceOf(InvalidRequestIdException::class.java).hasMessage("Request ID not found: $requestId")
        }
    }

    @Nested
    inner class GetLatestRevision {

        @Test
        fun `getLatestRevision should return correct revision number when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val expectedRevision = 5

            val citizenInformationEntity = CitizenInformationEntity(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.now(),
                nationalityCode = 111,
                address = Address(
                    street = "Main Street",
                    houseNumber = "42",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    boxNumber = "A",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                request = mockk(),
                updateStatus = UpdateStatus.EDITED,
            )

            val revisionEntity = mockk<Revision<Int, CitizenInformationEntity>>()
            every { revisionEntity.entity } returns citizenInformationEntity
            every { revisionEntity.revisionNumber } returns Optional.of(expectedRevision)

            every { citizenInformationRepository.findByRequestId(requestId) } returns citizenInformationEntity
            every { citizenInformationRepository.findLastChangeRevision(any()) } returns Optional.of(revisionEntity)

            // When
            val result = citizenInformationPersistenceAdapter.getLatestRevision(requestId)

            // Then
            assertThat(result).isEqualTo(expectedRevision)
            verify(exactly = 1) {
                citizenInformationRepository.findByRequestId(requestId)
                citizenInformationRepository.findLastChangeRevision(any())
            }
        }

        @Test
        fun `getLatestRevision should return 0 when no revision found`() {
            // Given
            val requestId = UUID.randomUUID()

            val citizenInformationEntity = CitizenInformationEntity(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.now(),
                nationalityCode = 111,
                address = Address(
                    street = "Main Street",
                    houseNumber = "42",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    boxNumber = "A",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                request = mockk(),
                updateStatus = UpdateStatus.EDITED,
            )

            every { citizenInformationRepository.findByRequestId(requestId) } returns citizenInformationEntity
            every { citizenInformationRepository.findLastChangeRevision(any()) } returns Optional.empty()

            // When
            val result = citizenInformationPersistenceAdapter.getLatestRevision(requestId)

            // Then
            assertThat(result).isZero()
            verify(exactly = 1) {
                citizenInformationRepository.findByRequestId(requestId)
                citizenInformationRepository.findLastChangeRevision(any())
            }
        }

        @Test
        fun `getLatestRevision should throw RequestIdNotFoundException when citizenInformation not found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { citizenInformationRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { citizenInformationPersistenceAdapter.getLatestRevision(requestId) }.isInstanceOf(
                    InvalidRequestIdException::class.java
                ).hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { citizenInformationRepository.findByRequestId(requestId) }
            verify(exactly = 0) { citizenInformationRepository.findLastChangeRevision(any()) }
        }
    }

    @Nested
    @DisplayName("Get Citizen Information for Revision Tests")
    inner class GetCitizenInformationForRevision {
        private val adapter = citizenInformationPersistenceAdapter

        @Test
        fun `getCitizenInformationForRevision should return citizen information for specific revision`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val revision = 2
            val entityId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val expectedRevision = 5

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901"
            ) {}.apply {
                id = requestId
            }

            val citizenInformationEntity = CitizenInformationEntity(
                request = requestEntity,
                birthDate = LocalDate.of(2000, 1, 1),
                nationalityCode = 111,
                firstName = "John",
                lastName = "Doe",
                address = Address(
                    street = "Main Street",
                    houseNumber = "123",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    boxNumber = "A",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                updateStatus = UpdateStatus.EDITED,
            ).apply { id = entityId }

            every {
                citizenInformationRepository.findByRequestId(requestId)
            } returns citizenInformationEntity
            val revisionEntity = mockk<Revision<Int, CitizenInformationEntity>>()
            every { revisionEntity.entity } returns citizenInformationEntity
            every { revisionEntity.revisionNumber } returns Optional.of(expectedRevision)
            every { citizenInformationRepository.findRevision(entityId, revision) } returns Optional.of(revisionEntity)

            // When
            val result = adapter.getCitizenInformationForRevision(requestId, revision)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.firstName).isEqualTo("John")
            assertThat(result?.lastName).isEqualTo("Doe")
            assertThat(result?.nationalityCode).isEqualTo(111)

            verify(exactly = 1) { citizenInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { citizenInformationRepository.findRevision(entityId, revision) }
        }

        @Test
        fun `getCitizenInformationForRevision should throw exception when request ID not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2

            every { citizenInformationRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getCitizenInformationForRevision(requestId, revision) }.isInstanceOf(
                    InvalidRequestIdException::class.java
                ).hasMessageContaining("Request ID not found")

            verify(exactly = 1) { citizenInformationRepository.findByRequestId(requestId) }
            verify(exactly = 0) { citizenInformationRepository.findRevision(any(), any()) }
        }

        @Test
        fun `getCitizenInformationForRevision should throw exception when revision not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2
            val entityId = UUID.randomUUID()

            every { citizenInformationRepository.findByRequestId(requestId) } returns CitizenInformationEntity(
                request = mockk(),
                birthDate = LocalDate.of(2000, 1, 1),
                nationalityCode = 111,
                firstName = "John",
                lastName = "Doe",
                address = Address(
                    street = "Main Street",
                    houseNumber = "123",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    boxNumber = "A",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                updateStatus = UpdateStatus.EDITED,
            ).apply { id = entityId }
            every { citizenInformationRepository.findRevision(entityId, revision) } returns Optional.empty()

            // When/Then
            assertThatThrownBy { adapter.getCitizenInformationForRevision(requestId, revision) }.isInstanceOf(
                    InvalidRequestIdException::class.java
                ).hasMessageContaining("Revision not found")

            verify(exactly = 1) { citizenInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { citizenInformationRepository.findRevision(entityId, revision) }
        }
    }

    @Nested
    @DisplayName("Patch Current Data with Revision Tests")
    inner class PatchCurrentDataWithRevision {

        @Test
        fun `patchCurrentDataWithRevision should successfully patch current data with revision data`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2
            val entityId = UUID.randomUUID()

            val requestEntity = mockk<ChangePersonalDataRequestEntity>()

            val currentData = CitizenInformationEntity(
                firstName = "OldFirstName",
                lastName = "OldLastName",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = Address(
                    street = "Old Street",
                    houseNumber = "1",
                    zipCode = "1000",
                    city = "Old City",
                    countryCode = 150,
                    boxNumber = "A",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                request = requestEntity,
                updateStatus = UpdateStatus.EDITED
            ).apply { id = entityId }

            val revisionData = CitizenInformationEntity(
                firstName = "NewFirstName",
                lastName = "NewLastName",
                birthDate = LocalDate.of(1985, 5, 15),
                nationalityCode = 111,
                address = Address(
                    street = "New Street",
                    houseNumber = "42",
                    zipCode = "2000",
                    city = "New City",
                    countryCode = 150,
                    boxNumber = "B",
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                request = requestEntity,
                updateStatus = UpdateStatus.EDITED
            )

            val revisionEntity = mockk<Revision<Int, CitizenInformationEntity>>()
            every { revisionEntity.entity } returns revisionData

            every { citizenInformationRepository.findByRequestId(requestId) } returns currentData
            every { citizenInformationRepository.findRevision(entityId, revision) } returns Optional.of(revisionEntity)
            every { citizenInformationRepository.save(any()) } returns currentData

            // When
            citizenInformationPersistenceAdapter.patchCurrentDataWithRevision(requestId, revision)

            // Then
            verify(exactly = 1) { citizenInformationRepository.findByRequestId(requestId) }
            verify(exactly = 1) { citizenInformationRepository.findRevision(entityId, revision) }
            verify(exactly = 1) { citizenInformationRepository.save(currentData) }

            // Vérifier que les données ont été mises à jour
            assertThat(currentData.firstName).isEqualTo("NewFirstName")
            assertThat(currentData.lastName).isEqualTo("NewLastName")
            assertThat(currentData.birthDate).isEqualTo(LocalDate.of(1985, 5, 15))
            assertThat(currentData.nationalityCode).isEqualTo(111)
            assertThat(currentData.address.street).isEqualTo("New Street")
            assertThat(currentData.address.houseNumber).isEqualTo("42")
            assertThat(currentData.address.zipCode).isEqualTo("2000")
            assertThat(currentData.address.city).isEqualTo("New City")
            assertThat(currentData.address.countryCode).isEqualTo(150)
            assertThat(currentData.address.boxNumber).isEqualTo("B")
            assertThat(currentData.updateStatus).isEqualTo(UpdateStatus.EDITED)
        }
    }
}
