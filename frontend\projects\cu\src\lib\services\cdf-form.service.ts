import {Injectable} from "@angular/core";
import {FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {ModeOfPaymentDetailResponse} from "@rest-client/cu-bff";
import {DateFormatType, DateUtils} from "../date.utils";
import CustomValidators, {validateNiss} from "../validators";
import {bankAccountValidator} from "@onemrvapublic/design-system/shared";

@Injectable({
    providedIn: "root",
})
export class CdfFormService {
    constructor(readonly formBuilder: FormBuilder) {
    }

    createEmptyForm(): FormGroup {
        return this.formBuilder.group({
            requestPanel: this.createRequestPanel(),
            employeePanel: this.createEmployeePanel(),
            bankPanel: this.createBankPanel(),
            syndicatePanel: this.createSyndicatePanel(),
        });
    }

    private createRequestPanel(): FormGroup {
        return this.formBuilder.group({
            startDate: ["", [
                Validators.required,
                CustomValidators.dateInPastOrPresent(),
            ]],
        });
    }

    private createEmployeePanel(): FormGroup {
        return this.formBuilder.group({
            niss: [{value: "", disabled: true}, [
                Validators.required,
                Validators.minLength(11),
                validateNiss,
            ]],
            birthDate: ["", [
                Validators.required,
                CustomValidators.dateInPastOrPresent(),
            ]],
            lastName: [{value: "", disabled: true}, []],
            firstName: [{value: "", disabled: true}, []],
            nationalityCode: ["", [
                Validators.required, CustomValidators.countryLookupValidator(),
            ]],
            searchedNationality: new FormControl(false),
            countryCode: ["", [
                Validators.required, CustomValidators.countryLookupValidator(),
            ]],
            searchedCountry: new FormControl(false),
            street: ["", [
                Validators.required,
                Validators.maxLength(255),
            ]],
            streetNbr: ["", [
                Validators.required,
                Validators.maxLength(11),
            ]],
            streetBox: ["", [
                Validators.maxLength(11),
            ]],
            postCode: ["", [
                Validators.required,
                Validators.maxLength(10),
            ]],
            city: ["", [
                Validators.required,
                Validators.maxLength(255),
            ]],
            addressFromDate: ["", [
                Validators.required,
            ]],
        });
    }

    private createBankPanel(): FormGroup {
        return this.formBuilder.group({
            isMyBankAccount: ["", [Validators.required]],
            otherPersonName: [{value: "", disabled: true}, [
                Validators.maxLength(255),
            ]],
            iban: ["", [Validators.required, bankAccountValidator()]],
            bic: ["", []],
            bankFromDate: ["", [Validators.required]],
        });
    }

    private createSyndicatePanel(): FormGroup {
        return this.formBuilder.group({
            syndicateContribution: ["", [Validators.required]],
            contributionDeductionFromTheMonth: ["", [Validators.required]],
            stopContributionDeductionFromTheMonth: ["", [Validators.required]],
        });
    }

    extractCitizenInformation(form: FormGroup): any {
        const employeePanel = form.get("employeePanel");
        if (!employeePanel) {
            return null;
        }

        return {
            birthDate: DateUtils.formatDateTo(employeePanel.get("birthDate")?.value, DateFormatType.BACKEND),
            nationalityCode: employeePanel.get("nationalityCode")?.value?.code,
            address: {
                countryCode: employeePanel.get("countryCode")?.value?.code,
                street: employeePanel.get("street")?.value,
                houseNumber: employeePanel.get("streetNbr")?.value,
                boxNumber: employeePanel.get("streetBox")?.value,
                zipCode: employeePanel.get("postCode")?.value,
                city: employeePanel.get("city")?.value,
                validFrom: DateUtils.formatDateTo(employeePanel.get("addressFromDate")?.value, DateFormatType.BACKEND),
            },
        };
    }

    extractPaymentInformation(form: FormGroup): ModeOfPaymentDetailResponse | undefined {
        const bankPanel = form.get("bankPanel");
        if (!bankPanel) {
            return undefined;
        }

        const isOtherPerson = bankPanel.get("isMyBankAccount")?.value === "bank_account_for_other_person_name";
        const iban = (bankPanel.get("iban")?.value || "").replace(/\s+/g, "");
        const isBelgianIban = iban.toUpperCase().startsWith("BE");

        return {
            otherPersonName: isOtherPerson ? bankPanel.get("otherPersonName")?.value : undefined,
            iban: iban || undefined,
            bic: !isBelgianIban ? bankPanel.get("bic")?.value : undefined,
            validFrom: DateUtils.formatDateTo(bankPanel.get("bankFromDate")?.value, DateFormatType.BACKEND),
        };
    }

    extractUnionContribution(form: FormGroup): any {
        const syndicatePanel = form.get("syndicatePanel");
        if (!syndicatePanel) {
            return null;
        }
        const syndicateContributionValue = syndicatePanel.get("syndicateContribution")?.value;
        const isContribution = (() => {
            if (syndicateContributionValue === "CONTRIBUTION") {
                return true;
            }
            if (syndicateContributionValue === "CONTRIBUTION_NOT_AUTHORIZED") {
                return null;
            }
            return false;
        })();

        const effectiveDate = isContribution ?
            syndicatePanel.get("contributionDeductionFromTheMonth")?.value :
            syndicatePanel.get("stopContributionDeductionFromTheMonth")?.value;

        return {
            authorized: isContribution,
            effectiveDate: effectiveDate?.toString().substring(0, 10),
        };
    }

    extractBasicInformation(form: FormGroup): any {
        const requestPanel = form.get("requestPanel");
        if (!requestPanel) {
            return null;
        }

        return {
            requestDate: DateUtils.formatDateTo(requestPanel.get("startDate")?.value, DateFormatType.BACKEND),
        };
    }
}
