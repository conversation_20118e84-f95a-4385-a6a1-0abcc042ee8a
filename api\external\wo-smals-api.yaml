openapi: 3.0.3
info:
  title: NssoWorkEnvironmentGateway REST service
  description: RESTful API for NSSO WorkEnvironment Gateway
  contact:
    name: Orchestrator Gateway Development Team
    url: http://www.smals.be
    email: <EMAIL>
  license:
    name: digitalbelgium
    url: http://digitalbelgium.be/
  version: "1.5"
servers:
  - url: "http://{hostname}:{port}/{basePath}/"
    variables:
      hostname:
        default: workenvironment-gateway.int.paas.socialsecurity.be
      port:
        default: "80"
      basePath:
        default: /REST/nssoWorkEnvironmentGateway/v1
paths:
  /doc/swagger.yaml:
    get:
      tags:
      - Documentation
      summary: Return the API documentation in YAML Swagger format
      operationId: getSwagger
      responses:
        200:
          description: successful operation
          content:
            application/yaml:
              schema:
                type: string
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /health:
    get:
      tags:
      - Monitoring
      summary: Check health of the service
      description: This resource is only available to supervision users
      externalDocs:
        url: https://www.gcloud.belgium.be/rest/#health
      operationId: checkHealth
      parameters:
      - name: type
        in: query
        description: Defines the which sanity checks must be ran
        schema:
          type: string
          default: default
          enum:
          - ping
          - default
          - deep
      responses:
        200:
          description: The service is UP
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
        503:
          description: The service is down or outOfService
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:monitoring
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /health/liveness:
    get:
      tags:
      - Monitoring
      summary: Liveness probe for Openshift
      operationId: livenessProbe
      responses:
        200:
          description: successful operation
          content:
            text/xml:
              schema:
                type: string
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:monitoring
      x-oauth2-required-scopes: any
  /health/readiness:
    get:
      tags:
      - Monitoring
      summary: Readiness probe for Openshift
      operationId: readinessProbe
      responses:
        200:
          description: successful operation
          content:
            text/xml:
              schema:
                type: string
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:monitoring
      x-oauth2-required-scopes: any
  /cachingWorkEnvironmentGateway/clearCaches:
    post:
      tags:
      - cache
      - support
      summary: Clear all caches
      description: Clear all cached resources from the cache
      operationId: clearAllCaches
      responses:
        200:
          description: Caches successfully cleared
          content: {}
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingWorkEnvironmentGateway/statistics:
    get:
      tags:
      - cache
      - support
      summary: Cache statistics
      description: Get the cache statistics
      operationId: statistics
      responses:
        200:
          description: Cache statistics successfully loaded
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Caches'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingWorkEnvironmentGateway/clearCaches/{cacheName}:
    post:
      tags:
      - cache
      - support
      summary: Clear a specific resource type
      description: Clear all resources of a specific type from the cache
      operationId: clearCache
      parameters:
      - name: cacheName
        in: path
        description: The name of the resource type to clear
        required: true
        schema:
          type: string
      responses:
        200:
          description: Cache successfully cleared
          content: {}
        404:
          description: Cache name not found
          content: {}
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingWorkEnvironmentGateway/content/{cacheName}:
    get:
      tags:
      - cache
      - support
      summary: Get the content a specific resource type
      description: Get all resources instances of a specific resource type from the
        cache
      operationId: getCacheContent
      parameters:
      - name: cacheName
        in: path
        description: The name of the resource type to retrieve the content from the
          cache
        required: true
        schema:
          type: string
      responses:
        200:
          description: Cache content successfully loaded
          content:
            application/json;charset=utf-8:
              schema:
                type: array
                items:
                  type: object
                  properties: {}
        404:
          description: Cache name not found
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoBusinessProcess/statistics:
    get:
      tags:
      - cache
      - support
      summary: Cache statistics
      description: Get the cache statistics
      operationId: prtaskStatistics
      responses:
        200:
          description: Cache statistics successfully loaded
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Caches'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoBusinessProcess/clearCaches:
    post:
      tags:
      - cache
      - support
      summary: Clear all caches
      description: Clear all cached resources from the cache
      operationId: clearAllPrtaskCaches
      responses:
        200:
          description: Caches successfully cleared
          content: {}
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoBusinessProcess/clearCaches/{cacheName}:
    post:
      tags:
      - cache
      - support
      summary: Clear a specific resource type
      description: Clear all resources of a specific type from the cache
      operationId: clearPrTaskCache
      parameters:
      - name: cacheName
        in: path
        description: The name of the resource type to clear
        required: true
        schema:
          type: string
      responses:
        200:
          description: Cache successfully cleared
          content: {}
        404:
          description: Cache name not found
          content: {}
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoBusinessProcess/content/{cacheName}:
    get:
      tags:
      - cache
      - support
      summary: Get the content a specific resource type
      description: Get all resources instances of a specific resource type from the
        cache
      operationId: getPrTaskCacheContent
      parameters:
      - name: cacheName
        in: path
        description: The name of the resource type to retrieve the content from the
          cache
        required: true
        schema:
          type: string
      responses:
        200:
          description: Cache content successfully loaded
          content:
            application/json;charset=utf-8:
              schema:
                type: array
                items:
                  type: object
                  properties: {}
        404:
          description: Cache name not found
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoDocument/clearCaches:
    post:
      tags:
      - cache
      - support
      summary: Clear all caches
      description: Clear all cached resources from the cache
      operationId: clearAllDocmanCaches
      responses:
        200:
          description: Caches successfully cleared
          content: {}
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoDocument/statistics:
    get:
      tags:
      - cache
      - support
      summary: Cache statistics
      description: Get the cache statistics
      operationId: docmanStatistics
      responses:
        200:
          description: Cache statistics successfully loaded
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Caches'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoDocument/clearCaches/{cacheName}:
    post:
      tags:
      - cache
      - support
      summary: Clear a specific resource type
      description: Clear all resources of a specific type from the cache
      operationId: clearDocmanCache
      parameters:
      - name: cacheName
        in: path
        description: The name of the resource type to clear
        required: true
        schema:
          type: string
      responses:
        200:
          description: Cache successfully cleared
          content: {}
        404:
          description: Cache name not found
          content: {}
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /cachingNssoDocument/content/{cacheName}:
    get:
      tags:
      - cache
      - support
      summary: Get the content a specific resource type
      description: Get all resources instances of a specific resource type from the
        cache
      operationId: getDocmanCacheContent
      parameters:
      - name: cacheName
        in: path
        description: The name of the resource type to retrieve the content from the
          cache
        required: true
        schema:
          type: string
      responses:
        200:
          description: Cache content successfully loaded
          content:
            application/json;charset=utf-8:
              schema:
                type: array
                items:
                  type: object
                  properties: {}
        404:
          description: Cache name not found
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /pollers:
    get:
      summary: Get pollers information
      description: Get pollers information
      operationId: getPollers
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Pollers'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
    post:
      summary: Create a new poller
      description: The method allows to create a new poller
      operationId: createPoller
      requestBody:
        description: The JSON body of the poller resource (see schema Document)
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/PollerConfiguration'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/PollerConfiguration'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: pollerBody
  /pollers/{pollerName}:
    delete:
      summary: Delete specific poller information
      description: Delete specific poller information
      operationId: deleteSpecificPoller
      parameters:
      - name: pollerName
        in: path
        description: The name of the updated poller
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content: {}
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
    patch:
      summary: Update specific poller information
      description: Update specific poller information
      operationId: updateSpecificPoller
      parameters:
      - name: pollerName
        in: path
        description: The name of the updated poller
        required: true
        schema:
          type: string
      requestBody:
        description: The JSON body of the poller resource (see schema Document)
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/PollerConfiguration'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/PollerConfiguration'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: pollerBody
  /mediaTypes:
    get:
      tags:
      - mediaType
      - configuration
      summary: Search a media type and its features
      description: Search a media type configuration
      operationId: searchMediaTypes
      parameters:
      - name: code
        in: query
        description: The type, tree and subtype of the media type (concatenate)
        schema:
          type: string
      - name: externalServiceCreation
        in: query
        description: Possibility of creation of the media type by the external service
        schema:
          type: boolean
      - name: previewFormat
        in: query
        description: The format of preview of the media type
        schema:
          type: string
      - name: signAvailable
        in: query
        description: The sign availability of the media type
        schema:
          type: boolean
      - name: shipmentAvailable
        in: query
        description: The shipment availability of the media type
        schema:
          type: boolean
      - name: editionSource
        in: query
        description: The edition source of the media type
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/MediaTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /tasks:
    get:
      tags:
      - task
      - instance
      summary: Search task matching the specified query parameters
      operationId: searchTask
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: searchType
        in: query
        description: "Two types of searches are provided: \n * full: retrieving a\
          \ list of elements with their entire details \n * fast: retrieving a list\
          \ of elements with some basic information \n   * The task metadata (task\
          \ id, type, title, due date, priority, state, technical status, responsible,\
          \ business identifier) \n   * The parent process code, labels and its identifier\
          \ \n   * The specific search criteria (value, labels and code) \n\n fast\
          \ search is to be used for large search to help user selecting the right\
          \ element. full search is to be used on small set of elements for which\
          \ a lot of information is required"
        schema:
          type: string
          default: fast
          enum:
          - full
          - fast
      - name: creationDate_gte
        in: query
        description: Search criterium on the minimal creation date
        schema:
          type: string
      - name: creationDate_lte
        in: query
        description: Search criterium on the maximal creation date
        schema:
          type: string
      - name: dueDate_gte
        in: query
        description: Search criterium on the minimal due date
        schema:
          type: string
      - name: dueDate_lte
        in: query
        description: Search criterium on the maximal due date
        schema:
          type: string
      - name: closeDate_gte
        in: query
        description: Search criterium on the minimal close date
        schema:
          type: string
      - name: closeDate_lte
        in: query
        description: Search criterium on the maximal close date
        schema:
          type: string
      - name: lastUpdateDate_gte
        in: query
        description: Search criterium on the minimal last update date
        schema:
          type: string
      - name: lastUpdateDate_lte
        in: query
        description: Search criterium on the maximal last update date
        schema:
          type: string
      - name: priority
        in: query
        description: Search criterium on task priorities
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: taskType
        in: query
        description: Search criterium on task types
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: title
        in: query
        description: Search criterium on task title. The search is a 'like'
        schema:
          type: string
      - name: state
        in: query
        description: Search criterium on task states
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: technicalStatus
        in: query
        description: Search criterium on task technical status
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
            enum:
            - OPEN
            - CLOSED
            - DELETED
            - open
            - deleted
            - closed
      - name: businessDomainType
        in: query
        description: Search criterium on task business domain types
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: businessId
        in: query
        description: Search criterium on task business identifier
        schema:
          type: string
      - name: entitySourceName
        in: query
        description: |-
          Search criterium on entity hosting source.
           To be used in combination with the entitySourceId
        style: form
        explode: false
        schema:
          type: array
          items:
            type: string
      - name: entitySourceId
        in: query
        description: Search criterium on entity identifier (for the specified source
          name). \n To be used in combination with the entitySourceName
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: entityRoleType
        in: query
        description: "Search criterium on entity role types. \n To be used in combination\
          \ with the entitySourceId"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: entityQualityType
        in: query
        description: "Search criterium on entity quality types. \n To be used in combination\
          \ with the entitySourceId"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: responsible
        in: query
        description: Search criterium on a task responsibles
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: team
        in: query
        description: Search criterium on a set of reponsibles known in the organigram
          (aka team)
        schema:
          type: string
      - name: parentProcessId
        in: query
        description: Search criterium on task parent process identifier
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: taskId
        in: query
        description: Search criterium on task identifiers
        style: form
        explode: false
        schema:
          type: array
          items:
            type: string
      - name: businessData
        in: query
        description: Search criterium on task business data
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: associatedDocumentId
        in: query
        description: Retrieve all the tasks referring a given document identifier.
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: producer
        in: query
        description: "Search criterium on the producer. \n To be used in combination\
          \ with the correlationId"
        schema:
          type: string
      - name: correlationId
        in: query
        description: "Search criterium on the correlationId. \n To be used in combination\
          \ with the producer"
        schema:
          type: string
      - name: confidentialityFilter
        in: query
        description: Must the confidentialityFilter be applied or not?
        schema:
          type: boolean
          default: true
      - name: textSearch
        in: query
        description: Text search on the available task attributes (i.e. title, business
          data value, business identifier, correlationId, task type, parent process
          type)
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 0
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sortBy
        in: query
        description: Field to sort by, one of [id, state, creationDate, dueDate, priority,
          taskType, closeDate, lastUpdateDate]
        schema:
          type: string
          default: created
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Tasks'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - task
      - instance
      summary: Create a task
      description: "In order to create a task one has to provide the following information:\
        \ \n * The expected title in FR/NL \n * The task type (one that exists) \n\
        \ * The parent process identifier (one that exists) \n * The responsible (department\
        \ or agent) \n * The business domain \n * The concerned entities and their\
        \ roles/qualities \n * The priority \n \n One can also provide: \n * A due\
        \ date \n * A correlation id (with a producer name) \n * A business identifier\
        \ \n * Business data values"
      operationId: createTask
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: Data of the task to be created
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Task'
        required: false
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Task'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Task already exists for producer & producerReference
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /tasks/{taskId}:
    get:
      tags:
      - task
      - instance
      summary: Retrieve a given task
      description: By providing a task identifier, one can retrieve a given task.
        The method returns all the details of the tasks.
      operationId: retrieveTask
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: taskId
        in: path
        description: The task identifier
        required: true
        schema:
          type: integer
          format: int64
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Task'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task found with the given task identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    put:
      tags:
      - task
      - instance
      summary: Update a given task
      description: "By providing a task identifier, one can update a given task. The\
        \ whole body of the task has to be provided with the PUT.\n One can change\
        \ the following information: \n * The title in FR/NL \n * The concerned entities\
        \ and their roles/qualities \n * The priority \n * The due date \n * The business\
        \ data values"
      operationId: updateTask
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: taskId
        in: path
        description: The task identifier
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        description: The full body of the task to update
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Task'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Task'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task found with the given task identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - task
      - instance
      summary: Update a given data of a task
      description: Method to update a data of a given task or to soft delete a task
        by updating the technical state (deleted)
      operationId: patchTask
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: taskId
        in: path
        description: The task identifier
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        description: The task details to update
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Task'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Task'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task found with the given task identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
        - scope:rsz-onss:workenvironmentgateway-rest:dataadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /tasks/{taskId}/act:
    post:
      tags:
      - task
      - instance
      summary: Take an action on a task instance
      description: Take one of the available action on a task instance
      operationId: actOnTask
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: taskId
        in: path
        description: The task identifier on which to act
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        description: The action to be performed
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Action'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Task'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task found with the given task identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /tasks/act:
    post:
      tags:
      - task
      - instance
      summary: Take an action on task instances
      description: Take one of the available action on a set of task instances given
        in parameters
      operationId: actOnTasks
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: taskId
        in: query
        description: The task identifier on which to act
        required: true
        style: form
        explode: true
        schema:
          type: array
          items:
            type: integer
            format: int64
      requestBody:
        description: The action to be performed
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Action'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Errors'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task found with the given task identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /tasks/{resourceId}/authorizations:
    get:
      tags:
      - task
      summary: Retrieve the user authorization for a given resource
      description: By providing a resource identifier, one can retrieve the user authorization
        for that resource.
      operationId: retrieveTaskAuthorizations
      parameters:
      - name: resourceId
        in: path
        description: The resource identifier
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Authorizations'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No resource found with the given identifier
          content: {}
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /tasks/{taskId}/fixBusinessData:
    put:
      tags:
      - task
      - instance
      - support
      summary: Technical method reserved to administrators to fix the configuration
        of business data
      operationId: fixTaskBusinessData
      parameters:
      - name: taskId
        in: path
        description: The identifier of the task
        required: true
        schema:
          type: integer
          format: int64
      responses:
        200:
          description: OK
          content: {}
        400:
          description: Bad request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task with that identifier found
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /tasks/{taskId}/documents:
    post:
      summary: Add a document to a given task
      operationId: addDocumentToTask
      parameters:
      - name: taskId
        in: path
        description: The task identifier in which a document has to be added
        required: true
        schema:
          type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - document
              - documentBody
              properties:
                document:
                  type: string
                  description: The document content linked to the version
                  format: binary
                documentBody:
                  type: string
                  description: The JSON body of the document resource (see schema
                    Document)
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        204:
          description: No process found with the given identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /versions/{resourceId}/authorizations:
    get:
      tags:
      - task
      summary: Retrieve the user authorization for a given resource
      description: By providing a resource identifier, one can retrieve the user authorization
        for that resource.
      operationId: retrieveVersionAuthorizations
      parameters:
      - name: resourceId
        in: path
        description: The resource identifier
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Authorizations'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No resource found with the given identifier
          content: {}
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /processes:
    get:
      tags:
      - process
      - instance
      summary: Search process matching the specified query parameters
      operationId: searchProcess
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: searchType
        in: query
        description: "Two types of searches are provided:\n \n * full: retrieving\
          \ a list of elements with their entire details \n * fast: retrieving a list\
          \ of elements with some basic information \n   * The process metadata (process\
          \ id, type, title, due date, priority, state, technical status, responsible,\
          \ business ) \n   * The parent process code, labels and its \n   * The specific\
          \ search criteria (value, labels and code) \n \n\n fast search is to be\
          \ used for large search to help user selecting the right element. full search\
          \ is to be used on small set of elements for which a lot of information\
          \ is required"
        schema:
          type: string
          default: fast
          enum:
          - full
          - fast
      - name: creationDate_gte
        in: query
        description: Search criterium on the minimal creation date
        schema:
          type: string
      - name: creationDate_lte
        in: query
        description: Search criterium on the maximal creation date
        schema:
          type: string
      - name: dueDate_gte
        in: query
        description: Search criterium on the minimal due date
        schema:
          type: string
      - name: dueDate_lte
        in: query
        description: Search criterium on the maximal due date
        schema:
          type: string
      - name: closeDate_gte
        in: query
        description: Search criterium on the minimal close date
        schema:
          type: string
      - name: closeDate_lte
        in: query
        description: Search criterium on the maximal close date
        schema:
          type: string
      - name: lastUpdateDate_gte
        in: query
        description: Search criterium on the minimal last update date
        schema:
          type: string
      - name: lastUpdateDate_lte
        in: query
        description: Search criterium on the maximal last update date
        schema:
          type: string
      - name: processType
        in: query
        description: Search criterium on process types
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: state
        in: query
        description: Search criterium on process states
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: technicalStatus
        in: query
        description: Search criterium on process technical status
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
            enum:
            - OPEN
            - CLOSED
            - DELETED
            - open
            - deleted
            - closed
      - name: businessDomainType
        in: query
        description: Search criterium on process business domain type
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: entitySourceName
        in: query
        description: |-
          Search criterium on entity hosting source.
           To be used in combination with the entitySourceId
        schema:
          type: string
      - name: entitySourceId
        in: query
        description: Search criterium on entity identifier (for the specified source
          name). \n To be used in combination with the entitySourceName
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: entityRoleType
        in: query
        description: "Search criterium on entity role types. \n To be used in combination\
          \ with the entitySourceId"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: entityQualityType
        in: query
        description: "Search criterium on entity quality types. \n To be used in combination\
          \ with the entitySourceId"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: parentProcessId
        in: query
        description: |-
          Search criterium on process parent process.
           To be used in combination with the processType
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: businessData
        in: query
        description: Search criterium on task business data
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: associatedDocumentId
        in: query
        description: Retrieve all the tasks referring a given document identifier.
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: producer
        in: query
        description: "Search criterium on the producer. \n To be used in combination\
          \ with the correlationId"
        schema:
          type: string
      - name: correlationId
        in: query
        description: "Search criterium on the correlationId. \n To be used in combination\
          \ with the producer"
        schema:
          type: string
      - name: confidentialityFilter
        in: query
        description: Must the confidentialityFilter be applied or not?
        schema:
          type: boolean
          default: true
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 0
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sortBy
        in: query
        description: Field to sort by, one of [created, dueDate, processType, closeDate,
          businessState]
        schema:
          type: string
          default: created
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Processes'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - process
      - instance
      summary: Create a process
      description: "In order to create a process one has to provide the following\
        \ information: \n * The process type (one that exists) \n * The business domain\
        \ \n * The concerned entities and their roles/qualities \n One can also provide:\
        \ \n * A correlation id (with a producer name) \n * Business data values \n\
        \ * Milestones \n * The parent process identifier (one that exists)"
      operationId: createProcess
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: Create a process
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Process'
        required: false
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Process'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /processes/documentSearch:
    get:
      tags:
      - process
      - instance
      summary: Search process matching the specified query parameters
      operationId: searchDocumentProcess
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: searchType
        in: query
        required: true
        schema:
          type: string
          default: fast
          enum:
          - full
          - fast
      - name: processType
        in: query
        description: Search criterium on process types
        schema:
          type: string
      - name: state
        in: query
        description: Search criterium on process states
        schema:
          type: string
      - name: technicalStatus
        in: query
        description: Search criterium on process technical status
        schema:
          type: string
          default: open
          enum:
          - open
          - closed
          - deleted
          - OPEN
          - DELETED
          - CLOSED
      - name: associatedDocumentId
        in: query
        description: Retrieve all the processes referring a given document identifier.
        schema:
          type: string
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 0
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sortBy
        in: query
        description: Field to sort by, one of [id, state, creationDate, dueDate, processType,
          closeDate, lastUpdateDate]
        schema:
          type: string
          default: created
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Processes'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /processes/{processId}:
    get:
      tags:
      - process
      - instance
      summary: Retrieve a given process
      description: By providing a process identifier, one can retrieve a given process.
        The method returns all the details of the process.
      operationId: retrieveProcess
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: processId
        in: path
        description: The process identifier
        required: true
        schema:
          type: integer
          format: int64
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Process'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process found with the given identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    put:
      tags:
      - process
      - instance
      summary: Update a given process
      description: "By providing a process identifier, one can update a given process.\
        \ The whole body of the process has to be provied with the PUT.\n One can\
        \ change the following information: \n * The concerned entities and their\
        \ roles/qualities \n * The milestones \n * The business data values"
      operationId: updateProcess
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: processId
        in: path
        description: The process identifier
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        description: The full body of the process to update
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Process'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Process'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process found with the given identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - process
      - instance
      summary: Patch a given process
      description: Method to update a data of a given process or to soft delete a
        process by updating the technical state (deleted)
      operationId: patchProcess
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: processId
        in: path
        description: The process identifier
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        description: The process details to update
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Process'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Process'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process found with the given identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
        - scope:rsz-onss:workenvironmentgateway-rest:dataadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /processes/{processId}/documents:
    post:
      summary: Add a document to a given process
      operationId: addDocumentToProcess
      parameters:
      - name: processId
        in: path
        description: The process identifier in which a file has to be added
        required: true
        schema:
          type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - document
              - documentBody
              properties:
                document:
                  type: string
                  description: The document content linked to the version
                  format: binary
                documentBody:
                  type: string
                  description: The JSON body of the document resource (see schema
                    Document)
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        204:
          description: No process found with the given identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
        - scope:rsz-onss:workenvironmentgateway-rest:dataadmin
      x-oauth2-required-scopes: any
  /tasks/sync:
    post:
      tags:
      - task
      - support
      summary: Resync the tasks with external systems
      description: Resync the tasks with external systems
      operationId: resyncTask
      responses:
        200:
          description: OK
          content: {}
        400:
          description: Bad request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /processes/{processId}/fixBusinessData:
    put:
      tags:
      - process
      - instance
      - support
      summary: Technical method reserved to administrators to fix the configuration
        of business data
      operationId: fixProcessBusinessData
      parameters:
      - name: processId
        in: path
        description: The identifier of the process
        required: true
        schema:
          type: integer
          format: int64
      responses:
        200:
          description: OK
          content: {}
        400:
          description: Bad request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process with that identifier found
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /processes/{processId}/fixProcessAssociations:
    put:
      tags:
      - process
      - instance
      - support
      summary: Fix bidirectionnal associations for processes
      operationId: fixProcessAssociations
      parameters:
      - name: processId
        in: path
        description: ID of the process to update
        required: true
        schema:
          type: integer
          format: int64
      responses:
        200:
          description: OK
          content: {}
        400:
          description: Bad request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process found for the given ID
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /processes/{processId}/act:
    post:
      tags:
      - process
      - instance
      summary: Take an action on a process instance
      description: Take one of the available action on a process instance
      operationId: actOnProcess
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: processId
        in: path
        description: The process identifier on which to act
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        description: The action to be performed
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Action'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Task'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process found with the given process identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /events:
    post:
      tags:
      - event
      - instance
      summary: Publish events
      description: |-
        The method allows to publish or to republish some events.
         To republish an event, one has to provide the original event identifier. A link between the original event and the newly created one is going to be kept in database.
      operationId: publishEvents
      requestBody:
        description: The events to be published
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Events'
        required: false
      responses:
        201:
          description: Created
          content: {}
        400:
          description: Bad request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /events/{eventId}:
    get:
      tags:
      - process
      - instance
      summary: Retrieve an event
      description: The method allows to retrieve an event with a given identifier
      operationId: retrieveEvent
      parameters:
      - name: eventId
        in: path
        description: ID of the event
        required: true
        schema:
          type: integer
          format: int64
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Event'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /taskTypes:
    get:
      tags:
      - task
      - configuration
      summary: Search task type matching the specified query parameters
      operationId: searchTaskConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/TaskTypes'
        204:
          description: No content
          content: {}
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - task
      - configuration
      summary: Create a task type
      description: Create a new task type that is identified by a unique code
      operationId: createTaskConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: The data for the configuration
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/TaskType'
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/TaskType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /taskTypes/{code}:
    get:
      tags:
      - task
      - configuration
      summary: Retrieve a task type
      description: Retrieve one of the existing task configuration using its unique
        code
      operationId: retrieveTaskConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the task type
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/TaskType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    put:
      tags:
      - task
      - configuration
      summary: Update a task type
      description: Update some data of a task type
      operationId: updateTaskConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the task type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the task type
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/TaskType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/TaskType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task type found
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - task
      - configuration
      summary: Patch a task
      description: Patch some data of the configuration of a task. The patch is the
        only method allowed to make a configuration obsolete.
      operationId: patchTaskConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the task type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the task type
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/TaskType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/TaskType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No task type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /processTypes:
    get:
      tags:
      - process
      - configuration
      summary: Search process types matching the specified query parameters
      description: Search process types matching the specified query parameters
      operationId: searchProcessConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ProcessTypes'
        204:
          description: No content
          content: {}
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - process
      - configuration
      summary: Create a process type
      description: Create a new process type that is identified by a unique code
      operationId: createProcessConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: The data for the process type
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/ProcessType'
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ProcessType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /processTypes/{code}:
    get:
      tags:
      - process
      - configuration
      summary: Retrieve the details of a process type
      description: Retrieve the details of a process type using its unique code
      operationId: retrieveProcessConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the process type
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ProcessType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process type found
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    put:
      tags:
      - process
      - configuration
      summary: Update a process type
      description: Update some data of a process type
      operationId: updateProcessConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the process type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the configuration
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/ProcessType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ProcessType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - process
      - configuration
      summary: Patch a process type
      description: Patch some data of a process type. The patch is the only method
        allowed to make a configuration obsolete.
      operationId: patchProcessConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the process type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the configuration
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/ProcessType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ProcessType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No process type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /businessDomainTypes:
    get:
      tags:
      - businessDomain
      - configuration
      summary: Search business domain types matching the specified query parameters
      description: Search business domain types matching the specified query parameters
      operationId: searchBusinessDomainType
      parameters:
      - name: activityDomainCode
        in: query
        description: The type of activity domain
        schema:
          type: string
      - name: processTypeCode
        in: query
        description: The type of process type
        schema:
          type: string
      - name: includingProcess
        in: query
        description: Does the results list has to include business domain having at
          least one process type?
        schema:
          type: string
      - name: includingManuallyCreatableProcess
        in: query
        description: Does the results list has to include business domain having at
          least one process type that can be created by an agent?
        schema:
          type: string
      - name: includingManuallyCreatableDocument
        in: query
        description: Does the results list has to include business domain having at
          least one document type that can be created by an agent?
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDomainTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - businessDomain
      - configuration
      summary: Create a business domain type
      description: Create a new business domain type that is identified by a unique
        code
      operationId: createBusinessDomainConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: The data for the configuration
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/BusinessDomainType'
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDomainType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /businessDomainTypes/{code}:
    get:
      tags:
      - businessDomain
      - configuration
      summary: Retrieve a business domain configuration
      description: Retrieve the details of a business domain
      operationId: retrieveBusinessDomainType
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The identifier of the business domain type
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDomainType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No business domain type found with that code
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    put:
      tags:
      - businessDomain
      - configuration
      summary: Update a business domain type
      description: Update some data of the type of a business domain
      operationId: updateBusinessDomainConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the business domain type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the business domain type
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/BusinessDomainType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDomainType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No business domain type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - businessDomain
      - configuration
      summary: Patch a business domain type
      description: Patch some data of the business domain type. The patch is the only
        method allowed to make a configuration obsolete.
      operationId: patchBusinessDomainConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the business domain type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the configuration
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/BusinessDomainType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDomainType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No business domain type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: Precondition Failed
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /milestoneTypes:
    get:
      tags:
      - process
      - configuration
      summary: Retrieve the milestone types
      description: Retrieve the complete list of milestone types available
      operationId: retrieveMilestoneTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/MilestoneTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /milestoneTypes/{code}:
    patch:
      tags:
      - process
      - configuration
      summary: Patch a milestone type
      description: Patch some data of a milestone type. The patch is the only method
        allowed to make a configuration obsolete.
      operationId: patchMilestoneTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the milestone type
        required: true
        schema:
          type: string
      requestBody:
        description: The milestone type details to update
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/MilestoneType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/MilestoneTypes'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No milestone type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /businessDataTypes:
    get:
      tags:
      - businessDataType
      - configuration
      summary: Retrieve the list of business data types
      description: Retrieve the complete list of availables business data types
      operationId: retrieveBusinessDataTypes
      parameters:
      - name: technicalStatus
        in: query
        description: Search criterium on business data type technical status
        schema:
          type: string
          enum:
          - OPEN
          - OBSOLETE
      - name: usageType
        in: query
        description: Search criterium on which process/task uses this business data
          type
        schema:
          type: string
          enum:
          - PROCESS
          - TASK
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 1
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDataTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
  /businessDataTypes/{code}:
    patch:
      tags:
      - task
      - process
      - configuration
      summary: Patch a business data type
      description: Patch some data of a business data type. The patch is the only
        method allowed to make a configuration obsolete.
      operationId: patchBusinessDataConfiguration
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: The code of the business data type
        required: true
        schema:
          type: string
      requestBody:
        description: The data for the configuration
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/BusinessDataType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/BusinessDataType'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No business data type found with that code
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /refData/priorities:
    get:
      tags:
      - task
      - reference
      summary: Retrieve the possible priorities
      operationId: retrievePriorities
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Priorities'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/states:
    get:
      tags:
      - task
      - process
      - reference
      summary: Retrieve the possible states
      description: List all the states and their labels
      operationId: retrieveStates
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/States'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/entityQualityTypes:
    get:
      tags:
      - task
      - process
      - reference
      summary: Retrieve the entity quality types
      description: List all the entity quality types and their labels
      operationId: retrieveEntityQualityTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/EntityQualityTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/entityRoleTypes:
    get:
      tags:
      - task
      - process
      - reference
      summary: Retrieve the entity role types
      description: List all the entity role types and their labels
      operationId: retrieveEntityRoleTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/EntityRoleTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /documents:
    get:
      tags:
      - document
      - instance
      summary: Search documents matching the specified query parameters
      description: List all the documents matching the specified filters. The list
        comes sorted (as specified or with the default sort) and pagined (as specified
        or with the default behavior)
      operationId: searchDocuments
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: searchType
        in: query
        required: true
        schema:
          type: string
          enum:
          - full
          - fast
      - name: completeness
        in: query
        description: Filter on the completeness of the metadata of the document. true
          means document with all the mandatory metadata provided. false the opposite.
          empty means both
        schema:
          type: boolean
      - name: documentId
        in: query
        description: Search criterium on documents identifiers
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: creationDate_gte
        in: query
        description: Search criterium on the creation date
        schema:
          type: string
      - name: creationDate_lte
        in: query
        description: Search criterium on the creation date
        schema:
          type: string
      - name: closingDate_gte
        in: query
        description: Search criterium on the closing date
        schema:
          type: string
      - name: closingDate_lte
        in: query
        description: Search criterium on the closing date
        schema:
          type: string
      - name: lastUpdateDate_gte
        in: query
        description: Search criterium on the minimal last update date
        schema:
          type: string
      - name: lastUpdateDate_lte
        in: query
        description: Search criterium on the maximal last update date
        schema:
          type: string
      - name: documentType
        in: query
        description: Search criterium on the document types
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: origin
        in: query
        description: Search criterium on origins of documents
        schema:
          type: string
      - name: entitySourceName
        in: query
        description: |-
          Search criterium on entity hosting source.
           To be used in combination with the entitySourceId
        schema:
          type: string
      - name: entitySourceId
        in: query
        description: "Search criterium on entity identifier (for the specified source\
          \ name). \n To be used in combination with the entitySourceName"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: entityRoleType
        in: query
        description: Search criterium on entity roles. To be used in combination with
          the entitySourceId
        schema:
          type: string
          enum:
          - concerned
          - addressee
          - origin
      - name: state
        in: query
        description: Search on the document state
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: contentLanguage
        in: query
        description: Search on the document language
        schema:
          type: string
      - name: title
        in: query
        description: Search on the document title
        schema:
          type: string
      - name: technicalStatus
        in: query
        description: Search criterium on technical status
        schema:
          type: string
          enum:
          - open
          - closed
          - deleted
          - OPEN
          - DELETED
          - CLOSED
      - name: internalReference
        in: query
        description: Search on the internal reference of the document
        schema:
          type: string
      - name: producer
        in: query
        description: "Search criterium on the producer. \n To be used in combination\
          \ with the correlationId"
        schema:
          type: string
      - name: correlationId
        in: query
        description: "Search criterium on the correlationId. \n To be used in combination\
          \ with the producer"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: confidentialityFilter
        in: query
        description: Must the confidentialityFilter be applied or not?
        schema:
          type: boolean
          default: true
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 1
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sort
        in: query
        description: Field to sort by, one of [creationDate, lastUpdateDate, state,
          documentType, closeDate]
        schema:
          type: string
          default: creationDate
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Documents'
        204:
          description: No content
          content: {}
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
    post:
      tags:
      - document
      - instance
      summary: Create a document
      description: "The method allows to create a document and directly add a first\
        \ version. The following data are mandatory: \n * The document type"
      operationId: createDocument
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - documentBody
              properties:
                payloadOriginal:
                  type: string
                  description: The payload content linked to the version
                  format: binary
                payloadPreview:
                  type: string
                  description: The payload content linked to the version
                  format: binary
                documentBody:
                  type: string
                  description: The JSON body of the document resource (see schema
                    Document)
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        409:
          description: Conflict with the correlation identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
  /documents/{documentId}:
    get:
      tags:
      - document
      - instance
      summary: Retrieve a document with its identifier
      description: Retrieve a document with all its details if it does exist
      operationId: retrieveDocument
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: documentId
        in: path
        description: Identifier of the document
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        404:
          description: No document found with that identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
    put:
      tags:
      - document
      - instance
      summary: Update a document's metadata
      description: "The method allows to alters the document's metadata and its current\
        \ version. It is possible to alter: \n * The document type \n * The origin\
        \ \n * The linked entities and their roles"
      operationId: updateDocument
      parameters:
      - name: documentId
        in: path
        description: Identifier of the document
        required: true
        schema:
          type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - documentBody
              properties:
                payloadOriginal:
                  type: string
                  description: The payload content linked to the version
                  format: binary
                payloadPreview:
                  type: string
                  description: The payload content linked to the version
                  format: binary
                documentBody:
                  type: string
                  description: The JSON body of the document resource (see schema
                    Document)
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No document found with that identifier
          content: {}
        409:
          description: Conflict with the correlation identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
    patch:
      tags:
      - document
      - instance
      summary: Patch a document
      description: "Method to update a data of a given document or to soft delete\
        \ that document by updating the technical state (deleted). It is possible\
        \ to soft delete in the following conditions: \n * No lock on the versions\
        \ \n * No shipment realized \n * The document is currenlty active (not deleted)\
        \ \n * The origin channel allows such deletion (force probante) \n \n A soft\
        \ delete of a document has the following impact: \n * Soft delete of all linked\
        \ versions \n * Soft delete the relations with other documents"
      operationId: patchDocument
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: documentId
        in: path
        description: Identifier of the document
        required: true
        schema:
          type: string
      requestBody:
        description: The document metadata
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No document found with that identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /documents/{documentId}/history:
    get:
      tags:
      - document
      - instance
      summary: Retrieve actions from a document
      description: List all the actions of a given document matching the specified
        filters. The list comes sorted (as specified or with the default sort) and
        pagined (as specified or with the default behavior)
      operationId: searchDocumentAction
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: documentId
        in: path
        description: The document identifier for which the action history has to be
          retrieved
        required: true
        schema:
          type: string
      - name: actionType
        in: query
        description: Search criterium on the action type
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: resultingState
        in: query
        description: Search criterium on the state after the action was realized
        schema:
          type: string
      - name: originalState
        in: query
        description: Search criterium on the state before the action was realized
        schema:
          type: string
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 1
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sort
        in: query
        description: Field to sort by, one of [creationDate, actionType]
        schema:
          type: string
          default: creationDate
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Actions'
        204:
          description: No content
          content: {}
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: Not found
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
  /documents/{documentId}/versions:
    get:
      tags:
      - document
      - version
      - instance
      summary: Search versions of a document
      description: List the versions of a given document that matches the specified
        criteria
      operationId: searchVersionInDocument
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: documentId
        in: path
        description: Identifier of the document
        required: true
        schema:
          type: string
      - name: creationDate_gte
        in: query
        description: Minimal creation date
        required: true
        schema:
          type: string
          format: date-time
      - name: creationDate_lte
        in: query
        description: Maximal creation date
        required: true
        schema:
          type: string
          format: date-time
      - name: creationUser
        in: query
        description: The creation user
        required: true
        schema:
          type: string
      - name: state
        in: query
        description: 'The state of the version '
        required: true
        schema:
          type: string
          default: open
      - name: page
        in: query
        description: Page number to be returned, default 1
        schema:
          type: integer
          format: int32
          default: 1
      - name: pageSize
        in: query
        description: Page size used for pagination, default 20
        schema:
          type: integer
          format: int32
          default: 20
      - name: sort
        in: query
        description: Field to sort by, one of []
        schema:
          type: string
          default: creationDate
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Versions'
        204:
          description: No version found
          content: {}
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
    post:
      tags:
      - document
      - version
      - instance
      summary: Create a version of a document
      description: Add a new version to an existing document
      operationId: createVersion
      parameters:
      - name: documentId
        in: path
        description: Identifier of the document
        required: true
        schema:
          type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - versionBody
              properties:
                payloadOriginal:
                  type: string
                  description: The payload content linked to the version
                  format: binary
                payloadPreview:
                  type: string
                  description: The payload content linked to the version
                  format: binary
                versionBody:
                  type: string
                  description: The JSON body of the version resource (see schema Version)
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Document'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No document found with that identifier
          content: {}
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
  /documents/{documentId}/sign:
    post:
      tags:
      - document
      - instance
      summary: Create a request to sign a document
      description: Request a digital signature of a document even though the document
        is not send through a digital medium but printed
      operationId: createSignatureRequest
      parameters:
      - name: documentId
        in: path
        description: The identifier of the document
        required: true
        schema:
          type: string
      responses:
        201:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/SignatureRequest'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No documentfound with that identifier
          content: {}
        412:
          description: Pre-conditions failed
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
  /versions/{versionId}:
    get:
      tags:
      - version
      - instance
      summary: Retrieve a version of a document
      description: Retrieve the details of an existing version through its unique
        identifier
      operationId: getVersion
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: versionId
        in: path
        description: Identifier of the version
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Version'
        404:
          description: No version found with that identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
  /versions/{versionId}/download:
    get:
      tags:
      - version
      - instance
      summary: Download the original version
      description: Retrieve the original payload of an existing version through its
        unique identifier
      operationId: downloadVersion
      parameters:
      - name: versionId
        in: path
        description: Identifier of the version
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            multipart/form-data;charset=utf-8:
              schema:
                type: string
                format: binary
        404:
          description: No version found with that identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
  /shipments:
    get:
      tags:
      - shipment
      - document
      - instance
      summary: Retrieve the shipment details
      description: Retrieve the details of all the shipments that match with some
        search criteria
      operationId: retrieveShipments
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: searchType
        in: query
        required: true
        schema:
          type: string
          enum:
          - full
          - fast
      - name: shipmentId
        in: query
        description: Search criterium on shipments identifiers
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: documentId
        in: query
        description: Search criterium on documents identifiers
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: mainDocument
        in: query
        description: |-
          Search criterium on the importance order of the document searched.
           To be used in combination with the documentId
        schema:
          type: boolean
      - name: state
        in: query
        description: Search on the shipment execution state
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: addresseeNature
        in: query
        description: Search criterium on the nature of the addressee
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: addresseeSourceName
        in: query
        description: |-
          Search criterium on the source name where the addressee is defined.
           To be used in combination with the addresseeSourceId
        schema:
          type: string
      - name: addresseeSourceId
        in: query
        description: "Search criterium on the addressee identifier within the integrated\
          \ source (for the specified source name). \n To be used in combination with\
          \ the addresseeSourceName"
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: shipmentType
        in: query
        description: Search criterium on the shipment types
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: creationDate_gte
        in: query
        description: Search criterium on the creation date
        schema:
          type: string
          format: date-time
      - name: creationDate_lte
        in: query
        description: Search criterium on the creation date
        schema:
          type: string
          format: date-time
      - name: technicalStatus
        in: query
        description: Search criterium on technical status
        schema:
          type: string
          enum:
          - open
          - closed
          - deleted
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 1
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sort
        in: query
        description: Field to sort by, one of [creationDate, state, shipmentType]
        schema:
          type: string
          default: creationDate
      - name: sortOrder
        in: query
        description: Sort order, ASC (default) or DESC
        schema:
          type: string
          default: ASC
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Shipments'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No shipment found with this identifier
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        412:
          description: addresseeSourceName must be used in combination with addresseeSourceId
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
    post:
      tags:
      - version
      - shipment
      - instance
      summary: Create a shipment of documents to external entities
      description: "Send one or several existing documents to specified addressees.\
        \ Note that shipment are asynchronous and are in three steps: \n * Generation\
        \ of the shipment request in the system (sync) \n * Approval (ack) by the\
        \ system responsible for the routing to realize the shipment (async) \n *\
        \ Confirmation of the execution of the shipment (async). \n \n Performing\
        \ a shipment (unless cancelled) makes the document version non-deletable."
      operationId: createShipment
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: The details of the shipment
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Shipment'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Shipment'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No document found with that identifier
          content: {}
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /shipments/{shipmentId}:
    get:
      tags:
      - shipment
      - version
      - instance
      summary: Retrieve the shipment details
      description: Retrieve the details of a shipment that has been initiated (whether
        they are completed or not)
      operationId: retrieveShipment
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: shipmentId
        in: path
        description: Identifier of the shipment
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Shipment'
        404:
          description: No shipment found with this identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
    put:
      tags:
      - shipment
      - version
      - instance
      summary: Update the shipment details
      description: Update the details of a shipment that has been initiated and allow
        to trigger the shipment execution. Only a non completed shipment can be updated
      operationId: updateShipment
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: shipmentId
        in: path
        description: Identifier of the shipment
        required: true
        schema:
          type: string
      requestBody:
        description: The details of the shipment
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Shipment'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Shipment'
        404:
          description: No shipment found with this identifier
          content: {}
        409:
          description: Pre conditions to trigger the shipment execution are not filled
          content: {}
        412:
          description: Missing mandatory parameter 'parameter name' for the  shipments
            update
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - shipment
      - version
      - instance
      summary: Patch a shipment execution
      description: Allow to cancel a shipment execution
      operationId: patchShipment
      parameters:
      - name: requestControl
        in: header
        description: Allow to bypass some constraints
        schema:
          type: string
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: shipmentId
        in: path
        description: Identifier of the shipment
        required: true
        schema:
          type: string
      requestBody:
        description: The details of the shipment
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/Shipment'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Shipment'
        404:
          description: No shipment found with this identifier
          content: {}
        409:
          description: Conflict with the linked document(s)
          content: {}
        412:
          description: Missing mandatory parameter 'parameter name' for the  shipments
            update
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /shipments/executions/{shipmentExecutionId}:
    get:
      tags:
      - shipment
      - document
      - instance
      summary: Retrieve the shipment execution details
      description: Retrieve the details of a shipment execution
      operationId: retrieveShipmentExecution
      parameters:
      - name: shipmentExecutionId
        in: path
        description: Identifier of the shipment execution
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ShipmentExecution'
        404:
          description: No shipment execution found with this identifier
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
  /shipments/{shipmentId}/executions:
    post:
      tags:
      - shipment
      - instance
      summary: Create a shipment execution
      description: Re-execute a shipment in error by providing a corrected encapsulation
      operationId: createShipmentExecution
      parameters:
      - name: shipmentId
        in: path
        description: Identifier of the shipment
        required: true
        schema:
          type: string
      requestBody:
        description: The details of the shipmentExecution
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/ShipmentExecution'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/ShipmentExecution'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No shipment found with that identifier
          content: {}
        412:
          description: Missing mandatory parameter 'parameter name' for the  execution
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:systemadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /decisionRules:
    get:
      tags:
      - administration
      summary: Retrieve the decision rules details
      description: The method retrieve all the versions of the decisions rules concerning
        the authorized actions
      operationId: getDecisionRules
      parameters:
      - name: decisionRulesId
        in: query
        description: Identifier of the decisionRule
        schema:
          type: integer
      - name: decisionRulesFileName
        in: query
        description: The file name to be retrieved (all the versions)
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: technicalStatus
        in: query
        description: Filter on the technical status of the decision rule
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DecisionRules'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        404:
          description: No file found with these parameters
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - administration
      summary: Create a new decision rules
      description: The method accepts decision rule files
      operationId: addDecisionRules
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - decisionRulesFile
              - decisionRulesFileName
              properties:
                decisionRulesFile:
                  type: string
                  description: The file containing the decision rules
                  format: binary
                decisionRulesFileName:
                  type: string
                  description: The decision rule file name (unique)
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DecisionRule'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        406:
          description: The value for the parameter 'parameter name' is too long
          content: {}
        409:
          description: Conflict with the file name
          content: {}
        412:
          description: Missing mandatory parameter 'parameter name' for the  decision
            rules creation
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /decisionRules/{decisionRulesId}:
    put:
      tags:
      - administration
      summary: Update decision rules
      description: The method accepts decision rule files
      operationId: updateDecisionRules
      parameters:
      - name: decisionRulesId
        in: path
        description: Identifier of the decision rules to update
        required: true
        schema:
          type: integer
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - decisionRulesFile
              properties:
                decisionRulesFile:
                  type: string
                  description: The file containing the decision rules
                  format: binary
                decisionRulesFileName:
                  type: string
                  description: The decision rule file name (unique)
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DecisionRule'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        406:
          description: The value for the parameter 'parameter name' is too long
          content: {}
        409:
          description: Conflict with the file name
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /documentTypes:
    get:
      tags:
      - document
      - configuration
      summary: Retrieve the various document types
      description: List all the available document types.
      operationId: getDocumentTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: technicalStatus
        in: query
        description: The technical status of the document type
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DocumentTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    post:
      tags:
      - document
      - configuration
      summary: Create a document types
      description: Create a new document type configuration
      operationId: createDocumentTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      requestBody:
        description: The document type details
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/DocumentType'
        required: true
      responses:
        201:
          description: Created
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DocumentType'
        409:
          description: Conflict
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /documentTypes/{code}:
    get:
      tags:
      - document
      - configuration
      summary: Retrieve a document type
      description: Retrieve a document type configuration
      operationId: retrieveDocumentType
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: Identifier of the document type
        required: true
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DocumentType'
        204:
          description: Not found
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
    put:
      tags:
      - document
      - configuration
      summary: Update a document type
      description: Update a document type configuration
      operationId: updateDocumentType
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: Identifier of the document type
        required: true
        schema:
          type: string
      requestBody:
        description: The document type details
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/DocumentType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DocumentType'
        204:
          description: Not found
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
    patch:
      tags:
      - document
      - configuration
      summary: Update some data of a document type
      description: Update a document type configuration or soft delete it by updating
        the   technical state (deleted)
      operationId: partialUpdateDocumentType
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: code
        in: path
        description: Identifier of the document type
        required: true
        schema:
          type: string
      requestBody:
        description: The document type details
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/DocumentType'
        required: true
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/DocumentType'
        204:
          description: Not found
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: body
  /refData/translations:
    get:
      tags:
      - document
      - reference
      summary: Retrieve the possible translations
      description: List all the states and/or types by category
      operationId: retrieveTranslations
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      - name: category
        in: query
        description: Identifier of the category for which the list of available translations
          must be returned
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      - name: technicalStatus
        in: query
        description: The technical status of the translation
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
            enum:
            - OPEN
            - OBSOLETE
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 0
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 1000
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Translations'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
  /refData/documentStates:
    get:
      tags:
      - document
      - reference
      summary: Retrieve the possible document states
      description: List all the document states and their labels
      operationId: retrieveDocumentStates
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/States'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/versionStates:
    get:
      tags:
      - version
      - reference
      summary: Retrieve the possible version states
      description: List all the version states and their labels
      operationId: retrieveVersionStates
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/States'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/shipmentStates:
    get:
      tags:
      - shipment
      - reference
      summary: Retrieve the possible shipment states
      description: List all the shipment states and their labels
      operationId: retrieveShipmentStates
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/States'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/associationTypes:
    get:
      tags:
      - process
      - reference
      summary: Retrieve the association types
      description: List all the association types and their labels
      operationId: retrieveAssociationTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/AssociationTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /refData/documentAssociationTypes:
    get:
      tags:
      - document
      - reference
      summary: Retrieve the association types
      description: List all the association types and their labels
      operationId: retrieveDocumentAssociationTypes
      parameters:
      - name: responseControl
        in: header
        description: 'technical parameter that defines the level of detail to be provided
          in the response (see #/definitions/ResponseControl for the structure details)'
        schema:
          type: string
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/AssociationTypes'
        204:
          description: No content
          content: {}
        500:
          description: Unexpected error
          content: {}
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
  /resources/search:
    post:
      tags:
      - resources
      summary: Search Work Environment Resources
      operationId: searchResources
      parameters:
      - name: page
        in: query
        description: Page number to be returned
        schema:
          type: integer
          format: int32
          default: 1
      - name: pageSize
        in: query
        description: Page size used for pagination
        schema:
          type: integer
          format: int32
          default: 20
      - name: sortBy
        in: query
        description: Field to sort by, one of [technicalId, businessState, resourceType,
          technicalInformation.creationDate, technicalInformation.lastUpdateDate]
        schema:
          type: string
          default: technicalInformation.creationDate
      - name: sortOrder
        in: query
        description: Sort order, asc (default) or desc
        schema:
          type: string
          default: asc
      requestBody:
        description: The search criteria
        content:
          application/json;charset=utf-8:
            schema:
              $ref: '#/components/schemas/SearchCriteria'
        required: false
      responses:
        200:
          description: OK
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/Resources'
        400:
          description: Bad request
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        500:
          description: Unexpected error
          content:
            application/json;charset=utf-8:
              schema:
                $ref: '#/components/schemas/StatusMessage'
        503:
          description: Service unavailable
          content: {}
      security:
      - oauth-implicit:
        - scope:rsz-onss:workenvironmentgateway-rest:user
        - scope:rsz-onss:workenvironmentgateway-rest:advanceduser
        - scope:rsz-onss:workenvironmentgateway-rest:consult
        - scope:rsz-onss:workenvironmentgateway-rest:configadmin
      x-oauth2-required-scopes: any
      x-codegen-request-body-name: searchCriteria
components:
  schemas:
    Authorizations:
      type: object
      properties:
        retrieve:
          type: boolean
          description: Has the user the authorization to retrieve the content of the
            resource?
        update:
          type: boolean
          description: Has the user the authorization to alter the content of the
            resource?
        delete:
          type: boolean
          description: Has the user the authorization to alter the content of the
            resource? (soft delete)
        execute:
          type: boolean
          description: Has the user the authorization to execute actions of the resource?
    Documents:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Document'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Document:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the link with the document
        sourceId:
          type: string
          description: The identifier of the document within the integrated source
        sourceName:
          type: string
          description: The name of the source where the document is defined
          example: nssoCase
        documentId:
          type: string
          description: The technical identifier of the document
          readOnly: true
        contentLanguage:
          type: string
          description: The language of the document content
          enum:
          - fr
          - nl
          - en
          - de
          - bi
          - other
          - undf
        title:
          type: string
          description: A freetext that one can add to provide a short title of the
            document
        internalReference:
          type: string
          description: A non mandatory internal reference for the document that is
            not necessarily unique. The user can edit that reference
        completeness:
          type: boolean
          description: Are the all the expected metadata provided?
          readOnly: true
        archivingRequest:
          type: boolean
          description: Has a request for archiving been triggered ?
        correlations:
          type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/Correlation'
            pagination:
              $ref: '#/components/schemas/Pagination'
        businessDomain:
          $ref: '#/components/schemas/TranslatedCode'
        documentType:
          $ref: '#/components/schemas/DocumentType'
        documentSubType:
          $ref: '#/components/schemas/TranslatedCode'
        origin:
          $ref: '#/components/schemas/Origin'
        associatedDocuments:
          $ref: '#/components/schemas/Associations'
        associatedProcesses:
          $ref: '#/components/schemas/Processes'
        associatedTasks:
          $ref: '#/components/schemas/Tasks'
        currentVersion:
          $ref: '#/components/schemas/Version'
        versions:
          $ref: '#/components/schemas/Versions'
        notes:
          $ref: '#/components/schemas/Notes'
        concernedEntities:
          $ref: '#/components/schemas/Entities'
        state:
          $ref: '#/components/schemas/State'
        acceptedActions:
          $ref: '#/components/schemas/ActionTypes'
        actionHistory:
          $ref: '#/components/schemas/Actions'
        addressees:
          $ref: '#/components/schemas/Addressees'
        shipments:
          $ref: '#/components/schemas/Shipments'
        signatureRequests:
          $ref: '#/components/schemas/SignatureRequests'
        permission:
          $ref: '#/components/schemas/Permission'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: A document is a set of versions and metadata applicable to all
        those versions. A document has a current version - the one that is shown as
        the most relevant at a given time. Moreover, a document may be associated
        to other documents (annexe, duplicate,...).
    Tasks:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Task'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Task:
      type: object
      properties:
        taskId:
          type: string
          description: The technical identifier of the task
        taskType:
          $ref: '#/components/schemas/TaskType'
        businessId:
          type: string
          description: |-
            A business-readable identifier of the task.
             To be provided by the producer
        businessDomain:
          $ref: '#/components/schemas/BusinessDomainType'
        parentProcess:
          $ref: '#/components/schemas/Process'
        correlation:
          $ref: '#/components/schemas/Correlation'
        correlations:
          type: array
          items:
            $ref: '#/components/schemas/Correlation'
        concernedEntities:
          $ref: '#/components/schemas/Entities'
        mainEntityNoss:
          type: string
        businessDataList:
          $ref: '#/components/schemas/BusinessDataList'
        actionHistory:
          $ref: '#/components/schemas/Actions'
        acceptedAction:
          $ref: '#/components/schemas/ActionTypes'
        wakeupDate:
          type: string
          description: An editable wake up date for task in state "wait"
        documents:
          $ref: '#/components/schemas/Documents'
        notes:
          $ref: '#/components/schemas/Notes'
        state:
          $ref: '#/components/schemas/State'
        responsible:
          $ref: '#/components/schemas/Node'
        priority:
          $ref: '#/components/schemas/Priority'
        dueDate:
          type: string
          description: An editable due date that defines the date the task should
            have been realized
        title:
          $ref: '#/components/schemas/TranslatedString'
        permission:
          $ref: '#/components/schemas/Permission'
        instruction:
          type: string
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
        matchingKeys:
          $ref: '#/components/schemas/MatchingKeys'
        shipments:
          $ref: '#/components/schemas/Shipments'
      description: |-
        A task is a consistent part of a process that can be realized by an agent.
         The task has a set of predefined actions that help the agent to move within the realization of that task.
         The actions can be taken by the responsible of the task (or a system), that responsible can be adapted by anyone.
    Processes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Process'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Process:
      type: object
      properties:
        processId:
          type: string
          description: The technical identifier of the process
        businessId:
          type: string
          description: A business-readable identifier of the process
        processType:
          $ref: '#/components/schemas/ProcessType'
        businessDomainType:
          $ref: '#/components/schemas/BusinessDomainType'
        parentProcess:
          $ref: '#/components/schemas/Process'
        correlation:
          $ref: '#/components/schemas/Correlation'
        correlations:
          type: array
          items:
            $ref: '#/components/schemas/Correlation'
        concernedEntities:
          $ref: '#/components/schemas/Entities'
        businessDataList:
          $ref: '#/components/schemas/BusinessDataList'
        actionHistory:
          $ref: '#/components/schemas/Actions'
        documents:
          $ref: '#/components/schemas/Documents'
        notes:
          $ref: '#/components/schemas/Notes'
        milestones:
          $ref: '#/components/schemas/Milestones'
        businessState:
          $ref: '#/components/schemas/Milestone'
        associatedProcess:
          $ref: '#/components/schemas/Associations'
        tasks:
          $ref: '#/components/schemas/Tasks'
        subprocesses:
          $ref: '#/components/schemas/Processes'
        permission:
          $ref: '#/components/schemas/Permission'
        dueDate:
          type: string
          description: An editable due date that defines the date the task should
            have been realized
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: |-
        A process is a set of tasks, events and subprocesses that define how a business process is currently achieved.
         That process has a configuration that defines the ranges of possible action for that process (see ProcessType).
         A process can have a state defined by its last reached milestone.
    Actions:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Action'
        pagination:
          $ref: '#/components/schemas/Pagination'
        more:
          type: string
          description: if this is filled in, this means that more items are available,
            the next 10 can be found by using the link
          format: uri
          example: documents/id/history?page=2&pageSize=10
    Action:
      type: object
      properties:
        actionId:
          type: string
          description: The technical identifier of the action
        taskId:
          type: string
          description: The technical identifier of the task for which the action was
            taken
        actionType:
          $ref: '#/components/schemas/ActionType'
        userId:
          $ref: '#/components/schemas/Node'
        originalNodeId:
          $ref: '#/components/schemas/Node'
        targetNodeId:
          $ref: '#/components/schemas/Node'
        originalState:
          $ref: '#/components/schemas/State'
        resultingState:
          $ref: '#/components/schemas/State'
        note:
          $ref: '#/components/schemas/Note'
        effectDate:
          type: string
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: An Action is an action that can be realized by a system or an agent
        in order to either move within the workflow of a task (change of state), either
        to change the responsible of a a task. There is a predefined set of actions
        that is the same for every task type. Yet some actions may or may not be enabled
        for specific task types
    Entities:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Entity'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Entity:
      type: object
      properties:
        roleId:
          type: string
          description: The technical identifier of the role
        sourceId:
          type: string
          description: The identifier of the entity within the integrated source
        sourceName:
          type: string
          description: The name of the source where the entity is defined
          example: nssoThirdParty
        nature:
          type: string
          enum:
          - organisation
          - person
        role:
          $ref: '#/components/schemas/EntityRoleType'
        quality:
          $ref: '#/components/schemas/EntityQualityType'
        sourceIdentifiers:
          type: array
          items:
            $ref: '#/components/schemas/EntityIdentifier'
        denomination:
          $ref: '#/components/schemas/TranslatedString'
        address:
          $ref: '#/components/schemas/Address'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
        href:
          $ref: '#/components/schemas/HttpLink'
      description: "An entity that may be of interest for the business. Entities are\
        \ of two natures:\n * Organization \n * Citizen \n That nature is derived\
        \ from the identifier (BCE or NOSS means Organization/NISS means Citizen)\n"
    EntityIdentifier:
      type: object
      properties:
        identifierType:
          type: string
          description: The type of identifier
          example: NSSO,BCE,FOLEEN_TVA,JKEY,SAA...
        identifierTypeLabel:
          $ref: '#/components/schemas/TranslatedString'
        identifier:
          type: string
          description: The identifier for that identifier type
    BusinessDataList:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BusinessData'
        total:
          type: integer
          description: The number of business data
          format: int32
    BusinessData:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the business data
        businessDataUsageType:
          $ref: '#/components/schemas/BusinessDataUsageType'
        internalValue:
          type: string
          description: The value for that business data. The value has to be read
            accordingly to its type
        externalValue:
          $ref: '#/components/schemas/TranslatedValue'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: A business data is a container for a value. Its type defines the
        format of the value
    FlattenedBusinessData:
      type: object
      properties:
        booleans:
          type: object
          additionalProperties:
            type: string
        timestamps:
          type: object
          additionalProperties:
            type: string
        amounts:
          type: object
          additionalProperties:
            type: string
        simpleNumerics:
          type: object
          additionalProperties:
            type: string
        longNumerics:
          type: object
          additionalProperties:
            type: string
        strings:
          type: object
          additionalProperties:
            type: string
        translatedTexts:
          $ref: '#/components/schemas/TranslatedTexts'
    Notes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Note'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Note:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the note
        taskId:
          type: string
          description: The technical identifier of the task
        processId:
          type: string
          description: The technical identifier of the process
        actionId:
          type: string
          description: The technical identifier of the action
        content:
          type: string
          description: The content of the note
        type:
          $ref: '#/components/schemas/TranslatedCode'
        important:
          type: boolean
          description: Is the note content of any general importance?
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: A note is a textual complement of information that can be attached
        to an action or a resource (task,process,document). The note is created by
        a user and can be altered overtime.
    Milestones:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Milestone'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Milestone:
      type: object
      properties:
        id:
          type: string
          description: The technical ID. Generated by the backend
        status:
          type: string
          description: The business status of the milestone
          enum:
          - REACHED
          - ABORTED
          - SCHEDULED
        category:
          type: string
          description: The functional category of the milestone
          enum:
          - FINAL
          - START
          - INTERMEDIATE
        milestoneType:
          $ref: '#/components/schemas/MilestoneType'
        description:
          $ref: '#/components/schemas/TranslatedString'
        reachedDate:
          type: string
        elapsedTime:
          type: string
        sequenceOrder:
          type: integer
          format: int32
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: "A milestone is step of a business process that can be reached\
        \ through the lifetime of a process instance. A milestone has various states\
        \ that define if it is:\n * reached\n * aborted \n * scheduled.\n\n Milestones\
        \ are optional for a process but they are the only way to have a business\
        \ state for a process.\n Each process type may have its own set of milestones\
        \ where one determine (using the category) the starting point and another\
        \ the final endpoint.\n Milestones have a sequence order (for display purpose)\
        \ and elaspe time between reached milestone to track the duration inbetween\
        \ business state."
    Associations:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Association'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Association:
      type: object
      properties:
        id:
          type: string
          description: The technical ID of the association. Generated by the backend
        associationType:
          $ref: '#/components/schemas/AssociationType'
        associatedProcess:
          $ref: '#/components/schemas/Process'
        annotation:
          $ref: '#/components/schemas/TranslatedString'
        associatedDocument:
          $ref: '#/components/schemas/Document'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: "Associations represent relations between instances. An association\
        \ has a type that define the nature of the relation. \n For tracking purpose,\
        \ the association can be joined by a comment that describes the context for\
        \ which the association was created."
    Events:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Event'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Event:
      type: object
      properties:
        id:
          type: string
          description: The technical ID of the event. Generated by the backend
        concernedTask:
          $ref: '#/components/schemas/Task'
        concernedProcess:
          $ref: '#/components/schemas/Process'
        header:
          type: string
          description: "The header of the event. JSON format.\n Contents: \n * the\
            \ destination topic. Ex: socsec.nsso.process.event.OSZ_REQUEST_PENSION.created\n\
            \ * the event URI identifier (on the bus)\n * the creator URN (taskprmanagement:administration:rest-nssoprtask)\n\
            \ * The content schema which describe how to read the content (currently:\
            \ content.json)\n * The payload schema which describe how to read the\
            \ payload of the content. Ex: processCreationPayload.json"
        content:
          type: string
          description: The content of the event. JSON format that can be read using
            the payload schema that is mentioned in the header.
        originalEvent:
          $ref: '#/components/schemas/Event'
        republishedEvents:
          $ref: '#/components/schemas/Events'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: An event is triggered by some actions in the system. Those events
        are available for a consumtion by other systems. The event is only a mean
        to be notified of the action. It is not purposed to make business information
        transit. It means that a system consuming the events should always consult
        the concerned object for which the action was taken in order to retrieve the
        business information.
    TaskTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/TaskType'
        pagination:
          $ref: '#/components/schemas/Pagination'
    TaskType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the task type
          example: INSPECTION-T3-INQUIRY
        label:
          $ref: '#/components/schemas/TranslatedString'
        description:
          $ref: '#/components/schemas/TranslatedString'
        defaultPriority:
          $ref: '#/components/schemas/Priority'
        processTypes:
          $ref: '#/components/schemas/ProcessTypes'
        businessDataTypes:
          $ref: '#/components/schemas/BusinessDataUsageTypes'
        documentTypes:
          $ref: '#/components/schemas/DocumentTypes'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
        templates:
          $ref: '#/components/schemas/Templates'
      description: |-
        The task type defines what is expected for a task of that type:
         * The business data
         * The context (parent process) in which it can be achieved
         * The Document type that can be attached to the task
    ProcessTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProcessType'
        pagination:
          $ref: '#/components/schemas/Pagination'
    ProcessType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the process type
          example: INSPECTION-P1-INVESTIGATION
        label:
          $ref: '#/components/schemas/TranslatedString'
        description:
          $ref: '#/components/schemas/TranslatedString'
        businessDataTypes:
          $ref: '#/components/schemas/BusinessDataUsageTypes'
        subprocessesTypes:
          type: array
          items:
            $ref: '#/components/schemas/CreatableProcessType'
        taskTypes:
          type: array
          items:
            $ref: '#/components/schemas/CreatableTaskType'
        parentProcessTypes:
          $ref: '#/components/schemas/ProcessTypes'
        milestoneTypes:
          $ref: '#/components/schemas/MilestoneTypes'
        businessDomainTypes:
          $ref: '#/components/schemas/BusinessDomainTypes'
        documentTypes:
          $ref: '#/components/schemas/DocumentTypes'
        permissions:
          $ref: '#/components/schemas/Permissions'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
        templates:
          $ref: '#/components/schemas/Templates'
      description: |-
        The process type defines what can be expected for a process of that type:
         * The expected business data
         * The achievable task types
         * The startable subprocess types
         * The document type that can be attached to the process
    CreatableProcessType:
      type: object
      properties:
        processType:
          $ref: '#/components/schemas/ProcessType'
        manuallyCreatable:
          type: string
          description: The process type can be created by an agent in that context
        displayOrder:
          type: string
          description: The process type display order proposal
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    CreatableTaskType:
      type: object
      properties:
        taskType:
          $ref: '#/components/schemas/TaskType'
        manuallyCreatable:
          type: string
          description: The task type can be created by an agent in that context
        displayOrder:
          type: string
          description: The task type display order proposal
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    BusinessDataUsageTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BusinessDataUsageType'
        total:
          type: integer
          description: The number of business data usage types
          format: int32
    BusinessDataUsageType:
      type: object
      properties:
        id:
          type: string
          description: The technical code of the business data usage type
          example: INSPECTION_GOT_TYPE
        businessDataType:
          $ref: '#/components/schemas/BusinessDataType'
        category:
          $ref: '#/components/schemas/Category'
        isHidden:
          type: boolean
          description: The business data is hidden or not in the detailed view
          example: false
        isFilter:
          type: boolean
          description: The business data is available as a specific filter criteria
          example: false
        isEditable:
          type: boolean
          description: The business data is editable by a user
          example: true
        isRequired:
          type: boolean
          description: The business data is required before the completion of an action.
            If the category is INPUT, then the action is the creation one. If the
            category is OUTPUT then the actions are treat/treatAndClose/askValidation/rejectValidation/close
          example: true
        order:
          type: integer
          description: The order in which the business data has to be displayed
          format: int32
          example: 1
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: |-
        A business data type can be attached to various object types (task/process). With that attachement comes some parameters that can be tuned for each object type:
         * The category (input/output)
         * The visibility
         * The editability
         * The editability
         * The utilization as a filter
         * The display order.
    BusinessDataTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BusinessDataType'
        total:
          type: integer
          description: The number of business data types
          format: int32
    BusinessDataType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the business data type
        label:
          $ref: '#/components/schemas/TranslatedString'
        description:
          $ref: '#/components/schemas/TranslatedString'
        formatType:
          type: string
          description: The type of the business data
          enum:
          - int
          - double
          - boolean
          - timestamp
          - shorttext
          - longtext
          - richtext
          - doc
          - amount
        acceptedValues:
          $ref: '#/components/schemas/ValueList'
        formatRegex:
          type: string
          description: The regular expression that refines the expected format of
            the value
        maxSize:
          type: integer
          format: int32
        defaultValue:
          type: string
          description: The default value that is set at the creation of the business
            data
        usedBy:
          type: array
          items:
            $ref: '#/components/schemas/UsedBy'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: "The business data type is the representation of the way a value\
        \ is formatted:\n * The type of value (boolean, date, text, numeric,...)\n\
        \ * The accepted values (when the possible value are restricted to a set)\n\
        \ * The regular expression that validated specific formatting\n * The maximum\
        \ accepted size for the value\n \n The business data type comes with a label"
    ValueList:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the value list
        label:
          $ref: '#/components/schemas/TranslatedString'
        sublist:
          type: array
          items:
            $ref: '#/components/schemas/ValueList'
        translatedValues:
          type: array
          description: list of ordered translated strings
          items:
            $ref: '#/components/schemas/TranslatedValue'
        order:
          type: string
          description: The order in which the values have to be displayed
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
        total:
          type: integer
          description: The number of values in the list (excluding sublist)
          format: int32
      description: Business data type may have a list of accepted values which are
        a set of values. Each value may have labels (translatedValues). Each element
        of the list can be a list itself. It comes ordered with a numeric sequence.
    TranslatedValue:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the value
        label:
          $ref: '#/components/schemas/TranslatedString'
    BusinessDomainTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BusinessDomainType'
        pagination:
          $ref: '#/components/schemas/Pagination'
    BusinessDomainType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the business domain type
          example: SUPPORT_METHODOLOGY
        activityCode:
          $ref: '#/components/schemas/BusinessDomainType'
        label:
          $ref: '#/components/schemas/TranslatedString'
        creatableProcessTypes:
          type: array
          items:
            $ref: '#/components/schemas/CreatableProcessType'
        creatableDocumentTypes:
          type: array
          items:
            $ref: '#/components/schemas/CreatableDocumentType'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: Business domains are the categories of activities that are held
        on the business side. The busines domains help to gather process types with
        the same business goals
    CreatableDocumentType:
      type: object
      properties:
        documentType:
          $ref: '#/components/schemas/DocumentType'
        manuallyCreatable:
          type: string
          description: The document type can be created by an agent in that context
        displayOrder:
          type: string
          description: The document type display order proposal
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    ActionTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ActionType'
        total:
          type: integer
          description: The number of action types
          format: int32
        pagination:
          $ref: '#/components/schemas/Pagination'
    ActionType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the action type
          example: validate
        label:
          $ref: '#/components/schemas/TranslatedString'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: An action type is one of the possible actions that can be achieved
        for task instance. Action types are available depending on the task state.
    EntityQualityTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/EntityQualityType'
        total:
          type: integer
          description: The number of entity quality types
          format: int32
    EntityQualityType:
      type: object
      properties:
        code:
          type: string
        label:
          $ref: '#/components/schemas/TranslatedString'
    EntityRoleTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/EntityRoleType'
        total:
          type: integer
          description: The number of entity role types
          format: int32
    EntityRoleType:
      type: object
      properties:
        code:
          type: string
        label:
          $ref: '#/components/schemas/TranslatedString'
    States:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/State'
        total:
          type: integer
          description: The number of states
          format: int32
    State:
      type: object
      properties:
        category:
          type: string
          description: The category to retrieve the labels for the code
          example: DOCUMENT_SUBTYPE
        code:
          type: string
        label:
          $ref: '#/components/schemas/TranslatedString'
      description: The state is a business indication of the step that has been reached
        through a workflow of the given object (task or process). That state can evolve
        while the technical status is OPEN.
    Priorities:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Priority'
        total:
          type: integer
          description: The number of priorities
          format: int32
    Priority:
      type: object
      properties:
        code:
          type: string
          description: The priority with which the elements have to be considered
        label:
          $ref: '#/components/schemas/TranslatedString'
      description: The priority is an indicator to help agents to organize their work
        (based on the priority given to a task). System can set an initial value for
        the priority (but the priority can always be adapted by the agent).
    MilestoneTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MilestoneType'
        pagination:
          $ref: '#/components/schemas/Pagination'
    MilestoneType:
      type: object
      properties:
        code:
          type: string
          description: The identifier of the milestone type
        label:
          $ref: '#/components/schemas/TranslatedString'
        processType:
          $ref: '#/components/schemas/ProcessType'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    AssociationTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AssociationType'
        pagination:
          $ref: '#/components/schemas/Pagination'
    AssociationType:
      type: object
      properties:
        code:
          type: string
          description: The code of the association type
        label:
          $ref: '#/components/schemas/TranslatedString'
        revertCode:
          type: string
          description: The code of the association type on the other side of the association
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    Templates:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Template'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Template:
      type: object
      properties:
        relationId:
          type: string
          description: The code of the relation between a template and the objet it
            is related to
        code:
          type: string
          description: The code of the template type
        label:
          $ref: '#/components/schemas/TranslatedString'
        displayOrder:
          type: string
          description: The order in which it has to be displayed
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    Translations:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Translation'
        pagination:
          $ref: '#/components/schemas/Pagination'
      description: List of translations linked to a specific category (shipment states,
        action typesn ...)
    Translation:
      type: object
      properties:
        category:
          type: string
        code:
          type: string
        label:
          $ref: '#/components/schemas/TranslatedString'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    TranslatedString:
      type: object
      properties:
        fr:
          type: string
          description: The french translation of the element
        nl:
          type: string
          description: The dutch translation of the element
    TechnicalInformation:
      type: object
      properties:
        creationDate:
          type: string
          format: date-time
          readOnly: true
          example: 2019-08-02T13:46:52.483+02:00
        creationSource:
          type: string
          readOnly: true
          example: socialinspection:inquiry:NSSOInspectionInquiry:RestService
        creationUser:
          type: string
          readOnly: true
          example: A498
        lastUpdateDate:
          type: string
          format: date-time
          readOnly: true
          example: 2019-08-02T13:46:52.483+02:00
        lastUpdateSource:
          type: string
          readOnly: true
          example: socialinspection:inquiry:NSSOInspectionInquiry:RestService
        lastUpdateUser:
          type: string
          readOnly: true
          example: SYSTEM
        closureDate:
          type: string
          format: date-time
          readOnly: true
          example: 2019-08-02T13:46:52.483+02:00
        closureSource:
          type: string
          readOnly: true
          example: socialinspection:inquiry:NSSOInspectionInquiry:RestService
        closureUser:
          type: string
          readOnly: true
          example: A392
        creationNode:
          $ref: '#/components/schemas/Node'
        lastUpdateNode:
          $ref: '#/components/schemas/Node'
        closureNode:
          $ref: '#/components/schemas/Node'
        technicalStatus:
          type: string
          enum:
          - OPEN
          - CLOSED
          - DELETED
          - OBSOLETE
          - PENDING
        technicalVersion:
          type: integer
          description: The technical version
          format: int32
    HttpLink:
      type: object
      properties:
        href:
          type: string
          description: Any absolute URI that is using http or https protocol
          format: uri
          readOnly: true
    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: The number of items
          format: int32
          example: 128
        pageSize:
          maximum: 4E+3
          minimum: 0
          type: integer
          description: The number of item in the page
          format: int32
          example: 10
        first:
          type: string
          description: The URI of the first page
          format: uri
          example: /item?page=1&pageSize=10
        last:
          type: string
          description: The URI of the last page
          format: uri
          example: /item?page=13&pageSize=10
        next:
          type: string
          description: The URI of the next page
          format: uri
          example: /item?page=3&pageSize=10
        prev:
          type: string
          description: The URI of the previous page
          format: uri
          example: /item?page=1&pageSize=10
      description: The details regarding the pagination for a search
    Detail:
      type: object
      properties:
        kind:
          type: object
          properties: {}
        message:
          type: object
          properties: {}
        ref:
          type: object
          properties: {}
        value:
          type: object
          properties: {}
        additionalProperties:
          type: object
          additionalProperties:
            type: object
            properties: {}
    StatusMessage:
      type: object
      properties:
        id:
          type: string
          format: uuid
        code:
          type: string
        message:
          type: string
        contact:
          type: string
        environment:
          type: string
          enum:
          - DEVELOPMENT
          - TEST
          - INTEGRATION
          - ACCEPTATION
          - SIMULATION
          - PRODUCTION
        stackTrace:
          type: string
        details:
          type: array
          items:
            $ref: '#/components/schemas/Detail'
        additionalProperties:
          type: object
          properties: {}
    Errors:
      type: array
      items:
        $ref: '#/components/schemas/Error'
    Error:
      type: object
      properties:
        businessObjectId:
          type: string
          description: The id of the business object (Task, Process, Document...)
            on which the action returned an error
        statusMessage:
          $ref: '#/components/schemas/StatusMessage'
    HealthStatus:
      required:
      - status
      type: object
      properties:
        status:
          $ref: '#/components/schemas/HealthLevel'
        details:
          type: object
          properties: {}
        additionalProperties:
          $ref: '#/components/schemas/ComponentStatus'
      description: Response message for the API health
    ComponentStatus:
      required:
      - status
      type: object
      properties:
        status:
          $ref: '#/components/schemas/HealthLevel'
        message:
          type: string
          description: Optional description clarifying the health status of the component
      description: Status of a service component
    HealthLevel:
      type: string
      description: Level indicating the health status of the service
      enum:
      - up
      - degraded
      - down
      - outOfService
    Versions:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Version'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Version:
      type: object
      properties:
        versionId:
          type: string
          description: The technical identifier of the version
          readOnly: true
        fileName:
          type: string
          description: The name of the original payload that was uploaded
          readOnly: true
        mimeType:
          type: string
          description: The media type of the document
          example: plain/text
        contentSize:
          type: integer
          description: The size, in bytes, of the document version
          readOnly: true
        lockedBy:
          type: string
          description: The user that is currently locking the version
          readOnly: true
          example: A932
        signature:
          type: string
          description: Is the document version signed?
          enum:
          - SIGNED
          - NOT_SIGNED
          - UNKNOWN
        shipments:
          $ref: '#/components/schemas/Shipments'
        payloadOriginal:
          $ref: '#/components/schemas/Payload'
        payloadPreview:
          $ref: '#/components/schemas/Payload'
        versionNumber:
          type: integer
          description: The document version number
          readOnly: true
        hash:
          type: string
          description: The sha256 hash of the document version content
          readOnly: true
          example: 8a9f51d294e88ee1c600452542397cdac4c1064972e000c198e83e9a5052f488
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: "A version is a set of metadata associated with a payload (stored\
        \ into a given system) and that is part of a document. The metadata covers\
        \ generic elements such as: \n * The mime type \n * The origin of the version\
        \ \n * Some notes. \n \n One can take actions on a version. The ones taken\
        \ and those yet available are listed within the version details."
    Payload:
      type: object
      properties:
        reference:
          type: string
          description: The reference of the document within the integrated repository
        repository:
          type: string
          description: The name of the repository where the payload is stored
          example: AaaS
        contentSize:
          type: integer
          description: The size, in bytes, of the version payload
          readOnly: true
        availability:
          type: string
          description: The availability of the payload content. If not available the
            document cannot be downloaded or used in shipments
          readOnly: true
          enum:
          - available
          - unavailable
          - processing
        unavailabilityReason:
          type: string
          description: The reason why the payload is unavailable (if unavailable)
          readOnly: true
        href:
          $ref: '#/components/schemas/HttpLink'
      description: A payload is the actual content of a version. The payload is defined
        by the repository in which it is contained, the identifier whithin that repository
        and the URI to retrieve it
    Origin:
      type: object
      properties:
        sendingDate:
          type: string
          description: The date at which the message was sent
          format: date-time
          example: 2019-08-02T13:46:52.483+02:00
        receptionDate:
          type: string
          description: The date at which the message was receive within the organization
            (not necessarily processed at that time)
          format: date-time
          example: 2019-08-02T13:46:52.483+02:00
        sender:
          $ref: '#/components/schemas/Entity'
        recipient:
          $ref: '#/components/schemas/Node'
      description: "An origin is the description of the external message that brought\
        \ the document within the system. \n Not all documents have origins (i.e;\
        \ uploaded document). \n An origin is expected when the document comes from\
        \ channels such as the mails. Origin have a recipient (someone from the organization)\
        \ and a sender (an entity)."
    Shipments:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Shipment'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Shipment:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the shipment
          readOnly: true
        shipmentId:
          type: string
          description: The identifier of the document within the integrated source
        addressees:
          $ref: '#/components/schemas/Addressees'
        linkedDocuments:
          $ref: '#/components/schemas/LinkedDocuments'
        registeredMail:
          type: boolean
          description: Is it a registered shipment or not?
        requestedSignature:
          type: boolean
          description: Is a signature requested or not?
        signatoryType:
          type: string
          description: The type of the signatory (individual or enterprise)
          enum:
          - individual
          - enterprise
        complementaryInformation:
          type: string
          description: Json containing complementary data which can be used in the
            communication (title, display,...)
        shipmentExecutionRequest:
          $ref: '#/components/schemas/ShipmentExecution'
        shipmentExecutionsHistory:
          $ref: '#/components/schemas/ShipmentExecutionsHistory'
        associatedTask:
          $ref: '#/components/schemas/Task'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: A shipment is a complement of information that can be attached
        to a task.
    LinkedDocument:
      required:
      - order
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the decision rules
        order:
          type: integer
          description: The importance order of the document (1 is the main document)
        document:
          $ref: '#/components/schemas/Document'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    LinkedDocuments:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/LinkedDocument'
    ShipmentExecutionsHistory:
      type: object
      properties:
        items:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/ShipmentExecution'
    ShipmentExecution:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the execution
          readOnly: true
        initialState:
          $ref: '#/components/schemas/State'
        currentState:
          $ref: '#/components/schemas/State'
        sender:
          type: string
          description: The sender of the document
        systemRequestId:
          type: string
          description: The id of the request returned by the system in charge of the
            shipment execution
        systemResponse:
          type: string
          description: The response (success or error) returned by the system in charge
            of the shipment execution.
        encapsulation:
          type: string
          description: The encapsulation of the shipment made by the system in charge
            of the shipment execution.
        systemRequestRFH2:
          type: string
          description: The RFH2 of the genericXML generated by the system in charge
            of the shipment execution.
        acceptationDate:
          type: string
          description: The date when the third party system accepts to process the
            shipment
          format: date-time
        executionDate:
          type: string
          description: The date when the third party system executes the shipment
          format: date-time
        receptionDate:
          type: string
          description: The date of the shipment reception by the addressee
          format: date-time
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    ShipmentType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the shipment type
          example: EDED003
        label:
          $ref: '#/components/schemas/TranslatedString'
        format:
          type: string
          description: The optional format of the message. The format is constraining.
          readOnly: true
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    Correlation:
      type: object
      properties:
        id:
          type: integer
          description: The technical id
          format: int64
        correlationId:
          type: string
          description: The technical identifier of the object on the producer side
        producer:
          type: string
          description: The code that identifies the producer of the object
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    DocumentTypes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/DocumentType'
        pagination:
          $ref: '#/components/schemas/Pagination'
    DocumentType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the document type
          example: TYP_D_058
        label:
          $ref: '#/components/schemas/TranslatedString'
        subtypes:
          type: array
          items:
            $ref: '#/components/schemas/TranslatedCode'
        legalCategory:
          type: string
          description: The legal category for the document. Specifically, it governs
            the archiving rules
        visibility:
          type: boolean
          description: The visibility of the document type
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    TranslatedCode:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the element
        category:
          type: string
          description: The technical category of translation
        translation:
          type: string
          description: URI to rearch the translation for the code
          format: uri
          readOnly: true
      description: Code to be translated into the appropriated language
    DecisionRules:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/DecisionRule'
        pagination:
          $ref: '#/components/schemas/Pagination'
    DecisionRule:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the decision rules
        fileContent:
          type: string
          description: The content of the decision rule file
        version:
          type: integer
          description: The version of the decision rules (the highest the most recent)
        fileName:
          type: string
          description: The name of the decision rule file
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    PollersArray:
      title: pollersArray
      type: object
      properties:
        caches:
          type: array
          items:
            $ref: '#/components/schemas/Pollers'
      description: List of pollers
    Pollers:
      title: pollers
      type: object
      properties:
        pollerConfiguration:
          type: array
          items:
            $ref: '#/components/schemas/PollerConfiguration'
        totalItems:
          type: integer
        _links:
          $ref: '#/components/schemas/Links'
      description: List of pollers configuration information.
    PollerConfiguration:
      title: Poller configuration information
      required:
      - pollerName
      - status
      type: object
      properties:
        pollerName:
          type: string
          description: This is the poller name.
        messageClass:
          type: string
          description: This is the poller message class
        delay:
          type: string
          description: This is the number of seconds between two poller executions.
            Exclusive with cronPattern
        batchSize:
          type: string
          description: This is the number of entries taken at each poll.
        status:
          type: string
          description: This is poller status
          enum:
          - RUNNING
          - STOPPED
        startDate:
          type: string
          description: This is the poller first run datewith the current configuration
          format: date-time
        stopDate:
          type: string
          description: This is poller stop date.
          format: date-time
        cronPattern:
          type: string
          description: This is the cron pattern used by the poller. Exclusive with
            delay
        acceptedMimeTypes:
          type: array
          description: This is accepted mimeTypes for conversion pollers.
          items:
            type: string
        excludedMimeTypes:
          type: array
          description: This is excluded mimeTypes for conversion pollers.
          items:
            type: string
      description: Poller configuration information.
    Links:
      title: HalLinks
      type: object
      properties:
        self:
          $ref: '#/components/schemas/Link'
        clear:
          $ref: '#/components/schemas/Link'
      description: Object of links with the rels as the keys
    LinkArray:
      title: HalLinkArray
      type: object
      properties:
        links:
          $ref: '#/components/schemas/Links'
      description: An array of links of the same link relation
    Link:
      title: HalLink
      required:
      - href
      type: object
      properties:
        href:
          type: string
          description: Its value is either a URI [RFC3986] or a URI Template [RFC6570].
          format: uri
        hreflang:
          title: Language indication of the target resource [RFC5988]
          pattern: ^([a-zA-Z]{2,3}(-[a-zA-Z]{3}(-[a-zA-Z]{3}){0,2})?(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-([a-zA-Z0-9]{5,8}|[0-9][a-zA-Z0-9]{3}))*([0-9A-WY-Za-wy-z](-[a-zA-Z0-9]{2,8}){1,})*(x-[a-zA-Z0-9]{2,8})?)|(x-[a-zA-Z0-9]{2,8})|(en-GB-oed)|(i-ami)|(i-bnn)|(i-default)|(i-enochian)|(i-hak)|(i-klingon)|(i-lux)|(i-mingo)|(i-navajo)|(i-pwn)|(i-tao)|(i-tay)|(i-tsu)|(sgn-BE-FR)|(sgn-BE-NL)|(sgn-CH-DE)|(art-lojban)|(cel-gaulish)|(no-bok)|(no-nyn)|(zh-guoyu)|(zh-hakka)|(zh-min)|(zh-min-nan)|(zh-xiang)$
          type: string
          description: When present, is a hint in RFC5646 format indicating what the
            language of the result of dereferencing the link should be.  Note that
            this is only a hint; for example, it does not override the Content-Language
            header of a HTTP response obtained by actually following the link.
        templated:
          type: boolean
          description: Its value is boolean and SHOULD be true when the Link Object's
            href property is a URI Template.
        type:
          type: string
          description: Its value is a string used as a hint to indicate the media
            type expected when dereferencing the target resource.
        deprecation:
          type: boolean
          description: Its presence indicates that the link is to be deprecated (i.e.
            removed) at a future date. Its value is a URL that SHOULD provide further
            information about the deprecation.
        name:
          type: string
          description: Its value MAY be used as a secondary key for selecting Link
            Objects which share the same relation type.
        title:
          type: string
          description: Its value is a string and is intended for labelling the link
            with a human-readable identifier (as defined by [RFC5988]).
        id:
          type: string
    MatchingKeys:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MatchingKey'
    MatchingKey:
      type: object
      properties:
        attributeType:
          type: string
          example: business data, title,...
        attributeKey:
          type: string
          description: The code of the matching key. To be filled for attributeType
            business data
          example: 30BISTERDW_P_NUM_REPORT
        attributeLabel:
          $ref: '#/components/schemas/TranslatedString'
        attributeValue:
          type: string
          description: The value of the attribute for which the text search matched
      description: ""
    Permissions:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        total:
          type: integer
          description: The number of elements
          format: int32
    Permission:
      type: object
      properties:
        group:
          $ref: '#/components/schemas/Node'
        accessRights:
          type: string
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: ""
    Addressees:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Addressee'
    Addressee:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the decision rules
        nature:
          type: string
          description: The category of the addressee (main or copy).
          enum:
          - main
          - copy
        proposedName:
          type: string
          description: The name of the addressee
        proposedAddress:
          $ref: '#/components/schemas/Address'
        entity:
          $ref: '#/components/schemas/Entity'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    HalResource:
      type: object
      properties:
        _links:
          $ref: '#/components/schemas/HalLinks'
      description: A resource modeled after the HAL specification
    HalLinks:
      type: object
      properties:
        self:
          $ref: '#/components/schemas/link'
        clear:
          $ref: '#/components/schemas/link'
      description: object of links with the rels as the keys
    links:
      title: HalLinks
      type: object
      properties:
        self:
          $ref: '#/components/schemas/link'
        clear:
          $ref: '#/components/schemas/link'
      description: Object of links with the rels as the keys
    link:
      required:
      - href
      type: object
      properties:
        href:
          type: string
          description: Its value is either a URI [RFC3986] or a URI Template [RFC6570].
          format: uri
        hreflang:
          title: 'Language indication of the target resource [RFC5988] description:
            When present, is a hint in RFC5646 format indicating what the language
            of the result of dereferencing the link should be.  Note that this is
            only a hint; for example, it does not override the Content-Language header
            of a HTTP response obtained by actually following the link.'
          pattern: ^([a-zA-Z]{2,3}(-[a-zA-Z]{3}(-[a-zA-Z]{3}){0,2})?(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-([a-zA-Z0-9]{5,8}|[0-9][a-zA-Z0-9]{3}))*([0-9A-WY-Za-wy-z](-[a-zA-Z0-9]{2,8}){1,})*(x-[a-zA-Z0-9]{2,8})?)|(x-[a-zA-Z0-9]{2,8})|(en-GB-oed)|(i-ami)|(i-bnn)|(i-default)|(i-enochian)|(i-hak)|(i-klingon)|(i-lux)|(i-mingo)|(i-navajo)|(i-pwn)|(i-tao)|(i-tay)|(i-tsu)|(sgn-BE-FR)|(sgn-BE-NL)|(sgn-CH-DE)|(art-lojban)|(cel-gaulish)|(no-bok)|(no-nyn)|(zh-guoyu)|(zh-hakka)|(zh-min)|(zh-min-nan)|(zh-xiang)$
          type: string
        templated:
          type: boolean
          description: Its value is boolean and SHOULD be true when the Link object's
            href property is a URI Template.
        type:
          type: string
          description: Its value is a string used as a hint to indicate the media
            type expected when dereferencing the target resource.
        deprecation:
          type: boolean
          description: Its presence indicates that the link is to be deprecated (i.e.
            removed) at a future date. Its value is a URL that SHOULD provide further
            information about the deprecation.
        name:
          type: string
          description: Its value MAY be used as a secondary key for selecting Link
            objects which share the same relation type.
        title:
          type: string
          description: Its value is a string and is intended for labelling the link
            with a human-readable identifier (as defined by [RFC5988]).
        id:
          type: string
    CacheActions:
      type: object
      properties:
        contentLink:
          $ref: '#/components/schemas/link'
        clearLink:
          $ref: '#/components/schemas/link'
      description: List of cache actions.
    CacheStatistics:
      required:
      - cacheName
      - currentNumberOfEntries
      - evictions
      - hits
      - misses
      - removeHits
      - removeMisses
      - retrievals
      - stores
      - timeSinceStart
      - totalNumberOfEntries
      type: object
      properties:
        cacheName:
          type: string
          description: This is the cache name.
        timeSinceStart:
          type: string
          description: This is the number of seconds since cache started.
        currentNumberOfEntries:
          type: string
          description: This is the number of entries currently in this cache instance.
        totalNumberOfEntries:
          type: string
          description: This is the number of entries stored in cache since the cache
            started running.
        stores:
          type: string
          description: This is the number of put operations on the cache.
        retrievals:
          type: string
          description: This is the number of get operations.
        hits:
          type: string
          description: This is the number of cache get hits.
        misses:
          type: string
          description: This is the number of cache get misses.
        removeHits:
          type: string
          description: This is the number of cache resmoval hits.
        removeMisses:
          type: string
          description: This is the number of cache removal misses.
        evictions:
          type: string
          description: This is the number of cache eviction.
        links:
          $ref: '#/components/schemas/CacheActions'
      description: Cache statistics with link to cache content.
    Caches:
      title: caches
      type: object
      properties:
        applicationName:
          type: string
        cacheStatistics:
          type: array
          items:
            $ref: '#/components/schemas/CacheStatistics'
        totalItems:
          type: integer
        _links:
          $ref: '#/components/schemas/links'
      description: List of caches statistics.
    ResponseControl:
      type: object
      properties:
        detailedResponse:
          type: boolean
          description: If false, return only the ID of the resource and the technical
            information
          default: true
        detailedData:
          $ref: '#/components/schemas/DetailedData'
        detailedConfiguration:
          type: boolean
          description: If false, return only the code of the types and the technical
            information (no label, no details)
          default: true
    DetailedData:
      type: object
      properties:
        task:
          type: boolean
          description: Control on the level of details to be provided for associated
            tasks (linked tasks)
          default: false
        process:
          type: boolean
          description: Control on the level of details to be provided for associated
            processes (subprocess and linked processes)
          default: false
        document:
          type: boolean
          description: Control on the level of details to be provided for associated
            documents (business data and linked documents)
          default: false
        version:
          type: boolean
          description: Control on the level of details to be provided for associated
            versions
          default: false
        shipment:
          type: boolean
          description: Control on the level of details to be provided for associated
            shipments
          default: false
        entity:
          type: boolean
          description: Control on the level of details to be provided for associated
            entities (business data and related entity)
          default: false
        actor:
          type: boolean
          description: Control on the level of details to be provided for actors (creator,
            assignee, ...)
          default: false
        businessData:
          type: boolean
          description: Control on the level of details to be provided for business
            data (specific filter criteria and external details)
          default: false
      description: 'For each controller, the boolean offer the same mechanism: either
        the ID (false), either the full details (true)'
    BusinessStates:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BusinessState'
    BusinessState:
      required:
      - acceleration
      - countNumber
      - lastExecutionDate
      - type
      type: object
      properties:
        type:
          type: string
          description: Code that defines which business monitoring check is concerned
        concernedDataType:
          type: string
          description: The type of data concerned by the business anomaly monitored
            (e.g. Process, task, document,...)
        concernedDataTypeId:
          type: array
          description: The list of identifiers retrieved for the type of business
            anomaly monitored (e.g. The ids of the process not linked to an open task)
          items:
            type: string
        countNumber:
          type: integer
          description: The number of anomalies detected
          format: int32
        acceleration:
          type: string
          description: The increase/decrease of anomalies in function of the time
            elapsed since the last check
        lastExecutionDate:
          type: string
          description: date of the last check
        averageOccurenceNumber:
          type: string
          description: The average number of occurences between two checks
        authorizedOccurenceNumber:
          type: string
          description: The tolerated number of occurences between two checks
      description: Response details for the business monitoring check
    Address:
      type: object
      properties:
        street:
          type: string
        houseNbr:
          type: string
        postBox:
          type: string
        zipCode:
          type: string
        city:
          type: string
        countryCode:
          type: string
        language:
          type: string
          description: The language in which the address is written
          enum:
          - fr
          - nl
          - bi
          - en
          - de
          - other
          - undf
    UsedBy:
      type: object
      properties:
        taskType:
          $ref: '#/components/schemas/TaskType'
        processType:
          $ref: '#/components/schemas/ProcessType'
        category:
          $ref: '#/components/schemas/Category'
        isHidden:
          type: boolean
          description: The business data is hidden or not in the detailed view
          example: false
        isEditable:
          type: boolean
          description: The business data is editable by a user
          example: true
        isRequired:
          type: boolean
          description: The business data is required before the completion of an action.
            If the category is INPUT, then the action is the creation one. If the
            category is OUTPUT then the actions are treat/treatAndClose/askValidation/rejectValidation/close
          example: true
    Category:
      type: string
      description: The business category
      example: OUTPUT
      enum:
      - COMMON
      - INPUT
      - OUTPUT
    RequestControl:
      type: object
      properties:
        generateTechnicalInformation:
          type: boolean
          description: Let the system generate the creation/last update/closure/technical
            status information?
          default: true
        generateEvents:
          type: boolean
          description: Let the system generate events?
          default: true
    SyncRequest:
      type: object
      properties:
        technicalIds:
          type: array
          items:
            type: string
        targetSystem:
          type: string
      description: The elements to be resynchronized and the system with which the
        sync has to be realized
    Problem:
      type: object
      properties:
        type:
          type: string
          description: An URI reference that identifies the problem type. When dereferenced,
            it SHOULD provide human-readable documentation for the problem type (e.g.
            using HTML).
          format: uri
        title:
          type: string
          description: A short, summary of the problem type. Written in english and
            readable for engineers (usually not suited for non technical stakeholders
            and not localized)
          example: Service Unavailable
        status:
          maximum: 6E+2
          exclusiveMaximum: false
          minimum: 4E+2
          exclusiveMinimum: false
          type: integer
          description: The HTTP status code generated by the origin server for this
            occurrence of the problem.
          format: int32
          example: 503
        detail:
          type: string
          description: A human-readable explanation specific to this occurrence of
            the problem
        instance:
          type: string
          description: An URI reference that identifies the specific occurrence of
            the problem. It may or may not yield further information, if dereferenced.
          format: uri
      description: A Problem details object (RFC 7807)
    Problems:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Problem'
    InvalidParamProblem:
      description: Problem details for invalid input parameter(s)
      allOf:
      - $ref: '#/components/schemas/Problem'
      - type: object
        properties:
          invalidParams:
            type: array
            description: An array of parameter OpenAPI violations
            items:
              $ref: '#/components/schemas/InvalidParam'
    InvalidParam:
      type: object
      properties:
        in:
          type: string
          description: The location of the invalid parameter (cfr Swagger parameters)
          enum:
          - body
          - path
          - query
          - header
        name:
          type: string
          description: The name of the invalid parameter
        reason:
          type: string
          description: A message explaining the violation
        value:
          type: object
          description: The value of the erroneous parameter
    Resources:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Resource'
        statistics:
          $ref: '#/components/schemas/Statistics'
        matchingKeys:
          $ref: '#/components/schemas/MatchingKeys'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Resource:
      type: object
      properties:
        technicalId:
          type: string
        category:
          type: string
          enum:
          - task
          - process
          - document
        businessId:
          type: string
        resourceType:
          $ref: '#/components/schemas/ResourceType'
        businessDomainType:
          type: string
          description: The technical code of the business domain type
        denomination:
          $ref: '#/components/schemas/TranslatedString'
        parent:
          $ref: '#/components/schemas/ParentResource'
        businessState:
          $ref: '#/components/schemas/State'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
        externalReferences:
          type: array
          items:
            $ref: '#/components/schemas/ExternalReference'
        businessData:
          $ref: '#/components/schemas/FlattenedBusinessData'
        entities:
          $ref: '#/components/schemas/Entities'
        actors:
          type: array
          items:
            $ref: '#/components/schemas/Actor'
        matchingKeys:
          $ref: '#/components/schemas/MatchingKeys'
      description: ""
    ParentResource:
      type: object
      properties:
        technicalId:
          type: string
        category:
          type: string
          enum:
          - task
          - process
          - document
      description: ""
    ResourceType:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the WO item type
          example: TYP_D_058
        label:
          $ref: '#/components/schemas/TranslatedString'
      description: A type is a code identifying the configuration of a WO item within
        the microservice responsible for this WO item
    TranslatedTexts:
      type: object
      properties:
        fr:
          type: object
          additionalProperties:
            type: string
        nl:
          type: object
          additionalProperties:
            type: string
    ExternalReference:
      type: object
      properties:
        externalSource:
          type: string
        externalIdentifier:
          type: string
      description: ""
    Actor:
      type: object
      properties:
        role:
          type: string
          enum:
          - creator
          - responsible
          - lastUpdater
        node:
          $ref: '#/components/schemas/Node'
        system:
          type: boolean
          description: True if the actor for the given role was a system
      description: ""
    Nodes:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Node'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Node:
      type: object
      properties:
        id:
          type: string
          description: The Organizational chart identifier of the node - some services
            use it instead of nodeId
          example: A454
        nodeId:
          type: string
          description: The Organizational chart identifier of the node - some services
            use it instead of id
          example: A999
        nature:
          type: string
          description: The nature of the node. This attribute is not part of OrganizationalChart
            but is there for retro-compatibility with PrTask
          enum:
          - AGENT
          - DEPARTMENT
          - SYSTEM
        type:
          type: string
          description: The indication of the type of the node, in the Organizational
            Chart there are two types, an employee and a group.
          example: Employee
        businessIdentifiers:
          $ref: '#/components/schemas/BusinessIdentifiers'
        employee:
          $ref: '#/components/schemas/Employee'
        group:
          $ref: '#/components/schemas/Group'
        contact:
          $ref: '#/components/schemas/Contact'
        history:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/NodeHistory'
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
      description: A node is a part of the Organizational Chart that can be of a different
        type (employee, group). A group may be a department,a service or a workteam.
        Each node of the type group may have sub nodes as part of the whole Organizational
        Chart tree. A node may or may be not active. If a group is inactive, its sub
        nodes are also inactive.
    BusinessIdentifiers:
      type: object
      additionalProperties:
        type: string
      description: The businessIdentifiers of the node. A business identifier is specific
        information about the node, this can be a identifier for the department, an
        agent number of an employee or a INSS of an employee.
    NodeHistory:
      type: object
      properties:
        operationDate:
          type: string
          format: date-time
          example: 2019-08-02T13:46:52.483+02:00
        nodeAfterOperation:
          $ref: '#/components/schemas/Node'
        operationType:
          type: string
          enum:
          - create
          - update
          - delete
      description: ""
    EmployeeFunction:
      type: object
      properties:
        identifier:
          type: string
          description: The identifier of the function of the employee
          example: ""
        description:
          type: string
          description: The description of the function of the employee
      description: The function of an employee describes if an employee is an inspector
        or not.
    Contact:
      type: object
      properties:
        email:
          type: string
          description: The email address to contact the node through their work email
            address.
        phoneNumber:
          type: string
          description: The phone number of the node.
        timeZone:
          type: string
          description: The local timezone of the node.
    Employee:
      type: object
      properties:
        familyName:
          type: string
          description: The family name of the employee.
          example: Dupont
        givenName:
          type: string
          description: The given name of the employee.
          example: Thomas
        civilStatus:
          $ref: '#/components/schemas/TranslatedString'
        level:
          type: string
          description: The level of the employee within the company.
          example: Niveau A
        contract:
          type: string
          description: The kind of contract of the employee within the company.
          example: Statutaire or Consultant
        function:
          $ref: '#/components/schemas/EmployeeFunction'
        memberOf:
          type: array
          items:
            $ref: '#/components/schemas/Node'
    Group:
      type: object
      properties:
        name:
          $ref: '#/components/schemas/TranslatedString'
        nature:
          type: string
          description: The nature of the group (department, service or workteam).
          example: ""
        responsible:
          $ref: '#/components/schemas/Node'
        members:
          type: array
          items:
            $ref: '#/components/schemas/Node'
        parent:
          $ref: '#/components/schemas/Node'
    Statistics:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Statistic'
    Statistic:
      type: object
      properties:
        categoryCode:
          type: string
          enum:
          - responsible
          - domainBusiness
          - resourceType
          - state
        categoryLabel:
          $ref: '#/components/schemas/TranslatedString'
        subcategory:
          type: array
          items:
            $ref: '#/components/schemas/Statistic'
        sum:
          type: integer
          description: the aggregated number of elements for that category
      description: ""
    SearchCriteria:
      type: object
      properties:
        textSearches:
          type: array
          description: A free text search mechanism that allows one to provide some
            text elements to be matched or not (see operator) for a given search.
          items:
            $ref: '#/components/schemas/TextSearch'
        resourceId:
          type: array
          description: The identifiers of the resources to be matched (exact match)
          items:
            type: integer
        creationDate:
          $ref: '#/components/schemas/DateRange'
        closeDate:
          $ref: '#/components/schemas/DateRange'
        updateDate:
          $ref: '#/components/schemas/DateRange'
        businessDataSearches:
          type: array
          description: The business data type and value that have to be matched
          items:
            $ref: '#/components/schemas/BusinessDataSearch'
        entities:
          $ref: '#/components/schemas/Entities'
        actors:
          type: array
          description: The actors acting or having acted within the resources.
          items:
            $ref: '#/components/schemas/Actor'
        resourceTypes:
          type: array
          description: The resource type code has identified in its authentic source
          items:
            type: string
            example: T1_INSP_TREATMENT
        categories:
          type: array
          description: The category of resource
          items:
            type: string
            example: task
        externalReferences:
          type: array
          description: The external reference and their managing sources that have
            to be matched
          items:
            $ref: '#/components/schemas/ExternalReference'
        states:
          type: array
          items:
            type: string
            example: treatment
        technicalStatus:
          type: array
          items:
            type: string
            example: open
        businessDomainTypes:
          type: array
          items:
            type: string
            example: FRAUD_MANAGEMENT
        parent:
          $ref: '#/components/schemas/SearchCriteria'
      description: The search criteria for the advanced search. No criterion is mandatory
    BusinessDataSearch:
      type: object
      properties:
        code:
          type: string
          description: The technical code of the result type
          example: INSPECTION-P1-INVESTIGATION
        valueDate:
          $ref: '#/components/schemas/DateRange'
        valueAmount:
          $ref: '#/components/schemas/NumericRange'
        valueInteger:
          $ref: '#/components/schemas/NumericRange'
        valueText:
          type: string
    TextSearch:
      type: object
      properties:
        keyOperator:
          type: string
          enum:
          - NOT IN
          - IN
        keys:
          type: array
          items:
            type: string
        valueOperator:
          type: string
          enum:
          - OR
          - AND
          - NOT
        values:
          type: array
          items:
            type: array
            items:
              type: string
    DateRange:
      type: object
      properties:
        date_lte:
          type: string
          format: date-time
          example: 2019-08-02T13:46:52.483+02:00
        date_gte:
          type: string
          format: date-time
          example: 2019-08-02T13:46:52.483+02:00
    NumericRange:
      type: object
      properties:
        numeric_lte:
          type: integer
        numeric_gte:
          type: integer
    SignatureRequests:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/SignatureRequest'
    SignatureRequest:
      type: object
      properties:
        id:
          type: string
          description: The technical identifier of the signature request
          readOnly: true
        state:
          $ref: '#/components/schemas/State'
        signatory:
          $ref: '#/components/schemas/Node'
        systemRequestId:
          type: string
          description: The id of the request returned by the system in charge of the
            signature execution
        systemResponse:
          type: string
          description: The response (success or error) returned by the system in charge
            of the signature execution.
        systemRequestRFH2:
          type: string
          description: The RFH2 of the genericXML generated by the system in charge
            of the signature execution
        encapsulation:
          type: string
          description: The encapsulation of the shipment made by the system in charge
            of the signature execution.
        acceptationDate:
          type: string
          description: The date when the third party system accepts to process the
            signature
          format: date-time
        executionDate:
          type: string
          description: The date when the third party system executes the signature
          format: date-time
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
    MediaTypes:
      title: mediaTypes
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MediaType'
        total:
          type: integer
      description: List of media types.
    MediaType:
      type: object
      properties:
        code:
          type: string
          description: The type, tree and subtype of the media type (concatenate)
          example: "For type: 'application','audio','example','font','image','message','model','multipart','text','video
            - For tree: x.','x-','vnd.','prs. - For subtype: 'pdf', 'plain', 'gif',
            ..."
        externalServiceCreation:
          type: boolean
          description: The possibility of creation of the media type by the external
            service
          example: false
        previewFormat:
          type: string
          description: The format of preview of the media type
          example: application/pdf
        signAvailable:
          type: boolean
          description: The sign availability of the media type
        shipmentAvailable:
          type: boolean
          description: The shipment availabilty of the media type
        editionSource:
          type: string
          description: The edition source of the media type
          example: sharepoint
        technicalInformation:
          $ref: '#/components/schemas/TechnicalInformation'
  securitySchemes:
    oauth-implicit:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://professionalservices.socialsecurity.be/REST/oauth/v3/authorize
          scopes:
            scope:rsz-onss:workenvironmentgateway-rest:user: User
            scope:rsz-onss:workenvironmentgateway-rest:advanceduser: AdvancedUser
            scope:rsz-onss:workenvironmentgateway-rest:consult: Consult
            scope:rsz-onss:workenvironmentgateway-rest:monitoring: monitoring
            scope:rsz-onss:workenvironmentgateway-rest:configadmin: ConfigAdmin
            scope:rsz-onss:workenvironmentgateway-rest:dataadmin: DataAdmin
            scope:rsz-onss:workenvironmentgateway-rest:systemadmin: SystemAdmin
            scope:rsz-onss:workenvironmentgateway-rest:systemuser: SystemUser
