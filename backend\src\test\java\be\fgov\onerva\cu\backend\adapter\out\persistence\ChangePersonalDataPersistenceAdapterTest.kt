package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.domain.Limit
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ModeOfPaymentRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.UnionContributionRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.WaveTaskRepository
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataPersistCommand
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class ChangePersonalDataPersistenceAdapterTest {

    @MockK
    private lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @MockK
    private lateinit var citizenInformationRepository: CitizenInformationRepository

    @MockK
    private lateinit var modeOfPaymentRepository: ModeOfPaymentRepository

    @MockK
    private lateinit var unionContributionRepository: UnionContributionRepository

    @MockK
    private lateinit var waveTaskRepository: WaveTaskRepository

    @InjectMockKs
    private lateinit var adapter: ChangePersonalDataPersistenceAdapter

    @Nested
    inner class PersistChangePersonalData {

        @Test
        fun `persistChangePersonalData should persist basic request without optional entities`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                scanUrl = null,
                unemploymentOffice = 123,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            val entitySlot = slot<ChangePersonalDataRequestEntity>()

            every { changePersonalDataRepository.save(capture(entitySlot)) } answers { firstArg() }

            // When
            val result = adapter.persistChangePersonalData(command)

            // Then
            assertThat(result).isNotNull
            assertThat(result.c9id).isEqualTo(command.c9id)
            assertThat(result.ssin).isEqualTo(command.ssin)

            verify(exactly = 1) { changePersonalDataRepository.save(any()) }
            verify(exactly = 0) {
                citizenInformationRepository.save(any())
                modeOfPaymentRepository.save(any())
                unionContributionRepository.save(any())
            }
        }

        @Test
        fun `persistChangePersonalData should persist request with all optional entities`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                introductionDate = LocalDate.now(),
                requestDate = LocalDate.of(2025, 3, 25),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.ELECTRONIC,
                scanUrl = "http://thescanurl",
                citizenInformation = CitizenInformation(
                    firstName = "John",
                    lastName = "Doe",
                    birthDate = LocalDate.of(1990, 1, 1),
                    nationalityCode = 111,
                    address = Address(
                        street = "Test Street",
                        houseNumber = "1",
                        city = "Brussels",
                        countryCode = 150,
                        zipCode = "1000",
                        validFrom = LocalDate.of(2022, 1, 1),
                    ),
                ),
                modeOfPayment = ModeOfPayment(
                    otherPersonName = null,
                    iban = "BE123456789",
                    bic = null,
                    validFrom = LocalDate.of(2025, 1, 1),
                ),
                unionContribution = UnionContribution(
                    authorized = true, effectiveDate = LocalDate.now(),
                ),
                unemploymentOffice = 123,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            val changePersonalDataRequestEntitySlot = slot<ChangePersonalDataRequestEntity>()
            val citizenInformationEntitySlot = slot<CitizenInformationEntity>()
            val modeOfPaymentEntitySlot = slot<ModeOfPaymentEntity>()
            val unionContributionEntitySlot = slot<UnionContributionEntity>()

            every { changePersonalDataRepository.save(capture(changePersonalDataRequestEntitySlot)) } answers { firstArg() }
            every { citizenInformationRepository.save(capture(citizenInformationEntitySlot)) } answers { firstArg() }
            every { modeOfPaymentRepository.save(capture(modeOfPaymentEntitySlot)) } answers { firstArg() }
            every { unionContributionRepository.save(capture(unionContributionEntitySlot)) } answers { firstArg() }

            // When
            val result = adapter.persistChangePersonalData(command)

            // Then
            assertThat(result).isNotNull

            verify(exactly = 1) {
                changePersonalDataRepository.save(any())
                citizenInformationRepository.save(any())
                modeOfPaymentRepository.save(any())
                unionContributionRepository.save(any())
            }
            assertThat(changePersonalDataRequestEntitySlot.captured).isNotNull.extracting(
                ChangePersonalDataRequestEntity::c9Id,
                ChangePersonalDataRequestEntity::opKey,
                ChangePersonalDataRequestEntity::sectOp,
                ChangePersonalDataRequestEntity::ssin,
                ChangePersonalDataRequestEntity::documentType,
                ChangePersonalDataRequestEntity::requestDate,
            ).containsExactly(
                12345L,
                "OP123",
                "SO123",
                "12345678901",
                IdentityDocumentType.ELECTRONIC,
                LocalDate.of(2025, 3, 25),
            )
            assertThat(citizenInformationEntitySlot.captured).isNotNull.extracting(
                CitizenInformationEntity::firstName,
                CitizenInformationEntity::lastName,
                CitizenInformationEntity::birthDate,
                CitizenInformationEntity::nationalityCode,
            ).containsExactly(
                "John",
                "Doe",
                LocalDate.of(1990, 1, 1),
                111,
            )
            assertThat(citizenInformationEntitySlot.captured.address).isNotNull.extracting(
                { it.street },
                { it.houseNumber },
                { it.city },
                { it.countryCode },
                { it.zipCode },
            ).containsExactly(
                "Test Street",
                "1",
                "Brussels",
                150,
                "1000",
            )
            assertThat(modeOfPaymentEntitySlot.captured).isNotNull.extracting(
                ModeOfPaymentEntity::iban,
                ModeOfPaymentEntity::bic,
                ModeOfPaymentEntity::validFrom,
            ).containsExactly(
                "BE123456789",
                null,
                LocalDate.of(2025, 1, 1),
            )
            assertThat(unionContributionEntitySlot.captured).isNotNull.extracting(
                UnionContributionEntity::authorized,
                UnionContributionEntity::effectiveDate,
            ).containsExactly(
                true,
                LocalDate.now(),
            )
        }

        @Test
        fun `persistChangePersonalData should handle repository exceptions`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                introductionDate = LocalDate.now(),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                scanUrl = null,
                unemploymentOffice = 123,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            every { changePersonalDataRepository.save(any()) } throws RuntimeException("Database error")

            // When/Then
            assertThatThrownBy { adapter.persistChangePersonalData(command) }.isInstanceOf(RuntimeException::class.java)
                .hasMessage("Database error")
        }

        @Test
        fun `persistChangePersonalData should set correct update status for all entities`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                introductionDate = LocalDate.now(),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = CitizenInformation(
                    firstName = "John",
                    lastName = "Doe",
                    birthDate = LocalDate.now(),
                    nationalityCode = 111,
                    address = Address(
                        street = "Test Street",
                        houseNumber = "1",
                        city = "Brussels",
                        countryCode = 150,
                        zipCode = "1000",
                        validFrom = LocalDate.of(2022, 1, 1),
                    ),
                ),
                modeOfPayment = ModeOfPayment(
                    otherPersonName = null,
                    iban = "BE123456789",
                    bic = null,
                    validFrom = LocalDate.of(2025, 1, 1)
                ),
                unionContribution = UnionContribution(
                    authorized = true, effectiveDate = LocalDate.now(),
                ),
                scanUrl = null,
                unemploymentOffice = 123,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            val employeeInfoSlot = slot<CitizenInformationEntity>()
            val modeOfPaymentSlot = slot<ModeOfPaymentEntity>()
            val unionContributionSlot = slot<UnionContributionEntity>()

            every { changePersonalDataRepository.save(any()) } answers { firstArg() }
            every { citizenInformationRepository.save(capture(employeeInfoSlot)) } answers { firstArg() }
            every { modeOfPaymentRepository.save(capture(modeOfPaymentSlot)) } answers { firstArg() }
            every { unionContributionRepository.save(capture(unionContributionSlot)) } answers { firstArg() }

            // When
            adapter.persistChangePersonalData(command)

            // Then
            assertThat(employeeInfoSlot.captured.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
            assertThat(modeOfPaymentSlot.captured.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
            assertThat(unionContributionSlot.captured.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
        }
    }

    @Nested
    inner class GetChangePersonalDataById {
        @Test
        fun `getChangePersonalDataById should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThrows<RequestIdNotFoundException> {
                adapter.getChangePersonalDataById(requestId)
            }
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getChangePersonalDataById should return mapped request when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 456,
                entityCode = "EC456",
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan",
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                numbox = 42,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity
            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When
            val result = adapter.getChangePersonalDataById(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.c9id).isEqualTo(entity.c9Id)
            assertThat(result?.ssin).isEqualTo(entity.ssin)
            assertThat(result?.changePersonalDataCaptureWaveTask).isNull()
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `getChangePersonalDataById should map wave task when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 456,
                entityCode = "EC456",
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan",
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                numbox = 42,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                request = entity,
                status = WaveTaskStatus.OPEN,
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 1,
                processId = "123",
                taskId = "456"
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity
            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            val result = adapter.getChangePersonalDataById(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.c9id).isEqualTo(entity.c9Id)
            assertThat(result?.ssin).isEqualTo(entity.ssin)
            assertThat(result?.changePersonalDataCaptureWaveTask).isNotNull
            assertThat(result?.changePersonalDataCaptureWaveTask?.status).isEqualTo(WaveTaskStatus.OPEN)
            assertThat(result?.changePersonalDataCaptureWaveTask?.processId).isEqualTo("123")
            assertThat(result?.changePersonalDataCaptureWaveTask?.taskId).isEqualTo("456")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class GetChangePersonalDataByC9Id {

        @Test
        fun `should return null when entity not found by c9Id`() {
            // Given
            val c9Id = 12345L

            every {
                changePersonalDataRepository.findByC9Id(
                    c9Id,
                    Sort.by(Sort.Direction.DESC, "createdDate"),
                    Limit.of(1),
                )
            } returns emptyList()

            // When
            val result = adapter.getChangePersonalDataByC9Id(c9Id)

            // Then
            assertThat(result).isNull()
            verify(exactly = 1) {
                changePersonalDataRepository.findByC9Id(
                    c9Id,
                    Sort.by(Sort.Direction.DESC, "createdDate"),
                    Limit.of(1),
                )
            }
            verify(exactly = 0) {
                changePersonalDataRepository.findByIdOrNull(any())
                waveTaskRepository.findAllByRequestId(any())
            }
        }

        @Test
        fun `should propagate exceptions from getChangePersonalDataById`() {
            // Given
            val c9Id = 12345L
            val requestId = UUID.randomUUID()
            val entity = ChangePersonalDataRequestEntity(
                c9Id = c9Id,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 456,
                entityCode = "EC456",
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan",
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                numbox = 42,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            entity.id = requestId

            every {
                changePersonalDataRepository.findByC9Id(
                    c9Id,
                    Sort.by(Sort.Direction.DESC, "createdDate"),
                    Limit.of(1)
                )
            } returns listOf(entity)
            every {
                changePersonalDataRepository.findByIdOrNull(requestId)
            } throws RuntimeException("Database error")

            // When/Then
            val thrown = org.junit.jupiter.api.assertThrows<RuntimeException> {
                adapter.getChangePersonalDataByC9Id(c9Id)
            }

            assertThat(thrown).hasMessage("Database error")

            verify(exactly = 1) {
                changePersonalDataRepository.findByC9Id(c9Id, Sort.by(Sort.Direction.DESC, "createdDate"), Limit.of(1))
                changePersonalDataRepository.findByIdOrNull(requestId)
            }
            verify(exactly = 0) {
                waveTaskRepository.findAllByRequestId(any())
            }
        }
    }

    @Nested
    inner class UpdateChangePersonalDataWithDecision {

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val command = UpdateChangePersonalDataDecisionCommand(
                decisionType = DecisionType.C2,
                decisionDate = LocalDate.now(),
                user = "test-user",
                decisionBarema = "A47"
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThrows<RequestIdNotFoundException> {
                adapter.updateChangePersonalDataWithDecision(requestId, command)
            }

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `should update request entity with decision information`() {
            // Given
            val requestId = UUID.randomUUID()
            val decisionDate = LocalDate.of(2025, 4, 15)
            val command = UpdateChangePersonalDataDecisionCommand(
                decisionType = DecisionType.C2,
                decisionDate = decisionDate,
                user = "test-user",
                decisionBarema = "A47"
            )

            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 456,
                entityCode = "EC456",
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan",
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                numbox = 42,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity

            // When
            adapter.updateChangePersonalDataWithDecision(requestId, command)

            // Then
            assertThat(entity.decisionFromMfx).isTrue()
            assertThat(entity.decisionType).isEqualTo(DecisionType.C2)
            assertThat(entity.decisionDate).isEqualTo(decisionDate)
            assertThat(entity.decisionUser).isEqualTo("test-user")
            assertThat(entity.decisionBarema).isEqualTo("A47")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `should update request with null barema value when not provided`() {
            // Given
            val requestId = UUID.randomUUID()
            val command = UpdateChangePersonalDataDecisionCommand(
                decisionType = DecisionType.C51,
                decisionDate = LocalDate.now(),
                user = "test-user",
                decisionBarema = null
            )

            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 456,
                entityCode = "EC456",
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan",
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                numbox = 42,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            ).also {
                it.decisionType = null
                it.decisionBarema = null
            }

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity

            // When
            adapter.updateChangePersonalDataWithDecision(requestId, command)

            // Then
            assertThat(entity.decisionFromMfx).isTrue()
            assertThat(entity.decisionType).isEqualTo(DecisionType.C51)
            assertThat(entity.decisionUser).isEqualTo("test-user")
            assertThat(entity.decisionBarema).isNull() // Should be updated to null

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }
    }

    @Nested
    inner class UpdateChangePersonalDataWithC9EnvelopeData {

        @Test
        fun `should update request with C9 envelope data when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val command = be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataUpdateCommand(
                paymentInstitution = 789,
                entityCode = "NEW_CODE",
                unemploymentOffice = 456,
                introductionDate = LocalDate.of(2024, 1, 15),
                dateValid = LocalDate.of(2024, 6, 15),
                operatorCode = 999,
                scanUrl = "http://new-scan-url.com",
                scanNumber = 987654L,
                ec1Id = 5678,
                ec1DisplayUrl = "http://new-ec1-url.com",
                documentType = IdentityDocumentType.ELECTRONIC,
                introductionType = IntroductionType.INTRO_OTHERS
            )

            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 456,
                entityCode = "OLD_CODE",
                dateValid = LocalDate.now().plusDays(10),
                scanNumber = 12345L,
                scanUrl = "http://old-scan-url.com",
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(10),
                numbox = 42,
                ec1Id = 1234,
                ec1DisplayUrl = "http://old-ec1-url.com",
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity
            every { changePersonalDataRepository.save(entity) } returns entity

            // When
            adapter.updateChangePersonalDataWithC9EnvelopeData(requestId, command)

            // Then
            assertThat(entity.paymentInstitution).isEqualTo(789)
            assertThat(entity.entityCode).isEqualTo("NEW_CODE")
            assertThat(entity.unemploymentOffice).isEqualTo(456)
            assertThat(entity.introductionDate).isEqualTo(LocalDate.of(2024, 1, 15))
            assertThat(entity.dateValid).isEqualTo(LocalDate.of(2024, 6, 15))
            assertThat(entity.operatorCode).isEqualTo(999)
            assertThat(entity.scanUrl).isEqualTo("http://new-scan-url.com")
            assertThat(entity.scanNumber).isEqualTo(987654L)
            assertThat(entity.ec1Id).isEqualTo(5678)
            assertThat(entity.ec1DisplayUrl).isEqualTo("http://new-ec1-url.com")
            assertThat(entity.documentType).isEqualTo(IdentityDocumentType.ELECTRONIC)
            assertThat(entity.introductionType).isEqualTo(IntroductionType.INTRO_OTHERS)

            verify(exactly = 1) { 
                changePersonalDataRepository.findByIdOrNull(requestId)
                changePersonalDataRepository.save(entity)
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when request does not exist`() {
            // Given
            val requestId = UUID.randomUUID()
            val command = be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataUpdateCommand(
                paymentInstitution = 789,
                entityCode = "NEW_CODE",
                unemploymentOffice = 456,
                introductionDate = LocalDate.of(2024, 1, 15),
                dateValid = LocalDate.of(2024, 6, 15),
                operatorCode = 999,
                scanUrl = "http://new-scan-url.com",
                scanNumber = 987654L,
                ec1Id = 5678,
                ec1DisplayUrl = "http://new-ec1-url.com",
                documentType = IdentityDocumentType.ELECTRONIC,
                introductionType = IntroductionType.INTRO_OTHERS
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy {
                adapter.updateChangePersonalDataWithC9EnvelopeData(requestId, command)
            }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }
    }
}