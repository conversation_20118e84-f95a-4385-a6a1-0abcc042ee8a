import {CommonModule} from "@angular/common";
import {Component, Input, ViewEncapsulation} from "@angular/core";
import {MatButtonModule} from "@angular/material/button";
import {MatDialog} from "@angular/material/dialog";
import {MatIconModule} from "@angular/material/icon";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {CuDialogComponent} from "../../../components/cu-dialog/cu-dialog.component";
import {RedirectHandlerService} from "../../../http/redirect-handler.service";

@Component({
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
  ],
  selector: "lib-s24-action-button",
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  template: `
    <div class="S24Action">
      <button mat-stroked-button
              id="openS24Session"
              [hidden]="isHidden"
              (click)="openS24Session()"
              color="primary"
              aria-label="Basic"
              data-cy="openS24SessionButton"
      >{{ 'CU_DATA_CONSISTENCY.BUTTONS.OPEN_S24' | translate }}
      </button>
    </div>
  `,
})
export class S24ActionButtonComponent {
  @Input() isHidden: boolean = false;
  @Input() requestId!: string;

  constructor(
      private readonly matDialog: MatDialog,
      private readonly translate: TranslateService,
      private readonly redirectHandlerService: RedirectHandlerService
  ) {}

  openS24Session(): void {
    const dialogRef = this.matDialog.open(CuDialogComponent, {
      data: {
        title: this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.TITLE'),
        content: `${this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.SUBTITLE')}
                  <div class="validate">${this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_1')}
          <span class='S24Bold'>${this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_2')}</span>
          ${this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_3')}
          <span class='S24Orange'>${this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_4')}</span></div>`,
        primaryActionText: this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.PRIMARY'),
        secondaryActionText: this.translate.instant('CU_DATA_CONSISTENCY.S24_DIALOG.SECONDARY'),
        dialogType: 'warn',
        dialogSize: 'medium',
        onPrimaryAction: () => {
          dialogRef.close(true);
          this.redirectHandlerService.openS24Session(this.requestId).subscribe({
            error: () => {}
          });
        },
        onSecondaryAction: () => {
          dialogRef.close(false);
        },
      },
    });
  }
}
