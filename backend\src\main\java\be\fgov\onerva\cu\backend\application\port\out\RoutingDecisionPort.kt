package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem

/**
 * Port interface for routing decision persistence operations.
 * This interface defines operations to store and retrieve routing decision information.
 */
interface RoutingDecisionPort {
    
    /**
     * Retrieves all routing decisions for the specified request.
     * 
     * @param requestId The unique identifier of the request
     * @return Set of routing decision items for the request, empty if none exist
     */
    fun getRoutingDecisions(requestId: UUID): Set<RoutingDecisionItem>
    
    /**
     * Saves or updates routing decisions for the specified request and updates the processInWave status.
     *
     * @param requestId The unique identifier of the request
     * @param routingDecisions The routing decision items to save
     * @param processInWave Whether the request can be processed in Wave based on routing decisions
     */
    fun saveRoutingDecisions(requestId: UUID, routingDecisions: Set<RoutingDecisionItem>, processInWave: Boolean)
    
    /**
     * Deletes all routing decisions for the specified request.
     * 
     * @param requestId The unique identifier of the request
     */
    fun deleteRoutingDecisions(requestId: UUID)
}