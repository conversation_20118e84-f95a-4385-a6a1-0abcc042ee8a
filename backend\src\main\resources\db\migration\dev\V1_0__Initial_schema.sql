-- This is the initial schema for the database. It is based on the liquibase changelogs used before flyway was introduced.
-- This file serves a baseline for running the application on dev and ci. It is not used on other environments where
-- the files from common are used.

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-main-tables-ddl.sql::create-main-tables::bernard
create table employee_information
(
    id                 uniqueidentifier not null,
    request_id    uniqueidentifier                                            not null,
    birth_date    date                                                        not null,
    nationality   varchar(255),
    street        varchar(255)                                                not null,
    box_number         varchar(255),
    house_number  varchar(255)                                                not null,
    zip_code      varchar(255)                                                not null,
    city          varchar(255)                                                not null,
    country       varchar(255)                                                not null,
    update_status varchar(255) check (update_status in ('FROM_C9', 'EDITED')) not null,
    created_by         varchar(255),
    created_date  datetime2(6)                                                not null default sysdatetime(),
    last_modified_date datetime2(6),
    last_modified_by   varchar(255),
    primary key (id)
);
GO

create table mode_of_payment
(
    id                 uniqueidentifier                                            not null,
    request_id         uniqueidentifier                                            not null,
    own_account        bit                                                         not null,
    foreign_account    bit                                                         not null,
    other_person_name  varchar(255),
    iban               varchar(255)                                                not null,
    bic                varchar(255),
    valid_from         date,
    update_status      varchar(255) check (update_status in ('FROM_C9', 'EDITED')) not null,
    created_by         varchar(255),
    created_date       datetime2(6)                                                not null default sysdatetime(),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (id)
);
GO

create table request
(
    id           uniqueidentifier not null,
    c9_id        bigint           not null,
    type         varchar(31)      not null,
    ssin         varchar(255)     not null,
    request_date date             not null,
    sect_op      varchar(255)     not null,
    op_key       varchar(255)     not null,
    created_date datetime2(6)     not null default sysdatetime(),
    primary key (id)
);
GO

create table union_contribution
(
    id                 uniqueidentifier                                            not null,
    request_id         uniqueidentifier                                            not null,
    authorized         bit                                                         not null,
    effective_date     date                                                        not null,
    update_status      varchar(255) check (update_status in ('FROM_C9', 'EDITED')) not null,
    created_by         varchar(255),
    created_date       datetime2(6)                                                not null default sysdatetime(),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (id)
);
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-main-tables-ddl.sql::add-constraints-main-tables::bernard
create unique nonclustered index UKn5jt725wl5gltvu7cjloungb2
    on employee_information (request_id) where request_id is not null;
GO

create unique nonclustered index UK2whxtmcr0grip9wdvenq5x6gl
    on mode_of_payment (request_id) where request_id is not null;
GO

create unique nonclustered index UK35nnrxqmm8ld4f3dfteesiquh
    on union_contribution (request_id) where request_id is not null;
GO

alter table employee_information
    add constraint FK7uj5mpjiseo70g09pngu8936r
        foreign key (request_id)
            references request;
GO

alter table mode_of_payment
    add constraint FKt77364cwj3k6nt946spm6d6ve
        foreign key (request_id)
            references request;
GO

alter table union_contribution
    add constraint FK1cj303pv1fgyfsmd4kyotxvmw
        foreign key (request_id)
            references request;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-main-tables-ddl.sql::add-wave-task-table::bernard
create table wave_task
(
    id           uniqueidentifier not null,
    request_id   uniqueidentifier,
    process_id   varchar(255)     not null,
    task_id      varchar(255)     not null,
    created_date datetime2(6),
    primary key (id)
);
GO

create unique nonclustered index UK5ss1bxdkjox0lddvgle340u6q
    on wave_task (request_id) where request_id is not null

alter table wave_task
    add constraint FKgg50n68k87jxfyysd9x0q83tv
        foreign key (request_id)
            references request;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-main-tables-ddl.sql::add-document-type-request::bernard
alter table request
    add document_type varchar(255) check (document_type in ('ELECTRONIC', 'PAPER')) null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-main-audit-tables-ddl.sql::create-audit-tables::bernard
create table revinfo
(
    rev      int identity not null,
    revtstmp bigint,
    primary key (rev)
);
GO

create table employee_information_aud
(
    rev int not null,
    id                 uniqueidentifier not null,
    revtype            smallint,
    birth_date         date,
    nationality        varchar(255),
    street             varchar(255),
    box_number         varchar(255),
    house_number       varchar(255),
    zip_code           varchar(255),
    city               varchar(255),
    country            varchar(255),
    update_status      varchar(255),
    created_by         varchar(255),
    last_modified_date datetime2(6),
    last_modified_by   varchar(255),
    primary key (rev, id)
);
GO

create table mode_of_payment_aud
(
    id                 uniqueidentifier not null,
    revtype            smallint,
    rev             int not null,
    own_account     bit,
    foreign_account bit,
    other_person_name  varchar(255),
    iban               varchar(255),
    bic                varchar(255),
    valid_from         date,
    update_status   varchar(255) check (update_status in ('FROM_C9', 'EDITED')),
    created_by         varchar(255),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (rev, id)
);
GO

create table union_contribution_aud
(
    rev                int              not null,
    id                 uniqueidentifier not null,
    revtype            smallint,
    authorized         bit,
    effective_date     date,
    update_status      varchar(255) check (update_status in ('FROM_C9', 'EDITED')),
    created_by         varchar(255),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (rev, id)
);
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-main-audit-tables-ddl.sql::add-constraints-audit-tables::bernard
alter table employee_information_aud
    add constraint FKdg1m8oj7mdulah1t9jtwjyoww
        foreign key (rev)
            references revinfo;
GO

alter table mode_of_payment_aud
    add constraint FKsj45d4ddnvskxorsfyavra2dy
        foreign key (rev)
            references revinfo;
GO

alter table union_contribution_aud
    add constraint FKb5p0hrcgrmmkuul86o8kf1p86
        foreign key (rev)
            references revinfo;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/rename-employee-information-table.yaml::rename-employee-information-table::bernard
exec sp_rename 'employee_information', 'citizen_information';
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/rename-employee-information-table.yaml::rename-employee-information-aud-table::bernard
exec sp_rename 'employee_information_aud', 'citizen_information_aud';
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-table-add-revision-number.sql::add-wave-table-revision-number::bernard
alter table wave_task
    add citizen_information_revision_number int null;
GO

alter table wave_task
    add mode_of_payment_revision_number int null;
GO

alter table wave_task
    add union_contribution_revision_number int null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-table-add-revision-number.sql::add-wave-table-revision-number-request-information::bernard
alter table wave_task
    add request_information_revision_number int null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-table-add-revision-number.sql::add-wave-table-type::bernard
alter table wave_task
    add type varchar(255) not null default 'CHANGE_PERSONAL_DATA_CAPTURE';
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-table-add-revision-number.sql::add-wave-table-status::bernard
alter table wave_task
    add status varchar(255) check (status in ('OPEN', 'CLOSED')) not null default 'OPEN';
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-table-add-revision-number.sql::alter-index-wave-table::bernard
DROP INDEX UK5ss1bxdkjox0lddvgle340u6q ON wave_task;
GO

CREATE UNIQUE NONCLUSTERED INDEX UK5ss1bxdkjox0lddvgle340u6q
    ON wave_task (request_id, type)
    WHERE request_id IS NOT NULL;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/add-scan-url-column-request.sql::add-scan-url-column-request:ddl::hendrik
alter table request
    add scan_url varchar(255) null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/remove-scan-url-column-request.sql::remove-scan-url-column-request:ddl::bernard
alter table request
    drop column scan_url;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-request-information-table.sql::create-request-information-table::ben
create table request_information
(
    id                 uniqueidentifier not null,
    request_id         uniqueidentifier not null,
    request_date       date             not null,
    created_by         varchar(255),
    created_date       datetime2(6)     not null default sysdatetime(),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (id)
);
GO

create unique nonclustered index request_information_request_id_index
    on request_information (request_id) where request_id is not null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-request-information-table.sql::create-audit-tables::ben
create table request_information_aud
(
    rev          int          not null,
    id                 uniqueidentifier not null,
    revtype            smallint,
    request_date date         not null,
    created_by         varchar(255),
    created_date datetime2(6) not null default sysdatetime(),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (rev, id)
);
GO

alter table request_information_aud
    add constraint request_information_aud_rev_fk
        foreign key (rev)
            references revinfo;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-citizen-information-snapshot-table.sql::create-onem-information-snapshot-table:ddl::bernard
create table citizen_information_snapshot
(
    id                uniqueidentifier not null,
    request_id        uniqueidentifier not null,
    numbox            int              not null,
    first_name        varchar(255)     not null,
    last_name         varchar(255)     not null,
    street            varchar(255)     not null,
    house_number      varchar(255)     not null,
    box_number        varchar(255),
    city              varchar(255),
    country           varchar(255),
    foreign_account   bit,
    own_account       bit,
    valid_from        date,
    bic               varchar(255),
    iban              varchar(255),
    nationality       varchar(255)     not null,
    other_person_name varchar(255),
    zip_code          varchar(255)     not null,
    external_source   varchar(255)     not null check (external_source in ('ONEM', 'AUTHENTIC_SOURCES')),
    readonly          bit              not null default 0,
    created_date      datetime2(6)     not null,
    primary key (id)
);
GO

alter table citizen_information_snapshot
    add constraint citizen_information_snapshot_unique_request_id_source unique (request_id, external_source);
GO

alter table citizen_information_snapshot
    add constraint citizen_information_snapshot_request_id_fk foreign key (request_id) references request;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/alter-citizen-information-add-first-last-name-table.sql::add-first-last-name::bernard
alter table citizen_information
    add first_name varchar(255) not null default '',
        last_name varchar(255) not null default '';
GO

alter table citizen_information_aud
    add first_name varchar(255) not null default '',
        last_name varchar(255) not null default '';
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/alter-citizen-information-add-first-last-name-table.sql::make-nationality-not-null::bernard
alter table citizen_information
    alter column nationality varchar(255) not null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-table-field-source.sql::create-table-field-source::bernard
CREATE TABLE field_source
(
    id                 uniqueidentifier NOT NULL PRIMARY KEY DEFAULT NEWID(),
    entity_type        varchar(100)     NOT NULL,
    entity_id          uniqueidentifier NOT NULL,
    field_name         varchar(100)     NOT NULL,
    source             varchar(50)      NOT NULL
        CHECK (source IN ('C1', 'AUTHENTIC_SOURCES', 'ONEM')),
    created_by         varchar(255),
    created_date       datetime2(6)                          DEFAULT SYSDATETIME() NOT NULL,
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    CONSTRAINT UQ_field_source UNIQUE (entity_type, entity_id, field_name)
);
GO

CREATE TABLE field_source_aud
(
    rev          int              not null,
    id           uniqueidentifier NOT NULL,
    revtype            smallint,
    entity_type  varchar(100),
    entity_id    uniqueidentifier,
    field_name   varchar(100),
    source       varchar(50),
    created_by         varchar(255),
    created_date datetime2(6),
    last_modified_by   varchar(255),
    last_modified_date datetime2(6),
    primary key (rev, id)
);
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/alter-union-contribution-nullable.sql::alter-union-contribution-nullable::bernard
ALTER TABLE union_contribution
    ALTER COLUMN authorized bit NULL;
GO

ALTER TABLE union_contribution
    ALTER COLUMN effective_date date NULL;
GO

ALTER TABLE union_contribution_aud
    ALTER COLUMN authorized bit NULL;
GO

ALTER TABLE union_contribution_aud
    ALTER COLUMN effective_date date NULL;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/clean-mode-of-payment-foreign-account-own-account.sql::clean-mode-of-payment-foreign-account-own-account:ddl::bernard
alter table mode_of_payment
    drop
        column foreign_account;
GO

alter table mode_of_payment
    drop
        column own_account;
GO

alter table mode_of_payment_aud
    drop
        column foreign_account;
GO

alter table mode_of_payment_aud
    drop
        column own_account;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/create-barema-snapshot-table.sql::create-barema-snapshot-table:ddl::bernard
create table barema_snapshot
(
    id           uniqueidentifier not null,
    request_id   uniqueidentifier not null,
    found        bit              not null,
    barema       varchar(255),
    article      varchar(255),
    readonly     bit              not null default 0,
    created_date datetime2(6)     not null,
    primary key (id)
);
GO

alter table barema_snapshot
    add constraint barema_snapshot_request_id_fk foreign key (request_id) references request;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/add-decision-type-column-request.sql::add-decision-column-request::bernard
alter table request
    add decision_type varchar(25) check (decision_type in (
                                                           'C2Y',
                                                           'C2N',
                                                           'C2F',
                                                           'C2P',
                                                           'C51',
                                                           'C9B',
                                                           'C2',
                                                           'C9NA'
        )) null;
GO

alter table request
    add decision_from_mfx bit null;
GO

alter table request
    add decision_user varchar(255) null;
GO

alter table request
    add decision_date date null;
GO

alter table request
    add decision_barema varchar(25) null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/add-mode-of-payment-own-account.sql::add-own-account-column::michael
ALTER TABLE mode_of_payment
    ADD own_account bit default 1

ALTER TABLE mode_of_payment_aud
    ADD own_account bit default 1;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/add-value-date-columns.yaml::add-value-date-columns::bernard
exec sp_rename 'citizen_information_snapshot.valid_from', 'bank_account_value_date', 'COLUMN';
GO

ALTER TABLE citizen_information_snapshot
    ADD address_value_date date;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-task-add-waiting-status.sql::drop-existing-wave-task-status-constraint::bernard
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS CK__wave_task__statu__36D11DD4;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-task-add-waiting-status.sql::add-wave-task-waiting-status::bernard
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS CK__wave_task__status;
GO

ALTER TABLE wave_task
    ADD CONSTRAINT CK__wave_task__status
        CHECK (status in ('OPEN', 'CLOSED', 'WAITING'));
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-task-add-waiting-status.sql::drop-wave-task-status-constraint-test::bernard
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS
        CK__wave_task__statu__5DCAEF64;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/wave-task-add-waiting-status.sql::drop-wave-task-status-constraint-val::bernard
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS
        CK__wave_task__statu__6E01572D;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/remove-mode-of-payment-own-account.sql::remove-mode-of-payment-own-account-mode-of-payment::bernard
DECLARE @constraintName nvarchar(200)
SELECT @constraintName = name
FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('mode_of_payment')
  AND parent_column_id = (SELECT column_id
                          FROM sys.columns
                          WHERE object_id = OBJECT_ID('mode_of_payment')
                            AND name = 'own_account')
IF @constraintName IS NOT NULL
    EXEC ('ALTER TABLE mode_of_payment DROP CONSTRAINT ' + @constraintName)


ALTER TABLE mode_of_payment
    DROP COLUMN own_account;
GO

DECLARE @constraintNameAud nvarchar(200)
SELECT @constraintNameAud = name
FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('mode_of_payment_aud')
  AND parent_column_id = (SELECT column_id
                          FROM sys.columns
                          WHERE object_id = OBJECT_ID('mode_of_payment_aud')
                            AND name = 'own_account')
IF @constraintNameAud IS NOT NULL
    EXEC ('ALTER TABLE mode_of_payment_aud DROP CONSTRAINT ' + @constraintNameAud)


ALTER TABLE mode_of_payment_aud
    DROP COLUMN own_account;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/add-payment-mode-citizen-information-snapshot.sql::add-payment-mode-citizen-information-snapshot::bernard
alter table citizen_information_snapshot
    add payment_mode int null;
GO

alter table citizen_information_snapshot
    add constraint df_payment_mode default 1 for payment_mode;
GO

update citizen_information_snapshot
set payment_mode = 1
where payment_mode is null;
GO

alter table citizen_information_snapshot
    alter column payment_mode int not null;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/add-union-contribution-to-citizen-snapshot.sql::raw::includeAll
alter table citizen_information_snapshot
    add union_contribution_value_date date;
alter table citizen_information_snapshot
    add union_contribution_authorized bit default 0;
alter table citizen_information_snapshot
    add union_contribution_effective_date date;
GO

-- Changeset backend/src/main/resources/db/changelog/0.1/ddl/refactor-citizen-snapshot.sql::refactor-citizen-snapshot::bernard
alter table citizen_information_snapshot
    alter column numbox int null;
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-17/ddl/add-sync-follow-up.sql::add-sync-follow-up::bernard
create table sync_follow_up
(
    id                     uniqueidentifier  not null,
    created_date           datetime2(6)      not null,
    correlation_id         varchar(255),
    date_message_sent      datetimeoffset(6) not null,
    date_response_received datetimeoffset(6),
    error                  varchar(255),
    status                 varchar(255)      not null,
    request_id             uniqueidentifier,
    primary key (id)
);
GO

ALTER TABLE wave_task
    ADD CONSTRAINT CK__sync_follow_up__status
        CHECK (status in ('OPEN', 'CLOSED', 'WAITING'));
GO

alter table sync_follow_up
    add constraint FK_sync_follow_up_request_id foreign key (request_id) references request;
GO

create unique nonclustered index UIX_sync_follow_up_correlation_id on sync_follow_up (correlation_id);
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-18/ddl/add-birth-date-citizen-snapshot.sql::add-birth-date-citizen-snapshot::bernard
alter table citizen_information_snapshot
    add birth_date date null;
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-18/ddl/add-birth-date-citizen-snapshot.sql::make-house-number-nullable-citizen-snapshot::bernard
ALTER TABLE citizen_information_snapshot
    ALTER COLUMN house_number varchar(255) null;
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-18/ddl/wave-task-add-deleted-status.sql::raw::includeAll
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS CK__wave_task__status;

ALTER TABLE wave_task
    ADD CONSTRAINT CK__wave_task__status
        CHECK (status IN ('OPEN', 'CLOSED', 'WAITING', 'DELETED'));

ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS CK__sync_follow_up__status;
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-18/ddl/add-c9type-to-request.sql::add-c9type-to-request::bernard
alter table request
    add c9_type varchar(15) default '410';
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-18/ddl/add-c9type-to-request.sql::update-null-c9type-to-410::system
UPDATE request
SET c9_type = '410'
WHERE c9_type IS NULL;
GO

-- Changeset backend/src/main/resources/db/changelog/sprint-18/ddl/add-c9type-to-request.sql::make-c9type-not-null::system
ALTER TABLE request
    ALTER COLUMN c9_type varchar(15) NOT NULL;
GO

