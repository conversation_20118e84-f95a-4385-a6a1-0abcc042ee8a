package be.fgov.onerva.cu.backend.application.port.out

import java.time.LocalDate
import be.fgov.onerva.cu.backend.application.domain.Barema

/**
 * Port interface for retrieving Barema information.
 * This interface defines operations for accessing Barema data from external systems.
 */
fun interface BaremaPort {
    /**
     * Retrieves the latest Barema for a citizen as of the specified request date.
     *
     * - Do the call to the barema API V2, with the criteria specified in the user story.
     * - The API returns a list of baremas, take the first one in the list.
     * - In this barema there can be a list of items, take the first item regardless of its code.
     * - If a barema is found, take the article that has type ADMISSIBILITY and return the code for the article (the first one)
     *
     * @param citizenId The unique identifier of the citizen
     * @param requestDate The date for which the Barema is requested
     * @return The latest Barema for the citizen, or null if no Barema exists
     */
    fun getLatestBarema(citizenId: Int, requestDate: LocalDate): Barema?
}