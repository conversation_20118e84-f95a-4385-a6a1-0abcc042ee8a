import {WoUtils} from "./wo-utils";

describe("WoUtils DOM interaction", () => {
    let parentElement: HTMLElement;
    let buttonElement: HTMLButtonElement;
    let clicked = false;

    beforeEach(() => {
        // Setup: Create the parent container and a mock button
        parentElement = document.createElement("div");
        parentElement.id = "task-interaction-panel";

        buttonElement = document.createElement("button");
        buttonElement.classList.add("btn-unstyled", "mb-0");

        clicked = false;
        buttonElement.addEventListener("click", () => {
            clicked = true;
        });

        parentElement.appendChild(buttonElement);
        document.body.appendChild(parentElement);
    });

    afterEach(() => {
        // Cleanup: Remove the parent from the DOM
        document.body.removeChild(parentElement);
    });

    it("should click the button if it exists", () => {
        WoUtils.triggerWoWebComponentMaxMinSize();
        expect(clicked).toBeTruthy();
    });

    it("should do nothing if the button is missing", () => {
        parentElement.removeChild(buttonElement);
        clicked = false;

        WoUtils.triggerWoWebComponentMaxMinSize();
        expect(clicked).toBeFalsy();
    });
});
