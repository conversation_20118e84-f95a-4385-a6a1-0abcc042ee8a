import {
  DEFAULT_LANGUAGE,
  FakeMissingTranslationHandler,
  ISOLATE_TRANSLATE_SERVICE,
  MissingTranslationHandler,
  TranslateCompiler,
  TranslateDefaultParser,
  TranslateDirective,
  TranslateFakeCompiler,
  TranslateFakeLoader,
  TranslateLoader,
  TranslateModule,
  TranslateParser,
  TranslatePipe,
  TranslateService,
  TranslateStore,
  USE_DEFAULT_LANG,
  USE_EXTEND,
  _,
  equals,
  getValue,
  isArray,
  isDefined,
  isDict,
  isFunction,
  isObject,
  isString,
  mergeDeep,
  provideTranslateService,
  setValue
} from "./chunk-HVSIS4C2.js";
import "./chunk-IY4PCAU4.js";
import "./chunk-6ONIGQ3T.js";
import "./chunk-VOL5AYIH.js";
import "./chunk-NFXQRNMD.js";
import "./chunk-3F3XMFWP.js";
import "./chunk-WDMUDEB6.js";
export {
  DEFAULT_LANGUAGE,
  FakeMissingTranslationHandler,
  ISOLATE_TRANSLATE_SERVICE,
  MissingTranslationHandler,
  TranslateCompiler,
  TranslateDefaultParser,
  TranslateDirective,
  TranslateFakeCompiler,
  TranslateFakeLoader,
  TranslateLoader,
  TranslateModule,
  TranslateParser,
  TranslatePipe,
  TranslateService,
  TranslateStore,
  USE_DEFAULT_LANG,
  USE_EXTEND,
  _,
  equals,
  getValue,
  isArray,
  isDefined,
  isDict,
  isFunction,
  isObject,
  isString,
  mergeDeep,
  provideTranslateService,
  setValue
};
