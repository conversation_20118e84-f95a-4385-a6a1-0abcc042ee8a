
package be.fgov.onerva.cu.backend.utils

object BaremaUtils {
    
    /**
     * Formats barema string like in frontend: adds '/' after first 2 digits if barema starts with 2 digits
     * Examples: "1234" -> "12/34", "AB34" -> "AB34", null -> "N/A", "12/34" -> "12/34"
     * @param barema The barema string to format
     * @param defaultValue The value to return when barema is null or empty (default: "N/A")
     */
    fun formatBarema(barema: String?, defaultValue: String = "N/A"): String {
        if (barema.isNullOrEmpty()) {
            return defaultValue
        }

        if (barema.length > 2 && barema[2] == '/') {
            return barema
        }

        return if (barema.length >= 2 && barema.substring(0, 2).matches(Regex("\\d{2}"))) {
            "${barema.substring(0, 2)}/${barema.substring(2)}"
        } else {
            barema
        }
    }
}
