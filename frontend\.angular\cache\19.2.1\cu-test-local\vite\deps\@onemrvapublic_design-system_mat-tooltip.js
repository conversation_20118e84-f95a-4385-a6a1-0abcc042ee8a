import {
  Overlay,
  OverlayPositionBuilder
} from "./chunk-CMBQECF5.js";
import "./chunk-JUXS7KNM.js";
import {
  ComponentPortal
} from "./chunk-LEXROUWS.js";
import "./chunk-BZG4E42E.js";
import "./chunk-KW5IDIQI.js";
import {
  NgTemplateOutlet
} from "./chunk-VK67YYVV.js";
import {
  Component,
  Directive,
  ElementRef,
  HostListener,
  Input,
  NgModule,
  setClassMetadata,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementContainer,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵreference,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-IY4PCAU4.js";
import "./chunk-6ONIGQ3T.js";
import "./chunk-VOL5AYIH.js";
import "./chunk-NFXQRNMD.js";
import "./chunk-3F3XMFWP.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-tooltip.mjs
function OnemrvaMatTooltipComponent_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵtextInterpolate1(" ", ctx_r0.text, " ");
  }
}
function OnemrvaMatTooltipComponent_ng_container_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
var OnemrvaMatTooltipComponent = class _OnemrvaMatTooltipComponent {
  static {
    this.ɵfac = function OnemrvaMatTooltipComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatTooltipComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatTooltipComponent,
      selectors: [["onemrva-mat-tooltip"]],
      inputs: {
        text: "text",
        contentTemplate: "contentTemplate"
      },
      decls: 5,
      vars: 1,
      consts: [["simpleText", ""], [1, "tooltip-container"], [4, "ngTemplateOutlet"]],
      template: function OnemrvaMatTooltipComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div")(1, "div", 1);
          ɵɵtemplate(2, OnemrvaMatTooltipComponent_ng_template_2_Template, 1, 1, "ng-template", null, 0, ɵɵtemplateRefExtractor)(4, OnemrvaMatTooltipComponent_ng_container_4_Template, 1, 0, "ng-container", 2);
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          const simpleText_r2 = ɵɵreference(3);
          ɵɵadvance(4);
          ɵɵproperty("ngTemplateOutlet", ctx.contentTemplate || simpleText_r2);
        }
      },
      dependencies: [NgTemplateOutlet],
      styles: [".tooltip-container[_ngcontent-%COMP%]{background:#fff;color:#000;box-shadow:0 2px 6px #00000052;border-radius:4px;margin:2px;padding:8px;line-height:normal}"]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatTooltipComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-tooltip",
      template: `
    <div>
      <div class="tooltip-container">
        <ng-template #simpleText>
          {{ text }}
        </ng-template>
        <ng-container *ngTemplateOutlet="contentTemplate || simpleText">
        </ng-container>
      </div>
    </div>
  `,
      standalone: true,
      imports: [NgTemplateOutlet],
      styles: [".tooltip-container{background:#fff;color:#000;box-shadow:0 2px 6px #00000052;border-radius:4px;margin:2px;padding:8px;line-height:normal}\n"]
    }]
  }], null, {
    text: [{
      type: Input
    }],
    contentTemplate: [{
      type: Input
    }]
  });
})();
var OnemrvaMatTooltipDirective = class _OnemrvaMatTooltipDirective {
  constructor(_overlay, _overlayPositionBuilder, _elementRef) {
    this._overlay = _overlay;
    this._overlayPositionBuilder = _overlayPositionBuilder;
    this._elementRef = _elementRef;
    this.showToolTip = true;
    this.position = "bottom";
    this.positionOffset = 5;
  }
  ngOnInit() {
    if (!this.showToolTip) {
      return;
    }
    const positionStrategy = this._overlayPositionBuilder.flexibleConnectedTo(this._elementRef).withPositions(this.defineStrategy());
    this._overlayRef = this._overlay.create({
      positionStrategy
    });
  }
  defineStrategy() {
    switch (this.position) {
      case "top":
        return [{
          originX: "center",
          originY: "top",
          overlayX: "center",
          overlayY: "bottom",
          offsetY: -this.positionOffset
        }];
      case "right":
        return [{
          originX: "end",
          originY: "center",
          overlayX: "start",
          overlayY: "center",
          offsetX: this.positionOffset
        }];
      case "bottom":
        return [{
          originX: "center",
          originY: "bottom",
          overlayX: "center",
          overlayY: "top",
          offsetY: this.positionOffset
        }];
      case "left":
        return [{
          originX: "start",
          originY: "center",
          overlayX: "end",
          overlayY: "center",
          offsetX: -this.positionOffset
        }];
    }
  }
  show() {
    if (this._overlayRef && !this._overlayRef.hasAttached()) {
      const tooltipRef = this._overlayRef.attach(new ComponentPortal(OnemrvaMatTooltipComponent));
      tooltipRef.instance.text = this.customToolTip;
      tooltipRef.instance.contentTemplate = this.contentTemplate;
    }
  }
  hide() {
    this.closeToolTip();
  }
  ngOnDestroy() {
    this.closeToolTip();
  }
  closeToolTip() {
    if (this._overlayRef) {
      this._overlayRef.detach();
    }
  }
  static {
    this.ɵfac = function OnemrvaMatTooltipDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatTooltipDirective)(ɵɵdirectiveInject(Overlay), ɵɵdirectiveInject(OverlayPositionBuilder), ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _OnemrvaMatTooltipDirective,
      selectors: [["", "OnemrvaMatTooltip", ""]],
      hostBindings: function OnemrvaMatTooltipDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("mouseenter", function OnemrvaMatTooltipDirective_mouseenter_HostBindingHandler() {
            return ctx.show();
          })("mouseleave", function OnemrvaMatTooltipDirective_mouseleave_HostBindingHandler() {
            return ctx.hide();
          });
        }
      },
      inputs: {
        showToolTip: "showToolTip",
        customToolTip: "customToolTip",
        contentTemplate: "contentTemplate",
        position: "position",
        positionOffset: "positionOffset"
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatTooltipDirective, [{
    type: Directive,
    args: [{
      selector: "[OnemrvaMatTooltip]",
      standalone: true
    }]
  }], () => [{
    type: Overlay
  }, {
    type: OverlayPositionBuilder
  }, {
    type: ElementRef
  }], {
    showToolTip: [{
      type: Input
    }],
    customToolTip: [{
      type: Input
    }],
    contentTemplate: [{
      type: Input
    }],
    position: [{
      type: Input
    }],
    positionOffset: [{
      type: Input
    }],
    show: [{
      type: HostListener,
      args: ["mouseenter"]
    }],
    hide: [{
      type: HostListener,
      args: ["mouseleave"]
    }]
  });
})();
var OnemrvaMatTooltipModule = class _OnemrvaMatTooltipModule {
  static {
    this.ɵfac = function OnemrvaMatTooltipModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatTooltipModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaMatTooltipModule,
      imports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent],
      exports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatTooltipModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent],
      exports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent]
    }]
  }], null, null);
})();
export {
  OnemrvaMatTooltipComponent,
  OnemrvaMatTooltipDirective,
  OnemrvaMatTooltipModule
};
//# sourceMappingURL=@onemrvapublic_design-system_mat-tooltip.js.map
