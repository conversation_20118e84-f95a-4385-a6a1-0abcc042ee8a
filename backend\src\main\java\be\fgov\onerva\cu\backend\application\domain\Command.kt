package be.fgov.onerva.cu.backend.application.domain

import java.time.LocalDate

/**
 * Command object representing a received change of address request.
 * This command captures all information needed to initiate a change of address process.
 * This command gathers all information received from the C9 mesaage.
 *
 * @property c9Id Unique identifier for the C9 form
 * @property c9Type Type of the C9
 * @property ssin Social Security Identification Number of the requester
 * @property ec1Id Identifier of the identity document
 */
data class ChangePersonalDataRequestReceivedCommand(
    val c9Id: Long,
    val c9Type: String,
    val ssin: String,
    val ec1Id: Int?,
    val ec1DisplayUrl: String?,
    val requestDate: LocalDate,
    val paymentInstitution: Int,
    val entityCode: String?,
    val opKey: String,
    val sectOp: String,
    val scanUrl: String?,
    val unemploymentOffice: Int?,
    val introductionDate: LocalDate,
    val dateValid: LocalDate?,
    val scanNumber: Long?,
    val operatorCode: Int?,
    val introductionType: IntroductionType?,
    val dueDate: LocalDate?,
)

data class ChangePersonalDataRequestTreatedCommand(
    val c9Id: Long,
    val type: String,
    val ssin: String,
    val decisionType: DecisionType,
    val decisionDate: LocalDate,
    val user: String,
)

data class ChangePersonalDataUpdateCommand(
    val paymentInstitution: Int,
    val entityCode: String,
    val unemploymentOffice: Int?,
    val introductionDate: LocalDate,
    val dateValid: LocalDate?,
    val operatorCode: Int?,
    val scanUrl: String?,
    val scanNumber: Long?,
    val ec1Id: Int?,
    val ec1DisplayUrl: String?,
    val documentType: IdentityDocumentType,
    val introductionType: IntroductionType?,
)

/**
 * Data structure for persisting a change of address request.
 * This model represents the minimal set of data required for storage in the system.
 *
 * @property c9id Associated C9 form identifier
 * @property c9Type Type of the C9
 * @property ssin Social Security Identification Number of the citizen
 * @property sectOp Section operator code
 * @property opKey Operation key identifier
 * @property numbox Citizen's numbox identifier
 * @property requestDate Date when the change was requested
 * @property documentType The document type (Electronic or Paper)
 * @property paymentInstitution Identifier of the payment institution
 * @property entityCode Optional code identifying the processing entity
 * @property dossierId Identifier of the associated dossier
 * @property scanUrl Url of the scanned documents
 * @property unemploymentOffice Identifier of the unemployment office
 * @property introductionDate Date of introduction of the request
 * @property dateValid Validity date of the request
 * @property scanNumber Number of the scan
 * @property operatorCode Code of the operator
 * @property introductionType Type of introduction of the request
 * @property dueDate Due date of the request
 * @property ec1Id Identifier of the identity document
 * @property ec1DisplayUrl Url to display the identity document
 */
data class ChangePersonalDataPersistCommand(
    val c9id: Long,
    val c9Type: String,
    val ssin: String,
    val sectOp: String,
    val opKey: String,
    val numbox: Int,
    val requestDate: LocalDate,
    val introductionDate: LocalDate,
    val paymentInstitution: Int,
    val entityCode: String?,
    val dossierId: String,
    val documentType: IdentityDocumentType,
    val citizenInformation: CitizenInformation?,
    val modeOfPayment: ModeOfPayment?,
    val unionContribution: UnionContribution?,
    val scanUrl: String?,
    val unemploymentOffice: Int?,
    val dateValid: LocalDate?,
    val scanNumber: Long?,
    val operatorCode: Int?,
    val introductionType: IntroductionType?,
    val dueDate: LocalDate?,
    val ec1Id: Int?,
    val ec1DisplayUrl: String?,
)

data class CreateChangePersonalDataTaskCommand(
    val c9id: Long,
    val c9Type: String,
    val ssin: String,
    val numbox: Int,
    val introductionDate: LocalDate,
    val entityCode: String?,
    val requestDate: LocalDate,
    val dossierId: String,
    val paymentInstitution: Int,
    val sectOp: String,
    val dueDate: LocalDate?,
)

data class UpdateChangePersonalDataDecisionCommand(
    val decisionType: DecisionType,
    val decisionDate: LocalDate,
    val user: String,
    val decisionBarema: String?,
)

data class UpdateCitizenCommand(
    val ssin: String,
    val address: Address,
    val birthDate: LocalDate?,
    val nationalityCode: Int,
    val correlationId: String,
    val userName: String,
    val requestDate: LocalDate,
    val unionContribution: UnionContribution,
    val modeOfPayment: ModeOfPayment,
    val valueDate: LocalDate,
    val unemploymentOffice: Int,
)

