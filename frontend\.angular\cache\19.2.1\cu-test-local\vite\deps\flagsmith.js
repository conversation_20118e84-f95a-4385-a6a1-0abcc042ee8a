import "./chunk-WDMUDEB6.js";

// node_modules/flagsmith/index.mjs
var t = {
  getItemSync: function(t3) {
    try {
      return localStorage.getItem(t3) || null;
    } catch (t4) {
      return null;
    }
  },
  getItem: function(t3, e2) {
    var n2 = this;
    return new Promise(function(i2, a2) {
      try {
        var o2 = n2.getItemSync(t3);
        null == e2 || e2(null, o2), i2(o2);
      } catch (t4) {
        e2 && e2(t4, null), a2(t4);
      }
    });
  },
  setItem: function(t3, e2, n2) {
    return new Promise(function(i2, a2) {
      try {
        localStorage.setItem(t3, e2), n2 && n2(null, e2), i2(e2);
      } catch (t4) {
        n2 && n2(t4, null), a2(t4);
      }
    });
  }
};
var e = function() {
  return e = Object.assign || function(t3) {
    for (var e2, n2 = 1, i2 = arguments.length; n2 < i2; n2++) for (var a2 in e2 = arguments[n2]) Object.prototype.hasOwnProperty.call(e2, a2) && (t3[a2] = e2[a2]);
    return t3;
  }, e.apply(this, arguments);
};
function n(t3, e2, n2, i2) {
  return new (n2 || (n2 = Promise))(function(a2, o2) {
    function r2(t4) {
      try {
        l2(i2.next(t4));
      } catch (t5) {
        o2(t5);
      }
    }
    function s2(t4) {
      try {
        l2(i2.throw(t4));
      } catch (t5) {
        o2(t5);
      }
    }
    function l2(t4) {
      var e3;
      t4.done ? a2(t4.value) : (e3 = t4.value, e3 instanceof n2 ? e3 : new n2(function(t5) {
        t5(e3);
      })).then(r2, s2);
    }
    l2((i2 = i2.apply(t3, e2 || [])).next());
  });
}
function i(t3, e2) {
  var n2, i2, a2, o2, r2 = {
    label: 0,
    sent: function() {
      if (1 & a2[0]) throw a2[1];
      return a2[1];
    },
    trys: [],
    ops: []
  };
  return o2 = {
    next: s2(0),
    throw: s2(1),
    return: s2(2)
  }, "function" == typeof Symbol && (o2[Symbol.iterator] = function() {
    return this;
  }), o2;
  function s2(o3) {
    return function(s3) {
      return function(o4) {
        if (n2) throw new TypeError("Generator is already executing.");
        for (; r2; ) try {
          if (n2 = 1, i2 && (a2 = 2 & o4[0] ? i2.return : o4[0] ? i2.throw || ((a2 = i2.return) && a2.call(i2), 0) : i2.next) && !(a2 = a2.call(i2, o4[1])).done) return a2;
          switch (i2 = 0, a2 && (o4 = [2 & o4[0], a2.value]), o4[0]) {
            case 0:
            case 1:
              a2 = o4;
              break;
            case 4:
              return r2.label++, {
                value: o4[1],
                done: false
              };
            case 5:
              r2.label++, i2 = o4[1], o4 = [0];
              continue;
            case 7:
              o4 = r2.ops.pop(), r2.trys.pop();
              continue;
            default:
              if (!(a2 = r2.trys, (a2 = a2.length > 0 && a2[a2.length - 1]) || 6 !== o4[0] && 2 !== o4[0])) {
                r2 = 0;
                continue;
              }
              if (3 === o4[0] && (!a2 || o4[1] > a2[0] && o4[1] < a2[3])) {
                r2.label = o4[1];
                break;
              }
              if (6 === o4[0] && r2.label < a2[1]) {
                r2.label = a2[1], a2 = o4;
                break;
              }
              if (a2 && r2.label < a2[2]) {
                r2.label = a2[2], r2.ops.push(o4);
                break;
              }
              a2[2] && r2.ops.pop(), r2.trys.pop();
              continue;
          }
          o4 = e2.call(t3, r2);
        } catch (t4) {
          o4 = [6, t4], i2 = 0;
        } finally {
          n2 = a2 = 0;
        }
        if (5 & o4[0]) throw o4[1];
        return {
          value: o4[0] ? o4[1] : void 0,
          done: true
        };
      }([o3, s3]);
    };
  }
}
function a(t3, e2, n2) {
  if (n2 || 2 === arguments.length) for (var i2, a2 = 0, o2 = e2.length; a2 < o2; a2++) !i2 && a2 in e2 || (i2 || (i2 = Array.prototype.slice.call(e2, 0, a2)), i2[a2] = e2[a2]);
  return t3.concat(i2 || Array.prototype.slice.call(e2));
}
var o = function t2(e2, n2) {
  if (e2 === n2) return true;
  if (e2 && n2 && "object" == typeof e2 && "object" == typeof n2) {
    if (e2.constructor !== n2.constructor) return false;
    var i2, a2, o2;
    if (Array.isArray(e2)) {
      if ((i2 = e2.length) != n2.length) return false;
      for (a2 = i2; 0 != a2--; ) if (!t2(e2[a2], n2[a2])) return false;
      return true;
    }
    if (e2.constructor === RegExp) return e2.source === n2.source && e2.flags === n2.flags;
    if (e2.valueOf !== Object.prototype.valueOf) return e2.valueOf() === n2.valueOf();
    if (e2.toString !== Object.prototype.toString) return e2.toString() === n2.toString();
    if ((i2 = (o2 = Object.keys(e2)).length) !== Object.keys(n2).length) return false;
    for (a2 = i2; 0 != a2--; ) if (!Object.prototype.hasOwnProperty.call(n2, o2[a2])) return false;
    for (a2 = i2; 0 != a2--; ) {
      var r2 = o2[a2];
      if (!t2(e2[r2], n2[r2])) return false;
    }
    return true;
  }
  return e2 != e2 && n2 != n2;
};
function r(t3, e2) {
  var n2 = Object.keys(e2 || {}).filter(function(n3) {
    var i2 = null == t3 ? void 0 : t3[n3], a2 = null == e2 ? void 0 : e2[n3];
    return !o(i2, a2);
  });
  return Object.keys(t3 || {}).filter(function(t4) {
    Object.keys(e2 || {}).includes(t4) || n2.push(t4);
  }), Object.keys(n2).length ? n2 : null;
}
var s;
var l;
function u(t3, e2, n2) {
  var i2 = "shortString", a2 = true;
  "number" == typeof n2 && (i2 = "javaDouble", a2 = false), t3[i2] = t3[i2] || {}, t3[i2][e2] = a2 ? n2 + "" : n2;
}
function c(t3) {
  return !!t3 && "object" == typeof t3 && void 0 !== t3.value;
}
function h(t3) {
  return Object.fromEntries(Object.entries(t3).map(function(t4) {
    var e2 = t4[0], n2 = t4[1];
    return [e2, c(n2) ? n2 : {
      value: n2
    }];
  }));
}
function v(t3) {
  return e(e({}, t3), {
    identity: t3.identity ? e(e({}, t3.identity), {
      traits: h(t3.identity.traits || {})
    }) : void 0
  });
}
!function(t3) {
  t3.NONE = "NONE", t3.DEFAULT_FLAGS = "DEFAULT_FLAGS", t3.CACHE = "CACHE", t3.SERVER = "SERVER";
}(s || (s = {}));
var d;
var g = null;
var f = "FLAGSMITH_EVENT";
var p = "https://edge.api.flagsmith.com/api/v1/";
var y = function() {
  function t3(t4) {
    var n2 = this;
    this._trigger = null, this._triggerLoadingState = null, this.timestamp = null, this.isLoading = false, this.eventSource = null, this.getFlags = function() {
      var t5 = n2, i2 = t5.api, a2 = t5.evaluationContext;
      n2.log("Get Flags"), n2.isLoading = true, n2.loadingState.isFetching || n2.setLoadingState(e(e({}, n2.loadingState), {
        isFetching: true
      }));
      var o2 = "".concat(n2.getContext().identity), l2 = function(t6) {
        var i3, a3, l3, c2;
        if (t6 && o2 === "".concat(n2.getContext().identity)) {
          var h2 = t6.flags, v2 = t6.traits, d2 = t6.identifier;
          n2.isLoading = false;
          var g2 = {}, f2 = {};
          v2 = v2 || [], (h2 = h2 || []).forEach(function(t7) {
            g2[t7.feature.name.toLowerCase().replace(/ /g, "_")] = {
              id: t7.feature.id,
              enabled: t7.enabled,
              value: t7.feature_state_value
            };
          }), v2.forEach(function(t7) {
            f2[t7.trait_key.toLowerCase().replace(/ /g, "_")] = {
              transient: t7.transient,
              value: t7.trait_value
            };
          }), n2.oldFlags = e({}, n2.flags);
          var p2 = r(n2.oldFlags, g2), y2 = r(null === (i3 = n2.evaluationContext.identity) || void 0 === i3 ? void 0 : i3.traits, f2);
          if ((d2 || Object.keys(f2).length) && (n2.evaluationContext.identity = e(e({}, n2.evaluationContext.identity), {
            traits: f2
          }), d2 && (n2.evaluationContext.identity.identifier = d2, n2.identity = d2)), n2.flags = g2, n2.updateStorage(), n2._onChange(n2.oldFlags, {
            isFromServer: true,
            flagsChanged: p2,
            traitsChanged: y2
          }, n2._loadedState(null, s.SERVER)), n2.datadogRum) try {
            if (n2.datadogRum.trackTraits) {
              var m2 = {};
              Object.keys((null === (a3 = n2.evaluationContext.identity) || void 0 === a3 ? void 0 : a3.traits) || {}).map(function(t7) {
                m2["flagsmith_trait_" + t7] = n2.getTrait(t7);
              });
              var S2 = e(e(e({}, n2.datadogRum.client.getUser()), {
                id: n2.datadogRum.client.getUser().id || (null === (l3 = n2.evaluationContext.identity) || void 0 === l3 ? void 0 : l3.identifier)
              }), m2);
              n2.log("Setting Datadog user", S2), n2.datadogRum.client.setUser(S2);
            }
          } catch (t7) {
            console.error(t7);
          }
          if (n2.dtrum) try {
            var C2 = {
              javaDouble: {},
              date: {},
              shortString: {},
              javaLongOrObject: {}
            };
            Object.keys(n2.flags).map(function(t7) {
              u(C2, "flagsmith_value_" + t7, n2.getValue(t7, {
                skipAnalytics: true
              })), u(C2, "flagsmith_enabled_" + t7, n2.hasFeature(t7, {
                skipAnalytics: true
              }));
            }), Object.keys((null === (c2 = n2.evaluationContext.identity) || void 0 === c2 ? void 0 : c2.traits) || {}).map(function(t7) {
              u(C2, "flagsmith_trait_" + t7, n2.getTrait(t7));
            }), n2.log("Sending javaLongOrObject traits to dynatrace", C2.javaLongOrObject), n2.log("Sending date traits to dynatrace", C2.date), n2.log("Sending shortString traits to dynatrace", C2.shortString), n2.log("Sending javaDouble to dynatrace", C2.javaDouble), n2.dtrum.sendSessionProperties(C2.javaLongOrObject, C2.date, C2.shortString, C2.javaDouble);
          } catch (t7) {
            console.error(t7);
          }
        }
      };
      return a2.identity ? Promise.all([a2.identity.traits && Object.keys(a2.identity.traits).length || !a2.identity.identifier ? n2.getJSON(i2 + "identities/", "POST", JSON.stringify({
        identifier: a2.identity.identifier,
        transient: a2.identity.transient,
        traits: Object.entries(a2.identity.traits).map(function(t6) {
          var e2 = t6[0], n3 = t6[1];
          return {
            trait_key: e2,
            trait_value: null == n3 ? void 0 : n3.value,
            transient: null == n3 ? void 0 : n3.transient
          };
        }).filter(function(t6) {
          return void 0 !== t6.trait_value || (n2.log("Warning - attempted to set an undefined trait value for key", t6.trait_key), false);
        })
      })) : n2.getJSON(i2 + "identities/?identifier=" + encodeURIComponent(a2.identity.identifier) + (a2.identity.transient ? "&transient=true" : ""))]).then(function(t6) {
        return n2.evaluationContext.identity = e(e({}, n2.evaluationContext.identity), {
          traits: {}
        }), l2(null == t6 ? void 0 : t6[0]);
      }).catch(function(t6) {
        var e2 = t6.message, n3 = new Error(e2);
        return Promise.reject(n3);
      }) : n2.getJSON(i2 + "flags/").then(function(t6) {
        return l2({
          flags: t6,
          traits: void 0
        });
      });
    }, this.analyticsFlags = function() {
      var t5 = n2.api;
      if (n2.evaluationEvent && n2.evaluationContext.environment && n2.evaluationEvent[n2.evaluationContext.environment.apiKey]) return n2.evaluationEvent && 0 !== Object.getOwnPropertyNames(n2.evaluationEvent).length && 0 !== Object.getOwnPropertyNames(n2.evaluationEvent[n2.evaluationContext.environment.apiKey]).length ? n2.getJSON(t5 + "analytics/flags/", "POST", JSON.stringify(n2.evaluationEvent[n2.evaluationContext.environment.apiKey])).then(function(t6) {
        if (n2.evaluationContext.environment) {
          var i2 = n2.getState();
          n2.evaluationEvent || (n2.evaluationEvent = {}), n2.evaluationEvent[n2.evaluationContext.environment.apiKey] = {}, n2.setState(e(e({}, i2), {
            evaluationEvent: n2.evaluationEvent
          })), n2.updateEventStorage();
        }
      }).catch(function(t6) {
        n2.log("Exception fetching evaluationEvent", t6);
      }) : void 0;
    }, this.datadogRum = null, this.loadingState = {
      isLoading: true,
      isFetching: true,
      error: null,
      source: s.NONE
    }, this.canUseStorage = false, this.analyticsInterval = null, this.api = null, this.cacheFlags = false, this.enableAnalytics = false, this.enableLogs = false, this.evaluationContext = {}, this.evaluationEvent = null, this.flags = null, this.getFlagInterval = null, this.headers = null, this.identity = null, this.initialised = false, this.oldFlags = null, this.onChange = null, this.onError = null, this.ticks = null, this.timer = null, this.dtrum = null, this.withTraits = null, this.cacheOptions = {
      ttl: 0,
      skipAPI: false,
      loadStale: false,
      storageKey: void 0
    }, this.getValue = function(t5, e2, i2) {
      var a2 = n2.flags && n2.flags[t5.toLowerCase().replace(/ /g, "_")], o2 = null;
      if (a2 && (o2 = a2.value), (null == e2 ? void 0 : e2.skipAnalytics) || i2 || n2.evaluateFlag(t5, "VALUE"), null === o2 && void 0 !== (null == e2 ? void 0 : e2.fallback)) return e2.fallback;
      if (null == e2 ? void 0 : e2.json) try {
        return null === o2 ? (n2.log("Tried to parse null flag as JSON: " + t5), null) : JSON.parse(o2);
      } catch (t6) {
        return e2.fallback;
      }
      return o2;
    }, this.getTrait = function(t5) {
      var e2, i2;
      return (null === (e2 = n2.evaluationContext.identity) || void 0 === e2 ? void 0 : e2.traits) && (null === (i2 = n2.evaluationContext.identity.traits[t5.toLowerCase().replace(/ /g, "_")]) || void 0 === i2 ? void 0 : i2.value);
    }, this.getAllTraits = function() {
      var t5;
      return Object.fromEntries(Object.entries((null === (t5 = n2.evaluationContext.identity) || void 0 === t5 ? void 0 : t5.traits) || {}).map(function(t6) {
        var e2 = t6[0], n3 = t6[1];
        return [e2, null == n3 ? void 0 : n3.value];
      }));
    }, this.setContext = function(t5) {
      var i2, a2, o2 = v(t5);
      return n2.evaluationContext = e(e({}, o2), {
        environment: o2.environment || n2.evaluationContext.environment
      }), n2.identity = null === (a2 = null === (i2 = n2.getContext()) || void 0 === i2 ? void 0 : i2.identity) || void 0 === a2 ? void 0 : a2.identifier, n2.initialised ? n2.getFlags() : Promise.resolve();
    }, this.getContext = function() {
      return n2.evaluationContext;
    }, this.updateContext = function(t5) {
      return n2.setContext(e(e({}, n2.getContext()), t5));
    }, this.setTrait = function(t5, i2) {
      var a2;
      if (n2.api) return n2.setContext(e(e({}, n2.evaluationContext), {
        identity: e(e({}, n2.evaluationContext.identity), {
          traits: e(e({}, null === (a2 = n2.evaluationContext.identity) || void 0 === a2 ? void 0 : a2.traits), h(Object.fromEntries([[t5, i2]])))
        })
      }));
    }, this.setTraits = function(t5) {
      var i2;
      if (n2.api) return n2.setContext(e(e({}, n2.evaluationContext), {
        identity: e(e({}, n2.evaluationContext.identity), {
          traits: e(e({}, null === (i2 = n2.evaluationContext.identity) || void 0 === i2 ? void 0 : i2.traits), Object.fromEntries(Object.entries(t5).map(function(t6) {
            var e2 = t6[0], n3 = t6[1];
            return [e2, c(n3) ? n3 : {
              value: n3
            }];
          })))
        })
      }));
      console.error("Attempted to setTraits a user before calling flagsmith.init. Call flagsmith.init first, if you wish to prevent it sending a request for flags, call init with preventFetch:true.");
    }, this.hasFeature = function(t5, e2) {
      var i2 = "object" == typeof e2, a2 = n2.flags && n2.flags[t5.toLowerCase().replace(/ /g, "_")], o2 = false;
      return !a2 && i2 && void 0 !== e2.fallback ? o2 = null == e2 ? void 0 : e2.fallback : a2 && a2.enabled && (o2 = true), (i2 && !e2.skipAnalytics || !e2) && n2.evaluateFlag(t5, "ENABLED"), o2;
    }, this.getStorageKey = function() {
      var t5, e2;
      return (null === (t5 = n2.cacheOptions) || void 0 === t5 ? void 0 : t5.storageKey) || "FLAGSMITH_DB_" + (null === (e2 = n2.evaluationContext.environment) || void 0 === e2 ? void 0 : e2.apiKey);
    }, this.getJSON = function(t5, e2, i2) {
      var a2, o2, r2, s2 = n2.headers, u2 = {
        method: e2 || "GET",
        body: i2,
        cache: "no-cache",
        headers: {}
      };
      n2.evaluationContext.environment && (u2.headers["X-Environment-Key"] = n2.evaluationContext.environment.apiKey), e2 && "GET" !== e2 && (u2.headers["Content-Type"] = "application/json; charset=utf-8"), (null === (a2 = n2.applicationMetadata) || void 0 === a2 ? void 0 : a2.name) && (u2.headers["Flagsmith-Application-Name"] = n2.applicationMetadata.name), (null === (o2 = n2.applicationMetadata) || void 0 === o2 ? void 0 : o2.version) && (u2.headers["Flagsmith-Application-Version"] = n2.applicationMetadata.version), s2 && Object.assign(u2.headers, s2), l || console.error("Flagsmith: fetch is undefined, please specify a fetch implementation into flagsmith.init to support SSR.");
      var c2 = "".concat(null === (r2 = n2.evaluationContext.identity) || void 0 === r2 ? void 0 : r2.identifier);
      return l(t5, u2).then(function(i3) {
        var a3, o3, r3 = "".concat(null === (a3 = n2.evaluationContext.identity) || void 0 === a3 ? void 0 : a3.identifier);
        if (c2 === r3) {
          var s3 = null === (o3 = i3.headers) || void 0 === o3 ? void 0 : o3.get("x-flagsmith-document-updated-at");
          if (s3) try {
            var l2 = parseFloat(s3);
            if (isNaN(l2)) return Promise.reject("Failed to parse x-flagsmith-document-updated-at");
            n2.timestamp = l2;
          } catch (t6) {
            n2.log(t6, "Failed to parse x-flagsmith-document-updated-at", s3);
          }
          return n2.log("Fetch response: " + i3.status + " " + (e2 || "GET") + 0 + t5), i3.text().then(function(t6) {
            var e3 = t6;
            try {
              e3 = JSON.parse(t6);
            } catch (t7) {
            }
            return !e3 && i3.status && (e3 = "API Response: ".concat(i3.status)), i3.status && i3.status >= 200 && i3.status < 300 ? e3 : Promise.reject(new Error(e3));
          });
        }
        n2.log("Received response with identity mismatch, ignoring response. Requested: ".concat(c2, ", Current: ").concat(r3));
      });
    }, this.evaluateFlag = function(t5, e2) {
      if (n2.datadogRum && (n2.datadogRum.client.addFeatureFlagEvaluation ? "VALUE" === e2 ? n2.datadogRum.client.addFeatureFlagEvaluation("flagsmith_value_" + t5, n2.getValue(t5, {}, true)) : n2.datadogRum.client.addFeatureFlagEvaluation("flagsmith_enabled_" + t5, n2.hasFeature(t5, true)) : console.error("Flagsmith: Your datadog RUM client does not support the function addFeatureFlagEvaluation, please update it.")), n2.enableAnalytics) {
        if (!n2.evaluationEvent || !n2.evaluationContext.environment) return;
        n2.evaluationEvent[n2.evaluationContext.environment.apiKey] || (n2.evaluationEvent[n2.evaluationContext.environment.apiKey] = {}), void 0 === n2.evaluationEvent[n2.evaluationContext.environment.apiKey][t5] && (n2.evaluationEvent[n2.evaluationContext.environment.apiKey][t5] = 0), n2.evaluationEvent[n2.evaluationContext.environment.apiKey][t5] += 1;
      }
      n2.updateEventStorage();
    }, this._onChange = function(t5, e2, i2) {
      var a2, o2;
      n2.setLoadingState(i2), null === (a2 = n2.onChange) || void 0 === a2 || a2.call(n2, t5, e2, n2.loadingState), null === (o2 = n2._trigger) || void 0 === o2 || o2.call(n2);
    }, l = t4.fetch ? t4.fetch : "undefined" != typeof fetch ? fetch : null === global || void 0 === global ? void 0 : global.fetch, this.canUseStorage = "undefined" != typeof window || !!t4.browserlessStorage, this.applicationMetadata = t4.applicationMetadata, this.log("Constructing flagsmith instance " + t4), t4.eventSource && (d = t4.eventSource), t4.AsyncStorage && (g = t4.AsyncStorage);
  }
  return t3.prototype.init = function(t4) {
    var a2, o2, u2;
    return n(this, void 0, void 0, function() {
      var c2, h2, d2, y2, m2, S2, C2, _2, b2, E2, x2, O2, F2, w2, L2, I, A, j, k, T, P, N, R, K, D, U, G, J, M, H, V, q, B, W = this;
      return i(this, function(X) {
        switch (X.label) {
          case 0:
            c2 = v(t4.evaluationContext || this.evaluationContext), X.label = 1;
          case 1:
            if (X.trys.push([1, 13, , 14]), h2 = t4.environmentID, d2 = t4.api, y2 = void 0 === d2 ? p : d2, m2 = t4.headers, S2 = t4.onChange, C2 = t4.cacheFlags, _2 = t4.datadogRum, b2 = t4.onError, E2 = t4.defaultFlags, x2 = t4.fetch, O2 = t4.preventFetch, F2 = t4.enableLogs, w2 = t4.enableDynatrace, L2 = t4.enableAnalytics, I = t4.realtime, A = t4.eventSourceUrl, j = void 0 === A ? "https://realtime.flagsmith.com/" : A, k = t4.AsyncStorage, T = t4.identity, P = t4.traits, N = t4.state, R = t4.cacheOptions, K = t4.angularHttpClient, D = t4._trigger, U = t4._triggerLoadingState, G = t4.applicationMetadata, c2.environment = h2 ? {
              apiKey: h2
            } : c2.environment, !c2.environment || !c2.environment.apiKey) throw new Error("Please provide `evaluationContext.environment` with non-empty `apiKey`");
            if (c2.identity = T || P ? {
              identifier: T,
              traits: P ? Object.fromEntries(Object.entries(P).map(function(t5) {
                return [t5[0], {
                  value: t5[1]
                }];
              })) : {}
            } : c2.identity, this.evaluationContext = c2, this.api = (Y = y2).endsWith("/") ? Y : Y + "/", this.headers = m2, this.getFlagInterval = null, this.analyticsInterval = null, this.onChange = S2, J = "Wrong Flagsmith Configuration: preventFetch is true and no defaulFlags provided", this._trigger = D || this._trigger, this._triggerLoadingState = U || this._triggerLoadingState, this.onError = function(t5) {
              W.setLoadingState(e(e({}, W.loadingState), {
                isFetching: false,
                isLoading: false,
                error: t5
              })), null == b2 || b2(t5);
            }, this.enableLogs = F2 || false, this.cacheOptions = R ? {
              skipAPI: !!R.skipAPI,
              ttl: R.ttl || 0,
              storageKey: R.storageKey,
              loadStale: !!R.loadStale
            } : this.cacheOptions, !this.cacheOptions.ttl && this.cacheOptions.skipAPI && console.warn("Flagsmith: you have set a cache ttl of 0 and are skipping API calls, this means the API will not be hit unless you clear local storage."), x2 && (l = x2), this.enableAnalytics = L2 || false, this.flags = Object.assign({}, E2) || {}, this.datadogRum = _2 || null, this.initialised = true, this.ticks = 1e4, this.timer = this.enableLogs ? (/* @__PURE__ */ new Date()).valueOf() : null, this.cacheFlags = void 0 !== g && !!C2, this.applicationMetadata = G, f = "FLAGSMITH_EVENT_" + c2.environment.apiKey, k && (g = k), I && "undefined" != typeof window && this.setupRealtime(j, c2.environment.apiKey), Object.keys(this.flags).length && (this.loadingState = e(e({}, this.loadingState), {
              isLoading: false,
              source: s.DEFAULT_FLAGS
            })), this.setState(N), this.log("Initialising with properties", t4, this), w2 && ("undefined" == typeof dtrum ? console.error("You have attempted to enable dynatrace but dtrum is undefined, please check you have the Dynatrace RUM JavaScript API installed.") : this.dtrum = dtrum), K && (l = /* @__PURE__ */ function(t5) {
              return function(e2, n2) {
                var i2 = n2.headers, a3 = n2.method, o3 = n2.body;
                return new Promise(function(n3) {
                  switch (a3) {
                    case "GET":
                      return t5.get(e2, {
                        headers: i2
                      }).subscribe(function(t6) {
                        n3({
                          ok: true,
                          text: function() {
                            return Promise.resolve(t6);
                          }
                        });
                      });
                    case "POST":
                    case "PUT":
                      return t5.post(e2, o3, {
                        headers: i2
                      }).subscribe(function(t6) {
                        n3({
                          ok: true,
                          text: function() {
                            return Promise.resolve(t6);
                          }
                        });
                      });
                  }
                });
              };
            }(K)), g && this.canUseStorage && g.getItem(f).then(function(t5) {
              try {
                W.evaluationEvent = JSON.parse(t5) || {};
              } catch (t6) {
                W.evaluationEvent = {};
              }
              W.analyticsInterval = setInterval(W.analyticsFlags, W.ticks);
            }), this.enableAnalytics && (this.analyticsInterval && clearInterval(this.analyticsInterval), g && this.canUseStorage && g.getItem(f, function(t5, n2) {
              if (n2 && W.evaluationContext.environment) {
                var i2 = JSON.parse(n2);
                if (i2[W.evaluationContext.environment.apiKey]) {
                  var a3 = W.getState();
                  W.log("Retrieved events from cache", n2), W.setState(e(e({}, a3), {
                    evaluationEvent: i2[W.evaluationContext.environment.apiKey]
                  }));
                }
              }
            })), !C2) return [3, 9];
            if (!g || !this.canUseStorage) return [3, 8];
            M = function(t5, a3) {
              return n(W, void 0, void 0, function() {
                var t6, n2, o3, l2, u3, c3, h3, d3, g2, f2, p2, y3, m3, S3, C3, _3, b3, x3, F3, w3 = this;
                return i(this, function(i2) {
                  switch (i2.label) {
                    case 0:
                      if (!a3) return [3, 7];
                      t6 = null, n2 = null, i2.label = 1;
                    case 1:
                      return i2.trys.push([1, 5, , 6]), o3 = JSON.parse(a3), l2 = false, u3 = false, o3 && o3.api === this.api && (null === (f2 = null === (g2 = o3.evaluationContext) || void 0 === g2 ? void 0 : g2.environment) || void 0 === f2 ? void 0 : f2.apiKey) === (null === (p2 = this.evaluationContext.environment) || void 0 === p2 ? void 0 : p2.apiKey) && (c3 = true, this.evaluationContext.identity && (null === (m3 = null === (y3 = o3.evaluationContext) || void 0 === y3 ? void 0 : y3.identity) || void 0 === m3 ? void 0 : m3.identifier) !== this.evaluationContext.identity.identifier && (this.log("Ignoring cache, identity has changed from " + (null === (C3 = null === (S3 = o3.evaluationContext) || void 0 === S3 ? void 0 : S3.identity) || void 0 === C3 ? void 0 : C3.identifier) + " to " + this.evaluationContext.identity.identifier), c3 = false), this.cacheOptions.ttl && (!o3.ts || (/* @__PURE__ */ new Date()).valueOf() - o3.ts > this.cacheOptions.ttl) && (o3.ts && !this.cacheOptions.loadStale ? (this.log("Ignoring cache, timestamp is too old ts:" + o3.ts + " ttl: " + this.cacheOptions.ttl + " time elapsed since cache: " + ((/* @__PURE__ */ new Date()).valueOf() - o3.ts) + "ms"), c3 = false) : o3.ts && this.cacheOptions.loadStale && (this.log("Loading stale cache, timestamp ts:" + o3.ts + " ttl: " + this.cacheOptions.ttl + " time elapsed since cache: " + ((/* @__PURE__ */ new Date()).valueOf() - o3.ts) + "ms"), u3 = true, c3 = true)), c3 && (l2 = true, t6 = r(this.flags, o3.flags), this.setState(e(e({}, o3), {
                        evaluationContext: v(e(e({}, o3.evaluationContext), {
                          identity: (null === (_3 = o3.evaluationContext) || void 0 === _3 ? void 0 : _3.identity) ? e(e({}, null === (b3 = o3.evaluationContext) || void 0 === b3 ? void 0 : b3.identity), {
                            traits: e({}, P || {})
                          }) : void 0
                        }))
                      })), this.log("Retrieved flags from cache", o3))), l2 ? (h3 = !O2 && (!this.cacheOptions.skipAPI || u3), this._onChange(null, {
                        isFromServer: false,
                        flagsChanged: t6,
                        traitsChanged: n2
                      }, this._loadedState(null, s.CACHE, h3)), this.oldFlags = this.flags, this.cacheOptions.skipAPI && l2 && !u3 && this.log("Skipping API, using cache"), h3 && this.getFlags().catch(function(t7) {
                        var e2;
                        null === (e2 = w3.onError) || void 0 === e2 || e2.call(w3, t7);
                      }), [3, 4]) : [3, 2];
                    case 2:
                      return O2 ? [3, 4] : [4, this.getFlags()];
                    case 3:
                      i2.sent(), i2.label = 4;
                    case 4:
                      return [3, 6];
                    case 5:
                      return d3 = i2.sent(), this.log("Exception fetching cached logs", d3), [3, 6];
                    case 6:
                      return [3, 10];
                    case 7:
                      return O2 ? [3, 9] : [4, this.getFlags()];
                    case 8:
                      return i2.sent(), [3, 10];
                    case 9:
                      if (E2) this._onChange(null, {
                        isFromServer: false,
                        flagsChanged: r({}, this.flags),
                        traitsChanged: r({}, null === (x3 = this.evaluationContext.identity) || void 0 === x3 ? void 0 : x3.traits)
                      }, this._loadedState(null, s.DEFAULT_FLAGS));
                      else {
                        if (!this.flags) throw new Error(J);
                        this._onChange(null, {
                          isFromServer: false,
                          flagsChanged: r({}, this.flags),
                          traitsChanged: r({}, null === (F3 = this.evaluationContext.identity) || void 0 === F3 ? void 0 : F3.traits)
                        }, this._loadedState(null, s.DEFAULT_FLAGS));
                      }
                      i2.label = 10;
                    case 10:
                      return [2];
                  }
                });
              });
            }, X.label = 2;
          case 2:
            return X.trys.push([2, 7, , 8]), g.getItemSync ? (H = g.getItemSync(this.getStorageKey()), [3, 5]) : [3, 3];
          case 3:
            return [4, g.getItem(this.getStorageKey())];
          case 4:
            H = X.sent(), X.label = 5;
          case 5:
            return [4, M(null, H)];
          case 6:
          case 7:
            return X.sent(), [3, 8];
          case 8:
            return [3, 12];
          case 9:
            return O2 ? [3, 11] : [4, this.getFlags()];
          case 10:
            return X.sent(), [3, 12];
          case 11:
            if (E2) this._onChange(null, {
              isFromServer: false,
              flagsChanged: r({}, E2),
              traitsChanged: r({}, null === (a2 = c2.identity) || void 0 === a2 ? void 0 : a2.traits)
            }, this._loadedState(null, s.DEFAULT_FLAGS));
            else if (this.flags && (V = null, 0 === Object.keys(this.flags).length && (V = J), this._onChange(null, {
              isFromServer: false,
              flagsChanged: r({}, this.flags),
              traitsChanged: r({}, null === (o2 = c2.identity) || void 0 === o2 ? void 0 : o2.traits)
            }, this._loadedState(V, s.DEFAULT_FLAGS)), V)) throw new Error(V);
            X.label = 12;
          case 12:
            return [3, 14];
          case 13:
            throw q = X.sent(), this.log("Error during initialisation ", q), B = q instanceof Error ? q : new Error("".concat(q)), null === (u2 = this.onError) || void 0 === u2 || u2.call(this, B), q;
          case 14:
            return [2];
        }
        var Y;
      });
    });
  }, t3.prototype.getAllFlags = function() {
    return this.flags;
  }, t3.prototype.identify = function(t4, e2, n2) {
    return this.identity = t4, this.evaluationContext.identity = {
      identifier: t4,
      transient: n2,
      traits: this.evaluationContext.identity && this.evaluationContext.identity.identifier == t4 ? this.evaluationContext.identity.traits : {}
    }, this.evaluationContext.identity.identifier = t4, this.log("Identify: " + this.evaluationContext.identity.identifier), e2 && (this.evaluationContext.identity.traits = Object.fromEntries(Object.entries(e2).map(function(t5) {
      var e3 = t5[0], n3 = t5[1];
      return [e3, c(n3) ? n3 : {
        value: n3
      }];
    }))), this.initialised ? this.getFlags() : Promise.resolve();
  }, t3.prototype.getState = function() {
    return {
      api: this.api,
      flags: this.flags,
      ts: this.ts,
      evaluationContext: this.evaluationContext,
      identity: this.identity,
      evaluationEvent: this.evaluationEvent
    };
  }, t3.prototype.setState = function(t4) {
    var e2, n2;
    t4 && (this.initialised = true, this.api = t4.api || this.api || p, this.flags = t4.flags || this.flags, this.evaluationContext = t4.evaluationContext || this.evaluationContext, this.evaluationEvent = t4.evaluationEvent || this.evaluationEvent, this.identity = null === (n2 = null === (e2 = this.getContext()) || void 0 === e2 ? void 0 : e2.identity) || void 0 === n2 ? void 0 : n2.identifier, this.log("setState called", this));
  }, t3.prototype.logout = function() {
    return this.identity = null, this.evaluationContext.identity = null, this.initialised ? this.getFlags() : Promise.resolve();
  }, t3.prototype.startListening = function(t4) {
    void 0 === t4 && (t4 = 1e3), this.getFlagInterval && clearInterval(this.getFlagInterval), this.getFlagInterval = setInterval(this.getFlags, t4);
  }, t3.prototype.stopListening = function() {
    this.getFlagInterval && (clearInterval(this.getFlagInterval), this.getFlagInterval = null);
  }, t3.prototype._loadedState = function(t4, e2, n2) {
    return void 0 === t4 && (t4 = null), void 0 === n2 && (n2 = false), {
      error: t4,
      isFetching: n2,
      isLoading: false,
      source: e2
    };
  }, t3.prototype.log = function() {
    for (var t4 = [], e2 = 0; e2 < arguments.length; e2++) t4[e2] = arguments[e2];
    this.enableLogs && console.log.apply(this, a(["FLAGSMITH:", (/* @__PURE__ */ new Date()).valueOf() - (this.timer || 0), "ms"], t4, true));
  }, t3.prototype.updateStorage = function() {
    if (this.cacheFlags) {
      this.ts = (/* @__PURE__ */ new Date()).valueOf();
      var t4 = JSON.stringify(this.getState());
      this.log("Setting storage", t4), g.setItem(this.getStorageKey(), t4);
    }
  }, t3.prototype.updateEventStorage = function() {
    if (this.enableAnalytics) {
      var t4 = JSON.stringify(this.getState().evaluationEvent);
      g.setItem(f, t4).catch(function(t5) {
        return console.error("Flagsmith: Error setting item in async storage", t5);
      });
    }
  }, t3.prototype.setLoadingState = function(t4) {
    var n2;
    o(t4, this.loadingState) || (this.loadingState = e({}, t4), this.log("Loading state changed", t4), null === (n2 = this._triggerLoadingState) || void 0 === n2 || n2.call(this));
  }, t3.prototype.setupRealtime = function(t4, e2) {
    var n2 = this, i2 = t4 + "sse/environments/" + e2 + "/stream";
    d ? this.eventSource || (this.log("Creating event source with url " + i2), this.eventSource = new d(i2), this.eventSource.addEventListener("environment_updated", function(t5) {
      var e3;
      try {
        e3 = JSON.parse(t5.data).updated_at;
      } catch (t6) {
        n2.log("Could not parse sse event", t6);
      }
      e3 ? !n2.timestamp || e3 > n2.timestamp ? n2.isLoading ? n2.log("updated_at is new, but flags are loading", t5.data, n2.timestamp) : (n2.log("updated_at is new, fetching flags", t5.data, n2.timestamp), n2.getFlags()) : n2.log("updated_at is outdated, skipping get flags", t5.data, n2.timestamp) : n2.log("No updated_at received, fetching flags", t5);
    })) : this.log("Error, EventSource is undefined");
  }, t3;
}();
function m(t3) {
  var e2 = t3.fetch, n2 = t3.AsyncStorage, i2 = t3.eventSource;
  return new y({
    fetch: e2,
    AsyncStorage: n2,
    eventSource: i2
  });
}
var S;
var C = (S = function(t3, e2) {
  return S = Object.setPrototypeOf || {
    __proto__: []
  } instanceof Array && function(t4, e3) {
    t4.__proto__ = e3;
  } || function(t4, e3) {
    for (var n2 in e3) Object.prototype.hasOwnProperty.call(e3, n2) && (t4[n2] = e3[n2]);
  }, S(t3, e2);
}, function(t3, e2) {
  if ("function" != typeof e2 && null !== e2) throw new TypeError("Class extends value " + String(e2) + " is not a constructor or null");
  function n2() {
    this.constructor = t3;
  }
  S(t3, e2), t3.prototype = null === e2 ? Object.create(e2) : (n2.prototype = e2.prototype, new n2());
});
var _ = function(t3) {
  var e2 = "function" == typeof Symbol && Symbol.iterator, n2 = e2 && t3[e2], i2 = 0;
  if (n2) return n2.call(t3);
  if (t3 && "number" == typeof t3.length) return {
    next: function() {
      return t3 && i2 >= t3.length && (t3 = void 0), {
        value: t3 && t3[i2++],
        done: !t3
      };
    }
  };
  throw new TypeError(e2 ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var b = function(t3, e2) {
  var n2 = "function" == typeof Symbol && t3[Symbol.iterator];
  if (!n2) return t3;
  var i2, a2, o2 = n2.call(t3), r2 = [];
  try {
    for (; (void 0 === e2 || e2-- > 0) && !(i2 = o2.next()).done; ) r2.push(i2.value);
  } catch (t4) {
    a2 = {
      error: t4
    };
  } finally {
    try {
      i2 && !i2.done && (n2 = o2.return) && n2.call(o2);
    } finally {
      if (a2) throw a2.error;
    }
  }
  return r2;
};
var E = function(t3, e2, n2) {
  if (n2 || 2 === arguments.length) for (var i2, a2 = 0, o2 = e2.length; a2 < o2; a2++) !i2 && a2 in e2 || (i2 || (i2 = Array.prototype.slice.call(e2, 0, a2)), i2[a2] = e2[a2]);
  return t3.concat(i2 || Array.prototype.slice.call(e2));
};
var x = function(t3) {
  function e2() {
    return t3.call(this, "EventSource not available.\nConsider loading an EventSource polyfill and making it available globally as EventSource, or passing one in as eventSourceClass to the ReconnectingEventSource constructor.") || this;
  }
  return C(e2, t3), e2;
}(Error);
var O = function() {
  function t3(t4, e2) {
    var n2 = this;
    if (this.CONNECTING = 0, this.OPEN = 1, this.CLOSED = 2, this._configuration = null != e2 ? Object.assign({}, e2) : void 0, this.withCredentials = false, this._eventSource = null, this._lastEventId = null, this._timer = null, this._listeners = {
      open: [],
      error: [],
      message: []
    }, this.url = t4.toString(), this.readyState = this.CONNECTING, this.max_retry_time = 3e3, this.eventSourceClass = globalThis.FlagsmithEventSource, null != this._configuration && (this._configuration.lastEventId && (this._lastEventId = this._configuration.lastEventId, delete this._configuration.lastEventId), this._configuration.max_retry_time && (this.max_retry_time = this._configuration.max_retry_time, delete this._configuration.max_retry_time), this._configuration.eventSourceClass && (this.eventSourceClass = this._configuration.eventSourceClass, delete this._configuration.eventSourceClass)), null == this.eventSourceClass || "function" != typeof this.eventSourceClass) throw new x();
    this._onevent_wrapped = function(t5) {
      n2._onevent(t5);
    }, this._start();
  }
  return t3.prototype.dispatchEvent = function(t4) {
    throw new Error("Method not implemented.");
  }, t3.prototype._start = function() {
    var t4, e2, n2 = this, i2 = this.url;
    this._lastEventId && (-1 === i2.indexOf("?") ? i2 += "?" : i2 += "&", i2 += "lastEventId=" + encodeURIComponent(this._lastEventId)), this._eventSource = new this.eventSourceClass(i2, this._configuration), this._eventSource.onopen = function(t5) {
      n2._onopen(t5);
    }, this._eventSource.onerror = function(t5) {
      n2._onerror(t5);
    }, this._eventSource.onmessage = function(t5) {
      n2.onmessage(t5);
    };
    try {
      for (var a2 = _(Object.keys(this._listeners)), o2 = a2.next(); !o2.done; o2 = a2.next()) {
        var r2 = o2.value;
        this._eventSource.addEventListener(r2, this._onevent_wrapped);
      }
    } catch (e3) {
      t4 = {
        error: e3
      };
    } finally {
      try {
        o2 && !o2.done && (e2 = a2.return) && e2.call(a2);
      } finally {
        if (t4) throw t4.error;
      }
    }
  }, t3.prototype._onopen = function(t4) {
    0 === this.readyState && (this.readyState = 1, this.onopen(t4));
  }, t3.prototype._onerror = function(t4) {
    var e2 = this;
    if (1 === this.readyState && (this.readyState = 0, this.onerror(t4)), this._eventSource) {
      this._eventSource.close(), this._eventSource = null;
      var n2 = Math.round(this.max_retry_time * Math.random());
      this._timer = setTimeout(function() {
        return e2._start();
      }, n2);
    }
  }, t3.prototype._onevent = function(t4) {
    var e2, n2;
    t4 && t4.lastEventId && (this._lastEventId = t4.lastEventId);
    var i2 = this._listeners[t4.type];
    if (null != i2) try {
      for (var a2 = _(E([], b(i2), false)), o2 = a2.next(); !o2.done; o2 = a2.next()) {
        o2.value.call(this, t4);
      }
    } catch (t5) {
      e2 = {
        error: t5
      };
    } finally {
      try {
        o2 && !o2.done && (n2 = a2.return) && n2.call(a2);
      } finally {
        if (e2) throw e2.error;
      }
    }
    "message" === t4.type && this.onmessage(t4);
  }, t3.prototype.onopen = function(t4) {
  }, t3.prototype.onerror = function(t4) {
  }, t3.prototype.onmessage = function(t4) {
  }, t3.prototype.close = function() {
    this._timer && (clearTimeout(this._timer), this._timer = null), this._eventSource && (this._eventSource.close(), this._eventSource = null), this.readyState = 2;
  }, t3.prototype.addEventListener = function(t4, e2, n2) {
    null == this._listeners[t4] && (this._listeners[t4] = [], null != this._eventSource && this._eventSource.addEventListener(t4, this._onevent_wrapped));
    var i2 = this._listeners[t4];
    i2.includes(e2) || (this._listeners[t4] = E(E([], b(i2), false), [e2], false));
  }, t3.prototype.removeEventListener = function(t4, e2, n2) {
    var i2 = this._listeners[t4];
    this._listeners[t4] = i2.filter(function(t5) {
      return t5 !== e2;
    });
  }, t3;
}();
globalThis.FlagsmithEventSource = "undefined" != typeof EventSource ? EventSource : null;
var F = function(t3, e2) {
  return e2 = e2 || {}, new Promise(function(n2, i2) {
    var a2 = new XMLHttpRequest(), o2 = [], r2 = [], s2 = {}, l2 = function() {
      return {
        ok: 2 == (a2.status / 100 | 0),
        statusText: a2.statusText,
        status: a2.status,
        url: a2.responseURL,
        text: function() {
          return Promise.resolve(a2.responseText);
        },
        json: function() {
          return Promise.resolve(a2.responseText).then(JSON.parse);
        },
        blob: function() {
          return Promise.resolve(new Blob([a2.response]));
        },
        clone: l2,
        headers: {
          keys: function() {
            return o2;
          },
          entries: function() {
            return r2;
          },
          get: function(t4) {
            return s2[t4.toLowerCase()];
          },
          has: function(t4) {
            return t4.toLowerCase() in s2;
          }
        }
      };
    };
    for (var u2 in a2.open(e2.method || "get", t3, true), a2.onload = function() {
      a2.getAllResponseHeaders().replace(/^(.*?):[^\S\n]*([\s\S]*?)$/gm, function(t4, e3, n3) {
        o2.push(e3 = e3.toLowerCase()), r2.push([e3, n3]), s2[e3] = s2[e3] ? s2[e3] + "," + n3 : n3;
      }), n2(l2());
    }, a2.onerror = i2, a2.withCredentials = "include" == e2.credentials, e2.headers) a2.setRequestHeader(u2, e2.headers[u2]);
    a2.send(e2.body || null);
  });
};
var w = m({
  AsyncStorage: t,
  fetch: F,
  eventSource: O
});
"undefined" != typeof window && (window.flagsmith = w);
var L = function() {
  return m({
    AsyncStorage: t,
    fetch: F,
    eventSource: O
  });
};
export {
  L as createFlagsmithInstance,
  w as default
};
//# sourceMappingURL=flagsmith.js.map
