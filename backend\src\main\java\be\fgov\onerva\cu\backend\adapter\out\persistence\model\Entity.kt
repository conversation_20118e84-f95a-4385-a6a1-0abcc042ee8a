package be.fgov.onerva.cu.backend.adapter.out.persistence.model

import java.time.LocalDate
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorColumn
import jakarta.persistence.DiscriminatorType
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Inheritance
import jakarta.persistence.InheritanceType
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.OneToMany
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.envers.Audited
import org.hibernate.envers.NotAudited
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RequestStatus

@Entity
@Table(name = "request")
@DiscriminatorColumn(discriminatorType = DiscriminatorType.STRING, name = "type")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
abstract class RequestEntity(
    @Column(name = "c9_id")
    open var c9Id: Long,

    @Column(name = "c9_type")
    open var c9Type: String,

    @Column(name = "op_key")
    open var opKey: String,

    @Column(name = "sect_op")
    open var sectOp: String,

    @Column(name = "request_date")
    open var requestDate: LocalDate,

    @Column(name = "ssin")
    open var ssin: String,

    @Column(name = "decision_type")
    @Enumerated(EnumType.STRING)
    open var decisionType: DecisionType? = null,

    @Column(name = "decision_from_mfx")
    open var decisionFromMfx: Boolean? = null,

    @Column(name = "decision_user")
    open var decisionUser: String? = null,

    @Column(name = "decision_date")
    open var decisionDate: LocalDate? = null,

    @Column(name = "decision_barema", length = 25)
    open var decisionBarema: String? = null,

    @Column(name = "unemployment_office")
    open var unemploymentOffice: Int? = null,

    @Column(name = "payment_institution")
    open var paymentInstitution: Int? = null,

    @Column(name = "introduction_date")
    open var introductionDate: LocalDate? = null,

    @Column(name = "date_valid")
    open var dateValid: LocalDate? = null,

    @Column(name = "scan_number")
    open var scanNumber: Long? = null,

    @Column(name = "scan_url")
    open var scanUrl: String? = null,

    @Column(name = "operator_code")
    open var operatorCode: Int? = null,

    @Column(name = "entity_code")
    open var entityCode: String? = null,

    @Column(name = "introduction_Type")
    @Enumerated(EnumType.STRING)
    open var introductionType: IntroductionType? = null,

    @Column(name = "due_date")
    open var dueDate: LocalDate? = null,

    @Column(name = "numbox")
    open var numbox: Int? = null,

    @Column(name = "ec1_id")
    open var ec1Id: Int? = null,

    @Column(name = "ec1_display_url")
    open var ec1DisplayUrl: String? = null,

    @OneToMany(mappedBy = "request", fetch = FetchType.LAZY)
    open var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf(),
) : BaseEntity()

@Entity
@Table(name = "request_information")
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
class RequestInformationEntity(
    @OneToOne
    @JoinColumn(name = "request_id")
    @NotAudited
    val request: RequestEntity,

    @Audited
    @Column(name = "request_date")
    var requestDate: LocalDate,
) : BaseEntityAudited()

@Entity
@DiscriminatorValue("CHANGE_PERSONAL_DATA")
class ChangePersonalDataRequestEntity(
    @OneToOne(mappedBy = "request")
    var citizenInformation: CitizenInformationEntity? = null,

    @OneToOne(mappedBy = "request")
    var modeOfPayment: ModeOfPaymentEntity? = null,

    @OneToOne(mappedBy = "request")
    var unionContribution: UnionContributionEntity? = null,

    @OneToOne(mappedBy = "request")
    var requestInformation: RequestInformationEntity? = null,

    @Column(name = "document_type")
    @Enumerated(EnumType.STRING)
    var documentType: IdentityDocumentType,

    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    var status: RequestStatus = RequestStatus.OPEN,

    @Column(name = "process_in_wave", nullable = false)
    var processInWave: Boolean = false,

    @OneToMany(mappedBy = "request", fetch = FetchType.LAZY)
    var routingDecisions: MutableSet<RoutingDecisionItemEntity> = mutableSetOf(),

    c9Id: Long,
    c9Type: String,
    opKey: String,
    sectOp: String,
    requestDate: LocalDate,
    ssin: String,
    unemploymentOffice: Int?,
    paymentInstitution: Int?,
    introductionDate: LocalDate,
    dateValid: LocalDate?,
    scanNumber: Long?,
    scanUrl: String?,
    operatorCode: Int?,
    entityCode: String?,
    introductionType: IntroductionType?,
    dueDate: LocalDate?,
    numbox: Int?,
    ec1Id: Int?,
    ec1DisplayUrl: String?,
) : RequestEntity(
    c9Id,
    c9Type,
    opKey,
    sectOp,
    requestDate,
    ssin,
    unemploymentOffice = unemploymentOffice,
    paymentInstitution = paymentInstitution,
    introductionDate = introductionDate,
    dateValid = dateValid,
    scanNumber = scanNumber,
    scanUrl = scanUrl,
    operatorCode = operatorCode,
    entityCode = entityCode,
    introductionType = introductionType,
    dueDate = dueDate,
    numbox = numbox,
    ec1Id = ec1Id,
    ec1DisplayUrl = ec1DisplayUrl,
)

@Entity
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
@Table(name = "citizen_information")
class CitizenInformationEntity(
    @OneToOne
    @JoinColumn(name = "request_id")
    @NotAudited
    val request: RequestEntity,

    @Column(name = "first_name", nullable = false)
    var firstName: String,

    @Column(name = "last_name", nullable = false)
    var lastName: String,

    @Column(name = "birth_date", nullable = false)
    var birthDate: LocalDate,

    // Mutable fields
    @Audited
    @Column(name = "nationality_code", nullable = false)
    var nationalityCode: Int,

    @Audited
    @Embedded
    var address: Address,

    @Column(name = "update_status")
    @Enumerated(EnumType.STRING)
    var updateStatus: UpdateStatus,

    ) : BaseEntityAudited()

@Embeddable
@Audited
data class Address(
    @Column(name = "country_code", nullable = false)
    var countryCode: Int,

    @Column(name = "street", nullable = false)
    var street: String,

    @Column(name = "house_number", nullable = false)
    var houseNumber: String,

    @Column(name = "box_number", nullable = true)
    var boxNumber: String? = null,

    @Column(name = "zip_code", nullable = false)
    var zipCode: String,

    @Column(name = "city", nullable = false)
    var city: String,

    @Column(name = "address_valid_from", nullable = false)
    var validFrom: LocalDate,
)

@Entity
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
@Table(name = "mode_of_payment")
class ModeOfPaymentEntity(
    @OneToOne
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: RequestEntity,

    @Column(name = "other_person_name", nullable = true)
    var otherPersonName: String?,

    @Column(name = "iban", nullable = false)
    var iban: String,

    @Column(name = "bic", nullable = true)
    var bic: String?,

    @Column(name = "valid_from", nullable = false)
    var validFrom: LocalDate,

    @Column(name = "update_status")
    @Enumerated(EnumType.STRING)
    var updateStatus: UpdateStatus,

    ) : BaseEntityAudited()

@Entity
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
@Table(name = "union_contribution")
class UnionContributionEntity(
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: RequestEntity,

    @Column(name = "authorized", nullable = false)
    var authorized: Boolean?,

    @Column(name = "effective_date", nullable = false)
    var effectiveDate: LocalDate?,

    @Column(name = "update_status")
    @Enumerated(EnumType.STRING)
    var updateStatus: UpdateStatus,
) : BaseEntityAudited()

@Entity
@Table(name = "routing_decision_item")
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
class RoutingDecisionItemEntity(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: ChangePersonalDataRequestEntity,

    @Audited
    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    var type: ManualVerificationType,

    @Audited
    @Column(name = "value", nullable = true)
    var value: Boolean?,
) : BaseEntityAudited()

enum class UpdateStatus {
    FROM_C9,
    EDITED
}
