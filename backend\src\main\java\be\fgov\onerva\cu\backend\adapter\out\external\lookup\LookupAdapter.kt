package be.fgov.onerva.cu.backend.adapter.out.external.lookup

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.port.out.LookupPort
import be.fgov.onerva.cu.backend.lookup.LookupService

@Service
class LookupAdapter(val lookupService: LookupService) : LookupPort {
    /**
     * Retrieves the set of valid nationality/country codes from the lookup service.
     *
     * @return Set of valid nationality/country codes
     */
    private fun getValidNationalityCodes(searchQuery: String?): Set<String> =
        lookupService.lookupNationality(searchQuery).map { it.code }.toSet()

    override fun validateCountryOrNationality(code: Int): Boolean =
        code.toString() in getValidNationalityCodes(code.toString())
}