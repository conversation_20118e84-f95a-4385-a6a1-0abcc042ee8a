package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RequestAdapterTest {

    @MockK
    lateinit var requestRepository: RequestRepository

    @InjectMockKs
    lateinit var requestAdapter: RequestAdapter

    @Test
    fun `getRequest returns domain request when entity exists`() {
        // Given
        val requestId = UUID.randomUUID()
        val entity = createRequestEntity()

        every { requestRepository.findByIdOrNull(requestId) } returns entity

        // When
        val result = requestAdapter.getRequest(requestId)

        // Then
        assertThat(result.id).isEqualTo(entity.id)
        assertThat(result.c9id).isEqualTo(entity.c9Id)
        assertThat(result.ssin).isEqualTo(entity.ssin)
        assertThat(result.opKey).isEqualTo(entity.opKey)
        assertThat(result.sectOp).isEqualTo(entity.sectOp)
        assertThat(result.requestDate).isEqualTo(entity.requestDate)

        verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
    }

    @Test
    fun `getRequest throws exception when request not found`() {
        // Given
        val requestId = UUID.randomUUID()
        every { requestRepository.findByIdOrNull(requestId) } returns null

        // When/Then
        assertThatThrownBy { requestAdapter.getRequest(requestId) }
            .isInstanceOf(RequestIdNotFoundException::class.java)
            .hasMessage("Request ID not found: $requestId")

        verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
    }

    private fun createRequestEntity() = object : RequestEntity(
        c9Id = 12345L,
        c9Type = "400",
        opKey = "OP123",
        sectOp = "SO123",
        requestDate = LocalDate.of(2024, 1, 1),
        ssin = "12345678901",
        ec1Id = null,
        entityCode = null,
        numbox = null,
        scanUrl = null,
        dateValid = null,
        introductionDate = null,
        unemploymentOffice = null,
        paymentInstitution = null,
        scanNumber = null,
        operatorCode = null,
        dueDate = null,
        introductionType = null,
    ) {}
}