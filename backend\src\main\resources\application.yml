spring:
  application:
    name: cu-backend
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    open-in-view: false
    hibernate:
      ddl-auto: validate
  #    show-sql: true      # Logs SQL statements to console
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}
      client:
        registration:
          keycloak:
            client-id: cu-backend
            client-secret:
            scope: openid
            authorization-grant-type: client_credentials
        provider:
          keycloak:
            issuer-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}
  rabbitmq:
    virtual-host: onemrva
  flyway:
    enabled: true
    locations: classpath:db/migration/common
    baseline-version: 1.0
    baseline-on-migrate: true
    validate-on-migrate: true

onerva:
  observability:
    prometheus:
      domain: 'UNEMP'
      system: 'cu'
      component: 'backend'

management:
  endpoints:
    web:
      exposure:
        include: '*'
        exclude: 'heapdump'

rabbitmq:
  enabled: true
  oauth:
    token-endpoint-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/token
    client-id: ${spring.security.oauth2.client.registration.keycloak.client-id}
    client-secret: ${spring.security.oauth2.client.registration.keycloak.client-secret}

app:
  allowMultipleC9s: false
  jobs:
    lookup:
      cron: 0 0 * * * *
