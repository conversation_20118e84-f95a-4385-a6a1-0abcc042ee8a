package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.time.LocalDate
import java.util.UUID
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.data.jpa.mapping.JpaMetamodelMappingContext
import org.springframework.http.MediaType
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.port.`in`.CitizenInformationUseCase
import be.fgov.onerva.cu.backend.config.SecurityConfig
import be.fgov.onerva.cu.backend.security.OnemRvaAuthenticationConverter
import be.fgov.onerva.cu.backend.security.Roles
import be.fgov.onerva.cu.backend.security.TestSecurityConfig
import be.fgov.onerva.cu.backend.security.WithMockedJwtToken
import be.fgov.onerva.cu.rest.priv.model.Address
import be.fgov.onerva.cu.rest.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify

@ActiveProfiles("unit")
@WebMvcTest(CitizenInformationController::class)
@Import(SecurityConfig::class, TestSecurityConfig::class, OnemRvaAuthenticationConverter::class)
@MockkBean(JpaMetamodelMappingContext::class)
class CitizenInformationControllerIT {
    @MockkBean
    private lateinit var citizenInformationUseCase: CitizenInformationUseCase

    @MockkBean
    private lateinit var jwtDecoder: JwtDecoder

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Nested
    @DisplayName("GET /api/requests/{requestId}/citizen-information")
    inner class GetCitizenInformation {

        @Test
        @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
        fun `should return 200 and employee information when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val response = CitizenInformationDetailResponse().apply {
                birthDate = LocalDate.of(1990, 1, 1)
                nationalityCode = 111
                address = Address().apply {
                    street = "Main Street"
                    houseNumber = "123"
                    boxNumber = "A"
                    zipCode = "1000"
                    city = "Brussels"
                    countryCode = 150
                }
            }
            val citizenInformation = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = be.fgov.onerva.cu.backend.application.domain.Address(
                    street = "Main Street",
                    houseNumber = "123",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    validFrom = LocalDate.of(2022, 1, 1)
                ),
            )

            every { citizenInformationUseCase.getCitizenInformation(requestId) } returns citizenInformation

            every { citizenInformationUseCase.getCitizenInformationFieldSources(requestId) } returns listOf(
                FieldSource("address", ExternalSource.C1)
            )

            // When/Then
            mockMvc.perform(
                get("/api/requests/$requestId/citizen-information").contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.nationalityCode").value(111))
                .andExpect(jsonPath("$.birthDate").value("1990-01-01"))
                .andExpect(jsonPath("$.address.street").value("Main Street"))

            verify(exactly = 1) { citizenInformationUseCase.getCitizenInformation(requestId) }
            verify(exactly = 1) { citizenInformationUseCase.getCitizenInformationFieldSources(requestId) }
        }

        @Test
        @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
        fun `should return 404 when request does not exist`() {
            // Given
            val requestId = UUID.randomUUID()
            every { citizenInformationUseCase.getCitizenInformation(requestId) } returns null

            // When/Then
            mockMvc.perform(
                get("/api/requests/$requestId/citizen-information").contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isNotFound)


            verify(exactly = 1) { citizenInformationUseCase.getCitizenInformation(requestId) }
        }


        @Test
        @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
        fun `should return 400 when validation  exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val response = CitizenInformationDetailResponse().apply {
                birthDate = LocalDate.of(1990, 1, 1)
                nationalityCode = 111
                address = Address().apply {
                    street = "Main Street"
                    houseNumber = "123"
                    boxNumber = "A"
                    zipCode = "1000"
                    city = "Brussels"
                    countryCode = 150
                }
            }
            val citizenInformation = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = be.fgov.onerva.cu.backend.application.domain.Address(
                    street = "Main Street",
                    houseNumber = "123",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    validFrom = LocalDate.of(2022, 1, 1),
                )
            )

            every { citizenInformationUseCase.getCitizenInformation(requestId) } returns citizenInformation
            every { citizenInformationUseCase.getCitizenInformationFieldSources(requestId) } returns listOf(
                FieldSource("address", ExternalSource.C1)
            )

            // When/Then
            mockMvc.perform(
                get("/api/requests/$requestId/citizen-information").contentType(MediaType.APPLICATION_JSON)
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.nationalityCode").value(111))
                .andExpect(jsonPath("$.birthDate").value("1990-01-01"))
                .andExpect(jsonPath("$.address.street").value("Main Street"))

            verify(exactly = 1) { citizenInformationUseCase.getCitizenInformation(requestId) }
        }


        @Test
        fun `should return 401 when user is not authenticated`() {
            // Given
            val requestId = UUID.randomUUID()

            // When/Then
            mockMvc.perform(
                get("/api/requests/$requestId/citizen-information").contentType(MediaType.APPLICATION_JSON)
            ).andExpect(status().isUnauthorized)
        }
    }

    @Nested
    @DisplayName("PUT /api/requests/{requestId}/citizen-information")
    inner class UpdateCitizenInformation {

        @Test
        @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
        @DisplayName("")
        fun `should return 204 when update is successful`() {
            // Given
            val requestId = UUID.randomUUID()
            val addressValidFrom = LocalDate.of(2022, 1, 1)
            val request =
                UpdateCitizenInformationRequest()
                    .birthDate(LocalDate.of(1990, 1, 1))
                    .nationalityCode(150)
                    .address(
                        Address().street("Main Street")
                            .houseNumber("123")
                            .zipCode("1000")
                            .city("Brussels")
                            .countryCode(150)
                            .validFrom(addressValidFrom)
                    )

            every {
                citizenInformationUseCase.updateCitizenInformation(any(), any())
            } returns Unit

            // When/Then
            mockMvc.perform(
                put("/api/requests/$requestId/citizen-information").contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            ).andExpect(status().isNoContent)

            verify(exactly = 1) {
                citizenInformationUseCase.updateCitizenInformation(any(), any())
            }
        }

        @Test
        @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
        fun `should return 400 when request body is invalid`() {
            // Given
            val requestId = UUID.randomUUID()
            val invalidRequest = UpdateCitizenInformationRequest()  // Missing required fields

            // When/Then
            mockMvc.perform(
                put("/api/requests/$requestId/citizen-information")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidRequest))
            )
                .andExpect(status().isBadRequest)
        }

        @Test
        @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
        fun `should return 400 when request body is invalid - null request`() {
            // Given
            val requestId = UUID.randomUUID()
            val invalidRequest = UpdateCitizenInformationRequest()  // Missing required fields

            // When/Then
            mockMvc.perform(
                put("/api/requests/$requestId/citizen-information")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(null))
            )
                .andExpect(status().isBadRequest)
        }

        @Test
        @DisplayName("should return 401 when user is not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val requestId = UUID.randomUUID()
            val addressValidFrom = LocalDate.of(2022, 1, 1)
            val request =
                UpdateCitizenInformationRequest()
                    .birthDate(LocalDate.of(1990, 1, 1))
                    .nationalityCode(150)
                    .address(
                        Address().street("Main Street")
                            .houseNumber("123")
                            .zipCode("1000")
                            .city("Brussels")
                            .countryCode(150)
                            .validFrom(addressValidFrom)
                    )

            // When/Then
            mockMvc.perform(
                put("/api/requests/$requestId/citizen-information")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
        }
    }
}