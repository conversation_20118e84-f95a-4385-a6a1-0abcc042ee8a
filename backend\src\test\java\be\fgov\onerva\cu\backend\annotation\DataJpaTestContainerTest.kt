package be.fgov.onerva.cu.backend.annotation

import java.lang.annotation.Inherited
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.TestPropertySource
import org.testcontainers.junit.jupiter.Testcontainers
import be.fgov.onerva.cu.backend.config.TestContainerConfig

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
@Testcontainers
@DataJpaTest
@TestPropertySource(
        properties = [
            "spring.jpa.hibernate.ddl-auto=validate",
            "spring.jpa.database-platform=org.hibernate.dialect.SQLServerDialect",
            "spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver",
            "spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver",
            "spring.flyway.enabled=true",
            "spring.flyway.locations=classpath:db/migration/dev,classpath:db/migration/common",
            "spring.flyway.baseline-on-migrate=true",
            "spring.flyway.baseline-version=0",
            "spring.flyway.validate-on-migrate=false"
        ]
)@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import(TestContainerConfig::class)
annotation class DataJpaTestContainerTest