import { TestBed } from '@angular/core/testing';
import {TranslateModule} from '@ngx-translate/core';
import { CuC9AnnexesComponent } from './cu-c9-annexes.component';
import { Annex } from '@rest-client/cu-bff';

describe('CuC9AnnexesComponent', () => {
  let component: CuC9AnnexesComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CuC9AnnexesComponent, TranslateModule.forRoot()], // Standalone component import
    }).compileComponents();
  });

  it('should create the component', () => {
    const fixture = TestBed.createComponent(CuC9AnnexesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    expect(component).toBeTruthy();
  });
});
