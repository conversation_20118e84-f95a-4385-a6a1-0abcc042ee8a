package be.fgov.onerva.cu.common.utils

import java.time.LocalDate

object NissUtils {
    fun getBirthDateFromSsin(ssin: String): LocalDate {
        if (ssin.length != 11 || !ssin.all { it.isDigit() }) {
            throw IllegalArgumentException("SSIN must be 11 characters long")
        }

        val baseNumber = ssin.substring(0, 9).toLong()
        val controlNumber = ssin.substring(9, 11).toInt()
        val yearFragment = ssin.substring(0, 2).toInt()
        val month = ssin.substring(2, 4).toInt()
        val day = ssin.substring(4, 6).toInt()

        val birthYear = when (controlNumber) {
            (97 - (baseNumber % 97)).toInt() -> 1900 + yearFragment
            (97 - ((2000000000 + baseNumber) % 97)).toInt() -> 2000 + yearFragment
            else -> throw IllegalArgumentException("Invalid SSIN control number")
        }

        return LocalDate.of(birthYear, month, day)
    }
}