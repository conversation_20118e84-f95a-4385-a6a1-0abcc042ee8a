spring:
  rabbitmq:
    listener:
      auto-startup: false
      simple:
        auto-startup: false
    dynamic: false
  autoconfigure:
    exclude:
      - be.fgov.onerva.observability.autoconfigure.prometheus.OnervaPrometheusPushGatewayAutoConfiguration
      - be.fgov.onerva.observability.autoconfigure.opentelemetry.OnervaOpenTelemetryAutoConfiguration
      - be.fgov.onerva.observability.autoconfigure.observation.OnervaObservationAutoConfiguration
  main:
    allow-bean-definition-overriding: true
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        enable_lazy_load_no_trans: true
  flyway:
    # Override locations to include both dev and common migrations
    # Dev migrations run first (V1_0__Initial_schema.sql), then common migrations
    locations:
      - classpath:db/migration/dev
      - classpath:db/migration/common
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://test-auth-server
      client:
        registration:
          keycloak:
            provider: keycloak
            client-id: cu-frontend
            client-secret: test-secret
            scope: [openid,profile,email]
        provider:
          keycloak:
            issuer-uri: http://test-auth-server
            authorization-uri: http://test-auth-server/auth
            token-uri: http://test-auth-server/token
            user-info-uri: http://test-auth-server/userinfo
            jwk-set-uri: http://test-auth-server/certs

keycloak:
  auth-server-url: http://test-auth-server
  realm: test-realm
  checkToken: false

onerva:
  metrics:
    enabled: false

logging:
  level:
    org.springframework.security: DEBUG


werkomgeving:
  mock: true

rabbitmq:
  enabled: false

flagsmith:
  api:
    key: XXXX
    url: https://sdfsdf
