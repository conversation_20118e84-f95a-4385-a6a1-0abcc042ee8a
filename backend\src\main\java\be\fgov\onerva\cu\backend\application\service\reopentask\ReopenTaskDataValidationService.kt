package be.fgov.onerva.cu.backend.application.service.reopentask

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.port.out.TaskApiPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort

@Service
@BusinessTransaction
class ReopenTaskDataValidationService (
    waveTaskPort: WaveTaskPort,
    waveTaskPersistencePort: WaveTaskPersistencePort,
    taskApiPort: TaskApiPort,
) : ReopenTaskAbstractService(waveTaskPort, waveTaskPersistencePort, taskApiPort) {

    private val taskType = WaveTaskPort.VALIDATION_DATA;

    override fun deleteTask(requestId: UUID) {
        val dataValidationTask = getOpenWaveTaskEntity(requestId, taskType)
        removeWaveTaskEntity(requestId, taskType)
        softDeleteTaskOnApi(dataValidationTask.taskId);
        executeWaveTaskSoftDelete(dataValidationTask.taskId)

    }

    override fun reopenTask(requestId: UUID) {
        val dataValidationTask = getWaveTaskEntity(requestId, taskType)
        deleteNextTask(requestId)
        openTask(dataValidationTask)
        executeWoAwakeTask(dataValidationTask.taskId)
    }

    override fun deleteNextTask(requestId: UUID) {

    }
}
