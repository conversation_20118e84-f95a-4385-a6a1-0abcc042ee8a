package be.fgov.onerva.cu.backend.application.service.reopentask

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.port.out.TaskApiPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.application.service.helpers.WaveTaskHelper
import be.fgov.onerva.cu.common.utils.logger

@Service
@BusinessTransaction
class ReopenTaskDataCaptureService(
    waveTaskPort: WaveTaskPort,
    waveTaskPersistencePort: WaveTaskPersistencePort,
    taskApiPort: TaskApiPort,
    private val reopenTaskDataValidationService: ReopenTaskDataValidationService,
    private val waveTaskHelper: WaveTaskHelper,
) : ReopenTaskAbstractService(waveTaskPort, waveTaskPersistencePort, taskApiPort) {
    private val log = logger

    private val taskType = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE;

    override fun deleteTask(requestId: UUID) {
        log.info("Deleting task for request ID: $requestId in ReopenTaskDataCaptureService")
        log.info("Finding request by id: $requestId")
        val dataCaptureWaveTask = getOpenWaveTaskEntity(requestId, taskType)
        log.info("Found data capture task with id: ${dataCaptureWaveTask.taskId}")
        softDeleteTaskOnApi(dataCaptureWaveTask.taskId);
        removeWaveTaskEntity(requestId, taskType)
        executeWaveTaskSoftDelete(dataCaptureWaveTask.taskId)

    }

    override fun reopenTask(requestId: UUID) {
        log.info("Reopening task for request ID: $requestId in ReopenTaskDataCaptureService")
        log.info("Finding request by id: $requestId")
        val dataCaptureWaveTask = getWaveTaskEntity(requestId, taskType)
        log.info("Found data capture task with id: ${dataCaptureWaveTask.taskId}")
        val revisionNumbers = getWaveTaskRevision(requestId, taskType)
        waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers);
        deleteNextTask(requestId)
        openTask(dataCaptureWaveTask)
        executeWoAwakeTask(dataCaptureWaveTask.taskId)
    }

    override fun deleteNextTask(requestId: UUID) {
        log.info("Removing next task from request")
        reopenTaskDataValidationService.deleteTask(requestId)
    }


}