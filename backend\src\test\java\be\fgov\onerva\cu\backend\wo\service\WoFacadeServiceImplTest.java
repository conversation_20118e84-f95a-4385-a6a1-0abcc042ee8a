package be.fgov.onerva.cu.backend.wo.service;

import be.fgov.onerva.cu.backend.wo.dto.WoMetadataDTO;
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO;
import be.fgov.onerva.cu.backend.wo.exception.WoProblemException;
import be.fgov.onerva.wo.facade.api.FacadeControllerApi;
import be.fgov.onerva.wo.facade.rest.model.CreatableTaskDTO;
import be.fgov.onerva.wo.facade.rest.model.InputMetaDataDTO;
import be.fgov.onerva.wo.facade.rest.model.StateDTO;
import be.fgov.onerva.wo.facade.rest.model.StatusDTO;
import be.fgov.onerva.wo.facade.rest.model.TaskDTO;
import be.fgov.onerva.wo.organizational.chart.api.NodeApi;
import be.fgov.onerva.wo.organizational.chart.rest.model.Node;
import be.fgov.onerva.wo_thirdparty.api.DefaultApi;
import be.fgov.onerva.wo_thirdparty.rest.model.Party;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WoFacadeServiceImplTest {
    @Mock
    private FacadeControllerApi facadeControllerApi;
    @Mock
    private DefaultApi partyApi;
    @Mock
    private NodeApi nodeApi;

    @Captor
    private ArgumentCaptor<CreatableTaskDTO> creatableTaskDTO;
    @Captor
    private ArgumentCaptor<Node> node;
    @Captor
    private ArgumentCaptor<Party> party;
    @Captor
    private ArgumentCaptor<List<InputMetaDataDTO>> listMetadataDTO;

    @InjectMocks
    private WoFacadeServiceImpl woFacadeService;

    @Test
    void createTask_mock_enabled() {
        ReflectionTestUtils.setField(woFacadeService, "mockEnabled", true);
        List<WoMetadataDTO> processMetadata = List.of(new WoMetadataDTO("process-key1", "process-value1"));
        List<WoMetadataDTO> taskMetadata = List.of(new WoMetadataDTO("task-key1", "task-value1"));
        var taskDTO = new TaskDTO(List.of(), // concernedEntities
                "test-assignee", // assignee
                "test-type", // taskTypeCode
                1234L, // processId
                5678L, // taskId
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(), // assigneeInfo
                null, // metadata
                null, // dueDate
                null, // correlation
                null, // processMetadata
                null, // permissionGroupId
                null, // businessId
                null, // creationDate
                null, // closureDate
                null, // comments
                new StatusDTO(StateDTO.OPEN, StateDTO.OPEN, null), // status
                null // history
        );

        when(facadeControllerApi.createTask(creatableTaskDTO.capture())).thenReturn(taskDTO);
        when(nodeApi.createNode(node.capture())).thenReturn(null);
        when(partyApi.createParty(party.capture(), isNull())).thenReturn(null);

        var actual =
                woFacadeService.createTask(1234L, "the-task-code", 9876, "the-assignee", null, processMetadata, taskMetadata);

        assertThat(actual).isNotNull().isEqualTo(new WoTaskDTO("1234", "5678", "OPEN", null));

        CreatableTaskDTO creatableTaskDTOValue = creatableTaskDTO.getValue();
        assertThat(creatableTaskDTOValue).isNotNull();
        assertThat(creatableTaskDTOValue.getProcessId()).isNotNull().isEqualTo(1234L);
        assertThat(creatableTaskDTOValue.getTaskTypeCode()).isEqualTo("the-task-code");
        assertThat(creatableTaskDTOValue.getAssignee()).isEqualTo("the-assignee");
        assertThat(creatableTaskDTOValue.getProcessMetadata()).hasSize(1)
                .contains(new InputMetaDataDTO("process-key1", "process-value1"));
        assertThat(creatableTaskDTOValue.getMetadata()).hasSize(1)
                .contains(new InputMetaDataDTO("task-key1", "task-value1"));

        var nodeValue = node.getValue();
        assertThat(nodeValue).isNotNull();
        assertThat(nodeValue.getNodeId()).isEqualTo("the-assignee");
        assertThat(nodeValue.getType()).isEqualTo(Node.TypeEnum.EMPLOYEE);
    }

    @Test
    void createTask_mock_enabled_node_api_throws() {
        ReflectionTestUtils.setField(woFacadeService, "mockEnabled", true);
        List<WoMetadataDTO> processMetadata = List.of(new WoMetadataDTO("process-key1", "process-value1"));
        List<WoMetadataDTO> taskMetadata = List.of(new WoMetadataDTO("task-key1", "task-value1"));

        when(nodeApi.createNode(node.capture())).thenThrow(new RuntimeException("An exception occured"));
        when(partyApi.createParty(party.capture(), isNull())).thenReturn(null);

        assertThatExceptionOfType(WoProblemException.class).isThrownBy(() -> {
            woFacadeService.createTask(1234L, "the-task-code", 9876, "the-assignee", null, processMetadata, taskMetadata);
        });
    }

    @Test
    void createTask_mock_not_enabled() {
        ReflectionTestUtils.setField(woFacadeService, "mockEnabled", false);
        List<WoMetadataDTO> processMetadata = List.of(new WoMetadataDTO("process-key1", "process-value1"));
        List<WoMetadataDTO> taskMetadata = List.of(new WoMetadataDTO("task-key1", "task-value1"));
        var taskDTO = new TaskDTO(List.of(), // concernedEntities
                "test-assignee", // assignee
                "test-type", // taskTypeCode
                1234L, // processId
                5678L, // taskId
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(), // assigneeInfo
                null, // metadata
                null, // dueDate
                null, // correlation
                null, // processMetadata
                null, // permissionGroupId
                null, // businessId
                null, // creationDate
                null, // closureDate
                null, // comments
                new StatusDTO(StateDTO.OPEN, StateDTO.OPEN, null), // status
                null // history
        );

        when(facadeControllerApi.createTask(creatableTaskDTO.capture())).thenReturn(taskDTO);

        var actual =
                woFacadeService.createTask(1234L, "the-task-code", 9876, "the-assignee", null, processMetadata, taskMetadata);

        assertThat(actual).isNotNull().isEqualTo(new WoTaskDTO("1234", "5678", "OPEN", null));

        verifyNoInteractions(nodeApi, partyApi);
    }

    @Test
    void assignTaskToUser_mock_enabled() {
        ReflectionTestUtils.setField(woFacadeService, "mockEnabled", true);

        when(facadeControllerApi.assignTask(eq(5678L), eq("the-user"), isNull())).thenReturn(new TaskDTO(List.of(),
                // concernedEntities
                "assignee",
                // assignee
                "type",
                // taskTypeCode
                123L,
                // processId
                5678L,
                // taskId
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                // assigneeInfo
                null,
                // metadata
                null,
                // dueDate
                null,
                // correlation
                null,
                // processMetadata
                null,
                // permissionGroupId
                null,
                // businessId
                null,
                // creationDate
                null,
                // closureDate
                null,
                // comments
                null,
                // status
                null
                // history
        ));
        when(nodeApi.createNode(node.capture())).thenReturn(null);

        woFacadeService.assignTaskToUser(5678L, "the-user");

        var nodeValue = node.getValue();
        assertThat(nodeValue).isNotNull();
        assertThat(nodeValue.getNodeId()).isEqualTo("the-user");
        assertThat(nodeValue.getType()).isEqualTo(Node.TypeEnum.EMPLOYEE);

        verify(facadeControllerApi, times(1)).assignTask(5678L, "the-user", null);
        verifyNoInteractions(partyApi);
    }

    @Test
    void assignTaskToUser_mock_not_enabled() {
        ReflectionTestUtils.setField(woFacadeService, "mockEnabled", false);

        when(facadeControllerApi.assignTask(eq(5678L), eq("the-user"), isNull())).thenReturn(new TaskDTO(List.of(),
                // concernedEntities
                "assignee",
                // assignee
                "type",
                // taskTypeCode
                123L,
                // processId
                5678L,
                // taskId
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                // assigneeInfo
                null,
                // metadata
                null,
                // dueDate
                null,
                // correlation
                null,
                // processMetadata
                null,
                // permissionGroupId
                null,
                // businessId
                null,
                // creationDate
                null,
                // closureDate
                null,
                // comments
                null,
                // status
                null
                // history
        ));

        woFacadeService.assignTaskToUser(5678L, "the-user");

        verify(facadeControllerApi, times(1)).assignTask(5678L, "the-user", null);
        verifyNoInteractions(nodeApi, partyApi);
    }

    @Test
    void updateProcessData() {
        List<WoMetadataDTO> processMetadata = List.of(new WoMetadataDTO("process-key1", "process-value1"));
        doNothing().when(facadeControllerApi).updateProcess(eq(1234L), listMetadataDTO.capture());

        woFacadeService.updateProcessData(1234L, processMetadata);

        var listMetadataDTOValue = listMetadataDTO.getValue();
        assertThat(listMetadataDTOValue).isNotNull()
                .hasSize(1)
                .contains(new InputMetaDataDTO("process-key1", "process-value1"));
    }

    @Test
    void closeTaskAndProcess() {
        when(facadeControllerApi.closeTask(eq(5678L), any())).thenReturn(new TaskDTO(List.of(), // concernedEntities
                "assignee", // assignee
                "type", // taskTypeCode
                123L, // processId
                5678L, // taskId
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(), // assigneeInfo
                null, // metadata
                null, // dueDate
                null, // correlation
                null, // processMetadata
                null, // permissionGroupId
                null, // businessId
                null, // creationDate
                null, // closureDate
                null, // comments
                null, // status
                null // history
        ));

        woFacadeService.closeTaskAndProcess(5678L);

        verify(facadeControllerApi, times(1)).closeTask(eq(5678L), any());
    }

    @Test
    void checkTaskCanBeClosed_closed() {
        var task = new TaskDTO(List.of(),
                "assignee",
                "type",
                123L,
                5678L,
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                new StatusDTO(StateDTO.CLOSED, StateDTO.CLOSED, null),
                null);

        when(facadeControllerApi.getTask(5678L)).thenReturn(task);

        var actual = woFacadeService.checkTaskCanBeUpdated(5678L);

        assertThat(actual).isFalse();
    }

    @Test
    void checkTaskCanBeClosed_open() {
        var task = new TaskDTO(List.of(),
                "assignee",
                "type",
                123L,
                5678L,
                new be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                new StatusDTO(StateDTO.OPEN, StateDTO.OPEN, null),
                null);

        when(facadeControllerApi.getTask(5678L)).thenReturn(task);

        var actual = woFacadeService.checkTaskCanBeUpdated(5678L);

        assertThat(actual).isTrue();
    }

    @Test
    void checkTaskCanBeClosed_task_null() {
        when(facadeControllerApi.getTask(5678L)).thenReturn(null);

        var actual = woFacadeService.checkTaskCanBeUpdated(5678L);

        assertThat(actual).isFalse();
    }
}