package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainRoutingDecisionItem
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toRoutingDecisionItemResponse
import be.fgov.onerva.cu.backend.application.port.`in`.RoutingDecisionUseCase
import be.fgov.onerva.cu.common.aop.LogWithRequestId
import be.fgov.onerva.cu.rest.priv.api.RoutingDecisionApi
import be.fgov.onerva.cu.rest.priv.model.RoutingDecisionResponse
import be.fgov.onerva.cu.rest.priv.model.RoutingDecisionUpdateRequest

@RestController
class RoutingDecisionController(
    private val routingDecisionUseCase: RoutingDecisionUseCase,
) : RoutingDecision<PERSON>pi {

    @LogWithRequestId
    override fun getRoutingDecision(requestId: UUID?): RoutingDecisionResponse {
        val routingDecision = routingDecisionUseCase.getRoutingDecisions(requestId!!)
        return RoutingDecisionResponse().apply {
            processInWave = routingDecision.processInWave
            routingDecisions = routingDecision.routingDecisions.map { it.toRoutingDecisionItemResponse() }
        }
    }

    @LogWithRequestId
    override fun updateRoutingDecision(requestId: UUID?, routingDecisionRequest: List<RoutingDecisionUpdateRequest>?) {
        val routingDecisions = routingDecisionRequest!!.map { it.toDomainRoutingDecisionItem() }.toSet()
        routingDecisionUseCase.updateRoutingDecisions(requestId!!, routingDecisions)
    }
}