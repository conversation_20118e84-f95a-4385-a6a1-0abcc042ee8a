apiVersion: mocks.microcks.io/v1alpha1
kind: APIExamples
metadata:
  name: Bareme REST API
  version: '1.0.0'
operations:
  'GET /v2/bareme':
    Barema-18031307065:
      request:
        parameters:
          citizenId: 47000
          dateCompareOperator: LESS_OR_EQUAL
          type: ALL
          date: 2022-09-22
          dateOrderDirection: DESC
      response:
        status: '200'
        mediaType: application/json
        body:
          citizenId: 47000
          baremes:
            - type: COMPLETE_UNEMPLOYMENT
              decisionDate: 2019-11-28
              comment: CSA/HAC           -
              items:
                - date: 2019-11-28
                  code: 04/59.B11
                  type: COMPLETE_UNEMPLOYMENT
                  articles:
                    - type: INDEMNISATION
                      code": 110&2
                    - type: ADMISSIBILITY
                      code: 110
            - type: COMPLETE_UNEMPLOYMENT
              decisionDate: 2019-11-28
              comment: CSA/HAC           -
              items:
                - date: 2019-11-28
                  code: 01/59.B11
                  type: COMPLETE_UNEMPLOYMENT
                  articles:
                    - type: INDEMNISATION
                      code": 110&2
                    - type: ADMISSIBILITY
                      code: 110
            - type: COMPLETE_UNEMPLOYMENT
              decisionDate: 2005-11-23
              comment: FP 40H ==)01/12/05
              items:
                - date: 2005-11-23
                  code: 11..W....
                  type: COMPLETE_UNEMPLOYMENT
                  articles:
                    - type: INDEMNISATION
                      code: 91,1
            - type: COMPLETE_UNEMPLOYMENT
              decisionDate: 2005-05-02
              comment: null
              items:
                - date: 2005-05-02
                  code: 01..W....
                  type: COMPLETE_UNEMPLOYMENT
                  articles:
                    - type: INDEMNISATION
                      code: 110&3
                    - type: ADMISSIBILITY
                      COMPLETE_UNEMPLOYMENT: "36"

