SET IDENTITY_INSERT revinfo ON;

INSERT INTO revinfo (rev, revtstmp)
VALUES (1, 1743416750496);


-- A request with citizen information, mode of payment and union contribution (EC1)
INSERT INTO request (id, c9_id, type, op_key, sect_op, request_date, ssin, document_type, status, process_in_wave)
VALUES (N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B15', 12345, N'CHANGE_PERSONAL_DATA', N'12345', N'67890', N'2022-09-22',
        N'18031307065', 'ELECTRONIC', 'OPEN', 1);
-- A request without citizen information, mode of payment and union contribution (C1)
INSERT INTO request (id, c9_id, type, op_key, sect_op, request_date, ssin, document_type, status, process_in_wave)
VALUES (N'C5CBAA34-FAF0-4F55-A343-8920221C3105', 12346, N'CHANGE_PERSONAL_DATA', N'12345', N'67890', N'2022-09-22',
        N'18031307065', 'PAPER', 'OPEN', 1);
-- A request with citizen information, mode of payment and union contribution (EC1) - In VALIDATE
INSERT INTO request (id, c9_id, type, op_key, sect_op, request_date, ssin, document_type, status, process_in_wave)
VALUES (N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16', 12347, N'CHANGE_PERSONAL_DATA', N'12345', N'67890', N'2022-09-22',
        N'18031307065', 'PAPER', 'OPEN', 1);

-- F1DA3F30-10A5-4124-AA5E-2D4E46A09B15
INSERT INTO request_information (id, request_id, request_date, created_by, created_date, last_modified_by,
                                 last_modified_date)
VALUES (N'1EEE01E1-5D18-42FD-809F-3B073E29A1A0', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B15', N'2025-01-02', null,
        N'2025-03-31 12:25:50.433070', N'cu_user', N'2025-03-31 12:28:13.781552');

INSERT INTO citizen_information (id, request_id, birth_date, street, house_number,
                                 box_number, zip_code, city, country_code, nationality_code, update_status,
                                 created_by, last_modified_by, last_modified_date, created_date, address_valid_from)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F8', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B15',
        N'1980-01-01', N'Main Street', N'333', N'4444', N'12345', N'Springfield', 150, 111, N'EDITED', null, null,
        null, N'2024-12-26 11:41:55.191814', N'2025-09-01');

INSERT INTO mode_of_payment (id, request_id, other_person_name, iban, bic, valid_from,
                             update_status, created_by, created_date, last_modified_by, last_modified_date)
VALUES (N'D28DBEDE-7116-4E56-8298-608319CA3A76', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B15', N'Karim Benzema',
        N'********************', N'CMRPL', N'2025-01-09', N'FROM_C9', null, N'2025-01-09 17:33:02.642775', null,
        N'2025-01-09 17:33:02.642775');


INSERT INTO union_contribution (id, request_id, authorized, effective_date, update_status, created_by, created_date,
                                last_modified_by, last_modified_date)
VALUES (N'4DB0906E-EEA1-442B-B880-8A1505031951', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B15', 1, N'2022-09-22', N'FROM_C9',
        null, N'2025-01-10 07:55:31.343736', null, N'2025-01-10 07:55:31.343736');


INSERT INTO wave_task (id, request_id, process_id, task_id, created_date, citizen_information_revision_number,
                       mode_of_payment_revision_number, union_contribution_revision_number, type, status)
VALUES (N'08549D5F-0B28-41C6-8F26-EB94E2FCCD92', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B15', N'4000004', N'40012004',
        N'2025-01-09 17:33:04.193980', null, null, null, N'CHANGE_PERSONAL_DATA_CAPTURE', N'OPEN');


-- F1DA3F30-10A5-4124-AA5E-2D4E46A09B16
INSERT INTO request_information (id, request_id, request_date, created_by, created_date, last_modified_by,
                                 last_modified_date)
VALUES (N'1EEE01E1-5D18-42FD-809F-3B073E29A1A1', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16', N'2025-01-02', null,
        N'2025-03-31 12:25:50.433070', N'cu_user', N'2025-03-31 12:28:13.781552');

INSERT INTO request_information_aud (rev, id, revtype, request_date, created_by, created_date, last_modified_by,
                                     last_modified_date)
VALUES (1, N'1EEE01E1-5D18-42FD-809F-3B073E29A1AD', 0, N'2022-09-22', null, N'2025-03-31 10:25:50.608765', null,
        N'2025-03-31 12:25:50.433070');
INSERT INTO request_information_aud (rev, id, revtype, request_date, created_by, created_date, last_modified_by,
                                     last_modified_date)
VALUES (1, N'1EEE01E1-5D18-42FD-809F-3B073E29A1A1', 0, N'2025-01-02', null,
        N'2025-03-31 12:25:50.433070', N'cu_user', N'2025-03-31 12:28:13.781552');

INSERT INTO citizen_information (id, request_id, birth_date, street, house_number,
                                 box_number, zip_code, city, country_code, nationality_code, update_status,
                                 created_by, last_modified_by, last_modified_date, created_date, address_valid_from)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F0', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16',
        N'1980-01-01', N'Main Street', N'333', N'4444', N'12345', N'Springfield', 150, 111, N'EDITED', null, null,
        null, N'2024-12-26 11:41:55.191814', N'2025-09-01');

INSERT INTO citizen_information_aud (rev, id, revtype, birth_date, nationality_code, street, box_number, house_number,
                                     zip_code, city, country_code, update_status, created_by, last_modified_date,
                                     last_modified_by, first_name, last_name, address_valid_from)
VALUES (1, N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F0', 0, N'1975-06-09', 111, N'Pasteur', null, N'37', N'1000',
        N'92250', 150, N'EDITED', N'cu_user', N'2025-03-31 12:28:14.886227', N'cu_user', N'EL MUSTAPHA', N'KARIM',
        N'2025-09-01');


INSERT INTO mode_of_payment (id, request_id, other_person_name, iban, bic, valid_from,
                             update_status, created_by, created_date, last_modified_by, last_modified_date)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F1', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16', N'Karim Benzema',
        N'********************', N'CMRPL', N'2025-01-09', N'FROM_C9', null, N'2025-01-09 17:33:02.642775', null,
        N'2025-01-09 17:33:02.642775');

INSERT INTO mode_of_payment_aud (id, revtype, rev, other_person_name, iban, bic, valid_from, update_status, created_by,
                                 last_modified_by, last_modified_date)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F1', 0, 1, N'The other person name', N'****************', null,
        N'2025-03-31', N'EDITED', N'cu_user', N'cu_user', N'2025-03-31 12:28:24.688982');


INSERT INTO union_contribution (id, request_id, authorized, effective_date, update_status, created_by, created_date,
                                last_modified_by, last_modified_date)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F2', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16', 1, N'2022-09-22', N'FROM_C9',
        null, N'2025-01-10 07:55:31.343736', null, N'2025-01-10 07:55:31.343736');

INSERT INTO union_contribution_aud (rev, id, revtype, authorized, effective_date, update_status, created_by,
                                    last_modified_by, last_modified_date)
VALUES (1, N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F2', 0, 1, N'2025-01-01', N'EDITED', N'cu_user', N'cu_user',
        N'2025-03-31 12:28:25.121529');


INSERT INTO wave_task (id, request_id, process_id, task_id, created_date, citizen_information_revision_number,
                       mode_of_payment_revision_number, union_contribution_revision_number,
                       request_information_revision_number, type, status)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F3', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16', N'4000004', N'40012004',
        N'2025-01-09 17:33:04.193980', 1, 1, 1, 1, N'CHANGE_PERSONAL_DATA_CAPTURE', N'CLOSED');
INSERT INTO wave_task (id, request_id, process_id, task_id, created_date, citizen_information_revision_number,
                       mode_of_payment_revision_number, union_contribution_revision_number,
                       request_information_revision_number, type, status)
VALUES (N'231F1D46-6EC0-4DFC-AFD2-76A1C37435F4', N'F1DA3F30-10A5-4124-AA5E-2D4E46A09B16', N'4000004', N'40012004',
        N'2025-01-09 17:33:04.193980', null, null, null, null, N'CHANGE_PERSONAL_DATA_VALIDATION_DATA', N'OPEN');


SET IDENTITY_INSERT revinfo OFF;
