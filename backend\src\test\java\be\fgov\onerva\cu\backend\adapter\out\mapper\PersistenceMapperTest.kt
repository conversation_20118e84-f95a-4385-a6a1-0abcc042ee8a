package be.fgov.onerva.cu.backend.adapter.out.mapper

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.Address
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.BaremaSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotAddress
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotModeOfPayment
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotUnionContribution
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.WaveTaskEntity
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.Snapshot
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskStatusException
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk

@ExtendWith(MockKExtension::class)
class PersistenceMapperTest {
    private val effectiveDate = LocalDate.of(2024, 1, 1)

    @MockK
    private lateinit var requestEntity: be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity

    @Nested
    @DisplayName("CitizenInformationEntity Mapping")
    inner class CitizenInformationMapping {

        @Test
        fun `should map employee information entity with all fields populated`() {
            // Given
            val entity = CitizenInformationEntity(
                firstName = "John",
                lastName = "Doe",
                request = mockk(),
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = Address(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                updateStatus = UpdateStatus.FROM_C9,
            )

            // When
            val result = entity.toDomainCitizenInformation()

            // Then
            assertThat(result).matches { employeeInfo ->
                employeeInfo.birthDate == LocalDate.of(1990, 1, 1) &&
                        employeeInfo.nationalityCode == 111 &&
                        employeeInfo.address.street == "Main Street" &&
                        employeeInfo.address.houseNumber == "42" &&
                        employeeInfo.address.boxNumber == "A" &&
                        employeeInfo.address.zipCode == "1000" &&
                        employeeInfo.address.city == "Brussels" &&
                        employeeInfo.address.countryCode == 150
            }
        }

        @Test
        fun `should map employee information entity with null box number`() {
            // Given
            val entity = CitizenInformationEntity(
                request = mockk(),
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = Address(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = null,
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
                updateStatus = UpdateStatus.FROM_C9,
            )

            // When
            val result = entity.toDomainCitizenInformation()

            // Then
            assertThat(result.address.boxNumber).isNull()
            assertThat(result.address.street).isEqualTo("Main Street")
            assertThat(result.address.houseNumber).isEqualTo("42")
        }
    }

    @Nested
    @DisplayName("ModeOfPaymentEntity Mapping")
    inner class ModeOfPaymentMapping {

        @Test
        fun `should map belgian bank account`() {
            // Given
            val entity = ModeOfPaymentEntity(
                request = mockk(),
                otherPersonName = null,
                iban = "****************",
                bic = null,
                validFrom = LocalDate.of(2024, 1, 1),
                updateStatus = UpdateStatus.FROM_C9,
            )

            // When
            val result = entity.toDomainModeOfPayment()

            // Then
            assertThat(result.otherPersonName).isNull()
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isNull()
        }

        @Test
        fun `should map foreign bank account`() {
            // Given
            val entity = ModeOfPaymentEntity(
                request = mockk(),
                otherPersonName = "Jane Doe",
                iban = "***************************",
                bic = "BNPAFRPP",
                validFrom = LocalDate.of(2024, 1, 1),
                updateStatus = UpdateStatus.FROM_C9,
            )

            // When
            val result = entity.toDomainModeOfPayment()

            // Then
            assertThat(result.otherPersonName).isEqualTo("Jane Doe")
            assertThat(result.iban).isEqualTo("***************************")
            assertThat(result.bic).isEqualTo("BNPAFRPP")
        }

        @Test
        fun `should map own bank account with null account holder name`() {
            // Given
            val entity = ModeOfPaymentEntity(
                request = mockk(),
                otherPersonName = null,
                iban = "****************",
                bic = null,
                validFrom = LocalDate.of(2025, 9, 1),
                updateStatus = UpdateStatus.FROM_C9,
            )

            // When
            val result = entity.toDomainModeOfPayment()

            // Then
            assertThat(result.otherPersonName).isNull()
        }
    }

    @Nested
    @DisplayName("CitizenInformation to Entity Mapping")
    inner class CitizenInformationToEntityMapping {

        @Test
        fun `should map CitizenInformation to entity with all fields populated`() {
            // Given
            val request = mockk<ChangePersonalDataRequestEntity>()
            val citizenInformation = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = be.fgov.onerva.cu.backend.application.domain.Address(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
            )
            val updateStatus = UpdateStatus.FROM_C9

            // When
            val result = citizenInformation.toCitizenInformationEntity(request, updateStatus)

            // Then
            assertThat(result)
                .isNotNull()
                .extracting(
                    { it.request },
                    { it.birthDate },
                    { it.nationalityCode },
                    { it.updateStatus }
                )
                .containsExactly(
                    request,
                    LocalDate.of(1990, 1, 1),
                    111,
                    UpdateStatus.FROM_C9
                )

            assertThat(result.address)
                .isNotNull()
                .extracting(
                    { it.street },
                    { it.houseNumber },
                    { it.boxNumber },
                    { it.zipCode },
                    { it.city },
                    { it.countryCode }
                )
                .containsExactly(
                    "Main Street",
                    "42",
                    "A",
                    "1000",
                    "Brussels",
                    150
                )
        }

        @Test
        fun `should map CitizenInformation to entity with null optional fields`() {
            // Given
            val requestEntity = ChangePersonalDataRequestEntity(
                documentType = IdentityDocumentType.ELECTRONIC,
                c9Id = 123,
                c9Type = "400",
                opKey = "opKey",
                sectOp = "sectOp",
                requestDate = LocalDate.of(2024, 1, 1),
                ssin = "12345678901",
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 123,
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                scanUrl = "http://example.com/scan",
                operatorCode = 123,
                entityCode = "entity123",
                introductionType = null,
                dueDate = LocalDate.now().plusDays(30),
                numbox = null,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            val citizenInformation = CitizenInformation(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationalityCode = 111,
                address = be.fgov.onerva.cu.backend.application.domain.Address(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = null,
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    validFrom = LocalDate.of(2022, 1, 1),
                ),
            )
            val updateStatus = UpdateStatus.FROM_C9

            // When
            val result = citizenInformation.toCitizenInformationEntity(requestEntity, updateStatus)

            // Then
            assertThat(result)
                .isNotNull()
                .extracting(
                    { it.request },
                    { it.birthDate },
                    { it.nationalityCode },
                    { it.updateStatus }
                )
                .containsExactly(
                    requestEntity,
                    LocalDate.of(1990, 1, 1),
                    111,
                    UpdateStatus.FROM_C9
                )

            assertThat(result.address)
                .isNotNull()
                .extracting(
                    { it.street },
                    { it.houseNumber },
                    { it.boxNumber },
                    { it.zipCode },
                    { it.city },
                    { it.countryCode }
                )
                .containsExactly(
                    "Main Street",
                    "42",
                    null,
                    "1000",
                    "Brussels",
                    150
                )
        }
    }

    @Test
    fun `toWaveTask should correctly map all fields`() {
        // Given
        val processId = "process123"
        val taskId = "task456"
        val requestEntity = ChangePersonalDataRequestEntity(
            citizenInformation = null,
            modeOfPayment = null,
            unionContribution = null,
            documentType = IdentityDocumentType.ELECTRONIC,
            c9Id = 123,
            c9Type = "400",
            opKey = "opKey",
            sectOp = "sectOp",
            requestDate = LocalDate.of(2024, 1, 1),
            ssin = "12345678901",
            introductionDate = LocalDate.now(),
            unemploymentOffice = 123,
            paymentInstitution = 123,
            dateValid = LocalDate.now().plusDays(20),
            scanNumber = 12345L,
            scanUrl = "http://example.com/scan",
            operatorCode = 123,
            entityCode = "entity123",
            introductionType = null,
            dueDate = LocalDate.now().plusDays(30),
            numbox = null,
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1",
        )
        val entity = ChangePersonalDataCaptureWaveTaskEntity(
            citizenInformationRevisionNumber = null,
            modeOfPaymentRevisionNumber = null,
            unionContributionRevisionNumber = null,
            requestInformationRevisionNumber = null,
            processId = processId,
            taskId = taskId,
            status = WaveTaskStatus.OPEN,
            request = requestEntity
        )

        // When
        val result = entity.toDomainWaveTask()

        // Then
        assertThat(result)
            .isNotNull()
            .extracting("processId", "taskId")
            .containsExactly(processId, taskId)
    }

    @Test
    fun ` toRequestInformation should correctly map all fields`() {
        // Given
        val requestEntity = ChangePersonalDataRequestEntity(
            citizenInformation = null,
            modeOfPayment = null,
            unionContribution = null,
            documentType = IdentityDocumentType.ELECTRONIC,
            c9Id = 123,
            c9Type = "400",
            opKey = "opKey",
            sectOp = "sectOp",
            requestDate = LocalDate.of(2024, 1, 1),
            ssin = "12345678901",
            introductionDate = LocalDate.now(),
            unemploymentOffice = 123,
            paymentInstitution = 123,
            dateValid = LocalDate.now().plusDays(20),
            scanNumber = 12345L,
            scanUrl = "http://example.com/scan",
            operatorCode = 123,
            entityCode = "entity123",
            introductionType = null,
            dueDate = LocalDate.now().plusDays(30),
            numbox = null,
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1",
        )
        val requestInformationEntity = RequestInformationEntity(
            request = requestEntity,
            requestDate = LocalDate.of(2024, 1, 1)
        )

        // when
        val result = requestInformationEntity.toDomainRequestInformation()

        // then
        assertThat(result)
            .isNotNull()
            .extracting("requestDate")
            .isEqualTo(LocalDate.of(2024, 1, 1))
    }

    @Test
    fun `toRequestInformationEntity should correctly map all fields`() {
        // Given
        val requestEntity = ChangePersonalDataRequestEntity(
            citizenInformation = null,
            modeOfPayment = null,
            unionContribution = null,
            documentType = IdentityDocumentType.ELECTRONIC,
            c9Id = 123,
            c9Type = "400",
            opKey = "opKey",
            sectOp = "sectOp",
            requestDate = LocalDate.of(2024, 1, 1),
            ssin = "12345678901",
            introductionDate = LocalDate.now(),
            unemploymentOffice = 123,
            paymentInstitution = 123,
            dateValid = LocalDate.now().plusDays(20),
            scanNumber = 12345L,
            scanUrl = "http://example.com/scan",
            operatorCode = 123,
            entityCode = "entity123",
            introductionType = null,
            dueDate = LocalDate.now().plusDays(30),
            numbox = null,
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1",
        )
        val requestInformation = RequestInformation(
            requestDate = LocalDate.of(2024, 1, 1),
        )

        // when
        val result = requestInformation.toRequestInformationEntity(requestEntity)

        // then
        assertThat(result)
            .isNotNull()
            .extracting("requestDate")
            .isEqualTo(LocalDate.of(2024, 1, 1))
    }

    @Test
    fun `toDomainRequest should correctly map all fields from RequestEntity to Request`() {
        // Given
        val id = UUID.randomUUID()
        val c9Id = 12345L
        val opKey = "OP123"
        val sectOp = "SO123"
        val requestDate = LocalDate.of(2024, 1, 1)
        val ssin = "123456789"
        val c9Type = "400"

        val requestEntity = mockk<RequestEntity>()
        every { requestEntity.id } returns id
        every { requestEntity.c9Id } returns c9Id
        every { requestEntity.opKey } returns opKey
        every { requestEntity.c9Type } returns c9Type
        every { requestEntity.sectOp } returns sectOp
        every { requestEntity.requestDate } returns requestDate
        every { requestEntity.ssin } returns ssin
        every { requestEntity.decisionType } returns DecisionType.C2
        every { requestEntity.decisionBarema } returns "01XXX"
        every { requestEntity.numbox } returns 123
        every { requestEntity.paymentInstitution } returns 12
        every { requestEntity.introductionDate } returns LocalDate.now()
        every { requestEntity.entityCode } returns "entity123"
        every { requestEntity.scanUrl } returns "http://example.com/scan"
        every { requestEntity.unemploymentOffice } returns 21
        every { requestEntity.dateValid } returns LocalDate.now().plusDays(20)
        every { requestEntity.scanNumber } returns 12345L
        every { requestEntity.operatorCode } returns 123
        every { requestEntity.introductionType } returns null
        every { requestEntity.dueDate } returns LocalDate.now().plusDays(30)
        every { requestEntity.ec1Id } returns 1234
        every { requestEntity.ec1DisplayUrl } returns "http://ec1-display-url"

        // When
        val result = requestEntity.toDomainRequest()

        // Then
        assertThat(result).isNotNull()
        assertThat(result.id).isEqualTo(id)
        assertThat(result.c9id).isEqualTo(c9Id)
        assertThat(result.c9Type).isEqualTo(c9Type)
        assertThat(result.opKey).isEqualTo(opKey)
        assertThat(result.sectOp).isEqualTo(sectOp)
        assertThat(result.requestDate).isEqualTo(requestDate)
        assertThat(result.ssin).isEqualTo(ssin)
        assertThat(result.decisionType).isEqualTo(DecisionType.C2)
        assertThat(result.decisionBarema).isEqualTo("01XXX")
    }

    @Test
    fun `toModeOfPaymentEntity should correctly map all fields from ModeOfPayment to ModeOfPaymentEntity`() {
        // Given
        val modeOfPayment = ModeOfPayment(
            otherPersonName = "John Doe",
            iban = "****************",
            bic = "GEBABEBB",
            validFrom = LocalDate.of(2025, 1, 1)
        )

        val request = mockk<ChangePersonalDataRequestEntity>()
        val updateStatus = UpdateStatus.EDITED

        // When
        val result = modeOfPayment.toModeOfPaymentEntity(request, updateStatus)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.otherPersonName).isEqualTo("John Doe")
        assertThat(result.iban).isEqualTo("****************")
        assertThat(result.bic).isEqualTo("GEBABEBB")
        assertThat(result.validFrom).isNotNull() // Just verify it's not null without checking exact value
        assertThat(result.request).isSameAs(request)
        assertThat(result.updateStatus).isEqualTo(UpdateStatus.EDITED)
        assertThat(result.validFrom).isEqualTo(LocalDate.of(2025, 1, 1))
    }

    @Test
    fun `toModeOfPaymentEntity should handle null values correctly`() {
        // Given
        val modeOfPayment = ModeOfPayment(
            otherPersonName = null,
            iban = "****************",
            bic = null,
            validFrom = LocalDate.of(2025, 1, 1)
        )

        val request = mockk<ChangePersonalDataRequestEntity>()
        val updateStatus = UpdateStatus.FROM_C9

        // When
        val result = modeOfPayment.toModeOfPaymentEntity(request, updateStatus)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.otherPersonName).isNull()
        assertThat(result.iban).isEqualTo("****************")
        assertThat(result.bic).isNull()
        assertThat(result.validFrom).isNotNull()
        assertThat(result.request).isSameAs(request)
        assertThat(result.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
        assertThat(result.validFrom).isEqualTo(LocalDate.of(2025, 1, 1))
    }

    @Test
    fun `toChangePersonalDataCaptureWaveTaskEntity should map WaveTask to ChangePersonalDataCaptureWaveTaskEntity`() {
        // Given
        val waveTask = WaveTask(
            processId = "process-123",
            taskId = "task-456",
            status = WaveTaskStatus.CLOSED // Note: this status will be ignored in the mapping
        )

        val requestEntity = object : RequestEntity(
            c9Id = 12345L,
            c9Type = "400",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            ssin = "12345678901"
        ) {}

        // When
        val result = waveTask.toChangePersonalDataCaptureWaveTaskEntity(requestEntity)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.processId).isEqualTo("process-123")
        assertThat(result.taskId).isEqualTo("task-456")
        assertThat(result.status).isEqualTo(WaveTaskStatus.OPEN) // Always OPEN regardless of input
        assertThat(result.request).isSameAs(requestEntity)
        assertThat(result.citizenInformationRevisionNumber).isNull()
        assertThat(result.modeOfPaymentRevisionNumber).isNull()
        assertThat(result.unionContributionRevisionNumber).isNull()
    }

    @Test
    fun `toChangePersonalDataValidateWaveTaskEntity should map WaveTask to ChangePersonalDataValidateWaveTaskEntity`() {
        // Given
        val waveTask = WaveTask(
            processId = "process-789",
            taskId = "task-012",
            status = WaveTaskStatus.CLOSED // Will be overridden in mapping
        )

        val requestEntity = object : RequestEntity(
            c9Id = 12345L,
            c9Type = "400",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            ssin = "12345678901"
        ) {}

        // When
        val result = waveTask.toChangePersonalDataValidateWaveTaskEntity(requestEntity)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.processId).isEqualTo("process-789")
        assertThat(result.taskId).isEqualTo("task-012")
        assertThat(result.status).isEqualTo(WaveTaskStatus.OPEN) // Always OPEN regardless of input
        assertThat(result.request).isSameAs(requestEntity)
        assertThat(result.citizenInformationRevisionNumber).isNull()
        assertThat(result.modeOfPaymentRevisionNumber).isNull()
        assertThat(result.unionContributionRevisionNumber).isNull()
    }

    @Nested
    inner class ToDomainUnionContribution {

        @Test
        fun `toDomainUnionContribution should correctly map UnionContributionEntity to UnionContribution`() {
            // Given
            val unionContributionEntity = mockk<UnionContributionEntity>()
            every { unionContributionEntity.authorized } returns true
            every { unionContributionEntity.effectiveDate } returns effectiveDate

            // When
            val result = unionContributionEntity.toDomainUnionContribution()

            // Then
            assertThat(result).isNotNull()
            assertThat(result.authorized).isTrue()
            assertThat(result.effectiveDate).isEqualTo(effectiveDate)
        }

        @Test
        fun `toDomainUnionContribution should map unauthorized UnionContributionEntity correctly`() {
            // Given
            val unionContributionEntity = mockk<UnionContributionEntity>()
            every { unionContributionEntity.authorized } returns false
            every { unionContributionEntity.effectiveDate } returns effectiveDate

            // When
            val result = unionContributionEntity.toDomainUnionContribution()

            // Then
            assertThat(result).isNotNull()
            assertThat(result.authorized).isFalse()
            assertThat(result.effectiveDate).isEqualTo(effectiveDate)
        }
    }

    @Nested
    inner class ToUnionContributionEntity {

        @Test
        fun `toUnionContributionEntity should correctly map UnionContribution to UnionContributionEntity`() {
            // Given
            val unionContribution = UnionContribution(
                authorized = true,
                effectiveDate = effectiveDate,
            )

            val requestEntity = mockk<ChangePersonalDataRequestEntity>()
            val updateStatus = UpdateStatus.EDITED

            // When
            val result = unionContribution.toUnionContributionEntity(requestEntity, updateStatus)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.authorized).isTrue()
            assertThat(result.effectiveDate).isEqualTo(effectiveDate)
            assertThat(result.request).isSameAs(requestEntity)
            assertThat(result.updateStatus).isEqualTo(UpdateStatus.EDITED)
        }

        @Test
        fun `toUnionContributionEntity should handle FROM_C9 update status correctly`() {
            // Given
            val unionContribution = UnionContribution(
                authorized = false,
                effectiveDate = effectiveDate,
            )

            val requestEntity = mockk<ChangePersonalDataRequestEntity>()
            val updateStatus = UpdateStatus.FROM_C9

            // When
            val result = unionContribution.toUnionContributionEntity(requestEntity, updateStatus)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.authorized).isFalse()
            assertThat(result.effectiveDate).isEqualTo(effectiveDate)
            assertThat(result.request).isSameAs(requestEntity)
            assertThat(result.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
        }
    }

    @Test
    fun `toDomainChangePersonalData should map ChangePersonalDataRequestEntity to ChangePersonalDataRequest with all fields`() {
        // Given
        val c9Id = 12345L
        val requestDate = LocalDate.of(2024, 1, 1)
        val ssin = "123456789"
        val documentType = IdentityDocumentType.ELECTRONIC

        val requestEntity = ChangePersonalDataRequestEntity(
            c9Id = c9Id,
            c9Type = "400",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = requestDate,
            ssin = ssin,
            documentType = documentType,
            citizenInformation = null,
            modeOfPayment = null,
            unionContribution = null,
            introductionDate = LocalDate.now(),
            unemploymentOffice = 123,
            paymentInstitution = 123,
            dateValid = LocalDate.now().plusDays(20),
            scanNumber = 12345L,
            scanUrl = "http://example.com/scan",
            operatorCode = 123,
            entityCode = "entity123",
            introductionType = null,
            dueDate = LocalDate.now().plusDays(30),
            numbox = 123,
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1",
        )

        val address = Address(
            street = "Main Street",
            houseNumber = "42",
            boxNumber = "A",
            zipCode = "1000",
            city = "Brussels",
            countryCode = 150,
            validFrom = LocalDate.of(2022, 1, 1),
        )

        val citizenInformationEntity = CitizenInformationEntity(
            request = requestEntity,
            birthDate = LocalDate.of(1990, 1, 1),
            firstName = "John",
            lastName = "Doe",
            updateStatus = UpdateStatus.FROM_C9,
            nationalityCode = 111,
            address = address,
        )

        val modeOfPaymentEntity = ModeOfPaymentEntity(
            request = requestEntity, // Will be set by the constructor
            otherPersonName = null,
            iban = "****************",
            bic = null,
            validFrom = LocalDate.of(2025, 9, 1),
            updateStatus = UpdateStatus.FROM_C9,
        )

        val unionContributionEntity = UnionContributionEntity(
            request = requestEntity, // Will be set by the constructor
            authorized = true,
            effectiveDate = LocalDate.of(2024, 1, 1),
            updateStatus = UpdateStatus.FROM_C9,
        )

        requestEntity.citizenInformation = citizenInformationEntity
        requestEntity.modeOfPayment = modeOfPaymentEntity
        requestEntity.unionContribution = unionContributionEntity

        // When
        val result = requestEntity.toDomainChangePersonalData()

        // Then
        assertThat(result).isNotNull()
        assertThat(result.id).isNotNull()
        assertThat(result.c9id).isEqualTo(c9Id)
        assertThat(result.requestDate).isEqualTo(requestDate)
        assertThat(result.ssin).isEqualTo(ssin)
        assertThat(result.documentType).isEqualTo(documentType)

        // Verify citizenInformation was mapped correctly
        assertThat(result.citizenInformation).isNotNull()
        assertThat(result.citizenInformation?.birthDate).isEqualTo(LocalDate.of(1990, 1, 1))
        assertThat(result.citizenInformation?.nationalityCode).isEqualTo(111)
        assertThat(result.citizenInformation?.address?.street).isEqualTo("Main Street")
        assertThat(result.citizenInformation?.address?.houseNumber).isEqualTo("42")

        // Verify modeOfPayment was mapped correctly
        assertThat(result.modeOfPayment).isNotNull()
        assertThat(result.modeOfPayment?.iban).isEqualTo("****************")

        // Verify unionContribution was mapped correctly
        assertThat(result.unionContribution).isNotNull()
        assertThat(result.unionContribution?.authorized).isTrue()
        assertThat(result.unionContribution?.effectiveDate).isEqualTo(LocalDate.of(2024, 1, 1))

        // Verify wave tasks are null
        assertThat(result.changePersonalDataCaptureWaveTask).isNull()
        assertThat(result.changePersonalDataValidateWaveTask).isNull()
    }

    @Test
    fun `toDomainChangePersonalData should handle null optional properties`() {
        // Given
        val id = UUID.randomUUID()
        val c9Id = 12345L
        val requestDate = LocalDate.of(2024, 1, 1)
        val ssin = "123456789"
        val documentType = IdentityDocumentType.PAPER
        val opKey = "OP123"
        val sectOp = "SO123"
        val c9Type = "400"

        val requestEntity = mockk<ChangePersonalDataRequestEntity>()
        every { requestEntity.id } returns id
        every { requestEntity.c9Id } returns c9Id
        every { requestEntity.c9Type } returns c9Type
        every { requestEntity.opKey } returns opKey
        every { requestEntity.sectOp } returns sectOp
        every { requestEntity.requestDate } returns requestDate
        every { requestEntity.ssin } returns ssin
        every { requestEntity.documentType } returns documentType
        every { requestEntity.citizenInformation } returns null
        every { requestEntity.modeOfPayment } returns null
        every { requestEntity.unionContribution } returns null
        every { requestEntity.requestInformation } returns null
        every { requestEntity.decisionType } returns null
        every { requestEntity.decisionBarema } returns null
        every { requestEntity.numbox } returns 123
        every { requestEntity.paymentInstitution } returns 12
        every { requestEntity.introductionDate } returns LocalDate.now()
        every { requestEntity.entityCode } returns "entity123"
        every { requestEntity.scanUrl } returns "http://example.com/scan"
        every { requestEntity.unemploymentOffice } returns 21
        every { requestEntity.dateValid } returns LocalDate.now().plusDays(20)
        every { requestEntity.scanNumber } returns 12345L
        every { requestEntity.operatorCode } returns 123
        every { requestEntity.introductionType } returns null
        every { requestEntity.dueDate } returns LocalDate.now().plusDays(30)
        every { requestEntity.ec1Id } returns 1234
        every { requestEntity.ec1DisplayUrl } returns null

        // When
        val result = requestEntity.toDomainChangePersonalData()

        // Then
        assertThat(result).isNotNull()
        assertThat(result.id).isEqualTo(id)
        assertThat(result.c9id).isEqualTo(c9Id)
        assertThat(result.c9Type).isEqualTo(c9Type)
        assertThat(result.opKey).isEqualTo(opKey)
        assertThat(result.sectOp).isEqualTo(sectOp)
        assertThat(result.requestDate).isEqualTo(requestDate)
        assertThat(result.ssin).isEqualTo(ssin)
        assertThat(result.documentType).isEqualTo(documentType)
        assertThat(result.citizenInformation).isNull()
        assertThat(result.modeOfPayment).isNull()
        assertThat(result.unionContribution).isNull()
        assertThat(result.changePersonalDataCaptureWaveTask).isNull()
        assertThat(result.changePersonalDataValidateWaveTask).isNull()
        assertThat(result.decisionType).isNull()
        assertThat(result.decisionBarema).isNull()
    }

    @Test
    fun `toDomainSnapshot should correctly map CitizenInformationSnapshotEntity to Snapshot`() {
        // Given
        val entity = mockk<CitizenInformationSnapshotEntity>()
        val requestEntity = mockk<RequestEntity>()

        val snapshotAddress = SnapshotAddress(
            street = "Main Street",
            houseNumber = "42",
            boxNumber = "A",
            zipCode = "1000",
            city = "Brussels",
            countryCode = 150,
            valueDate = LocalDate.of(2022, 1, 1)
        )

        val snapshotModeOfPayment = SnapshotModeOfPayment(
            otherPersonName = null,
            ownBankAccount = true,
            foreignAccount = false,
            iban = "****************",
            bic = null,
            valueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
        )

        val snapshotUnionContribution = SnapshotUnionContribution(
            authorized = true,
            effectiveDate = null
        )

        every { entity.firstName } returns "John"
        every { entity.lastName } returns "Doe"
        every { entity.birthDate } returns LocalDate.of(1990, 1, 1)
        every { entity.numbox } returns 123
        every { entity.nationalityCode } returns 111
        every { entity.address } returns snapshotAddress
        every { entity.modeOfPayment } returns snapshotModeOfPayment
        every { entity.unionContribution } returns snapshotUnionContribution
        every { entity.readonly } returns true
        every { entity.externalSource } returns ExternalSource.ONEM
        every { entity.request } returns requestEntity

        // When
        val result = entity.toDomainSnapshot()

        // Then
        assertThat(result).isNotNull()
        assertThat(result.readonly).isTrue()

        // Verify value
        val citizenInfo = result.value
        assertThat(citizenInfo.firstName).isEqualTo("John")
        assertThat(citizenInfo.lastName).isEqualTo("Doe")
        assertThat(citizenInfo.numbox).isEqualTo(123)
        assertThat(citizenInfo.nationalityCode).isEqualTo(111)
        assertThat(citizenInfo.iban).isEqualTo("****************")

        // Verify address
        assertThat(citizenInfo.address).isNotNull()
        assertThat(citizenInfo.address.street).isEqualTo("Main Street")
        assertThat(citizenInfo.address.houseNumber).isEqualTo("42")
        assertThat(citizenInfo.address.boxNumber).isEqualTo("A")
        assertThat(citizenInfo.address.zipCode).isEqualTo("1000")
        assertThat(citizenInfo.address.city).isEqualTo("Brussels")
        assertThat(citizenInfo.address.countryCode).isEqualTo(150)
    }

    @Test
    fun `toDomainSnapshot should handle null values correctly`() {
        // Given
        val entity = mockk<CitizenInformationSnapshotEntity>()
        val requestEntity = mockk<RequestEntity>()

        val snapshotAddress = SnapshotAddress(
            street = "Main Street",
            houseNumber = "42",
            boxNumber = null,
            zipCode = "1000",
            city = null,
            countryCode = null,
            valueDate = null,
        )

        val snapshotModeOfPayment = SnapshotModeOfPayment(
            otherPersonName = null,
            ownBankAccount = false,
            foreignAccount = false,
            iban = null,
            bic = null,
            valueDate = null,
            paymentMode = 1,
        )

        val snapshotUnionContribution = SnapshotUnionContribution(
            authorized = true,
            effectiveDate = null
        )

        every { entity.firstName } returns "Jane"
        every { entity.lastName } returns "Smith"
        every { entity.birthDate } returns LocalDate.of(1990, 1, 1)
        every { entity.numbox } returns 456
        every { entity.nationalityCode } returns 111
        every { entity.address } returns snapshotAddress
        every { entity.modeOfPayment } returns snapshotModeOfPayment
        every { entity.unionContribution } returns snapshotUnionContribution
        every { entity.readonly } returns false
        every { entity.externalSource } returns ExternalSource.AUTHENTIC_SOURCES
        every { entity.request } returns requestEntity

        // When
        val result = entity.toDomainSnapshot()

        // Then
        assertThat(result).isNotNull()
        assertThat(result.readonly).isFalse()

        // Verify value
        val citizenInfo = result.value
        assertThat(citizenInfo.firstName).isEqualTo("Jane")
        assertThat(citizenInfo.lastName).isEqualTo("Smith")
        assertThat(citizenInfo.numbox).isEqualTo(456)
        assertThat(citizenInfo.nationalityCode).isEqualTo(111)
        assertThat(citizenInfo.iban).isNull()

        // Verify address
        assertThat(citizenInfo.address).isNotNull()
        assertThat(citizenInfo.address.street).isEqualTo("Main Street")
        assertThat(citizenInfo.address.houseNumber).isEqualTo("42")
        assertThat(citizenInfo.address.boxNumber).isNull()
        assertThat(citizenInfo.address.zipCode).isEqualTo("1000")
        assertThat(citizenInfo.address.city).isNull()
        assertThat(citizenInfo.address.countryCode).isNull()
    }

    @Nested
    @DisplayName("WaveTaskEntity Mapping")
    inner class WaveTaskEntityMapping {

        @Test
        fun `toWaveTaskRevisionNumbers should map ChangePersonalDataCaptureWaveTaskEntity correctly`() {
            // Given
            val requestEntity = mockk<RequestEntity>()
            val entity = ChangePersonalDataCaptureWaveTaskEntity(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
                processId = "process123",
                taskId = "task456",
                status = WaveTaskStatus.OPEN,
                request = requestEntity
            )

            // When
            val result = entity.toWaveTaskRevisionNumbers()

            // Then
            assertThat(result.citizenInformationRevisionNumber).isEqualTo(1)
            assertThat(result.modeOfPaymentRevisionNumber).isEqualTo(2)
            assertThat(result.unionContributionRevisionNumber).isEqualTo(3)
            assertThat(result.requestInformationRevisionNumber).isEqualTo(4)
        }

        @Test
        fun `toWaveTaskRevisionNumbers should map ChangePersonalDataValidateWaveTaskEntity correctly`() {
            // Given
            val requestEntity = mockk<RequestEntity>()
            val entity = ChangePersonalDataValidateWaveTaskEntity(
                citizenInformationRevisionNumber = 5,
                modeOfPaymentRevisionNumber = 6,
                unionContributionRevisionNumber = 7,
                requestInformationRevisionNumber = 8,
                processId = "process789",
                taskId = "task012",
                status = WaveTaskStatus.OPEN,
                request = requestEntity
            )

            // When
            val result = entity.toWaveTaskRevisionNumbers()

            // Then
            assertThat(result.citizenInformationRevisionNumber).isEqualTo(5)
            assertThat(result.modeOfPaymentRevisionNumber).isEqualTo(6)
            assertThat(result.unionContributionRevisionNumber).isEqualTo(7)
            assertThat(result.requestInformationRevisionNumber).isEqualTo(8)
        }

        @Test
        fun `toWaveTaskRevisionNumbers should throw exception for unknown entity type`() {
            // Given
            // Create a custom WaveTaskEntity that doesn't match the known types
            val unknownEntity = object : WaveTaskEntity(
                processId = "process-unknown",
                taskId = "task-unknown",
                status = WaveTaskStatus.OPEN,
                request = null
            ) {}

            // When/Then
            assertThatThrownBy { unknownEntity.toWaveTaskRevisionNumbers() }
                .isInstanceOf(WaveTaskStatusException::class.java)
                .hasMessage("Unknown wave task type: ${unknownEntity.javaClass.simpleName}")
        }
    }

    @Nested
    inner class ToDomainSnapshotTests {

        @Test
        fun `should return null when barema snapshot entity is null`() {
            // Given
            val baremaSnapshotEntity: BaremaSnapshotEntity? = null

            // When
            val result = baremaSnapshotEntity.toDomainSnapshot()

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `should return Snapshot Found with readonly true when found is true and readonly is true`() {
            // Given
            val baremaCode = "01-TEST"
            val articleCode = "ARTICLE-123"

            val baremaSnapshotEntity = mockk<BaremaSnapshotEntity>()
            every { baremaSnapshotEntity.found } returns true
            every { baremaSnapshotEntity.readonly } returns true
            every { baremaSnapshotEntity.barema } returns baremaCode
            every { baremaSnapshotEntity.article } returns articleCode
            every { baremaSnapshotEntity.request } returns requestEntity

            // When
            val result = baremaSnapshotEntity.toDomainSnapshot()

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.Found::class.java)

            val foundSnapshot = result as Snapshot.Found<Barema>
            assertThat(foundSnapshot.readonly).isTrue()
            assertThat(foundSnapshot.value).isNotNull
            assertThat(foundSnapshot.value.barema).isEqualTo(baremaCode)
            assertThat(foundSnapshot.value.article).isEqualTo(articleCode)
        }

        @Test
        fun `should return Snapshot Found with readonly false when found is true and readonly is false`() {
            // Given
            val baremaCode = "03-TEST"
            val articleCode = "ARTICLE-456"

            val baremaSnapshotEntity = mockk<BaremaSnapshotEntity>()
            every { baremaSnapshotEntity.found } returns true
            every { baremaSnapshotEntity.readonly } returns false
            every { baremaSnapshotEntity.barema } returns baremaCode
            every { baremaSnapshotEntity.article } returns articleCode
            every { baremaSnapshotEntity.request } returns requestEntity

            // When
            val result = baremaSnapshotEntity.toDomainSnapshot()

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.Found::class.java)

            val foundSnapshot = result as Snapshot.Found<Barema>
            assertThat(foundSnapshot.readonly).isFalse()
            assertThat(foundSnapshot.value).isNotNull
            assertThat(foundSnapshot.value.barema).isEqualTo(baremaCode)
            assertThat(foundSnapshot.value.article).isEqualTo(articleCode)
        }

        @Test
        fun `should return Snapshot Found with null article when barema is found but article is null`() {
            // Given
            val baremaCode = "57-TEST"

            val baremaSnapshotEntity = mockk<BaremaSnapshotEntity>()
            every { baremaSnapshotEntity.found } returns true
            every { baremaSnapshotEntity.readonly } returns true
            every { baremaSnapshotEntity.barema } returns baremaCode
            every { baremaSnapshotEntity.article } returns null
            every { baremaSnapshotEntity.request } returns requestEntity

            // When
            val result = baremaSnapshotEntity.toDomainSnapshot()

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.Found::class.java)

            val foundSnapshot = result as Snapshot.Found<Barema>
            assertThat(foundSnapshot.value).isNotNull
            assertThat(foundSnapshot.value.barema).isEqualTo(baremaCode)
            assertThat(foundSnapshot.value.article).isNull()
        }

        @Test
        fun `should throw RequestInvalidStateException when found is true but barema is null`() {
            // Given
            val baremaSnapshotEntity = mockk<BaremaSnapshotEntity>()
            every { baremaSnapshotEntity.found } returns true
            every { baremaSnapshotEntity.readonly } returns true
            every { baremaSnapshotEntity.barema } returns null
            every { baremaSnapshotEntity.article } returns "ARTICLE-123"
            every { baremaSnapshotEntity.request } returns requestEntity

            // When/Then
            assertThatThrownBy { baremaSnapshotEntity.toDomainSnapshot() }
                .isInstanceOf(RequestInvalidStateException::class.java)
                .hasMessage("Barema found but not specified")
        }

        @Test
        fun `should return Snapshot NotFound when found is false`() {
            // Given
            val baremaSnapshotEntity = mockk<BaremaSnapshotEntity>()
            every { baremaSnapshotEntity.found } returns false
            every { baremaSnapshotEntity.readonly } returns true // Not relevant when found is false
            every { baremaSnapshotEntity.barema } returns "01-TEST" // Not relevant when found is false
            every { baremaSnapshotEntity.article } returns "ARTICLE-123" // Not relevant when found is false
            every { baremaSnapshotEntity.request } returns requestEntity

            // When
            val result = baremaSnapshotEntity.toDomainSnapshot()

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.NotFound::class.java)
        }
    }
}
