import {TestBed} from "@angular/core/testing";
import {DataCaptureService} from "./data-capture.service";
import {HttpClientTestingModule, HttpTestingController} from "@angular/common/http/testing";
import {ConfigService} from "../config/config.service";
import {
    AggregatedChangePersonalDataCaptureResponse,
    UpdateAggregatedChangePersonalDataRequest,
} from "./../../rest-client/cu-bff";

const mockConfigService = {
    getEnvironmentVariable: jest.fn().mockImplementation((key: string, _throwOnError?: boolean) => {
        if (key === "apiBaseUrl") {
            return "http://api";
        }
        if (key === "bffBaseUrl") {
            return "http://bff";
        }
        return "";
    }),
};

describe("DataCaptureService", () => {
    let service: DataCaptureService;
    let httpMock: HttpTestingController;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
            ],
            providers: [
                DataCaptureService,
                {provide: ConfigService, useValue: mockConfigService},
            ],
        });

        service = TestBed.inject(DataCaptureService);
        httpMock = TestBed.inject(HttpTestingController);
    });

    afterEach(() => {
        httpMock.verify();
        jest.clearAllMocks();
    });

    it("should be created", () => {
        expect(service).toBeTruthy();
    });

    describe("initializeServices", () => {
        it("should set default headers when a token is provided", () => {
            service.initializeServices("my-test-token");
            service.getAggregatedData("ABC").subscribe();

            const req = httpMock.expectOne(
                "http://bff/api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/ABC");
            expect(req.request.headers.has("Authorization")).toBe(true);
            expect(req.request.headers.get("Authorization")).toBe("Bearer my-test-token");

            req.flush({} as AggregatedChangePersonalDataCaptureResponse);
        });

        it("should set no authorization header when no token is provided", () => {
            service.initializeServices(undefined);

            service.getAggregatedData("XYZ").subscribe();

            const req = httpMock.expectOne(
                "http://bff/api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/XYZ");
            expect(req.request.headers.has("Authorization")).toBe(false);

            req.flush({} as AggregatedChangePersonalDataCaptureResponse);
        });
    });

    describe("getAggregatedData", () => {
        it("should call the correct endpoint and return data", () => {
            const mockResponse: AggregatedChangePersonalDataCaptureResponse = {
                requestId: "REQ123",
            };

            service.getAggregatedData("REQ123").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne(
                "http://bff/api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/REQ123");
            expect(req.request.method).toBe("GET");

            req.flush(mockResponse);
        });
    });

    describe("updateRequest", () => {
        it("should send a PUT or PATCH request (depending on your swagger client) with correct payload", () => {
            const updatePayload: UpdateAggregatedChangePersonalDataRequest = {};

            service.updateRequest("REQ456", updatePayload).subscribe((res) => {
                expect(res).toEqual({success: true});
            });

            const req = httpMock.expectOne("http://bff/api/aggregate-requests/change-personal-data/REQ456");
            expect(req.request.method).toBe("PUT" /* or 'PATCH', etc. */);

            expect(req.request.body).toEqual(updatePayload);

            req.flush({success: true});
        });
    });
});
