Feature: Mode of Payment

  Background:
    Given the database is clean
    And test data for change personal data is loaded
    Given I am authenticated as "test_user" with role "cu_role_user"

  Scenario: Select source for mode of payment
    Given a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    And a citizen with SSIN "***********" exists in the system
    When I select the following sources for mode of payment:
      | fieldName       | source |
      | account         | ONEM   |
      | otherPersonName | C1     |
    Then the response status should be 204
    And the mode of payment should be updated with:
      | iban              | ****************      |
      | bic               | CMRPL                 |
      | other_person_name | The other person name |

  Scenario Outline: Update mode of payment with various inputs
    Given a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B15"
    When I update the mode of payment with:
      | iban            | <iban>            |
      | bic             | <bic>             |
      | otherPersonName | <otherPersonName> |
    Then the response status should be <status>
    And if successful, the database should be updated

    Examples:
      | description             | iban                        | bic      | otherPersonName | status |
      | Valid Belgian SEPA      | ****************            |          |                 | 204    |
      | Valid foreign account   | *************************** | SOGEFRPP |                 | 204    |
      | Valid other person      | ****************            |          | John Doe        | 204    |
      | Invalid IBAN            | BE685390075470              |          |                 | 400    |
      | Invalid BIC             | *************************** | INVALID  |                 | 400    |
      | Missing BIC for foreign | *************************** |          |                 | 400    |
      | No payment method       |                             |          |                 | 400    |