-- Drop the existing named constraint
ALTER TABLE request
    DROP CONSTRAINT CK_request_decision_type;

-- Add the constraint back with all values including the two new ones
ALTER TABLE request
    ADD CONSTRAINT CK_request_decision_type CHECK (decision_type IN (
                                                                     'C2Y',
                                                                     'C2N',
                                                                     'C2F',
                                                                     'C2P',
                                                                     'C51',
                                                                     'C9B',
                                                                     'C2',
                                                                     'C9NA',
                                                                     'C9Bis',
                                                                     'C2Prolongation'
        ));