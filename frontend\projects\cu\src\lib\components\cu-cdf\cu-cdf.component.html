<onemrva-mat-panel>
    <onemrva-mat-panel-title> {{ 'CU_DATA_CAPTURE.CDF.TITLE' | translate }}
        <button mat-button
                (click)="accordionForCDF.openAll()"
                *ngIf="!(requestPanel.expanded || employeePanel.expanded || bankPanel.expanded || syndicatePanel.expanded)"
                data-cy="expandAllButton"
                color="primary">
            {{ 'CU_CALCULATION.CU_HISTORY.BUTTON.OPEN_ALL' | translate }}
        </button>
        <button mat-button
                (click)="accordionForCDF.closeAll()"
                *ngIf="requestPanel.expanded || employeePanel.expanded || bankPanel.expanded || syndicatePanel.expanded"
                data-cy="collapseAllButton"
                color="primary">
            {{ 'CU_CALCULATION.CU_HISTORY.BUTTON.CLOSE_ALL' | translate }}
        </button>
    </onemrva-mat-panel-title>
    <onemrva-mat-panel-content>

        <div [formGroup]="cdfForm" class="cdfForm" autocomplete="off">
            <mat-accordion multi>
                <!-- Request Panel -->
                <mat-expansion-panel #requestPanel id="requestPanel">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <mat-icon class="small circle-icon">1</mat-icon>
                            {{ 'CU_DATA_CAPTURE.CDF.REQUEST.TITLE' | translate }}
                        </mat-panel-title>
                        <mat-panel-description>
                            <onemrva-mat-sticker color="success" *ngIf="isPanelValid('requestPanel')">
                                <span data-cy="validity-request-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.COMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>
                            <onemrva-mat-sticker color="error" *ngIf="!isPanelValid('requestPanel')">
                                <span data-cy="validity-request-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.INCOMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>
                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="requestDetail">
                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.REQUEST.STARTDATE' | translate }}</mat-label>
                            <input matInput [matDatepicker]="startDatePicker"
                                   onemrvaDateFormat
                                   placeholder="DD/MM/YYYY"
                                   data-cy="inputStartDate"
                                   [formControl]="startDateCtrl"/>
                            <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
                            <mat-datepicker #startDatePicker (closed)="onClosed(startDateCtrl)"></mat-datepicker>
                            <mat-error *ngIf="startDateCtrl.errors?.['isInFuture']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.DATE_IN_FUTURE' | translate }}
                            </mat-error>
                            <mat-error *ngIf="startDateCtrl.errors?.['required']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                        </mat-form-field>
                    </div>
                </mat-expansion-panel>

                <!-- Employee Panel -->
                <mat-expansion-panel #employeePanel id="employeePanel">


                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <mat-icon class="small circle-icon">{{ 2 }}</mat-icon>
                            {{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.TITLE' | translate }}
                        </mat-panel-title>
                        <mat-panel-description>

                            <onemrva-mat-sticker color="success" *ngIf="isPanelValid('employeePanel')">
                        <span
                                data-cy="validity-employee-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.COMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>

                            <onemrva-mat-sticker color="error" *ngIf="!isPanelValid('employeePanel')">
                        <span
                                data-cy="validity-employee-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.INCOMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>

                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="requestDetail">
                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.NISS' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="nissCtrl"
                                   [showMaskTyped]="cdfForm.get('niss')?.value"
                                   mask="000000/000-00"
                                   placeholder="000000/000-00"
                                   [allowNegativeNumbers]="false"
                                   data-cy="nissInput"
                            />

                            <mat-error>
                                <mat-icon>error</mat-icon>
                                <span *ngIf="nissCtrl.errors?.['checkSum']">
              {{ 'ERROR.NISS_CHECKSUM' | translate }}&nbsp;
            </span>
                                <span *ngIf="nissCtrl.errors?.['minlength']">
              {{ 'ERROR.NISS_CHECKSUM' | translate }}&nbsp;
            </span>
                                <span *ngIf="nissCtrl.errors?.['required']">
              {{ 'ERROR.REQUIRED_FIELD' | translate }}&nbsp;
            </span>
                            </mat-error>

                        </mat-form-field>

                        <br>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.BIRTHDAY' | translate }}</mat-label>
                            <input matInput [matDatepicker]="birthDatePicker"
                                   onemrvaDateFormat
                                   [formControl]="birthDateCtrl"
                                   placeholder="DD/MM/YYYY"
                                   data-cy="birthDateInput"
                            />
                            <mat-datepicker-toggle matSuffix [for]="birthDatePicker"></mat-datepicker-toggle>
                            <mat-datepicker #birthDatePicker (closed)="onClosed(birthDateCtrl)"></mat-datepicker>

                            <mat-error
                                    *ngIf="birthDateCtrl.errors && birthDateCtrl.errors['isInFuture']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.DATE_IN_FUTURE' | translate }}
                            </mat-error>
                            <mat-error
                                    *ngIf="birthDateCtrl.errors && birthDateCtrl.errors['required']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                        </mat-form-field>

                        <br>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.LASTNAME' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="lastNameCtrl"
                                   data-cy="lastNameInput"
                            />
                        </mat-form-field>


                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.FIRSTNAME' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="firstNameCtrl"
                                   data-cy="firstNameInput"
                            />
                        </mat-form-field>

                        <mat-icon class="filled" color="info" matTooltipPosition="right"
                                  matTooltip="{{ 'CU_DATA_CAPTURE.CDF.TOOLTIPS.NAME' | translate }}"
                                  aria-label="Tooltip icon">
                            info
                        </mat-icon>

                        <br>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.NATIONALITY' | translate }}</mat-label>
                            <input matInput
                                   [matAutocomplete]="autoNationality"
                                   [formControl]="nationalityCtrl"
                                   data-cy="nationalityInput"
                                   type="text"/>

                            <mat-autocomplete #autoNationality="matAutocomplete"
                                              [displayWith]="displayLookup.bind(this)">
                                <mat-option *ngIf="hasNoSearchedNationalities()" data-cy="no-results-nationalitites"
                                            disabled
                                            id="no-results-nationalitites">
                                    {{ 'CU_DATA_CAPTURE.CDF.NO_COUNTRY_FOUND' | translate : {country: nationalityCtrl.value} }}
                                </mat-option>
                                <mat-option *ngFor="let nationality of nationalitiesList" [value]="nationality">
                                    {{ nationality | lookup:language }}
                                </mat-option>
                            </mat-autocomplete>
                            <mat-error
                                    *ngIf="nationalityCtrl.errors && nationalityCtrl.errors['required']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                            <mat-error
                                    *ngIf="nationalityCtrl.errors && nationalityCtrl.errors['countryLookupNotSelected']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'CU_DATA_CAPTURE.CDF.NATIONALITY_FIELD_NOT_SELECTED' | translate }}
                            </mat-error>
                            <mat-error *ngIf="nationalityCtrl.errors?.['maxlength']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(nationalityCtrl) }}
                            </mat-error>
                        </mat-form-field>

                        <br>

                        <strong>{{ 'CU_DATA_CAPTURE.CDF.ADDRESS' | translate }}</strong>

                        <br><br>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.COUNTRY' | translate }}</mat-label>
                            <input
                                    [formControl]="countryCtrl"
                                    [matAutocomplete]="autoCountry"
                                    data-cy="countryInput"
                                    matInput
                                    type="text"
                            />

                            <mat-autocomplete #autoCountry="matAutocomplete" [displayWith]="displayLookup.bind(this)"
                                              autoActiveFirstOption
                                              id="countrySelect">

                                <mat-option *ngIf="hasNoSearchedCountries()" data-cy="no-results-countries" disabled
                                            id="no-results-countries">
                                    {{ 'CU_DATA_CAPTURE.CDF.NO_COUNTRY_FOUND' | translate : {country: countryCtrl.value} }}
                                </mat-option>
                                <mat-option *ngFor="let country of countriesList; let indexOfElement = index;"
                                            [value]="country"
                                            attr.data-cy="country-{{indexOfElement}}">
                                    {{ country | lookup:language }}
                                </mat-option>
                            </mat-autocomplete>

                            <mat-error
                                    *ngIf="countryCtrl.errors && countryCtrl.errors['required']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                            <mat-error
                                    *ngIf="countryCtrl.errors && countryCtrl.errors['countryLookupNotSelected']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'CU_DATA_CAPTURE.CDF.COUNTRY_FIELD_NOT_SELECTED' | translate }}
                            </mat-error>
                            <mat-error *ngIf="countryCtrl.errors?.['maxlength']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(countryCtrl) }}
                            </mat-error>
                        </mat-form-field>

                        <br>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.STREET' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="streetCtrl"
                                   data-cy="streetInput"
                            />
                            <mat-error
                                    *ngIf="streetCtrl.errors && streetCtrl.errors['required']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                            <mat-error *ngIf="streetCtrl.errors?.['maxlength']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(streetCtrl) }}
                            </mat-error>
                        </mat-form-field>


                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.STREET_NUMBER' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="streetNbrCtrl"
                                   data-cy="numberInput"
                            />
                            <mat-error
                                    *ngIf="streetNbrCtrl.errors && streetNbrCtrl.errors['required']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                            <mat-error *ngIf="streetNbrCtrl.errors?.['maxlength']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(streetNbrCtrl) }}
                            </mat-error>
                        </mat-form-field>


                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.STREET_BOX' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="streetBoxCtrl"
                                   data-cy="postBoxInput"
                            />
                            <mat-error *ngIf="streetBoxCtrl.errors?.['maxlength']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(streetBoxCtrl) }}
                            </mat-error>
                        </mat-form-field>

                        <br>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.POST_CODE' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="postCodeCtrl"
                                   data-cy="postalCodeInput"
                            />
                            <mat-error
                                    *ngIf="postCodeCtrl.errors && postCodeCtrl.errors['required']"
                            >
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                            <mat-error *ngIf="postCodeCtrl.errors?.['maxlength']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(postCodeCtrl) }}
                            </mat-error>
                        </mat-form-field>

                        <mat-form-field>
                            <mat-label>{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.CITY' | translate }}</mat-label>
                            <input matInput
                                   [formControl]="cityCtrl"/>
                            <mat-error *ngIf="cityCtrl.errors?.['required']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                        </mat-form-field>

                        <br>

                        <label for="addressFromDateInput">{{ 'CU_DATA_CAPTURE.CDF.EMPLOYEE.VALID_FROM' | translate }}</label>
                        <mat-form-field>
                            <input matInput [matDatepicker]="addressFromDatePicker"
                                   onemrvaDateFormat
                                   [formControl]="addressFromDateCtrl"
                                   placeholder="DD/MM/YYYY"
                                   data-cy="addressFromDateInput"
                                   id="addressFromDateInput"
                            />
                            <mat-datepicker-toggle matSuffix [for]="addressFromDatePicker"></mat-datepicker-toggle>
                            <mat-datepicker #addressFromDatePicker
                                            (closed)="onClosed(addressFromDateCtrl)"></mat-datepicker>
                            <mat-error *ngIf="addressFromDateCtrl.errors?.['required']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                        </mat-form-field>

                    </div>

                </mat-expansion-panel>

                <!-- Bank Panel -->
                <mat-expansion-panel #bankPanel id="bankPanel">

                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <mat-icon class="small circle-icon">{{ 3 }}</mat-icon>
                            {{ 'CU_DATA_CAPTURE.CDF.BANK.TITLE' | translate }}
                        </mat-panel-title>
                        <mat-panel-description>

                            <onemrva-mat-sticker color="success" *ngIf="isPanelValid('bankPanel')">
                            <span
                                    data-cy="validity-bank-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.COMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>

                            <onemrva-mat-sticker color="error" *ngIf="!isPanelValid('bankPanel')">
                        <span
                                data-cy="validity-bank-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.INCOMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>

                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="requestDetail">

                        <mat-label>{{ 'CU_DATA_CAPTURE.CDF.BANK.IN_NAME_OFF' | translate }}</mat-label>
                        <br/>

                        <mat-radio-group [formControl]="isMyBankAccountCtrl"
                                         data-cy="isMyBankAccountInput"
                                         id="isMyBankAccountInput"
                        >
                            <mat-radio-button id="isMyBankAccountRadioButton" name="isMyBankAccountRadioButton"
                                              value="is_my_bank_account">{{ 'CU_DATA_CAPTURE.CDF.BANK.IN_EMPLOYEE_NAME' | translate }}
                            </mat-radio-button>
                            <br>
                            <mat-radio-button id="otherPersonNameRadioButton"
                                              name="otherPersonNameRadioButton"
                                              value="bank_account_for_other_person_name">
                                <label for="otherPersonNameInput">{{ 'CU_DATA_CAPTURE.CDF.BANK.IN_OTHER_NAME' | translate }}</label>
                                <mat-form-field>
                                    <input matInput
                                           id="otherPersonNameInput"
                                           [formControl]="otherPersonNameCtrl"
                                           data-cy="otherPersonNameInput"
                                    />

                                    <mat-error
                                            *ngIf="otherPersonNameCtrl.errors && otherPersonNameCtrl.errors['required']"
                                    >
                                        <mat-icon>error</mat-icon>
                                        {{ 'ERROR.REQUIRED_FIELD' | translate }}
                                    </mat-error>
                                    <mat-error *ngIf="cityCtrl.errors?.['maxlength']">
                                        <mat-icon>error</mat-icon>
                                        {{ 'ERROR.FIELD_LENGTH' | translate : FormUtilsService.checkLengthOfInput(otherPersonNameCtrl) }}
                                    </mat-error>
                                </mat-form-field>
                            </mat-radio-button>
                        </mat-radio-group>

                        <br>
                        <br>

                        <label for="unifiedIban">{{ 'CU_DATA_CAPTURE.CDF.BANK.SEPA_ACCOUNT' | translate }}</label>
                        <mat-form-field>
                            <onemrva-mat-input-iban
                                    id="unifiedIban"
                                    [formControl]="ibanCtrl"
                                    data-cy="ibanInput">
                            </onemrva-mat-input-iban>
                            <mat-error *ngIf="ibanCtrl.errors && ibanCtrl.errors['required']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                            <mat-error *ngIf="ibanCtrl.hasError('WrongBBANLength')">
                                {{ 'ERROR.LENGTH_ERROR' | translate }}
                            </mat-error>
                            <mat-error *ngIf="ibanCtrl.hasError('NoIBANCountry')">
                                {{ 'ERROR.NO_IBAN_COUNTRY' | translate }}
                            </mat-error>
                            <mat-error *ngIf="!ibanCtrl.hasError('WrongBBANLength') &&
                                 (ibanCtrl.hasError('WrongBBANFormat') ||
                                  ibanCtrl.hasError('WrongAccountBankBranchChecksum') ||
                                  ibanCtrl.hasError('WrongIBANChecksum'))">
                                {{ 'ERROR.INVALID_FIELD' | translate }}
                            </mat-error>
                        </mat-form-field>

                        <!-- BIC Field (only shown for non-Belgian IBANs) -->
                        <span *ngIf="!isBelgianIban">
                                <label for="bicInput">{{ 'CU_DATA_CAPTURE.CDF.BANK.FOREIGN_BIC' | translate }}</label>
                            <mat-form-field>
                                <input matInput
                                       id="bicInput"
                                       [formControl]="bicCtrl"
                                       data-cy="bicInput"/>
                                <mat-error *ngIf="bicCtrl.errors && bicCtrl.errors['required']">
                                    <mat-icon>error</mat-icon>
                                    {{ 'ERROR.REQUIRED_FIELD' | translate }}
                                </mat-error>
                                <mat-error *ngIf="bicCtrl.errors && bicCtrl.errors['invalidBic']">
                                    <mat-icon>error</mat-icon>
                                    {{ 'ERROR.INVALID_FIELD' | translate }}
                                </mat-error>
                            </mat-form-field>
                        </span>

                        <br>

                        <label for="bankFromDateInput">{{ 'CU_DATA_CAPTURE.CDF.BANK.VALID_FROM' | translate }}</label>
                        <mat-form-field>
                            <input matInput [matDatepicker]="bankFromDatePicker"
                                   onemrvaDateFormat
                                   [formControl]="bankFromDateCtrl"
                                   placeholder="DD/MM/YYYY"
                                   data-cy="bankFromDateInput"
                                   id="bankFromDateInput"
                            />
                            <mat-datepicker-toggle matSuffix [for]="bankFromDatePicker"></mat-datepicker-toggle>
                            <mat-datepicker #bankFromDatePicker
                                            (closed)="onClosed(bankFromDateCtrl)"></mat-datepicker>
                            <mat-error *ngIf="bankFromDateCtrl.errors?.['required']">
                                <mat-icon>error</mat-icon>
                                {{ 'ERROR.REQUIRED_FIELD' | translate }}
                            </mat-error>
                        </mat-form-field>

                    </div>

                </mat-expansion-panel>

                <!-- Syndicate Panel -->
                <mat-expansion-panel #syndicatePanel id="syndicatePanel">

                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <mat-icon class="small circle-icon">{{ 4 }}</mat-icon>
                            {{ 'CU_DATA_CAPTURE.CDF.SYNDICATE.TITLE' | translate }}
                        </mat-panel-title>
                        <mat-panel-description>
                            <onemrva-mat-sticker color="success" *ngIf="isPanelValid('syndicatePanel')">
                        <span
                                data-cy="validity-syndicate-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.COMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>

                            <onemrva-mat-sticker color="error" *ngIf="!isPanelValid('syndicatePanel')">
                        <span
                                data-cy="validity-syndicate-panel">{{ 'CU_DATA_CAPTURE.BUTTONS.INCOMPLETE' | translate }}</span>
                            </onemrva-mat-sticker>

                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="requestDetail">

                        <mat-radio-group
                                [formControl]="syndicateContributionCtrl"
                                data-cy="syndicateContributionInput">
                            <mat-radio-button data-cy="radio-button-take-syndicate-contribution"
                                              value="CONTRIBUTION">{{ 'CU_DATA_CAPTURE.CDF.SYNDICATE.CONTRIBUTION' | translate }}
                            </mat-radio-button>
                            <mat-form-field>
                                <input matInput [matDatepicker]="contributionDeductionFromTheMonthDatePicker"
                                       onemrvaDateFormat
                                       placeholder="DD/MM/YYYY"
                                       data-cy="inputContributionDeductionFromTheMonth"
                                       [formControl]="contributionDeductionFromTheMonthCtrl"/>
                                <mat-datepicker-toggle matSuffix onemrvaDateFormat
                                                       [for]="contributionDeductionFromTheMonthDatePicker"></mat-datepicker-toggle>
                                <mat-datepicker #contributionDeductionFromTheMonthDatePicker
                                                (closed)="onClosed(contributionDeductionFromTheMonthCtrl)"></mat-datepicker>
                                <mat-error
                                        *ngIf="contributionDeductionFromTheMonthCtrl.errors && contributionDeductionFromTheMonthCtrl.errors['required']"
                                >
                                    <mat-icon>error</mat-icon>
                                    {{ 'ERROR.REQUIRED_FIELD' | translate }}
                                </mat-error>
                            </mat-form-field>
                            <br>
                            <mat-radio-button data-cy="radio-button-do-not-take-syndicate-contribution"
                                              value="STOP_CONTRIBUTION">{{ 'CU_DATA_CAPTURE.CDF.SYNDICATE.STOP_CONTRIBUTION' | translate }}
                            </mat-radio-button>
                            <mat-form-field>

                                <input matInput [matDatepicker]="stopContributionDeductionFromTheMonthlDatePicker"
                                       onemrvaDateFormat
                                       placeholder="DD/MM/YYYY"
                                       [formControl]="stopContributionDeductionFromTheMonthCtrl"
                                       data-cy="stopContributionDeductionFromTheMonthInput"
                                />
                                <mat-datepicker-toggle matSuffix
                                                       [for]="stopContributionDeductionFromTheMonthlDatePicker"></mat-datepicker-toggle>
                                <mat-datepicker #stopContributionDeductionFromTheMonthlDatePicker
                                                (closed)="onClosed(stopContributionDeductionFromTheMonthCtrl)"></mat-datepicker>
                                <mat-error
                                        *ngIf="stopContributionDeductionFromTheMonthCtrl.errors && stopContributionDeductionFromTheMonthCtrl.errors['required']"
                                >
                                    <mat-icon>error</mat-icon>
                                    {{ 'ERROR.REQUIRED_FIELD' | translate }}
                                </mat-error>
                            </mat-form-field>
                            <br>
                            <mat-radio-button data-cy="radio-button-syndicate-contribution-not-authorized"
                                              value="CONTRIBUTION_NOT_AUTHORIZED">{{ 'CU_DATA_CAPTURE.CDF.SYNDICATE.CONTRIBUTION_NOT_AUTHORIZED' | translate }}
                            </mat-radio-button>
                        </mat-radio-group>

                    </div>

                </mat-expansion-panel>
            </mat-accordion>
        </div>

    </onemrva-mat-panel-content>
</onemrva-mat-panel>