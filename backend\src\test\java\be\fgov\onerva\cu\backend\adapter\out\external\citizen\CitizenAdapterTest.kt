package be.fgov.onerva.cu.backend.adapter.out.external.citizen

import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenCommand
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.lookup.LookupService
import be.fgov.onerva.person.api.CitizenApi
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoPageDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.CitizenUpdateRequestDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import be.fgov.onerva.person.rest.model.PaymentTypeDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class CitizenAdapterTest {

    @MockK
    private lateinit var citizenApi: CitizenApi

    @MockK
    private lateinit var citizenInfoApi: CitizenInfoApi

    @MockK
    private lateinit var lookupService: LookupService

    @InjectMockKs
    private lateinit var citizenAdapter: CitizenAdapter

    @Nested
    inner class GetCitizenNumbox {

        @Test
        fun `getCitizenNumbox should return numbox when citizen exists`() {
            // Given
            val ssin = "12345678901"
            val expectedNumbox = 42
            val citizenDTO = CitizenDTO(numbox = expectedNumbox)

            every { citizenApi.getByNiss(ssin) } returns citizenDTO
            // When
            val result = citizenAdapter.getCitizenNumbox(ssin)

            // Then
            assertThat(result).isEqualTo(expectedNumbox)
            verify(exactly = 1) { citizenApi.getByNiss(ssin) }
        }

        @Test
        fun `getCitizenNumbox should throw exception when citizen not found`() {
            // Given
            val ssin = "12345678901"
            val citizenDTO = CitizenDTO(numbox = null)

            every { citizenApi.getByNiss(ssin) } returns citizenDTO

            // When/Then
            assertThatThrownBy { citizenAdapter.getCitizenNumbox(ssin) }.isInstanceOf(CitizenNotFoundException::class.java)
        }

        @Nested
        inner class GetCitizenBySsin {

            @Test
            fun `getCitizenBySsin should return citizen when exists`() {
                // Given
                val ssin = "12345678901"
                val citizenDTO = CitizenDTO(
                    firstname = "John", lastname = "Doe", numbox = 42, zipCode = 1000
                )

                every { citizenApi.getByNiss(ssin) } returns citizenDTO

                // When
                val result = citizenAdapter.getCitizenBySsin(ssin)

                // Then
                assertThat(result).isNotNull().extracting("firstName", "lastName", "numbox", "zipCode")
                    .containsExactly("John", "Doe", 42, "1000")

                verify(exactly = 1) { citizenApi.getByNiss(ssin) }
            }

            @Test
            fun `getCitizenBySsin should handle null zipCode`() {
                // Given
                val ssin = "12345678901"
                val citizenDTO = CitizenDTO(
                    firstname = "John", lastname = "Doe", numbox = 42, zipCode = null
                )

                every { citizenApi.getByNiss(ssin) } returns citizenDTO

                // When
                val result = citizenAdapter.getCitizenBySsin(ssin)

                // Then
                assertThat(result).isNotNull().extracting("firstName", "lastName", "numbox", "zipCode")
                    .containsExactly("John", "Doe", 42, "")

                verify(exactly = 1) { citizenApi.getByNiss(ssin) }
            }
        }

        @Nested
        inner class GetCitizenWithAddress {

            @Test
            fun `getCitizenWithAddress should return mapped citizen info when citizen exists`() {
                // Given
                val ssin = "12345678901"
                val citizenInfoPageDTO = CitizenInfoPageDTO(
                    content = listOf(
                        CitizenInfoDTO(
                            firstName = "John",
                            lastName = "Doe",
                            numBox = BigDecimal(42),
                            flagNation = BigDecimal(150),
                            address = "Main Street 123 Box A",
                            postalCode = "1000",
                            paymentMode = 2,
                            bankAccount = BankAccountDTO(
                                iban = "****************", bic = "THEBIC", holder = "The Holder"
                            ),
                            unionDue = CitizenInfoUnionDueDTO(
                                mandateActive = false, validFrom = LocalDate.of(2022, 1, 1)
                            ),
                            addressObj = ForeignAddressDTO(
                                city = "Brussels",
                                street = "Main Street",
                                box = "Box A",
                                countryCode = 1,
                                zip = "1000",
                                number = "123",
                                validFrom = LocalDate.of(2022, 1, 1),
                            )
                        )
                    ), pageNumber = 0, pageSize = 10, totalElements = 1, isFirst = true, isLast = true
                )

                every {
                    citizenInfoApi.searchCitizenInfo(
                        listOf(ssin), null, "SUMMARY", 0, 10
                    )
                } returns citizenInfoPageDTO
                every {
                    lookupService.getNationalityCodeFromOnemCountryCode("1")
                } returns 150

                // When
                val result = citizenAdapter.getCitizenWithAddress(ssin)

                // Then
                assertThat(result).isNotNull()
                requireNotNull(result)
                assertThat(result.firstName).isEqualTo("John")
                assertThat(result.lastName).isEqualTo("Doe")
                assertThat(result.numbox).isEqualTo(42)
                assertThat(result.nationalityCode).isEqualTo(150)
                assertThat(result.iban).isEqualTo("****************")
                assertThat(result.bic).isEqualTo("THEBIC")
                assertThat(result.otherPersonName).isEqualTo("The Holder")
                assertThat(result.address).isNotNull()
                requireNotNull(result.address)
                assertThat(result.address.street).isEqualTo("Main Street")
                assertThat(result.address.houseNumber).isEqualTo("123")
                assertThat(result.address.boxNumber).isEqualTo("Box A")
                assertThat(result.address.zipCode).isEqualTo("1000")
                assertThat(result.authorized).isEqualTo(false)
                assertThat(result.effectiveDate).isEqualTo(
                    LocalDate.of(2022, 1, 1)
                )

                verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
            }

            @Test
            fun `getCitizenWithAddress should return null when citizen not found`() {
                // Given
                val ssin = "12345678901"
                val emptyPageResponse = CitizenInfoPageDTO(
                    content = emptyList(),
                    pageNumber = 0,
                    pageSize = 10,
                    totalElements = 0,
                    isFirst = true,
                    isLast = true
                )

                every {
                    citizenInfoApi.searchCitizenInfo(
                        listOf(ssin), null, "SUMMARY", 0, 10
                    )
                } returns emptyPageResponse

                // When
                val result = citizenAdapter.getCitizenWithAddress(ssin)

                // Then
                assertThat(result).isNull()
                verify(exactly = 1) { citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10) }
            }

            @Test
            fun `getCitizenWithAddress should handle null address fields`() {
                // Given
                val ssin = "12345678901"
                val citizenInfoPageDTO = CitizenInfoPageDTO(
                    content = listOf(
                        CitizenInfoDTO(
                            firstName = "John",
                            lastName = "Doe",
                            numBox = BigDecimal(42),
                            flagNation = BigDecimal(150),
                            iban = "****************",
                            address = null,
                            postalCode = null
                        )
                    ), pageNumber = 0, pageSize = 10, totalElements = 1, isFirst = true, isLast = true
                )

                every {
                    citizenInfoApi.searchCitizenInfo(
                        listOf(ssin), null, "SUMMARY", 0, 10
                    )
                } returns citizenInfoPageDTO

                // When / Then
                assertThatThrownBy {
                    citizenAdapter.getCitizenWithAddress(ssin)
                }.isInstanceOf(InvalidExternalDataException::class.java).hasMessage("Invalid address format: null")
            }

            @Test
            fun `getCitizenWithAddress should handle null content`() {
                // Given
                val ssin = "12345678901"
                val citizenInfoPageDTO = CitizenInfoPageDTO(
                    content = null, pageNumber = 0, pageSize = 10, totalElements = 0, isFirst = true, isLast = true
                )

                every {
                    citizenInfoApi.searchCitizenInfo(
                        listOf(ssin), null, "SUMMARY", 0, 10
                    )
                } returns citizenInfoPageDTO

                // When
                val result = citizenAdapter.getCitizenWithAddress(ssin)

                // Then
                assertThat(result).isNull()
            }
        }

        @Nested
        inner class UpdateCitizenInformation {

            @Test
            fun `updateCitizenInformation should call citizenApi with correct parameters`() {
                // Given
                val requestId = UUID.randomUUID()
                val ssin = "85050599890"
                val userName = "test_user"
                val street = "Main Street"
                val houseNumber = "123"
                val boxNumber = "A"
                val zipCode = "1000"
                val city = "Brussels"
                val nationality = 150
                val authorized = true
                val valueDate = LocalDate.of(2023, 5, 15)
                val addressValidFrom = LocalDate.of(2022, 1, 1)

                val updateCitizenCommand = UpdateCitizenCommand(
                    ssin = ssin,
                    userName = userName,
                    address = Address(
                        street = street,
                        houseNumber = houseNumber,
                        boxNumber = boxNumber,
                        zipCode = zipCode,
                        city = city,
                        countryCode = 150,
                        validFrom = addressValidFrom,
                    ),
                    nationalityCode = nationality,
                    correlationId = requestId.toString(),
                    requestDate = LocalDate.now().minusDays(7),
                    unionContribution = UnionContribution(
                        authorized = authorized,
                        effectiveDate = valueDate
                    ),
                    modeOfPayment = ModeOfPayment(
                        iban = "****************",
                        bic = "THEBIC",
                        validFrom = valueDate,
                        otherPersonName = null
                    ),
                    birthDate = LocalDate.of(1989, 7, 7),
                    valueDate = valueDate,
                    unemploymentOffice = 721,
                )

                val requestDtoSlot = slot<CitizenUpdateRequestDTO>()

                every {
                    lookupService.getOnemCountryCodeFromNationalityCode(150)
                } returns "250"
                every {
                    citizenApi.updateCitizen(eq(ssin), eq(userName), capture(requestDtoSlot))
                } returns Unit

                // When
                citizenAdapter.updateCitizenInformation(requestId, updateCitizenCommand)

                // Then
                verify(exactly = 1) { citizenApi.updateCitizen(eq(ssin), eq(userName), any()) }

                val capturedRequest = requestDtoSlot.captured
                assertThat(capturedRequest.address).isNotNull
                assertThat(capturedRequest.address.street).isEqualTo(street)
                assertThat(capturedRequest.address.number).isEqualTo(houseNumber)
                assertThat(capturedRequest.address.box).isEqualTo(boxNumber)
                assertThat(capturedRequest.address.zip).isEqualTo(zipCode)
                assertThat(capturedRequest.address.zip).isEqualTo("1000")
                assertThat(capturedRequest.address.city).isEqualTo(city)
                assertThat(capturedRequest.address.countryCode).isEqualTo(250) // Should be converted ONEM country code
                assertThat(capturedRequest.address.validFrom).isEqualTo(addressValidFrom)
                assertThat(capturedRequest.validFrom).isEqualTo(valueDate)
                assertThat(capturedRequest.nationalityCode).isEqualTo(150)
                assertThat(capturedRequest.validFrom).isEqualTo(valueDate)
                assertThat(capturedRequest.correlationId).isEqualTo(requestId.toString())
                assertThat(capturedRequest.bankInfo).isNotNull
                assertThat(capturedRequest.bankInfo!!.iban).isEqualTo(updateCitizenCommand.modeOfPayment.iban)
                assertThat(capturedRequest.bankInfo!!.bic).isEqualTo(updateCitizenCommand.modeOfPayment.bic)
                assertThat(capturedRequest.bankInfo!!.validFrom).isEqualTo(updateCitizenCommand.modeOfPayment.validFrom)
                assertThat(capturedRequest.bankInfo!!.paymentType).isEqualTo(PaymentTypeDTO.BANK_TRANSFER)
                assertThat(capturedRequest.birthDate).isEqualTo(LocalDate.of(1989, 7, 7))
                assertThat(capturedRequest.correlationId).isEqualTo(requestId.toString())
                assertThat(capturedRequest.unionDueInfo).isNotNull
                assertThat(capturedRequest.unionDueInfo?.unionDue).isEqualTo(authorized)
                assertThat(capturedRequest.unionDueInfo?.validFrom).isEqualTo(valueDate)
                assertThat(capturedRequest.unemploymentOffice).isEqualTo(721)
            }
        }
    }
}
