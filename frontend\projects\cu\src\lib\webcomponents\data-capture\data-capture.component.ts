import {ApplicationConfig, Component, signal, SimpleChanges, ViewEncapsulation} from "@angular/core";
import {FormGroup, ReactiveFormsModule} from "@angular/forms";
import {CommonModule, registerLocaleData} from "@angular/common";
import {HttpClient} from "@angular/common/http";
import {MatButtonModule} from "@angular/material/button";
import {MatIconModule} from "@angular/material/icon";
import {MatSnackBarModule} from "@angular/material/snack-bar";
import {TranslateLoader, TranslateModule, TranslateService} from "@ngx-translate/core";
import {DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE} from "@angular/material/core";
import {LuxonDateAdapter, MAT_LUXON_DATE_ADAPTER_OPTIONS} from "@angular/material-luxon-adapter";
import {TranslateHttpLoader} from "@ngx-translate/http-loader";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";
import {
    BASE_PATH,
    CitizenInformationDetailNullableResponse,
    Configuration,
    ModeOfPaymentDetailResponse,
    RequestBasicInfoResponse,
    UnionContributionDetailResponse,
} from "@rest-client/cu-bff";
import {ONEMRVA_MAT_NATIVE_DATE_FORMAT} from "@onemrvapublic/design-system/shared";
import {catchError, EMPTY, of, switchMap, takeUntil, tap} from "rxjs";
import {CuC9AnnexesComponent} from "../../components/cu-c9-annexes/cu-c9-annexes.component";
import {CuCdfComponent} from "../../components/cu-cdf/cu-cdf.component";
import {CuTaskStatusMessageComponent} from "../../components/cu-task-status-message/cu-task-status-message.component";
import {DataLoaderWrapperComponent} from "../../components/data-loader-wrapper/data-loader-wrapper.component";
import {environment} from "../../environments/environment";
import {DataCaptureService} from "../../http/data-capture.service";
import {GeoLookupService} from "../../http/geo-lookup.service";
import {CdfFormService} from "../../services/cdf-form.service";
import {ConfigService} from "../../config/config.service";
import localeFrBE from "@angular/common/locales/fr-BE";
import localeNlBE from "@angular/common/locales/nl-BE";
import {FormUtilsService} from "../../services/form-utils.service";
import {ToastService} from "../../services/toast.service";
import {RedirectHandlerService} from "../../http/redirect-handler.service";
import {BaseWebComponent} from "../common/base-web-component";
import {FeatureFlagsService} from "../../http/feature-flags.service";
import {S24ActionButtonComponent} from "../common/s24-action-button/s24-action-button.component";
import {MatDialog} from "@angular/material/dialog";
import {CuDialogComponent} from "../../components/cu-dialog/cu-dialog.component";

export function provideCuApi(): ApplicationConfig["providers"] {
    return [
        {
            provide: Configuration,
            useFactory: () => new Configuration({
                basePath: environment.apiBasePath,
            }),
        },
    ];
}

registerLocaleData(localeFrBE, "fr");
registerLocaleData(localeNlBE, "nl");

@Component({
    selector: "lib-data-capture",
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        OnemrvaThemeModule,
        MatButtonModule,
        MatIconModule,
        TranslateModule,
        CuCdfComponent,
        CuC9AnnexesComponent,
        MatSnackBarModule,
        CuTaskStatusMessageComponent,
        S24ActionButtonComponent,
        DataLoaderWrapperComponent,
    ],
    providers: [
        ToastService,
        FeatureFlagsService,
        {
            provide: DateAdapter,
            useClass: LuxonDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
        {
            provide: MAT_LUXON_DATE_ADAPTER_OPTIONS,
            useValue: {useUtc: true},
        },
        {
            provide: MAT_DATE_FORMATS,
            useValue: ONEMRVA_MAT_NATIVE_DATE_FORMAT,
        },
        {
            provide: BASE_PATH,
            useValue: environment.apiBasePath,
        },
        provideCuApi(),
        {
            provide: TranslateLoader,
            useFactory: (http: HttpClient, configService: ConfigService) => {
                const baseURL = configService.getEnvironmentVariable("baseUrl");
                return new TranslateHttpLoader(
                    http,
                    baseURL + `/assets/i18n/`,
                    ".json",
                );
            },
            deps: [HttpClient, ConfigService],
        },
        TranslateService,
    ],
    templateUrl: "./data-capture.component.html",
    styleUrl: "./data-capture.component.scss",
    encapsulation: ViewEncapsulation.None,
})
export class DataCaptureComponent extends BaseWebComponent {

    // Data signals
    citizenData = signal<RequestBasicInfoResponse | null>(null);
    paymentData = signal<ModeOfPaymentDetailResponse | null>(null);
    unionData = signal<UnionContributionDetailResponse | null>(null);
    citizenInformation = signal<CitizenInformationDetailNullableResponse | null>(null);
    cdfForm = signal<FormGroup>(this.cdfFormService.createEmptyForm());

    constructor(
        translate: TranslateService,
        geoLookupService: GeoLookupService,
        configService: ConfigService,
        readonly dataCaptureService: DataCaptureService,
        readonly cdfFormService: CdfFormService,
        readonly toastService: ToastService,
        readonly redirectHandlerService: RedirectHandlerService,
        readonly featureFlagsService: FeatureFlagsService,
        readonly matDialog: MatDialog
    ) {
        super(translate, geoLookupService, configService);
    }

    reopenTask() {

        const dialogRef = this.matDialog.open(CuDialogComponent, {
            data: {
                title: this.translate.instant('CU_DATA_CONSISTENCY.REOPEN_DIALOG.TITLE'),
                content: `
                  <div class="validate">${this.translate.instant('CU_DATA_CONSISTENCY.REOPEN_DIALOG.BODY_1')}
                    <ul>
                        <li>${this.translate.instant('CU_DATA_CONSISTENCY.REOPEN_DIALOG.BODY_2')}</li>
                        <li>${this.translate.instant('CU_DATA_CONSISTENCY.REOPEN_DIALOG.BODY_3')}</li> 
                    </ul>
                </div>`,
                primaryActionText: this.translate.instant('CU_DATA_CONSISTENCY.REOPEN_DIALOG.PRIMARY'),
                secondaryActionText: this.translate.instant('CU_DATA_CONSISTENCY.REOPEN_DIALOG.SECONDARY'),
                dialogType: 'warn',
                dialogSize: 'medium',
                onPrimaryAction: () => {
                    dialogRef.close(true);
                    this.dataCaptureService.reopenTask(this.requestId,"CHANGE_PERSONAL_DATA_CAPTURE").subscribe({
                        next: (response) => {
                            console.log("Task reopened successfully:", response);
                            window.location.reload();
                            //this.goToNextTask(response.waveUrl);
                        },
                        error: (error) => {
                            console.error("Error reopening task:", error);
                        },
                    });
                },
                onSecondaryAction: () => {
                    dialogRef.close(false);
                },
            },
        });

    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['task']) {
            const currentValue = changes['task'].currentValue;
            const previousValue = changes['task'].previousValue;

            console.log('Task changed from', previousValue, 'to', currentValue);
        }
    }

    isReopenTaskFeatureFlagEnabled() {
        return this.featureFlagsService.isFeatureEnabled(FeatureFlagsService.REOPEN_TASK);
    }

    protected isFormClosedOrWaiting(): boolean {
        return FormUtilsService.isClosedOrWaiting(this.status, this.task);
    }

    protected override initializeComponentServices(token: string): void {
        this.dataCaptureService.initializeServices(token);
        this.redirectHandlerService.initializeServices(token);
    }

    protected override async getWoTaskById(requestId: string): Promise<void> {
        const newForm = signal<FormGroup>(this.cdfFormService.createEmptyForm());
        this.cdfForm.set(newForm());

        this.dataCaptureService.getAggregatedData(requestId).pipe(
            tap(response => {
                if (response) {
                    this.citizenData.set(response.basicInfo || null);
                    this.citizenInformation.set(response.citizenInformation || null);
                    this.paymentData.set(response.modeOfPayment || null);
                    this.unionData.set(response.unionContribution || null);

                    if (FormUtilsService.isClosedOrWaiting(this.status, this.task)) {
                        this.cdfForm().disable();
                    }
                }
            }),
            catchError(error => {
                this.handleLoadingError(error);
                return EMPTY;
            }),
            takeUntil(this.destroy$),
        ).subscribe();
    }

    save(validate: boolean = false): void {
        this.dataCaptureService.updateRequest(
            this.requestId,
            {
                basicInfo: this.cdfFormService.extractBasicInformation(this.cdfForm()) || undefined,
                citizenInformation: this.cdfFormService.extractCitizenInformation(this.cdfForm()) || undefined,
                modeOfPayment: this.cdfFormService.extractPaymentInformation(this.cdfForm()),
                unionContribution: this.cdfFormService.extractUnionContribution(this.cdfForm()) || undefined,
            },
        ).pipe(
            switchMap(response => validate ?
                this.dataCaptureService.closeTask(this.requestId) :
                of(response),
            ),
            takeUntil(this.destroy$),
        ).subscribe({
            next: (response) => {
                if (validate) {
                    console.log("Task closed successfully:", response);
                    this.goToNextTask(response.waveUrl);
                } else {
                    this.toastService.success("TOASTS.SEND_SUCCESS");
                    this.action.emit({ messageType: 'refreshTask' });
                }
            },
            error: (error) => {
                console.error("Error closing task:", error);
            },
        });
    }

    goToNextTask(waveUrl: string | undefined) {
        let targetUrl = null;

        if (this.configService.isOnWO()) {
            if (waveUrl) {
                targetUrl = this.configService.getWoDomain() + waveUrl;
            }
        } else {
            targetUrl = `http://localhost:4300/DataValidation?language=NL&requestId=${this.requestId}`;
        }

        if (targetUrl) {
            window.open(targetUrl, "_self");
        }
    }

    sendC51() {
        this.redirectHandlerService.getC51RedirectUrl(this.requestId).subscribe({
            next: (url: string) => {
                window.open(url, "_blank");
            },
            error: (error) => {
                this.toastService.error("TOASTS.C51_ERROR");
                console.error("Error getting C51 redirect URL:", error);
            },
        });
    }

    protected readonly FormUtilsService = FormUtilsService;
}