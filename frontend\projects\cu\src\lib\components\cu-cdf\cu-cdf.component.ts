import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    DEFAULT_CURRENCY_CODE,
    Input,
    input,
    LOCALE_ID,
    OnChanges,
    OnDestroy,
    OnInit,
    QueryList,
    SimpleChanges,
    ViewChild,
    ViewChildren,
    ViewEncapsulation,
} from "@angular/core";
import {CommonModule, NgIf} from "@angular/common";
import {AbstractControl, FormControl, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE} from "@angular/material/core";
import {
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelDescription,
    MatExpansionPanelHeader,
    MatExpansionPanelTitle,
} from "@angular/material/expansion";
import {MatButton} from "@angular/material/button";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatInputModule} from "@angular/material/input";
import {MatIcon, MatIconModule} from "@angular/material/icon";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatRadioButton, MatRadioGroup} from "@angular/material/radio";
import {MatAutocompleteModule} from "@angular/material/autocomplete";
import {MatTooltipModule} from "@angular/material/tooltip";
import {TranslateModule} from "@ngx-translate/core";
import {
    OnemrvaMatPanelComponent,
    OnemrvaMatPanelContentComponent,
    OnemrvaMatPanelTitleComponent,
} from "@onemrvapublic/design-system/mat-panel";
import {OnemrvaMatStickerModule} from "@onemrvapublic/design-system/mat-sticker";
import {OnemrvaMatTooltipModule} from "@onemrvapublic/design-system/mat-tooltip";
import {
    bankAccountValidator,
    ONEMRVA_MAT_NATIVE_DATE_FORMAT,
    OnemrvaDateFormatDirective,
} from "@onemrvapublic/design-system/shared";
import {CitizenInformationDetailNullableResponse, CountryResponse, NationalityResponse} from "@rest-client/cu-bff";
import {
    ModeOfPaymentDetailResponse,
    RequestBasicInfoResponse,
    UnionContributionDetailResponse,
} from "@rest-client/cu-config";
import {NgxMaskDirective, provideNgxMask} from "ngx-mask";
import {debounceTime, forkJoin, of, Subject} from "rxjs";
import {takeUntil} from "rxjs/operators";
import {LuxonDateAdapter, LuxonDateModule} from "@angular/material-luxon-adapter";
import {LookupPipe} from "../../directives/lookup.pipe";
import {GeoLookupService} from "../../http/geo-lookup.service";
import {Lookup, PanelsIdMap} from "../../model/types";
import {FormUtilsService} from "../../services/form-utils.service";
import {OnemrvaMatInputIbanComponent} from "@onemrvapublic/design-system/mat-input-iban";
import CustomValidators from "../../validators";

@Component({
    selector: "lib-cu-cdf",
    standalone: true,
    imports: [
        CommonModule,
        MatAccordion,
        MatButton,
        MatAutocompleteModule,
        MatExpansionPanel,
        MatExpansionPanelDescription,
        MatExpansionPanelHeader,
        MatExpansionPanelTitle,
        NgIf,
        TranslateModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatDatepickerModule,
        OnemrvaMatStickerModule,
        NgxMaskDirective,
        MatIcon,
        MatRadioButton,
        MatRadioGroup,
        LuxonDateModule,
        OnemrvaMatInputIbanComponent,
        LookupPipe,
        OnemrvaMatPanelComponent,
        OnemrvaMatPanelContentComponent,
        OnemrvaMatPanelTitleComponent,
        MatTooltipModule,
        OnemrvaMatTooltipModule,
        MatIconModule,
        OnemrvaDateFormatDirective,
    ],
    providers: [
        provideNgxMask(),
        {
            provide: MAT_DATE_LOCALE,
            useValue: "fr",
        },
        {
            provide: MAT_DATE_FORMATS,
            useValue: ONEMRVA_MAT_NATIVE_DATE_FORMAT,
        },
        {
            provide: DateAdapter,
            useClass: LuxonDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
        {
            provide: LOCALE_ID,
            useValue: "fr",
        },
        {
            provide: DEFAULT_CURRENCY_CODE,
            useValue: "EURO",
        },
    ],
    templateUrl: "./cu-cdf.component.html",
    styleUrl: "./cu-cdf.component.scss",
    encapsulation: ViewEncapsulation.None,
})
export class CuCdfComponent implements OnChanges, OnInit, AfterViewInit, OnDestroy {

    @ViewChild(MatAccordion) accordionForCDF: MatAccordion = new MatAccordion();

    @ViewChildren(MatExpansionPanel) panels!: QueryList<MatExpansionPanel>;
    @Input() language!: string;
    @Input() task: any;
    @Input() taskStatus: string | undefined;

    readonly destroyed$ = new Subject<void>();
    private nationalitySearched = false;
    private countrySearched = false;
    nationalitiesList: NationalityResponse[] = [];
    countriesList: CountryResponse[] = [];
    citiesList: Lookup[] = [];

    private isInitialLoad = true;
    isBelgianIban = true;

    // Inputs
    requestBasicInfoResponse = input.required<RequestBasicInfoResponse>();
    citizenInformation = input.required<CitizenInformationDetailNullableResponse>();
    paymentData = input.required<ModeOfPaymentDetailResponse>();
    unionData = input.required<UnionContributionDetailResponse>();

    @Input() cdfForm!: FormGroup;

    panelsIdMap: PanelsIdMap = {
        "cdk-accordion-child-0": "requestPanel",
        "cdk-accordion-child-1": "employeePanel",
        "cdk-accordion-child-2": "bankPanel",
        "cdk-accordion-child-3": "syndicatePanel",
    };

    isPanelValid(panelName: string): boolean {
        const panel = this.cdfForm?.get(panelName);
        return panel ? (panel.valid || panel.disabled) : false;
    }

    openInvalidPanels() {
        if (this.panels && this.panels.length > 0) {
            this.panels.forEach(panel => {
                const panelId: string = panel.id;
                const panelName = this.panelsIdMap[panelId];
                if (panelName && !this.isPanelValid(panelName)) {
                    panel.open();
                } else {
                    panel.close();
                }
            });
        }
    }

    constructor(readonly geoLookupService: GeoLookupService,
                readonly cd: ChangeDetectorRef) {
    }

    ngOnInit() {
        this.setupSubscriptions();
    }

    ngAfterViewInit() {
        this.setupSearchEffects();
        this.cd.detectChanges();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes["language"]) {
            this.updateLookupDisplays();
        }

        const hasRelevantChanges = this.isInitialLoad ||
            changes["requestBasicInfoResponse"] ||
            changes["citizenInformation"] ||
            changes["paymentData"] ||
            changes["unionData"];

        if (!hasRelevantChanges) {
            return;
        }

        this.setupSubscriptions();
        this.setupSearchEffects();

        const currentFormValues = this.isInitialLoad ? {} : this.getCurrentFormValues();

        if (!this.allRequiredDataAvailable()) {
            this.isInitialLoad = false;
            return;
        }

        this.updateRequestPanel(currentFormValues);
        this.updateEmployeePanel(currentFormValues);
        this.updateBankPanel(currentFormValues);
        this.updateSyndicatePanel(currentFormValues);

        this.runValidators();

        this.scheduleInvalidPanelsCheck();

        this.isInitialLoad = false;
    }

    private runValidators() {
        this.destroyed$.next();

        if (FormUtilsService.isClosedOrWaiting(this.taskStatus, this.task)) {
            this.clearAllValidators();
            return;
        }

        this.updateSyndicateValidators(this.cdfForm.get("syndicatePanel"));
        this.updateIbanValidation(this.ibanCtrl.value);
        this.updateSyndicateValidation(this.syndicateContributionCtrl.value);

        this.setupValueChangeListeners();
    }

    private setupValueChangeListeners() {
        this.setupBankPanelValidation();
        this.setupSyndicatePanelValidation();
    }

    private clearAllValidators() {
        this.clearGroupValidators(this.cdfForm);
    }

    private clearGroupValidators(formGroup: FormGroup) {
        if (!formGroup) {
            return;
        }

        Object.keys(formGroup.controls).forEach(key => {
            const control = formGroup.get(key);
            if (control instanceof FormGroup) {
                this.clearGroupValidators(control);
                if (!control) {
                    return;
                }
            } else if (control instanceof FormControl) {
                control.clearValidators();
                control.updateValueAndValidity({emitEvent: false});
            }
        });

        formGroup.updateValueAndValidity({emitEvent: false});
    }

    private getCurrentFormValues(): any {
        return {
            requestPanel: this.cdfForm.get("requestPanel")?.value,
            employeePanel: this.cdfForm.get("employeePanel")?.value,
            bankPanel: this.cdfForm.get("bankPanel")?.value,
            syndicatePanel: this.cdfForm.get("syndicatePanel")?.value,
        };
    }

    private allRequiredDataAvailable(): boolean {
        return !!(this.requestBasicInfoResponse() &&
            this.citizenInformation() &&
            this.paymentData() &&
            this.unionData());
    }

    private updateRequestPanel(currentFormValues: any): void {
        this.cdfForm?.get("requestPanel.startDate")?.patchValue(
            currentFormValues?.requestPanel?.startDate ||
            this.requestBasicInfoResponse().requestDate,
        );
    }

    private updateEmployeePanel(currentFormValues: any): void {
        const employeePanel = this.cdfForm.get("employeePanel");
        if (!employeePanel) {
            return;
        }

        const nationalityLookup = this.citizenInformation().nationalityCode ?
            this.geoLookupService.searchNationalityByCode(this.citizenInformation().nationalityCode!.toString()) :
            of(null);

        const country = this.citizenInformation().address?.countryCode;
        const countryLookup = country ?
            this.geoLookupService.searchNationalityByCode(country.toString()) :
            of(null);

        forkJoin({
            nationality: nationalityLookup,
            country: countryLookup,
        }).subscribe(lookups => {
            employeePanel.patchValue({
                niss: currentFormValues?.employeePanel?.niss || this.requestBasicInfoResponse().ssin,
                birthDate: currentFormValues?.employeePanel?.birthDate ||
                    this.citizenInformation().birthDate,
                lastName: currentFormValues?.employeePanel?.lastName ||
                    this.requestBasicInfoResponse().lastName,
                firstName: currentFormValues?.employeePanel?.firstName ||
                    this.requestBasicInfoResponse().firstName,
                nationalityCode: currentFormValues?.employeePanel?.nationalityCode || lookups.nationality,
                street: currentFormValues?.employeePanel?.street ||
                    this.citizenInformation().address?.street,
                streetNbr: currentFormValues?.employeePanel?.streetNbr ||
                    this.citizenInformation().address?.houseNumber,
                streetBox: currentFormValues?.employeePanel?.streetBox ||
                    this.citizenInformation().address?.boxNumber,
                postCode: currentFormValues?.employeePanel?.postCode ||
                    this.citizenInformation().address?.zipCode,
                countryCode: currentFormValues?.employeePanel?.countryCode || lookups.country,
                city: currentFormValues?.employeePanel?.city ||
                    this.getCityLookup(this.citizenInformation().address?.city),
                addressFromDate: currentFormValues?.employeePanel?.validFromDate ||
                    this.citizenInformation().address?.validFrom,
            });
        });
    }

    private updateBankPanel(currentFormValues: any): void {
        const bankPanel = this.cdfForm.get("bankPanel");
        if (!bankPanel) {
            return;
        }

        const iban = this.paymentData().iban || "";
        this.isBelgianIban = iban.toUpperCase().startsWith("BE");
        const otherPersonName = this.paymentData().otherPersonName;
        const isMyBankAccount = otherPersonName ?
            "bank_account_for_other_person_name" :
            "is_my_bank_account";

        bankPanel.patchValue({
            otherPersonName: currentFormValues?.bankPanel?.otherPersonName ||
                otherPersonName,
            isMyBankAccount: currentFormValues?.bankPanel?.isMyBankAccount ||
                isMyBankAccount,
            iban: currentFormValues?.bankPanel?.iban || iban,
            bic: currentFormValues?.bankPanel?.bic || this.paymentData().bic,
            bankFromDate: currentFormValues?.bankPanel?.bankFromDate ||
                this.paymentData().validFrom,
        });
    }

    private updateSyndicatePanel(currentFormValues?: any): void {
        const syndicatePanel = this.cdfForm.get("syndicatePanel");
        if (!syndicatePanel) {
            return;
        }

        const contributionStatus = this.determineContributionStatus();
        const effectiveDate = this.unionData().effectiveDate;

        syndicatePanel.patchValue({
            syndicateContribution: currentFormValues?.syndicatePanel?.syndicateContribution || contributionStatus,
            contributionDeductionFromTheMonth: currentFormValues?.syndicatePanel?.contributionDeductionFromTheMonth ||
                (this.unionData().authorized === true ? effectiveDate : null),
            stopContributionDeductionFromTheMonth: currentFormValues?.syndicatePanel?.stopContributionDeductionFromTheMonth ||
                (this.unionData().authorized === false ? effectiveDate : null),
        });
    }

    private determineContributionStatus(): string {
        if (this.unionData().authorized === true) {
            return "CONTRIBUTION";
        }
        if (this.unionData().authorized === false) {
            return "STOP_CONTRIBUTION";
        }
        return "CONTRIBUTION_NOT_AUTHORIZED";
    }

    private updateSyndicateValidators(syndicatePanel: AbstractControl | null): void {
        if (!syndicatePanel) {
            return;
        }

        const currentValue = syndicatePanel.get("syndicateContribution")?.value;
        const deductionFromControl = syndicatePanel.get("contributionDeductionFromTheMonth");
        const stopDeductionControl = syndicatePanel.get("stopContributionDeductionFromTheMonth");

        deductionFromControl?.clearValidators();
        stopDeductionControl?.clearValidators();

        if (currentValue === "CONTRIBUTION") {
            deductionFromControl?.setValidators([Validators.required]);
        } else if (currentValue === "STOP_CONTRIBUTION") {
            stopDeductionControl?.setValidators([Validators.required]);
        } else if (currentValue === "CONTRIBUTION_NOT_AUTHORIZED") {
            deductionFromControl?.setValue(null);
            stopDeductionControl?.setValue(null);
        }

        deductionFromControl?.updateValueAndValidity();
        stopDeductionControl?.updateValueAndValidity();
        syndicatePanel.markAllAsTouched();
        syndicatePanel.updateValueAndValidity();
    }

    private scheduleInvalidPanelsCheck(): void {
        Promise.resolve().then(() => {
            setTimeout(() => {
                this.openInvalidPanels();
            }, 500);
        });
    }

    ngOnDestroy() {
        this.destroyed$.next();
        this.destroyed$.complete();
    }

    private setupSubscriptions() {
        this.destroyed$.next();
        this.runValidators();
    }

    private setupBankPanelValidation() {
        this.isMyBankAccountCtrl.valueChanges.pipe(
            takeUntil(this.destroyed$),
        ).subscribe(value => {
            const otherPersonNameControl = this.otherPersonNameCtrl;

            if (value === "bank_account_for_other_person_name") {
                otherPersonNameControl.enable();
                otherPersonNameControl.setValidators([Validators.required, Validators.maxLength(255)]);
            } else {
                otherPersonNameControl.clearValidators();
                otherPersonNameControl.disable();
                otherPersonNameControl.reset();
            }
            otherPersonNameControl.updateValueAndValidity();
        });

        this.ibanCtrl.valueChanges.pipe(
            debounceTime(300),
            takeUntil(this.destroyed$),
        ).subscribe(newValue => {
            this.updateIbanValidation(newValue);
        });
    }

    private updateIbanValidation(iban: string) {
        const ibanValue = iban ? iban.toString().replace(/\s+/g, "").toUpperCase() : "";
        this.isBelgianIban = ibanValue.toUpperCase().startsWith("BE");
        this.ibanCtrl.setValidators([Validators.required, bankAccountValidator()]);

        if (this.isBelgianIban) {
            this.bicCtrl.clearValidators();
        } else {
            this.bicCtrl.setValidators([Validators.required, CustomValidators.bicValidator()]);
        }
        this.bicCtrl.updateValueAndValidity();
    }

    private setupSyndicatePanelValidation() {
        this.syndicateContributionCtrl.valueChanges.pipe(
            takeUntil(this.destroyed$),
        ).subscribe(value => {
            this.updateSyndicateValidation(value);
        });
    }

    private updateSyndicateValidation(value: string) {
        const deductionFromControl = this.contributionDeductionFromTheMonthCtrl;
        const stopDeductionControl = this.stopContributionDeductionFromTheMonthCtrl;

        deductionFromControl.clearValidators();
        stopDeductionControl.clearValidators();

        if (value === "CONTRIBUTION") {
            deductionFromControl.setValidators([Validators.required]);
            stopDeductionControl.reset();
            stopDeductionControl.disable();
            deductionFromControl.enable();
        } else if (value === "STOP_CONTRIBUTION") {
            stopDeductionControl.setValidators([Validators.required]);
            deductionFromControl.reset();
            deductionFromControl.disable();
            stopDeductionControl.enable();
        } else if (value === "CONTRIBUTION_NOT_AUTHORIZED") {
            deductionFromControl.reset();
            stopDeductionControl.reset();
            deductionFromControl.disable();
            stopDeductionControl.disable();
        }

        deductionFromControl.updateValueAndValidity();
        stopDeductionControl.updateValueAndValidity();
    }

    private getCityLookup(cityCode: string | undefined): any {
        return cityCode; // for now just show the code (city name) as is
    }

    private getCountryLookup(code: string | undefined): any {
        if (!code) {
            return of(null);
        }
        return this.geoLookupService.searchCountryByCode(code);
    }

    private updateLookupDisplays() {
        const cityControl = this.cdfForm.get("employeePanel.city");
        const nationalityControl = this.cdfForm.get("employeePanel.nationalityCode");
        const countryControl = this.cdfForm.get("employeePanel.countryCode");

        if (cityControl?.value) {
            cityControl.setValue(cityControl.value, {emitEvent: false});
        }
        if (nationalityControl?.value) {
            nationalityControl.setValue(nationalityControl.value, {emitEvent: false});
        }
        if (countryControl?.value) {
            countryControl.setValue(countryControl.value, {emitEvent: false});
        }
    }

    readonly mockCities: any[] = [
        {
            code: "92250",
            descFr: "Bruxelles",
            descNl: "Brussel",
        }, {
            code: "2070",
            descFr: "ZWIJNDRECHT",
            descNl: "ZWIJNDRECHT",
        },
    ];

    displayLookup(lookup?: Lookup): string {
        if (!lookup) {
            return "";
        }
        const currentLang = this.language;
        return currentLang.toUpperCase() === "FR" ? lookup.descFr : lookup.descNl;
    }

    private setupSearchEffects() {
        if (this.cityCtrl) {
            this.cityCtrl.valueChanges.pipe(
                debounceTime(300),
            ).subscribe(value => {
                if (value) {
                    if (typeof value === "object") {
                        return;
                    }

                    const searchValue = value.toLowerCase();
                    this.citiesList = this.mockCities.filter(city =>
                        city.descFr.toLowerCase().includes(searchValue) ||
                        city.descNl.toLowerCase().includes(searchValue) ||
                        city.code.includes(searchValue),
                    );
                }
            });
        }
        if (this.nationalityCtrl) {

            this.geoLookupService.setupSearchControl(this.nationalityCtrl, "nationality")
                .subscribe(results => {
                    this.nationalitiesList = results;
                    if (this.nationalityCtrl.value != null && this.nationalityCtrl.value.length >= 3) {
                        this.nationalitySearched = true;
                    }
                });
        }

        if (this.countryCtrl) {
            this.countryCtrl.valueChanges.subscribe(val => {
            });

            this.geoLookupService.setupSearchControl(this.countryCtrl, "nationality")
                .subscribe(results => {
                    this.countriesList = results;
                    if (this.countryCtrl.value && this.countryCtrl.value.length >= 3) {
                        this.countrySearched = true;
                    }
                });
        }

    }

    get startDateCtrl(): FormControl {
        return this.cdfForm.get("requestPanel.startDate") as FormControl;
    }

    get nissCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.niss") as FormControl;
    }

    get birthDateCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.birthDate") as FormControl;
    }

    get lastNameCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.lastName") as FormControl;
    }

    get firstNameCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.firstName") as FormControl;
    }

    get nationalityCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.nationalityCode") as FormControl;
    }

    get countryCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.countryCode") as FormControl;
    }

    get streetCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.street") as FormControl;
    }

    get streetNbrCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.streetNbr") as FormControl;
    }

    get streetBoxCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.streetBox") as FormControl;
    }

    get postCodeCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.postCode") as FormControl;
    }

    get cityCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.city") as FormControl;
    }

    get addressFromDateCtrl(): FormControl {
        return this.cdfForm.get("employeePanel.addressFromDate") as FormControl;
    }

    get otherPersonNameCtrl(): FormControl {
        return this.cdfForm.get("bankPanel.otherPersonName") as FormControl;
    }

    get isMyBankAccountCtrl(): FormControl {
        return this.cdfForm.get("bankPanel.isMyBankAccount") as FormControl;
    }

    get ibanCtrl(): FormControl {
        return this.cdfForm.get("bankPanel.iban") as FormControl;
    }

    get bicCtrl(): FormControl {
        return this.cdfForm.get("bankPanel.bic") as FormControl;
    }

    get bankFromDateCtrl(): FormControl {
        return this.cdfForm.get("bankPanel.bankFromDate") as FormControl;
    }

    get syndicateContributionCtrl(): FormControl {
        return this.cdfForm.get("syndicatePanel.syndicateContribution") as FormControl;
    }

    get contributionDeductionFromTheMonthCtrl(): FormControl {
        return this.cdfForm.get("syndicatePanel.contributionDeductionFromTheMonth") as FormControl;
    }

    get stopContributionDeductionFromTheMonthCtrl(): FormControl {
        return this.cdfForm.get("syndicatePanel.stopContributionDeductionFromTheMonth") as FormControl;
    }

    onClosed(formControl: FormControl) {
        setTimeout(() => {
            formControl.updateValueAndValidity();
        }, 0);
    }

    hasNoSearchedNationalities() {
        return (
            (!this.nationalitiesList || this.nationalitiesList.length === 0) && this.nationalitySearched
        );
    }

    hasNoSearchedCountries() {
        return (
            (!this.countriesList || this.countriesList.length === 0) && this.countrySearched
        );
    }

    protected readonly FormUtilsService = FormUtilsService;
}
