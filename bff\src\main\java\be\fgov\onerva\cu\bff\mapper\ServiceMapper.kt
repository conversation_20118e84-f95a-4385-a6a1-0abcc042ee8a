package be.fgov.onerva.cu.bff.mapper

import be.fgov.onerva.cu.bff.rest.server.priv.model.Address
import be.fgov.onerva.cu.bff.rest.server.priv.model.AddressNullable
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RoutingDecisionItemResponse as ServerRoutingDecisionItemResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.RoutingDecisionItemResponse as ClientRoutingDecisionItemResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ManualVerificationType as ServerManualVerificationType
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.ManualVerificationType as ClientManualVerificationType

fun Address.toAddressNullable() = AddressNullable(
    boxNumber = this.boxNumber,
    city = this.city,
    countryCode = this.countryCode,
    houseNumber = this.houseNumber,
    street = this.street,
    zipCode = this.zipCode,
    validFrom = this.validFrom,
)

fun CitizenInformationDetailResponse.toCitizenInformationDetailNullableResponse() =
    CitizenInformationDetailNullableResponse(
        birthDate = this.birthDate,
        nationalityCode = this.nationalityCode,
        address = this.address?.toAddressNullable(),
        fieldSources = this.fieldSources,
    )

fun ClientRoutingDecisionItemResponse.toServerRoutingDecisionItemResponse(): ServerRoutingDecisionItemResponse? =
    this.type?.let { clientType ->
        ServerRoutingDecisionItemResponse(
            type = clientType.toServerManualVerificationType(),
            value = this.value,
        )
    }

fun ClientManualVerificationType.toServerManualVerificationType(): ServerManualVerificationType =
    when (this) {
        ClientManualVerificationType.CITIZEN_OVER_65_YEARS_OLD ->
            ServerManualVerificationType.CITIZEN_OVER_65_YEARS_OLD
        ClientManualVerificationType.RELEVANT_TO_PORT_WORKER ->
            ServerManualVerificationType.RELEVANT_TO_PORT_WORKER
        ClientManualVerificationType.NON_BELGIAN_RESIDENT ->
            ServerManualVerificationType.NON_BELGIAN_RESIDENT
        ClientManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC ->
            ServerManualVerificationType.TRANSFER_BETWEEN_OP_OR_OC
        ClientManualVerificationType.REQUEST_FOR_ECONOMIC_REASON ->
            ServerManualVerificationType.REQUEST_FOR_ECONOMIC_REASON
        ClientManualVerificationType.RELEVANT_TO_APPRENTICESHIP ->
            ServerManualVerificationType.RELEVANT_TO_APPRENTICESHIP
        ClientManualVerificationType.CASE_OF_IMPULSION ->
            ServerManualVerificationType.CASE_OF_IMPULSION
    }