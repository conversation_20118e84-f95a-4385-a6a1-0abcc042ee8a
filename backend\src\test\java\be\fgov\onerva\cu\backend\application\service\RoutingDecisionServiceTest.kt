package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType
import be.fgov.onerva.cu.backend.application.domain.RequestStatus
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.exception.IncompleteRoutingDecisionsException
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.RoutingDecisionPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RoutingDecisionServiceTest {

    @MockK
    lateinit var routingDecisionPort: RoutingDecisionPort
    
    @MockK
    lateinit var changePersonalDataPort: PersistChangePersonalDataPort

    @InjectMockKs
    lateinit var service: RoutingDecisionService

    private val requestId = UUID.randomUUID()

    private fun createChangePersonalDataRequest(status: RequestStatus = RequestStatus.OPEN): ChangePersonalDataRequest {
        return ChangePersonalDataRequest(
            id = requestId,
            c9Type = "400",
            c9id = 123L,
            ssin = "***********",
            opKey = "OP123",
            sectOp = "SO123",
            requestDate = LocalDate.now(),
            decisionType = null,
            decisionBarema = null,
            numbox = 12,
            documentType = IdentityDocumentType.PAPER,
            paymentInstitution = 1,
            entityCode = "123456",
            dossierId = null,
            introductionDate = LocalDate.now(),
            scanUrl = "http://example.com/scan1",
            unemploymentOffice = 123,
            dateValid = LocalDate.now(),
            scanNumber = 12345L,
            operatorCode = 123,
            introductionType = IntroductionType.INTRO_FIRST_DEMAND,
            dueDate = LocalDate.now().plusDays(12),
            ec1Id = 1234,
            ec1DisplayUrl = "http://example.com/ec1",
            citizenInformation = null,
            modeOfPayment = null,
            unionContribution = null,
            requestInformation = null,
            changePersonalDataCaptureWaveTask = null,
            changePersonalDataValidateWaveTask = null,
            status = status,
            processInWave = false,
            routingDecisions = emptySet()
        )
    }

    private fun createAllRoutingDecisions(value: Boolean? = false): Set<RoutingDecisionItem> {
        return ManualVerificationType.entries.map { type ->
            RoutingDecisionItem(type, value)
        }.toSet()
    }

    private fun createPartialRoutingDecisions(): Set<RoutingDecisionItem> {
        return setOf(
            RoutingDecisionItem(ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD, false),
            RoutingDecisionItem(ManualVerificationType.NON_BELGIAN_RESIDENT, null)
            // Missing other types
        )
    }

    @Nested
    inner class GetRoutingDecisionsTests {

        @Test
        fun `should return existing routing decisions when all types are present`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val existingDecisions = createAllRoutingDecisions(false)
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request
            every { routingDecisionPort.getRoutingDecisions(requestId) } returns existingDecisions

            // When
            val result = service.getRoutingDecisions(requestId)

            // Then
            assertThat(result.routingDecisions).hasSize(ManualVerificationType.entries.size)
            assertThat(result).isEqualTo(existingDecisions)
            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 1) { routingDecisionPort.getRoutingDecisions(requestId) }
        }

    }

    @Nested
    inner class UpdateRoutingDecisionsTests {

        @Test
        fun `should successfully update routing decisions for open request`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val routingDecisions = createAllRoutingDecisions(false)
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request
            every { routingDecisionPort.saveRoutingDecisions(requestId, routingDecisions, true) } returns Unit

            // When
            service.updateRoutingDecisions(requestId, routingDecisions)

            // Then
            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 1) { routingDecisionPort.saveRoutingDecisions(requestId, routingDecisions, true) }
        }

        @Test
        fun `should throw exception when request not found`() {
            // Given
            val routingDecisions = createAllRoutingDecisions(false)
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns null

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, routingDecisions)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("Request not found: $requestId")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any(), any()) }
        }

        @Test
        fun `should throw exception when request is closed`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.CLOSED)
            val routingDecisions = createAllRoutingDecisions(false)
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, routingDecisions)
            }
                .isInstanceOf(IllegalStateException::class.java)
                .hasMessageContaining("Cannot update routing decisions for a closed request: $requestId")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any(), any()) }
        }

        @Test
        fun `should throw exception when routing decisions are incomplete - missing types`() {
            // Given
            val request = createChangePersonalDataRequest(RequestStatus.OPEN)
            val incompleteDecisions = createPartialRoutingDecisions()
            
            every { changePersonalDataPort.getChangePersonalDataById(requestId) } returns request

            // When/Then
            assertThatThrownBy {
                service.updateRoutingDecisions(requestId, incompleteDecisions)
            }
                .isInstanceOf(IncompleteRoutingDecisionsException::class.java)
                .hasMessageContaining("Routing decisions validation failed")
                .hasMessageContaining("Missing types:")

            verify(exactly = 1) { changePersonalDataPort.getChangePersonalDataById(requestId) }
            verify(exactly = 0) { routingDecisionPort.saveRoutingDecisions(any(), any(), any()) }
        }


    }
}