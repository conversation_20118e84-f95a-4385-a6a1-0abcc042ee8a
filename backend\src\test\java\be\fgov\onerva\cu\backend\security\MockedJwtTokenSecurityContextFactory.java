package be.fgov.onerva.cu.backend.security;

import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.test.context.support.WithSecurityContextFactory;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MockedJwtTokenSecurityContextFactory implements WithSecurityContextFactory<WithMockedJwtToken> {

    @Override
    public SecurityContext createSecurityContext(WithMockedJwtToken annotation) {
        String username = annotation.username();
        String[] roles = annotation.roles();

        return MockedJwtTokenSecurityContextFactory.createSecurityContext(username, roles);
    }

    @NotNull
    private static SecurityContext createSecurityContext(@NotNull String username, @NotNull String[] roles) {
        SecurityContext context = SecurityContextHolder.createEmptyContext();

        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", username);
        claims.put("preferred_username", username);

        // Add roles to both realm_access and authorities
        List<String> rolesAsList = Arrays.asList(roles);
        Map<String, Object> realmAccess = new HashMap<>();
        realmAccess.put("roles", rolesAsList);
        claims.put("realm_access", realmAccess);

        Jwt jwt = Jwt.withTokenValue("token")
                .header("alg", "none")
                .claims(c -> c.putAll(claims))
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(60))
                .build();

        Collection<SimpleGrantedAuthority> authorities =
                Stream.concat(rolesAsList.stream().map(SimpleGrantedAuthority::new),
                        rolesAsList.stream().map(role -> new SimpleGrantedAuthority("ROLE_" + role))
        ).collect(Collectors.toSet());

        Authentication auth = new JwtAuthenticationToken(jwt, authorities);
        context.setAuthentication(auth);
        return context;
    }

    public static void populateSecurityContext(@NotNull String username, @NotNull String[] roles) {
        SecurityContext context = MockedJwtTokenSecurityContextFactory.createSecurityContext(username, roles);
        SecurityContextHolder.setContext(context);
    }
}