package be.fgov.onerva.cu.common.utils

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import be.fgov.onerva.cu.common.utils.NissUtils.getBirthDateFromSsin
import kotlinx.coroutines.runBlocking

class NissUtilsTest {

    @Nested
    inner class getBirthDateFromSsinTest {
        @Test
        fun validSinBefore2000() {
            val ssin = "85031512369"
            val expected = LocalDate.of(1985, 3, 15)
            assertThat(getBirthDateFromSsin(ssin)).isEqualTo(expected)
        }

        @Test
        fun validSinAfter2000() {
            val ssin = "25071012383"
            val expected = LocalDate.of(2025, 7, 10)
            assertThat(getBirthDateFromSsin(ssin)).isEqualTo(expected)
        }

        @Test
        fun SSINtooshortthrowsexception() {
            val ssin = "850315123"  // 9 digits
            assertThatThrownBy { runBlocking { getBirthDateFromSsin(ssin) } }
                .isInstanceOf(IllegalArgumentException::class.java)
        }

        @Test
        fun `SSIN with letters throws exception`() {
            val ssin = "85A31512369"
            assertThatThrownBy { runBlocking { getBirthDateFromSsin(ssin) } }
                .isInstanceOf(IllegalArgumentException::class.java)
        }
    }
}