package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.mapper.WaveTaskTypeResolver
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.WaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.WaveTaskRepository
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class WaveTaskPersistenceAdapterTest {

    @MockK
    lateinit var requestRepository: RequestRepository

    @MockK
    lateinit var waveTaskRepository: WaveTaskRepository

    @MockK
    lateinit var taskTypeResolver: WaveTaskTypeResolver

    @InjectMockKs
    lateinit var adapter: WaveTaskPersistenceAdapter

    @Nested
    inner class CreateWaveTask {

        @Test
        fun `createWaveTask should create and save wave task when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
            )

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {}

            val waveTaskSlot = slot<WaveTaskEntity>()

            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity
            every { waveTaskRepository.save(capture(waveTaskSlot)) } answers { waveTaskSlot.captured }

            // When
            adapter.persistChangePersonalDataCaptureWaveTask(requestId, waveTask)

            // Then
            verify(exactly = 1) { waveTaskRepository.save(any()) }
            with(waveTaskSlot.captured) {
                assertThat(processId).isEqualTo("PROC-123")
                assertThat(taskId).isEqualTo("TASK-456")
                assertThat(request).isEqualTo(requestEntity)
            }
        }

        @Test
        fun `createWaveTask should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
            )

            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.persistChangePersonalDataCaptureWaveTask(requestId, waveTask) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")

            verify(exactly = 0) { waveTaskRepository.save(any()) }
        }
    }

    @Nested
    inner class CloseWaveTaskChangePersonalDataCapture {

        @Test
        fun `closeWaveTaskChangePersonalDataCapture should update status and revisions when task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val citizenInformationRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 1

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)
            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When
            adapter.closeWaveTaskChangePersonalDataCapture(
                requestId,
                citizenInformationRevision,
                modeOfPaymentRevision,
                unionContributionRevision,
                requestInformationRevision,
            )

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.CLOSED)
            assertThat(waveTaskEntity.citizenInformationRevisionNumber).isEqualTo(citizenInformationRevision)
            assertThat(waveTaskEntity.modeOfPaymentRevisionNumber).isEqualTo(modeOfPaymentRevision)
            assertThat(waveTaskEntity.unionContributionRevisionNumber).isEqualTo(unionContributionRevision)

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `closeWaveTaskChangePersonalDataCapture should throw WaveTaskNotFoundException when no task found`() {
            // Given
            val requestId = UUID.randomUUID()

                every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy {
                adapter.closeWaveTaskChangePersonalDataCapture(
                    requestId,
                    1,
                    2,
                    3,
                    1
                )
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `closeWaveTaskChangePersonalDataCapture should throw WaveTaskNotFoundException when wrong task type found`() {
            // Given
            val requestId = UUID.randomUUID()
            val wrongTypeTask = mockk<WaveTaskEntity>()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(wrongTypeTask)
            every { requestRepository.findByIdOrNull(requestId) } returns null


            // When/Then
            assertThatThrownBy {
                adapter.closeWaveTaskChangePersonalDataCapture(
                    requestId,
                    1,
                    2,
                    3,
                    1
                )
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `closeWaveTaskChangePersonalDataCapture should handle null revision numbers`() {
            // Given
            val requestId = UUID.randomUUID()

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            adapter.closeWaveTaskChangePersonalDataCapture(
                requestId,
                0,
                0,
                0,
                0
            )

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.CLOSED)
            assertThat(waveTaskEntity.citizenInformationRevisionNumber).isZero()
            assertThat(waveTaskEntity.modeOfPaymentRevisionNumber).isZero()
            assertThat(waveTaskEntity.unionContributionRevisionNumber).isZero()

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class CloseWaveTaskChangePersonalDataValidate {

        @Test
        fun `closeWaveTaskChangePersonalDataValidate should update status and revisions when task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val citizenInformationRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 1

            val waveTaskEntity = ChangePersonalDataValidateWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            adapter.closeWaveTaskChangePersonalDataValidate(
                requestId,
                citizenInformationRevision,
                modeOfPaymentRevision,
                unionContributionRevision,
                requestInformationRevision
            )

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.CLOSED)
            assertThat(waveTaskEntity.citizenInformationRevisionNumber).isEqualTo(citizenInformationRevision)
            assertThat(waveTaskEntity.modeOfPaymentRevisionNumber).isEqualTo(modeOfPaymentRevision)
            assertThat(waveTaskEntity.unionContributionRevisionNumber).isEqualTo(unionContributionRevision)

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `closeWaveTaskChangePersonalDataValidate should throw WaveTaskNotFoundException when no task found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy {
                adapter.closeWaveTaskChangePersonalDataValidate(
                    requestId,
                    1,
                    2,
                    3,
                    1
                )
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `closeWaveTaskChangePersonalDataValidate should throw WaveTaskNotFoundException when wrong task type found`() {
            // Given
            val requestId = UUID.randomUUID()
            val wrongTypeTask = mockk<WaveTaskEntity>()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(wrongTypeTask)

            // When/Then
            assertThatThrownBy {
                adapter.closeWaveTaskChangePersonalDataValidate(
                    requestId,
                    1,
                    2,
                    3,
                    1
                )
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `closeWaveTaskChangePersonalDataValidate should handle null revision numbers`() {
            // Given
            val requestId = UUID.randomUUID()

            val waveTaskEntity = ChangePersonalDataValidateWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            adapter.closeWaveTaskChangePersonalDataValidate(
                requestId,
                0,
                0,
                0,
                0
            )

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.CLOSED)
            assertThat(waveTaskEntity.citizenInformationRevisionNumber).isZero()
            assertThat(waveTaskEntity.modeOfPaymentRevisionNumber).isZero()
            assertThat(waveTaskEntity.unionContributionRevisionNumber).isZero()

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class PersistChangePersonalDataValidateWaveTask {

        @Test
        fun `persistChangePersonalDataValidateWaveTask should create and save wave task when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
            )

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {}

            val waveTaskSlot = slot<WaveTaskEntity>()

            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity
            every { waveTaskRepository.save(capture(waveTaskSlot)) } answers { waveTaskSlot.captured }

            // When
            adapter.persistChangePersonalDataValidateWaveTask(requestId, waveTask)

            // Then
            verify(exactly = 1) { waveTaskRepository.save(any()) }
            with(waveTaskSlot.captured) {
                assertThat(processId).isEqualTo("PROC-123")
                assertThat(taskId).isEqualTo("TASK-456")
                assertThat(request).isEqualTo(requestEntity)
            }
        }

        @Test
        fun `persistChangePersonalDataValidateWaveTask should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
            )

            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.persistChangePersonalDataValidateWaveTask(requestId, waveTask) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")

            verify(exactly = 0) { waveTaskRepository.save(any()) }
        }

        @Test
        fun `assignTaskToUser should throw WaveTaskNotFoundException when the task is not found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy { adapter.getOpenWaveTaskByRequestId(requestId) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class GetClosedWaveTaskByRequestId {

        @Test
        fun `getClosedWaveTaskByRequestId should return closed wave task when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.WAITING,
                request = mockk(),
            )
            val expectedWaveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.WAITING
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            val result = adapter.getWaitingWaveTaskByRequestId(requestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE)

            // Then
            assertThat(result).isEqualTo(expectedWaveTask)
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `getClosedWaveTaskByRequestId should throw WaveTaskNotFoundException when no closed task found`() {
            // Given
            val requestId = UUID.randomUUID()
            val openWaveTaskEntity = mockk<WaveTaskEntity>()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(openWaveTaskEntity)
            every { openWaveTaskEntity.status } returns WaveTaskStatus.OPEN

            // When/Then
            assertThatThrownBy { adapter.getWaitingWaveTaskByRequestId(requestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `getClosedWaveTaskByRequestId should throw WaveTaskNotFoundException when no tasks found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy { adapter.getWaitingWaveTaskByRequestId(requestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }


    @Nested
    inner class GetOpenWaveTaskByRequestId {
        @Test
        fun `getOpenWaveTaskByRequestId should return open wave task when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
            )
            val expectedWaveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            val result = adapter.getOpenWaveTaskByRequestId(requestId)

            // Then
            assertThat(result).isEqualTo(expectedWaveTask)
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `getOpenWaveTaskByRequestId should throw WaveTaskNotFoundException when no open task found`() {
            // Given
            val requestId = UUID.randomUUID()
            val closedWaveTaskEntity = mockk<WaveTaskEntity>()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(closedWaveTaskEntity)
            every { closedWaveTaskEntity.status } returns WaveTaskStatus.CLOSED

            // When/Then
            assertThatThrownBy { adapter.getOpenWaveTaskByRequestId(requestId) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `getOpenWaveTaskByRequestId should throw WaveTaskNotFoundException when no tasks found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy { adapter.getOpenWaveTaskByRequestId(requestId) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class GetCitizenInformationRevision {
        @Test
        fun `getCitizenInformationRevision should return revision numbers when closed task found`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskCode = "CHANGE_PERSONAL_DATA_CAPTURE"
            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.CLOSED,
                request = mockk(),
            )
            val expectedRevisionNumbers = WaveTaskRevisionNumbers(1, 2, 3, 4)

            every {
                waveTaskRepository.findAllByRequestIdAndStatusIn(
                    requestId,
                    listOf(WaveTaskStatus.CLOSED, WaveTaskStatus.WAITING)
                )
            } returns listOf(waveTaskEntity)

            // When
            val result = adapter.getCitizenInformationRevision(requestId, taskCode)

            // Then
            assertThat(result).isEqualTo(expectedRevisionNumbers)
            verify(exactly = 1) {
                waveTaskRepository.findAllByRequestIdAndStatusIn(
                    requestId,
                    listOf(WaveTaskStatus.CLOSED, WaveTaskStatus.WAITING)
                )
            }
        }

        @Test
        fun `getCitizenInformationRevision should throw WaveTaskNotFoundException when no closed task found`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskCode = "CHANGE_PERSONAL_DATA_CAPTURE"

            every {
                waveTaskRepository.findAllByRequestIdAndStatusIn(
                    requestId,
                    listOf(WaveTaskStatus.CLOSED, WaveTaskStatus.WAITING)
                )
            } returns emptyList()

            // When/Then
            assertThatThrownBy { adapter.getCitizenInformationRevision(requestId, taskCode) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found (CLOSED or WAITING) for request $requestId")

            verify(exactly = 1) {
                waveTaskRepository.findAllByRequestIdAndStatusIn(
                    requestId,
                    listOf(WaveTaskStatus.CLOSED, WaveTaskStatus.WAITING)
                )
            }
        }
    }

    @Nested
    inner class SleepWaveTaskChangePersonalDataValidate {
        @Test
        fun `sleepWaveTaskChangePersonalDataValidate should update status to SLEEPING when task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val waveTaskEntity = ChangePersonalDataValidateWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            adapter.sleepWaveTaskChangePersonalDataValidate(requestId, 1, 2, 3, 4)

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.WAITING)
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `sleepWaveTaskChangePersonalDataValidate should throw WaveTaskNotFoundException when no task found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy {
                adapter.sleepWaveTaskChangePersonalDataValidate(requestId, 1, 2, 3, 4)
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `sleepWaveTaskChangePersonalDataValidate should throw WaveTaskNotFoundException when wrong task type found`() {
            // Given
            val requestId = UUID.randomUUID()
            val wrongTypeTask = mockk<WaveTaskEntity>()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(wrongTypeTask)

            // When/Then
            assertThatThrownBy {
                adapter.sleepWaveTaskChangePersonalDataValidate(requestId, 1, 2, 3, 4)
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class SleepWaveTaskChangePersonalDataCapture {
        @Test
        fun `sleepWaveTaskChangePersonalDataCapture should update status and revisions when task exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val citizenInformationRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 4

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            adapter.sleepWaveTaskChangePersonalDataCapture(
                requestId,
                citizenInformationRevision,
                modeOfPaymentRevision,
                unionContributionRevision,
                requestInformationRevision
            )

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.WAITING)
            assertThat(waveTaskEntity.citizenInformationRevisionNumber).isEqualTo(citizenInformationRevision)
            assertThat(waveTaskEntity.modeOfPaymentRevisionNumber).isEqualTo(modeOfPaymentRevision)
            assertThat(waveTaskEntity.unionContributionRevisionNumber).isEqualTo(unionContributionRevision)
            assertThat(waveTaskEntity.requestInformationRevisionNumber).isEqualTo(requestInformationRevision)

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `sleepWaveTaskChangePersonalDataCapture should throw WaveTaskNotFoundException when no task found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When/Then
            assertThatThrownBy {
                adapter.sleepWaveTaskChangePersonalDataCapture(
                    requestId,
                    citizenInformationLatestRevision = 1,
                    modeOfPaymentLatestRevision = 1,
                    unionContributionLatestRevision = 1,
                    requestInformationLatestRevision = 1
                )
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `sleepWaveTaskChangePersonalDataCapture should throw WaveTaskNotFoundException when wrong task type found`() {
            // Given
            val requestId = UUID.randomUUID()
            val wrongTypeTask = mockk<WaveTaskEntity>()

            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(wrongTypeTask)

            // When/Then
            assertThatThrownBy {
                adapter.sleepWaveTaskChangePersonalDataCapture(
                    requestId,
                    citizenInformationLatestRevision = 1,
                    modeOfPaymentLatestRevision = 1,
                    unionContributionLatestRevision = 1,
                    requestInformationLatestRevision = 1
                )
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class GetWaveTaskEntityByRequestId {

        @Test
        fun `getWaveTaskEntityByRequestId should return wave task when request and task exist`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
            )

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {
                override var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf(waveTaskEntity)
            }

            val expectedWaveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN
            )

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity

            // When
            val result = adapter.getWaveTaskEntityByRequestId(requestId, taskType)

            // Then
            assertThat(result).isEqualTo(expectedWaveTask)
            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getWaveTaskEntityByRequestId should throw WaveTaskNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getWaveTaskEntityByRequestId(requestId, taskType) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getWaveTaskEntityByRequestId should throw WaveTaskNotFoundException when task not found in request`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            val wrongTaskEntity = ChangePersonalDataValidateWaveTaskEntity(
                processId = "PROC-789",
                taskId = "TASK-999",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {
                override var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf(wrongTaskEntity)
            }

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity

            // When/Then
            assertThatThrownBy { adapter.getWaveTaskEntityByRequestId(requestId, taskType) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found in the request $requestId")

            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getWaveTaskEntityByRequestId should throw WaveTaskNotFoundException when request has no wave tasks`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {
                override var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf()
            }

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity

            // When/Then
            assertThatThrownBy { adapter.getWaveTaskEntityByRequestId(requestId, taskType) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found in the request $requestId")

            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }
    }

    @Nested
    inner class GetOpenWaveTaskEntityByRequestId {

        @Test
        fun `getOpenWaveTaskEntityByRequestId should return wave task when request and task exist`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
            )

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {
                override var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf(waveTaskEntity)
            }

            val expectedWaveTask = WaveTask(
                processId = "PROC-123",
                taskId = "TASK-456",
                status = WaveTaskStatus.OPEN
            )

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity

            // When
            val result = adapter.getOpenWaveTaskEntityByRequestId(requestId, taskType)

            // Then
            assertThat(result).isEqualTo(expectedWaveTask)
            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getOpenWaveTaskEntityByRequestId should throw WaveTaskNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getOpenWaveTaskEntityByRequestId(requestId, taskType) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for request $requestId")

            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getOpenWaveTaskEntityByRequestId should throw WaveTaskNotFoundException when task not found in request`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            val wrongTaskEntity = ChangePersonalDataValidateWaveTaskEntity(
                processId = "PROC-789",
                taskId = "TASK-999",
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {
                override var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf(wrongTaskEntity)
            }

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity

            // When/Then
            assertThatThrownBy { adapter.getOpenWaveTaskEntityByRequestId(requestId, taskType) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found in the request $requestId")

            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getOpenWaveTaskEntityByRequestId should throw WaveTaskNotFoundException when request has no wave tasks`() {
            // Given
            val requestId = UUID.randomUUID()
            val taskType = "CHANGE_PERSONAL_DATA_CAPTURE"
            val clazz = ChangePersonalDataCaptureWaveTaskEntity::class.java

            val requestEntity = object : RequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                ssin = "12345678901",
            ) {
                override var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf()
            }

            every { taskTypeResolver.resolve(taskType) } returns clazz
            every { requestRepository.findByIdOrNull(requestId) } returns requestEntity

            // When/Then
            assertThatThrownBy { adapter.getOpenWaveTaskEntityByRequestId(requestId, taskType) }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found in the request $requestId")

            verify(exactly = 1) { taskTypeResolver.resolve(taskType) }
            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
        }
    }

    @Nested
    inner class SoftDeleteTaskById {

        @Test
        fun `softDeleteTaskById should update task status to DELETED when task exists`() {
            // Given
            val taskId = "TASK-456"
            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                processId = "PROC-123",
                taskId = taskId,
                status = WaveTaskStatus.OPEN,
                request = mockk(),
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4,
            )

            every { waveTaskRepository.findByTaskId(taskId) } returns waveTaskEntity
            every { waveTaskRepository.save(any<WaveTaskEntity>()) } returns waveTaskEntity

            // When
            adapter.softDeleteTaskById(taskId)

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.DELETED)
            verify(exactly = 1) { waveTaskRepository.findByTaskId(taskId) }
            verify(exactly = 1) { waveTaskRepository.save(waveTaskEntity) }
        }

        @Test
        fun `softDeleteTaskById should do nothing when task does not exist`() {
            // Given
            val taskId = "NON-EXISTENT-TASK"

            every { waveTaskRepository.findByTaskId(taskId) } returns null

            // When
            adapter.softDeleteTaskById(taskId)

            // Then
            verify(exactly = 1) { waveTaskRepository.findByTaskId(taskId) }
            verify(exactly = 0) { waveTaskRepository.save(any()) }
        }

        @Test
        fun `softDeleteTaskById should update status from any existing status to DELETED`() {
            // Given
            val taskId = "TASK-789"
            val waveTaskEntity = ChangePersonalDataValidateWaveTaskEntity(
                processId = "PROC-456",
                taskId = taskId,
                status = WaveTaskStatus.CLOSED,
                request = mockk(),
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null,
            )

            every { waveTaskRepository.findByTaskId(taskId) } returns waveTaskEntity
            every { waveTaskRepository.save(any<WaveTaskEntity>()) } returns waveTaskEntity

            // When
            adapter.softDeleteTaskById(taskId)

            // Then
            assertThat(waveTaskEntity.status).isEqualTo(WaveTaskStatus.DELETED)
            verify(exactly = 1) { waveTaskRepository.findByTaskId(taskId) }
            verify(exactly = 1) { waveTaskRepository.save(waveTaskEntity) }
        }
    }
}
