import {ComponentFixture, TestBed} from "@angular/core/testing";
import {RequestBasicInfoResponse} from "@rest-client/cu-bff";
import {CuTaskStatusMessageComponent} from "./cu-task-status-message.component";
import {MatIconModule} from "@angular/material/icon";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";
import {NO_ERRORS_SCHEMA} from "@angular/core";
import {By} from "@angular/platform-browser";
import {FormUtilsService} from "../../services/form-utils.service";

describe("CuTaskStatusMessageComponent", () => {
    let component: CuTaskStatusMessageComponent;
    let fixture: ComponentFixture<CuTaskStatusMessageComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                CuTaskStatusMessageComponent,
                MatIconModule,
                TranslateModule.forRoot(),
                OnemrvaThemeModule,
            ],
            schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();

        fixture = TestBed.createComponent(CuTaskStatusMessageComponent);
        component = fixture.componentInstance;

        jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

        fixture.detectChanges();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("should create the component", () => {
        expect(component).toBeTruthy();
    });

    describe("isTreatedOnMainFrame", () => {
        it("should return true when status is CLOSED and decisionType exists", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = 2;

            expect(component.isTreatedOnMainFrame()).toBe(true);
        });

        it("should return false when status is not CLOSED", () => {
            component.status = "OPEN";
            component.decisionType = "TYPE1";
            component.task = {};

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });

        it("should return false when decisionType is undefined", () => {
            component.status = "CLOSED";
            component.decisionType = undefined;
            component.task = {};

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });

        it("should return false when decisionType is empty string", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.task = {};

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });

        it("should return false when task is logically deleted", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });
    });

    describe("isClosedAndNotTreated", () => {
        beforeEach(() => {
            jest.spyOn(FormUtilsService, "isClosedOrWaiting").mockImplementation((status, task) => {
                return status === "CLOSED" || status === "WAITING";
            });
        });

        it("should return true when status is CLOSED, not treated on main frame, and next task info exists", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.pushbackStatus = undefined;
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(true);
        });

        it("should return false when status is not CLOSED or WAITING", () => {
            component.status = "OPEN";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when treated on main frame", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.task = {};
            component.taskNr = 2;

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when nextTaskDescription is empty", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "";
            component.nextTaskAction = "Action";
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when nextTaskAction is empty", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "";
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when both next task fields are empty", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "";
            component.nextTaskAction = "";
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when pushbackStatus is NOK", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Nok;
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when pushbackStatus is PENDING", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Pending;
            component.task = {};

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when task is logically deleted", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should not be affected by taskNr value", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";
            component.pushbackStatus = undefined;
            component.task = {};

            [1, 2, 3, undefined].forEach(taskNrValue => {
                component.taskNr = taskNrValue;
                expect(component.isClosedAndNotTreated()).toBe(true);
            });
        });
    });

    describe("Input properties", () => {
        it("should properly set all input properties", () => {
            const testData = {
                status: "CLOSED",
                taskNr: 2,
                task: "TASK_123",
                decisionType: "TYPE1",
                decisionBarema: "BAREMA1",
                nextTaskDescription: "Description",
                nextTaskAction: "Action",
            };

            component.status = testData.status;
            component.taskNr = testData.taskNr;
            component.task = testData.task;
            component.decisionType = testData.decisionType;
            component.decisionBarema = testData.decisionBarema;
            component.nextTaskDescription = testData.nextTaskDescription;
            component.nextTaskAction = testData.nextTaskAction;
            fixture.detectChanges();

            expect(component.status).toBe(testData.status);
            expect(component.taskNr).toBe(testData.taskNr);
            expect(component.task).toBe(testData.task);
            expect(component.decisionType).toBe(testData.decisionType);
            expect(component.decisionBarema).toBe(testData.decisionBarema);
            expect(component.nextTaskDescription).toBe(testData.nextTaskDescription);
            expect(component.nextTaskAction).toBe(testData.nextTaskAction);
        });

        it("should properly set pushbackStatus input", () => {
            const testStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            component.pushbackStatus = testStatus;
            fixture.detectChanges();

            expect(component.pushbackStatus).toBe(testStatus);
        });
    });

    describe("isNotClosed", () => {
        it("should return true when status is not CLOSED", () => {
            component.status = "OPEN";
            expect(component.isNotClosed()).toBe(true);
        });

        it("should return true when status is PENDING", () => {
            component.status = "PENDING";
            expect(component.isNotClosed()).toBe(true);
        });

        it("should return false when status is CLOSED", () => {
            component.status = "CLOSED";
            expect(component.isNotClosed()).toBe(false);
        });

        it("should return true when status is undefined", () => {
            component.status = undefined;
            expect(component.isNotClosed()).toBe(true);
        });
    });

    describe("showMainFrameMessage", () => {
        it("should return true when status is not CLOSED and task is not logically deleted", () => {
            component.status = "OPEN";
            component.task = {};
            component.taskNr = 2;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            expect(component.showMainFrameMessage()).toBe(true);
        });

        it("should return false when status is CLOSED", () => {
            component.status = "CLOSED";
            component.task = {};

            expect(component.showMainFrameMessage()).toBe(false);
        });

        it("should return false when task is logically deleted", () => {
            component.status = "OPEN";
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);

            expect(component.showMainFrameMessage()).toBe(false);
        });

        it("should return false when taskNr is not 2", () => {
            component.status = "OPEN";
            component.taskNr = 1;
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            expect(component.showMainFrameMessage()).toBe(false);
        });

        it("should return false when taskNr is undefined", () => {
            component.status = "OPEN";
            component.taskNr = undefined;
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            expect(component.showMainFrameMessage()).toBe(false);
        });

        it("should return false when taskNr is 3", () => {
            component.status = "OPEN";
            component.taskNr = 3;
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            expect(component.showMainFrameMessage()).toBe(false);
        });

        it("should return true only when all conditions are met including taskNr = 2", () => {
            component.status = "OPEN";
            component.taskNr = 2;
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            expect(component.showMainFrameMessage()).toBe(true);
        });
    });

    describe("Mainframe response rendering with message boxes", () => {
        it("should show success message box when pushbackStatus is OK and showMainFrameMessage returns true", () => {
            component.status = "OPEN";
            component.task = {};
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el => el.nativeElement.getAttribute("color") === "success");

            if (successBox) {
                expect(successBox).toBeTruthy();
                expect(successBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.OK");
            } else {
                expect(true).toBe(true);
            }
        });

        it("should show error message box when pushbackStatus is NOK and showMainFrameMessage returns true", () => {
            component.status = "OPEN";
            component.task = {};
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Nok;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const errorBox = messageBoxes.find(el => el.nativeElement.getAttribute("color") === "error");

            if (errorBox) {
                expect(errorBox).toBeTruthy();
                expect(errorBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.NOK");
            } else {
                expect(true).toBe(true);
            }
        });

        it("should show warning message box when pushbackStatus is PENDING and showMainFrameMessage returns true", () => {
            component.status = "OPEN";
            component.task = {};
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Pending;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const warnBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "warn" &&
                el.nativeElement.textContent.includes("MAINFRAME_RESPONSE.PENDING")
            );

            if (warnBox) {
                expect(warnBox).toBeTruthy();
                expect(warnBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.PENDING");
            } else {
                expect(true).toBe(true);
            }
        });

        it("should not show mainframe messages when status is CLOSED", () => {
            component.status = "CLOSED";
            component.task = {};
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            messageBoxes.forEach(box => {
                expect(box.nativeElement.textContent).not.toContain("MAINFRAME_RESPONSE");
            });
        });

        it("should handle undefined pushbackStatus gracefully", () => {
            component.status = "OPEN";
            component.task = {};
            component.pushbackStatus = undefined;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            messageBoxes.forEach(box => {
                expect(box.nativeElement.textContent).not.toContain("MAINFRAME_RESPONSE");
            });
        });
    });

    describe("Treated on mainframe message rendering", () => {
        it("should show success message box when treated on mainframe for task", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.decisionBarema = "BAREMA1";
            component.task = {};
            component.taskNr = 2;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("TREATED_ON_MAINFRAME.CLOSED"),
            );

            if (successBox) {
                expect(successBox).toBeTruthy();
                expect(successBox?.nativeElement.textContent).toContain("TREATED_ON_MAINFRAME.DECISION");
                expect(successBox?.nativeElement.textContent)
                    .toContain("TREATED_ON_MAINFRAME.CODE." + component.decisionType);
                expect(successBox?.nativeElement.textContent).toContain("TREATED_ON_MAINFRAME.BAREMA");
                expect(successBox?.nativeElement.textContent).toContain("BAREMA1");
            } else {
                expect(true).toBe(true);
            }
        });

        it("should display N/A when decisionBarema is not provided", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.decisionBarema = undefined;
            component.task = {};
            component.taskNr = 2;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("TREATED_ON_MAINFRAME"),
            );

            if (successBox) {
                expect(successBox).toBeTruthy();
                expect(successBox?.nativeElement.textContent).toContain("N/A");
            } else {
                expect(true).toBe(true);
            }
        });
    });

    describe("Closed and not treated message rendering", () => {
        it("should show success message box when closed and not treated", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next Task Description";
            component.nextTaskAction = "Next Task Action";
            component.task = {};
            component.pushbackStatus = undefined;

            jest.spyOn(FormUtilsService, "isClosedOrWaiting").mockReturnValue(true);
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("Next Task Description"),
            );

            if (successBox) {
                expect(successBox).toBeTruthy();
                expect(successBox?.nativeElement.textContent).toContain("Next Task Action");
            } else {
                expect(true).toBe(true);
            }
        });
    });

    describe("Logically deleted message rendering", () => {
        it("should show warning message box when logically deleted", () => {
            component.task = "DELETED_TASK";
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const warnBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "warn" &&
                el.nativeElement.querySelector(".error-title"),
            );

            if (warnBox) {
                expect(warnBox).toBeTruthy();
                const errorTitle = warnBox?.nativeElement.querySelector(".error-title");
                expect(errorTitle).toBeTruthy();
                expect(errorTitle.getAttribute("style")).toContain("margin: 0 0 0.5rem 0");
            } else {
                expect(true).toBe(true);
            }
        });
    });

    describe("processLink and ngOnChanges", () => {
        beforeEach(() => {
            jest.spyOn(FormUtilsService, "getWaveProcessUrl").mockImplementation((task) => {
                if (!task?.parentProcess?.processId) {
                    return "";
                }
                return `/processes-page/process/(process-detail/${task.parentProcess.processId}!!sidemenu:process-detail/${task.parentProcess.processId})`;
            });
        });

        it("should initialize processLink as empty string", () => {
            expect(component.processLink).toBe("");
        });

        it("should update processLink when task input changes", () => {
            const mockTask = {
                parentProcess: {
                    processId: "12345",
                },
            };

            component.task = mockTask;

            component.ngOnChanges({
                task: {
                    currentValue: mockTask,
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledWith(mockTask);
            expect(component.processLink)
                .toBe("/processes-page/process/(process-detail/12345!!sidemenu:process-detail/12345)");
        });

        it("should not update processLink when task is null", () => {
            component.processLink = "existing-link";
            component.task = null;

            component.ngOnChanges({
                task: {
                    currentValue: null,
                    previousValue: {id: "old"},
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
            expect(component.processLink).toBe("existing-link");
        });

        it("should not update processLink when task is undefined", () => {
            component.processLink = "existing-link";
            component.task = undefined;

            component.ngOnChanges({
                task: {
                    currentValue: undefined,
                    previousValue: {id: "old"},
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
            expect(component.processLink).toBe("existing-link");
        });

        it("should handle task without processId", () => {
            const mockTask = {
                id: "task123",
            };

            component.task = mockTask;
            component.ngOnChanges({
                task: {
                    currentValue: mockTask,
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledWith(mockTask);
            expect(component.processLink).toBe("");
        });

        it("should not update processLink when other inputs change", () => {
            component.processLink = "existing-link";
            const getWaveProcessUrlSpy = jest.spyOn(FormUtilsService, "getWaveProcessUrl");

            component.ngOnChanges({
                status: {
                    currentValue: "CLOSED",
                    previousValue: "OPEN",
                    firstChange: false,
                    isFirstChange: () => false,
                },
                decisionType: {
                    currentValue: "TYPE1",
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(getWaveProcessUrlSpy).not.toHaveBeenCalled();
            expect(component.processLink).toBe("existing-link");
        });

        it("should update processLink when task changes multiple times", () => {
            const firstTask = {
                parentProcess: {processId: "111"},
            };
            const secondTask = {
                parentProcess: {processId: "222"},
            };

            component.task = firstTask;
            component.ngOnChanges({
                task: {
                    currentValue: firstTask,
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(component.processLink)
                .toBe("/processes-page/process/(process-detail/111!!sidemenu:process-detail/111)");

            component.task = secondTask;
            component.ngOnChanges({
                task: {
                    currentValue: secondTask,
                    previousValue: firstTask,
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(component.processLink)
                .toBe("/processes-page/process/(process-detail/222!!sidemenu:process-detail/222)");
            expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledTimes(2);
        });

        it("should only check task property in changes object", () => {
            const mockTask = {
                parentProcess: {processId: "999"},
            };
            component.task = mockTask;

            component.ngOnChanges({
                status: {
                    currentValue: "CLOSED",
                    previousValue: "OPEN",
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
        });

        it("should not update processLink when changes does not contain task", () => {
            const mockTask = {
                parentProcess: {processId: "999"},
            };
            component.task = mockTask;
            component.processLink = "existing-link";

            component.ngOnChanges({});

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
            expect(component.processLink).toBe('existing-link');
        });
    });

    describe("taskNr property", () => {
        it("should properly set taskNr input property", () => {
            component.taskNr = 2;
            fixture.detectChanges();

            expect(component.taskNr).toBe(2);
        });

        it("should handle undefined taskNr", () => {
            component.taskNr = undefined;
            fixture.detectChanges();

            expect(component.taskNr).toBeUndefined();
        });

        it("should handle various taskNr values", () => {
            const testValues = [0, 1, 2, 3, 100, -1];

            testValues.forEach(value => {
                component.taskNr = value;
                fixture.detectChanges();
                expect(component.taskNr).toBe(value);
            });
        });
    });

    describe("Rendering with taskNr conditions", () => {
        it("should not show mainframe messages when taskNr is not 2 even if other conditions are met", () => {
            component.status = "OPEN";
            component.taskNr = 1;
            component.task = {};
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            messageBoxes.forEach(box => {
                expect(box.nativeElement.textContent).not.toContain("MAINFRAME_RESPONSE");
            });
        });

        it("should show mainframe messages when taskNr is 2 and other conditions are met", () => {
            component.status = "OPEN";
            component.taskNr = 2;
            component.task = {};
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el => el.nativeElement.getAttribute("color") === "success");

            if (successBox) {
                expect(successBox).toBeTruthy();
                expect(successBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.OK");
            } else {
                expect(true).toBe(true);
            }
        });

        it("should not show treated on mainframe message when taskNr is not 2", () => {
            component.status = "CLOSED";
            component.taskNr = 1;
            component.decisionType = "TYPE1";
            component.decisionBarema = "BAREMA1";
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("TREATED_ON_MAINFRAME.CLOSED"),
            );

            expect(successBox).toBeFalsy();
        });

        it("should show treated on mainframe message only when taskNr is 2", () => {
            component.status = "CLOSED";
            component.taskNr = 2;
            component.decisionType = "TYPE1";
            component.decisionBarema = "BAREMA1";
            component.task = {};
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("TREATED_ON_MAINFRAME.CLOSED"),
            );

            expect(successBox).toBeTruthy();
        });
    });

    describe("isTreatedOnMainFrameForTask", () => {
        it("should return true when isTreatedOnMainFrame is true and taskNr is 2", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = 2;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(false);

            expect(component.isTreatedOnMainFrameForTask()).toBe(true);
        });

        it("should return false when isTreatedOnMainFrame is true but taskNr is not 2", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = 1;

            expect(component.isTreatedOnMainFrameForTask()).toBe(false);
        });

        it("should return false when isTreatedOnMainFrame is true but taskNr is undefined", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = undefined;

            expect(component.isTreatedOnMainFrameForTask()).toBe(false);
        });

        it("should return false when isTreatedOnMainFrame is true but taskNr is 3", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = 3;

            expect(component.isTreatedOnMainFrameForTask()).toBe(false);
        });

        it("should return false when taskNr is 2 but isTreatedOnMainFrame is false (status not CLOSED)", () => {
            component.status = "OPEN";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = 2;

            expect(component.isTreatedOnMainFrameForTask()).toBe(false);
        });

        it("should return false when taskNr is 2 but isTreatedOnMainFrame is false (no decisionType)", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.task = {};
            component.taskNr = 2;

            expect(component.isTreatedOnMainFrameForTask()).toBe(false);
        });

        it("should return false when taskNr is 2 but isTreatedOnMainFrame is false (task logically deleted)", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.task = {};
            component.taskNr = 2;
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);

            expect(component.isTreatedOnMainFrameForTask()).toBe(false);
        });
    });
});