apiVersion: mocks.microcks.io/v1alpha1
kind: APIExamples
metadata:
  name: C9 REST API
  version: '1.0.0'
operations:
  'GET /c9s/{id}':
    C9WithEc1:
      request:
        parameters:
          id: 123
      response:
        status: '200'
        mediaType: application/json
        body:
          id: 12345
          unemploymentOffice: 71
          paymentInstitution: 4
          type: "410"
          opKey: "344015078"
          sectOp: "1340"
          requestDate: 2022-09-22
          introductionDate: 2022-09-22
          dateValid: 2022-09-23
          treatmentStatus: READY_TO_BE_TREATED
          scanNumber: 951345
          operatorCode: 5749
          ssin: "18031307065"
          entityCode: "921"
          attestRefs:
            - type: EC1
              id: 12340
              url: https://c9.test.paas.onemrva.priv/api/eC1s/12340
              displayUrl: https://c9.test.paas.onemrva.priv/api/eC1s/ref2/99999999999/344015078
    C9WithoutEc1:
      request:
        parameters:
          id: 1234
      response:
        status: '200'
        mediaType: application/json
        body:
          id: 1234
          unemploymentOffice: 71
          paymentInstitution: 4
          type: "820"
          opKey: "344015078"
          sectOp: "1340"
          requestDate: 2022-09-22
          introductionDate: 2022-09-22
          dateValid: 2022-09-23
          treatmentStatus: RECEIVED
          scanNumber: 951345
          operatorCode: 5749
          ssin: "18031307065"
          entityCode: "921"
          attestRefs: [ ]

  'GET /eC1s/{id}':
    EC1WithCitizenInfo:
      request:
        parameters:
          id: 12341
      response:
        status: '200'
        mediaType: application/json
        body:
          version: "20220916"
          completeUnemployment: true
          temporaryUnemployment: false
          identity:
            inss: "93033157680"
            lastName: LIEGEOIS
            firstName: CATHERINE
            street: Pasteur
            houseNumber: "37"
            boxNumber: null
            zipCode:
              zipCode: "1000"
              descFr: Bruxelles
              descNl: Brussel
            city: "92250"
            country:
              code: 150
              descFr: "Belgique "
              descNl: "België "
            dateOfBirth: 1975-06-09
            nationality: null
            language: null
            phoneNumber: null
            email: null
          reasonIntroduction:
            askAllowanceFromDate: 2022-09-22
            hasAlternateTrainingAndTemporaryUnemployment: false
            isAskingAllowanceFirstTime: false
            changeOfPaymentOfficeFromDate: 2022-09-22
            hasDeclareModificationAbout: true
            changeMyAddressFromDate: 2022-09-22
            hasChangeDeductionTradeUnionContribution: null
            hasChangeResidenceOrWorkPermit: null
            changeSituationPersonalOrFamilyFromDate: 2022-09-22
            changeModeOfPaymentOrAccountNrFromDate: 2022-09-22
          familySituation:
            cohabitingPerson:
              persons:
                - lastName: LIEGEOIS
                  firstName: lionnel
                  affinity: Husband-wife
                  dateOfBirth: 1990-06-09
                  hasChildSupport: true
                  professionalActivities:
                    - natureProfessionalActivity: Independent
                      grossMonthlyProfessionalIncome: "1582.33"
                  replacementIncomes:
                    - natureReplacementIncome: UnemploymentBenefits
                      grossMonthlyReplacementIncome: "500.33"
                  financiallyDependent: true
                  isDeclaringC1Partner: true
                - lastName: LIEGEOIS
                  firstName: sami
                  affinity: Son-daughter
                  dateOfBirth: 1990-06-09
                  hasChildSupport: true
                  professionalActivities:
                    - natureProfessionalActivity: Independent
                      grossMonthlyProfessionalIncome: "1582.33"
                  replacementIncomes:
                    - natureReplacementIncome: UnemploymentBenefits
                      grossMonthlyReplacementIncome: "500.33"
                  financiallyDependent: false
                  isDeclaringC1Partner: null
              remarks: Remarque family situation
          activity:
            hasFullStudiesFromDate: 2022-09-22
            hasFollowApprenticeshipOrWorkLinkedTrainingFromDate: 2022-09-22
            hasFollowATrainingWithAnInternshipFromDate: 2022-09-22
            hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory: true
            isFistTimeIndependentAndJoinC46: false
            hasPracticePoliticalMandate: true
            hasDeclareModificationJoinC1A: false
            hasBenefitFromArtisticOrTechnicActivities: true
            hasComplementaryIndependentActivity: true
            isFistTimeIndependentAndJoinC1C: false
            hasPracticeComplementaryActivityOrHelpIndependent: true
            isAdministratorOfCompany: true
            hasRegisterAsIndependentComplementaryOrMain: true
          financialSituation:
            fullRetirement: true
            retirementOldAgeOrSurvivorPension: false
            diseaseBenefitOrDisability: true
            occupationalAccidentOrDiseaseBenefit: true
            declareModificationJoinC1B: true
            financialBenefitFollowingFormationOrStudy: false
          modeOfPayment:
            hasTransferMoneyOnBankAccount: true
            isMyBankAccount: true
            isNotMyBankAccount: false
            bankAccountForOtherPersonName: null
            belgianSEPABankAccount: null
            foreignBankAccountIBAN: ********************
            foreignBankAccountBIC: CMRPL
          tradeUnionContribution:
            contributionDeductionFromTheMonth: 2022-09-22
            stopContributionDeductionFromTheMonth: null
          foreignWorker:
            hasStatuteOfRefugeePerson: false
            isRecognizedStatelessPerson: false
            hasHaveResidencePermit: true
            hasHaveUnlimitedAccessToWorkMarket: false
            hasHaveLimitedAccessToWorkMarket: true
            description: droit de travail limite
            hasNotAccessToWorkMarket: false
          various:
            vacationWithoutSalary: true
            vacationStartDate: 2022-03-22
            vacationEndDate: 2022-09-22
            unableToWork33Percent: false
          declarationAndSignature:
            certificateFromMinistryOfSocialPrecaution: true
            copyPensionReceipt: true
            formC1AnnexeRegis: true
            copyResidencePermitOrPermitWorkMarket: true
            otherDeclarationExplanation: Other declaration
            declarationOfHonor: true
            readInformationForm: true
            communicationOfModificationToMyPaymentOffice: true
          signature:
            signatureXml: null
            isValid: false
            signatureDate: null
            errorMessage: null
          error: null

  'POST /mainframe/navigate/S24':
    S24Success:
      request:
        mediaType: application/json
        body:
          ssin: "18031307065"
          dateValid: "2025-01-01"
      response:
        status: '200'
        mediaType: application/json
        body:
          status: "NAVIGATION_SUCCESS"
          detail: "Successfully navigated to S24 screen for SSIN 18031307065"
