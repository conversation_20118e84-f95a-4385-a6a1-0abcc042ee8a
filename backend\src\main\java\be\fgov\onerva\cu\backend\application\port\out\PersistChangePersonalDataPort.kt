package be.fgov.onerva.cu.backend.application.port.out

import java.util.*
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataPersistCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataUpdateCommand
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand

/**
 * Port interface for persisting change of address requests.
 * This interface defines operations to store change of address information in the persistence layer.
 */
interface PersistChangePersonalDataPort {

    fun getChangePersonalDataByC9Id(c9Id: Long): ChangePersonalDataRequest?

    fun getChangePersonalDataById(requestId: UUID): ChangePersonalDataRequest?

    /**
     * Persists a change of address request to the storage system.
     *
     * @param changePersonalDataPersistCommand The change of address data to be persisted
     * @return The persisted [ChangePersonalDataRequest] entity with any additional system-generated data
     *
     * @throws PersistenceException If there is an error during the persistence operation
     */
    fun persistChangePersonalData(changePersonalDataPersistCommand: ChangePersonalDataPersistCommand): ChangePersonalDataRequest

    fun updateChangePersonalDataWithDecision(
        requestId: UUID,
        updateChangePersonalDataDecisionCommand: UpdateChangePersonalDataDecisionCommand,
    )

    fun updateChangePersonalDataWithC9EnvelopeData(requestId: UUID, command: ChangePersonalDataUpdateCommand)
}