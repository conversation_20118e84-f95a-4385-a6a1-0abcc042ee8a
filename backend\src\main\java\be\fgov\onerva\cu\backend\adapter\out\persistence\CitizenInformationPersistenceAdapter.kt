package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainCitizenInformation
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.Address
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationRepository
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

@Service
@Transactional
class CitizenInformationPersistenceAdapter(
    val changePersonalDataRepository: ChangePersonalDataRepository,
    val citizenInformationRepository: CitizenInformationRepository,
) : CitizenInformationPort {
    val log = logger

    @LogMethodCall
    override fun persistCitizenInformation(
        requestId: UUID,
        @SensitiveParam citizenInformation: CitizenInformation,
    ) {
        val changePersonalData = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")

        var citizenInformationEntity = changePersonalData.citizenInformation
        if (citizenInformationEntity == null) {
            citizenInformationEntity = CitizenInformationEntity(
                birthDate = citizenInformation.birthDate,
                nationalityCode = citizenInformation.nationalityCode,
                firstName = citizenInformation.firstName,
                lastName = citizenInformation.lastName,
                address = Address(
                    street = citizenInformation.address.street,
                    houseNumber = citizenInformation.address.houseNumber,
                    zipCode = citizenInformation.address.zipCode,
                    city = citizenInformation.address.city,
                    countryCode = citizenInformation.address.countryCode,
                    boxNumber = citizenInformation.address.boxNumber,
                    validFrom = citizenInformation.address.validFrom,
                ),
                request = changePersonalData,
                updateStatus = UpdateStatus.EDITED,
            )
            citizenInformationRepository.save(citizenInformationEntity)
        } else {
            citizenInformationEntity.apply {
                birthDate = citizenInformation.birthDate
                nationalityCode = citizenInformation.nationalityCode
                address = Address(
                    street = citizenInformation.address.street,
                    houseNumber = citizenInformation.address.houseNumber,
                    zipCode = citizenInformation.address.zipCode,
                    city = citizenInformation.address.city,
                    countryCode = citizenInformation.address.countryCode,
                    boxNumber = citizenInformation.address.boxNumber,
                    validFrom = citizenInformation.address.validFrom,
                )
                updateStatus = UpdateStatus.EDITED
            }
        }
    }

    @LogMethodCall
    override fun getCitizenInformation(requestId: UUID): CitizenInformation? {
        changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")

        return citizenInformationRepository.findByRequestId(requestId)?.toDomainCitizenInformation()
    }

    override fun getLatestRevision(requestId: UUID): Int {
        val id = citizenInformationRepository.findByRequestId(requestId)?.id
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")
        return citizenInformationRepository.findLastChangeRevision(id).getOrNull()?.revisionNumber?.getOrNull() ?: 0
    }

    override fun getCitizenInformationForRevision(requestId: UUID, revision: Int): CitizenInformation? {
        val id = getEntityId(requestId)
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")
        return citizenInformationRepository.findRevision(id, revision).orElseThrow {
            InvalidRequestIdException("Revision not found for request $requestId and revision $revision")
        }.entity.toDomainCitizenInformation()
    }

    override fun getEntityId(requestId: UUID): UUID {
        return citizenInformationRepository.findByRequestId(requestId)?.id
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")
    }

    override fun patchCurrentDataWithRevision(requestId: UUID, revision: Int) {
        log.info("Patching current data for request ID: $requestId with the latest revision data")

        val currentData = citizenInformationRepository.findByRequestId(requestId)

        if (currentData == null) {
            throw InvalidRequestIdException("Request ID not found: $requestId")
        }

        val latestRevision = citizenInformationRepository.findRevision(currentData.id, revision)

        currentData.apply {
            this.firstName = latestRevision.get().entity.firstName
            this.lastName = latestRevision.get().entity.lastName
            this.address = latestRevision.get().entity.address
            this.birthDate = latestRevision.get().entity.birthDate
            this.nationalityCode = latestRevision.get().entity.nationalityCode
            this.updateStatus = UpdateStatus.EDITED
        }

        log.info("Patching complete. Updated citizen data")

        citizenInformationRepository.save(currentData)
    }
}