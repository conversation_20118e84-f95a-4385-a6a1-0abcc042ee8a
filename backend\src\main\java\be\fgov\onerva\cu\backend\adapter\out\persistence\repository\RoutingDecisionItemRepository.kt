package be.fgov.onerva.cu.backend.adapter.out.persistence.repository

import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.data.repository.query.Param
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RoutingDecisionItemEntity

interface RoutingDecisionItemRepository : JpaRepository<RoutingDecisionItemEntity, UUID>,
    RevisionRepository<RoutingDecisionItemEntity, UUID, Int> {

    fun findByRequestId(@Param("requestId") requestId: UUID): List<RoutingDecisionItemEntity>

    fun deleteByRequestId(@Param("requestId") requestId: UUID)
}