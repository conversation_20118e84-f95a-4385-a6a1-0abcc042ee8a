package be.fgov.onerva.cu.backend.adapter.`in`.queue

import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.adapter.`in`.queue.model.PersonUpdatedPayload
import be.fgov.onerva.cu.backend.application.domain.PersonUpdated
import be.fgov.onerva.cu.backend.application.port.`in`.PersonUpdatedUseCase
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.rabbitmq.consumer.config.RabbitConsumer

/**
 * Consumer for person-related events from the RabbitMQ message queue.
 *
 * This class acts as an inbound adapter in the hexagonal architecture, receiving
 * messages from the RabbitMQ queue about person updates and forwarding them to the
 * appropriate use case. It specifically listens to the "cu.person.queue" and processes
 * "PersonUpdated" events.
 *
 * The consumer is configured to connect to the "person.exchange" and is active in all
 * profiles except "ci" and "unit" to prevent it from running during tests.
 *
 * @property personUpdatedUseCase The use case that handles person updated events
 */
@Service
@RabbitConsumer(exchangeName = "person.exchange")
@Profile("!ci & !unit")
class PersonQueueConsumer(val personUpdatedUseCase: PersonUpdatedUseCase) {
    private val log = logger

    /**
     * Processes messages received from the person queue.
     *
     * This method is triggered when a message arrives in the "cu.person.queue". It logs
     * the received message details and, if the message is of type "PersonUpdated",
     * forwards the correlation ID to the appropriate use case for processing.
     *
     * @param personUpdatedPayload The deserialized payload from the message
     * @param amqpMessage The raw AMQP message (optional)
     * @throws Exception If an error occurs during message processing, it is logged and rethrown
     */
    @RabbitListener(queues = ["cu.person.queue"])
    fun onPersonEventReceivedFromQueue(personUpdatedPayload: PersonUpdatedPayload, amqpMessage: Message?) {
        try {
            log.info(
                "Message received from person id: {} - type: {}.",
                personUpdatedPayload.id,
                personUpdatedPayload.type
            )

            if (personUpdatedPayload.type == "be.fgov.onerva.person.msg.v1.PersonUpdated") {
                log.info("Received person updated event: {}", personUpdatedPayload.data.correlationId)
                personUpdatedUseCase.receivedPersonUpdated(
                    PersonUpdated(
                        id = personUpdatedPayload.data.id,
                        correlationId =
                            personUpdatedPayload.data.correlationId,
                        ssin = personUpdatedPayload.data.ssin,
                        success = personUpdatedPayload.data.success,
                        names = personUpdatedPayload.data.names,
                        errorCode = personUpdatedPayload.data.errorCode,
                    )
                )
            }
        } catch (e: Exception) {
            log.error(
                "Error processing message from person id: {} - type: {}",
                personUpdatedPayload.id,
                personUpdatedPayload.type,
                e
            )
            throw e
        }
    }
}