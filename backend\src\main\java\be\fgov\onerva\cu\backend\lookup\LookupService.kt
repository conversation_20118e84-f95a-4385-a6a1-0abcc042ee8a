package be.fgov.onerva.cu.backend.lookup

/**
 * Service interface for lookup operations on geographic and administrative reference data.
 * 
 * This service provides cached access to lookup data from the external WPPT lookup API,
 * including countries, nationalities, cities, streets, and postal code language regimes.
 * All data is cached in memory and refreshed periodically via scheduled updates.
 */
interface LookupService {
    
    /**
     * Searches for countries matching the given query.
     * 
     * @param searchQuery Optional search term to filter results by French description, 
     *                   Dutch description, or exact code match. If null, returns all countries.
     * @return List of matching countries
     */
    fun lookupCountry(searchQuery: String?): List<CountryDTO>

    /**
     * Searches for nationalities matching the given query.
     * 
     * @param searchQuery Optional search term to filter results by French description,
     *                   Dutch description, or exact code match. If null, returns all nationalities.
     * @return List of matching nationalities
     */
    fun lookupNationality(searchQuery: String?): List<NationalityDTO>

    /**
     * Searches for cities matching the given query.
     * 
     * @param searchQuery Optional search term to filter results by French description,
     *                   Dutch description, or exact code match. If null, returns all cities.
     * @return List of matching cities
     */
    fun lookupCity(searchQuery: String?): List<CityDTO>

    /**
     * Retrieves street information by postal code and street code.
     * 
     * @param postalCode The postal code of the area
     * @param streetCode The numeric street code within the postal code area
     * @return Street information if found, null otherwise
     */
    fun getStreetNameByStreetCode(postalCode: String, streetCode: Int): StreetDTO?

    /**
     * Converts an ONEM country code to the corresponding nationality code.
     * 
     * @param onemCountryCode The ONEM country code to convert
     * @return The corresponding nationality code, or null if no matching nationality found.
     *         If multiple nationalities exist for the same country code, returns the one with the lowest code.
     */
    fun getNationalityCodeFromOnemCountryCode(onemCountryCode: String): Int?

    /**
     * Converts a nationality code to the corresponding ONEM country code.
     * 
     * @param nationalityCode The nationality code to convert
     * @return The corresponding ONEM country code, or null if nationality not found
     */
    fun getOnemCountryCodeFromNationalityCode(nationalityCode: Int): String?

    /**
     * Retrieves city information by postal code and NIS code.
     * 
     * @param postalCode The postal code
     * @param nisCode The National Institute for Statistics code
     * @return City information if found, null otherwise
     */
    fun getCityByPostalCode(postalCode: String, nisCode: Int): CityDTO?

    /**
     * Retrieves nationality information by nationality code.
     * 
     * @param nationalityCode The nationality code
     * @return Nationality information if found, null otherwise
     */
    fun getNationalityByCode(nationalityCode: Int): NationalityDTO?

    /**
     * Retrieves country information by country code.
     * 
     * @param countryCode The country code
     * @return Country information if found, null otherwise
     */
    fun getCountryByCode(countryCode: Int): CountryDTO?

    /**
     * Retrieves postal code language regime information by postal code.
     * 
     * @param searchQuery The postal code to search for
     * @return Postal code language regime information if found, null otherwise
     */
    fun lookupPostalCodeLanguageRegime(searchQuery: String?): PostalCodeLanguageRegimeDTO?
}