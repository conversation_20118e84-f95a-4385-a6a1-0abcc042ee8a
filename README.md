# cu

## Purpose

-- _Insert here the purpose of this project_ --

## Project composition

This project has the following components:



* backend - SPRING_BOOT

* bff - SPRING_BOOT

* frontnend - ANGULAR_WEB_COMPONENT
  

Default credentials: 
* user: `cu_user`
* password: `password`

## Run
To run the project you have two solutions:

* Start the infra (keycloak and database) first then the app by using
    ```shell
    skaffold dev -m infra
    ```
    and in another shell start the application
    ```shell
    skaffold dev -m app
    ```
* Start the whole project ine one command
    ```shell
    skaffold dev
    ```

## Database Migration (Flyway)

This project uses Flyway for database schema versioning and migration. We migrated from Liquibase to Flyway to simplify
our database management.

### Migration Structure

```
backend/src/main/resources/db/migration/
├── dev/
│   └── V1_0__Initial_schema.sql     # Complete schema for new environments
└── common/
    └── V1_1__rename_constraints.sql  # Incremental changes for all environments
    └── V1_2__Your_next_change.sql   # Future migrations go here
```

### Environment Configuration

The application uses Spring profiles to manage different migration paths:

- **Development/CI** (`-Dspring.profiles.active=dev` or `ci`):
  - Runs migrations from both `dev/` and `common/` folders
  - Starts with complete schema (V1_0)

- **Test/Val/Prod** (default profile):
  - Only runs migrations from `common/` folder
  - Baselined at version V1.0 (skips initial schema)

### Creating New Migrations

1. All new migrations go in the `common/` folder
2. Use sequential versioning: `V1_2__Description.sql`, `V1_3__Description.sql`, etc.
3. Follow naming conventions:

- Start with `V` followed by version number (e.g., `V1_2`)
- Use double underscore `__` after version
- Use underscores in description: `Add_user_email_column` not `Add-user-email-column`
- Be descriptive but concise

### Local Development

No special setup required. The application automatically uses the dev profile locally, which includes both migration
paths.

### Important Notes

- **Never modify an existing migration** once it's been applied
- **Never manually modify** the `flyway_schema_history` table
- The old Liquibase tables (`databasechangelog`, `databasechangeloglock`) remain in existing databases but are no longer
  used
- Test migrations locally before committing

### Troubleshooting

If you encounter migration issues:

1. Check the `flyway_schema_history` table to see applied migrations
2. Ensure your migration version number is higher than the latest applied
3. Verify SQL syntax is compatible with SQL Server
4. Check Spring profile is set correctly for your environment