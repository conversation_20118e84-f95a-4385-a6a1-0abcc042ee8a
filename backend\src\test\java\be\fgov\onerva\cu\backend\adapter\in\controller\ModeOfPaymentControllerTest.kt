package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.backend.application.port.`in`.ModeOfPaymentUseCase
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateModeOfPaymentRequest
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class ModeOfPaymentControllerTest {

    @MockK
    lateinit var modeOfPaymentUseCase: ModeOfPaymentUseCase

    @InjectMockKs
    lateinit var modeOfPaymentController: ModeOfPaymentController

    @Nested
    inner class GetModeOfPayment {

        @Test
        fun `getModeOfPayment should return mode of payment when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val modeOfPayment = ModeOfPayment(
                otherPersonName = "John Doe",
                iban = "****************",
                bic = "BBRUBEBB",
                validFrom = LocalDate.of(2025, 1, 1)
            )

            every { modeOfPaymentUseCase.getModeOfPayment(requestId) } returns modeOfPayment
            every { modeOfPaymentUseCase.getModeOfPaymentFieldSources(requestId) } returns listOf(
                FieldSource(
                    "otherPersonName",
                    ExternalSource.C1
                )
            )

            // When
            val result = modeOfPaymentController.getModeOfPayment(requestId)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.otherPersonName).isEqualTo("John Doe")
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isEqualTo("BBRUBEBB")
            assertThat(result.validFrom).isEqualTo(LocalDate.of(2025, 1, 1))
        }

        @Test
        fun `getModeOfPayment should throw when not found`() {
            // Given
            val requestId = UUID.randomUUID()
            every { modeOfPaymentUseCase.getModeOfPayment(requestId) } returns null

            // When / Then
            assertThatThrownBy {
                modeOfPaymentController.getModeOfPayment(requestId)
            }.isInstanceOf(InformationNotFoundException::class.java)
                .hasMessage("Mode of payment not found for request $requestId")
        }
    }

    @Nested
    inner class UpdateModeOfPayment {

        @Test
        fun `updateModeOfPayment should successfully update mode of payment`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = UpdateModeOfPaymentRequest().apply {
                otherPersonName = "John Doe"
                iban = "****************"
                bic = "BBRUBEBB"
                validFrom = LocalDate.of(2025, 1, 1)
            }

            every { modeOfPaymentUseCase.updateModeOfPayment(requestId, any()) } returns Unit

            // When
            modeOfPaymentController.updateModeOfPayment(requestId, request)

            // Then
            verify(exactly = 1) { modeOfPaymentUseCase.updateModeOfPayment(requestId, any()) }
        }

        @Test
        fun `updateModeOfPayment should throw InvalidInputException when request is null`() {
            // Given
            val requestId = UUID.randomUUID()

            // When/Then
            assertThatThrownBy { modeOfPaymentController.updateModeOfPayment(requestId, null) }
                .isInstanceOf(InvalidInputException::class.java)
                .hasMessage("updateModeOfPaymentRequest is required")
        }
    }

    @Nested
    inner class SelectModeOfPaymentSources {
        @Test
        fun `selectModeOfPaymentSources should call use case with mapped field sources`() {
            // Given
            val requestId = UUID.randomUUID()
            val selectRequest = SelectFieldSourcesRequest().apply {
                fieldSources = listOf(
                    be.fgov.onerva.cu.rest.priv.model.FieldSource().apply {
                        fieldName = "iban"
                        source = be.fgov.onerva.cu.rest.priv.model.ExternalSource.C1
                    },
                    be.fgov.onerva.cu.rest.priv.model.FieldSource().apply {
                        fieldName = "otherPersonName"
                        source = be.fgov.onerva.cu.rest.priv.model.ExternalSource.ONEM
                    }
                )
            }

            val fieldSourcesSlot = slot<List<FieldSource>>()
            every {
                modeOfPaymentUseCase.selectModeOfPaymentFieldSources(requestId, capture(fieldSourcesSlot))
            } returns Unit

            // When
            modeOfPaymentController.selectModeOfPaymentSources(requestId, selectRequest)

            // Then
            verify(exactly = 1) {
                modeOfPaymentUseCase.selectModeOfPaymentFieldSources(eq(requestId), eq(fieldSourcesSlot.captured))
            }

            // Verify the captured content
            val capturedFieldSources = fieldSourcesSlot.captured
            assertThat(capturedFieldSources).hasSize(2)
            assertThat(capturedFieldSources[0].fieldName).isEqualTo("iban")
            assertThat(capturedFieldSources[0].source).isEqualTo(ExternalSource.C1)
            assertThat(capturedFieldSources[1].fieldName).isEqualTo("otherPersonName")
            assertThat(capturedFieldSources[1].source).isEqualTo(ExternalSource.ONEM)
        }
    }
}