package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UpdateModeOfPayment
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.`in`.HistoricalInformationUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.ModeOfPaymentUseCase
import be.fgov.onerva.cu.backend.application.port.out.FieldSourcePort
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import be.fgov.onerva.cu.backend.application.validation.UniqueFieldSources
import be.fgov.onerva.cu.backend.application.validation.ValidFieldNames
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam

@Service
@Validated
@BusinessTransaction
class ModeOfPaymentService(
    val modeOfPaymentPort: ModeOfPaymentPort,
    val fieldSourcePort: FieldSourcePort,
    val historicalInformationUseCase: HistoricalInformationUseCase,
) : ModeOfPaymentUseCase {

    // Entity type constant for field source tracking
    companion object {
        const val ENTITY_TYPE = "mode_of_payment"
    }

    @LogMethodCall
    override fun getModeOfPayment(requestId: UUID): ModeOfPayment? =
        modeOfPaymentPort.getModeOfPayment(requestId)

    @LogMethodCall
    override fun updateModeOfPayment(
        requestId: UUID,
        @Valid @SensitiveParam updateModeOfPayment: UpdateModeOfPayment,
    ) {
        // Create the domain object without source field
        val modeOfPayment = ModeOfPayment(
            otherPersonName = updateModeOfPayment.otherPersonName,
            iban = updateModeOfPayment.iban,
            bic = updateModeOfPayment.bic,
            validFrom = updateModeOfPayment.validFrom
        )

        // Persist the mode of payment
        modeOfPaymentPort.persistModeOfPayment(requestId, modeOfPayment)
    }

    /**
     * Get all field sources for a mode of payment entity
     */
    @LogMethodCall
    override fun getModeOfPaymentFieldSources(requestId: UUID): List<FieldSource> {
        val entityId = modeOfPaymentPort.getEntityId(requestId)
        return fieldSourcePort.getAllFieldSources(ENTITY_TYPE, entityId)
    }

    /**
     * Select field sources for mode of payment fields
     */
    @LogMethodCall
    override fun selectModeOfPaymentFieldSources(
        requestId: UUID,
        @Valid
        @UniqueFieldSources
        @ValidFieldNames(names = ["account", "otherPersonName"])
        fieldSources: List<FieldSource>,
    ) {
        val modeOfPayment = getModeOfPayment(requestId)
            ?: throw RequestIdNotFoundException("Mode of payment not found for request ID: $requestId")
        val c1ModeOfPayment = historicalInformationUseCase.getModeOfPayment(requestId, ExternalSource.C1)
        val onemCitizenInformation = historicalInformationUseCase.getHistoricalCitizenOnem(requestId)
        var iban: String? = null
        var bic: String? = null
        var otherPersonName: String? = null
        var validFrom: LocalDate? = null

        fieldSources.forEach { fieldSource ->
            when (fieldSource.fieldName) {
                "account" -> {
                    when (fieldSource.source) {
                        ExternalSource.C1 -> {
                            iban = c1ModeOfPayment.iban
                            bic = c1ModeOfPayment.bic
                            validFrom = c1ModeOfPayment.validFrom
                        }

                        ExternalSource.ONEM -> {
                            iban = onemCitizenInformation.iban
                            bic = onemCitizenInformation.bic
                            validFrom = onemCitizenInformation.effectiveDate
                        }

                        ExternalSource.AUTHENTIC_SOURCES -> throw InvalidExternalDataException("Authentic sources does not provide bank account")
                    }
                }

                "otherPersonName" -> {
                    otherPersonName = when (fieldSource.source) {
                        ExternalSource.C1 -> c1ModeOfPayment.otherPersonName
                        ExternalSource.ONEM -> onemCitizenInformation.otherPersonName
                        ExternalSource.AUTHENTIC_SOURCES -> throw InvalidExternalDataException("Authentic sources does not provide other person name")
                    }
                }
            }
        }

        modeOfPaymentPort.persistModeOfPayment(
            requestId, ModeOfPayment(
                otherPersonName = otherPersonName ?: modeOfPayment.otherPersonName,
                iban = iban ?: modeOfPayment.iban,
                bic = bic ?: modeOfPayment.bic,
                validFrom = validFrom ?: modeOfPayment.validFrom
            )
        )

        val entityId = modeOfPaymentPort.getEntityId(requestId)
        fieldSourcePort.setMultipleFieldSources(ENTITY_TYPE, entityId, fieldSources)
    }
}