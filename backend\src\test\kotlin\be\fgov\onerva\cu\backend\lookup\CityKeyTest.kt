package be.fgov.onerva.cu.backend.lookup

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class CityKeyTest {

    @Nested
    inner class `Equality tests` {

        @Test
        fun `should be equal when nisCode and code are the same`() {
            // Given
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("21001", "1000")

            // When & Then
            assertThat(key1).isEqualTo(key2)
            assertThat(key1.hashCode()).isEqualTo(key2.hashCode())
        }

        @Test
        fun `should not be equal when nisCode differs`() {
            // Given
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("11001", "1000")

            // When & Then
            assertThat(key1).isNotEqualTo(key2)
        }

        @Test
        fun `should not be equal when code differs`() {
            // Given
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("21001", "2000")

            // When & Then
            assertThat(key1).isNotEqualTo(key2)
        }

        @Test
        fun `should not be equal when both nisCode and code differ`() {
            // Given
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("11001", "2000")

            // When & Then
            assertThat(key1).isNotEqualTo(key2)
        }

        @Test
        fun `should not be equal to null`() {
            // Given
            val key = CityKey("21001", "1000")

            // When & Then
            assertThat(key).isNotEqualTo(null)
        }

        @Test
        fun `should not be equal to different type`() {
            // Given
            val key = CityKey("21001", "1000")
            val other = "21001-1000"

            // When & Then
            assertThat(key).isNotEqualTo(other)
        }

        @Test
        fun `should be equal to itself`() {
            // Given
            val key = CityKey("21001", "1000")

            // When & Then
            assertThat(key).isEqualTo(key)
        }
    }

    @Nested
    inner class `Hash code tests` {

        @Test
        fun `should have consistent hash code`() {
            // Given
            val key = CityKey("21001", "1000")

            // When
            val hash1 = key.hashCode()
            val hash2 = key.hashCode()

            // Then
            assertThat(hash1).isEqualTo(hash2)
        }

        @Test
        fun `should have same hash code for equal objects`() {
            // Given
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("21001", "1000")

            // When & Then
            assertThat(key1.hashCode()).isEqualTo(key2.hashCode())
        }

        @ParameterizedTest
        @CsvSource(
            "21001, 1000, 11001, 1000",
            "21001, 1000, 21001, 2000",
            "21001, 1000, 11001, 2000"
        )
        fun `should likely have different hash codes for different objects`(
            nisCode1: String, code1: String,
            nisCode2: String, code2: String
        ) {
            // Given
            val key1 = CityKey(nisCode1, code1)
            val key2 = CityKey(nisCode2, code2)

            // When & Then
            // Note: Hash codes can theoretically be the same for different objects,
            // but it's very unlikely with a good hash function
            assertThat(key1.hashCode()).isNotEqualTo(key2.hashCode())
        }
    }

    @Nested
    inner class `Map usage tests` {

        @Test
        fun `should work correctly as Map key`() {
            // Given
            val map = mutableMapOf<CityKey, String>()
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("21001", "1000") // Same values, different instance
            val key3 = CityKey("11001", "2000")

            // When
            map[key1] = "Brussels"
            map[key3] = "Antwerp"

            // Then
            assertThat(map[key1]).isEqualTo("Brussels")
            assertThat(map[key2]).isEqualTo("Brussels") // Should find same value
            assertThat(map[key3]).isEqualTo("Antwerp")
            assertThat(map.size).isEqualTo(2)
        }

        @Test
        fun `should replace value when using equivalent key`() {
            // Given
            val map = mutableMapOf<CityKey, String>()
            val key1 = CityKey("21001", "1000")
            val key2 = CityKey("21001", "1000") // Same values, different instance

            // When
            map[key1] = "Brussels"
            map[key2] = "Bruxelles"

            // Then
            assertThat(map[key1]).isEqualTo("Bruxelles")
            assertThat(map.size).isEqualTo(1)
        }
    }

    @Nested
    inner class `String representation tests` {

        @Test
        fun `should have meaningful toString representation`() {
            // Given
            val key = CityKey("21001", "1000")

            // When
            val result = key.toString()

            // Then
            assertThat(result).isEqualTo("CityKey(nisCode='21001', code='1000')")
        }

        @ParameterizedTest
        @CsvSource(
            "21001, 1000",
            "11001, 2000",
            "44021, 9000"
        )
        fun `should include both nisCode and code in toString`(nisCode: String, code: String) {
            // Given
            val key = CityKey(nisCode, code)

            // When
            val result = key.toString()

            // Then
            assertThat(result).contains(nisCode)
            assertThat(result).contains(code)
        }
    }
}
