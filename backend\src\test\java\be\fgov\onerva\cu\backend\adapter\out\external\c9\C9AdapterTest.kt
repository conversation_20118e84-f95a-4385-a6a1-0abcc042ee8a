package be.fgov.onerva.cu.backend.adapter.out.external.c9

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.EC1Info
import be.fgov.onerva.unemployment.c9.api.C9Api
import be.fgov.onerva.unemployment.c9.api.Ec1Api
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef
import be.fgov.onerva.unemployment.c9.rest.model.BelgianCommunity
import be.fgov.onerva.unemployment.c9.rest.model.C9
import be.fgov.onerva.unemployment.c9.rest.model.EC1
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment
import be.fgov.onerva.unemployment.c9.rest.model.EC1TradeUnionContribution
import be.fgov.onerva.unemployment.c9.rest.model.NationalityBCSS
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class C9AdapterTest {

    @MockK
    private lateinit var ec1Api: Ec1Api

    @MockK
    private lateinit var c9Api: C9Api

    @InjectMockKs
    private lateinit var c9Adapter: C9Adapter

    @Test
    fun `loadEC1 should map EC1 response correctly when all fields are present`() {
        // Given
        val requestDate = LocalDate.of(2024, 1, 1)
        val ec1Id = 12345
        val ec1Identity = EC1Identity().apply {
            inss = "12345678901"
            lastName = "Doe"
            firstName = "John"
            dateOfBirth = "1990-01-01"
            street = "Main Street"
            houseNumber = "42"
            city = "Brussels"
            zipCode = BelgianCommunity().apply {
                zipCode = "1000"
            }
            nationality = NationalityBCSS().apply {
                code = "150"
                descFr = "Belgian"
            }
            country = NationalityBCSS().apply {
                code = "111"
                descFr = "France"
            }
        }

        val ec1ModeOfPayment = EC1ModeOfPayment().apply {
            isMyBankAccount = true
            belgianSEPABankAccount = "****************"
        }

        val ec1TradeUnionContribution = EC1TradeUnionContribution().apply {
            contributionDeductionFromTheMonth = LocalDate.of(2024, 1, 1)
        }

        val ec1ReasonIntroduction = be.fgov.onerva.unemployment.c9.rest.model.EC1ReasonIntroduction().apply {
            changeMyAddressFromDate = LocalDate.of(2025, 1, 1)
        }

        val ec1 = EC1().apply {
            identity = ec1Identity
            modeOfPayment = ec1ModeOfPayment
            tradeUnionContribution = ec1TradeUnionContribution
            reasonIntroduction = ec1ReasonIntroduction
        }

        every { ec1Api.getC1s(ec1Id) } returns ec1

        // When
        val result = c9Adapter.loadEC1(ec1Id, requestDate)

        // Then
        assertThat(result).isNotNull
            .isInstanceOf(EC1Info::class.java)
        assertThat(result.citizenInformation).isNotNull
        assertThat(result.modeOfPayment).isNotNull
        assertThat(result.unionContribution).isNotNull

        verify(exactly = 1) { ec1Api.getC1s(ec1Id) }
    }

    @Test
    fun `loadEC1 should handle null fields in EC1 response`() {
        // Given
        val requestDate = LocalDate.of(2024, 1, 1)
        val ec1Id = 12345
        val ec1 = EC1().apply {
            identity = null
            modeOfPayment = null
            tradeUnionContribution = null
            reasonIntroduction = null
        }

        every { ec1Api.getC1s(ec1Id) } returns ec1

        // When
        val result = c9Adapter.loadEC1(ec1Id, requestDate)

        // Then
        assertThat(result).isNotNull
        assertThat(result.citizenInformation).isNull()
        assertThat(result.modeOfPayment).isNull()
        assertThat(result.unionContribution).isNull()

        verify(exactly = 1) { ec1Api.getC1s(ec1Id) }
    }

    @Test
    fun `loadEC1 should propagate exceptions from EC1Api`() {
        // Given
        val requestDate = LocalDate.of(2024, 1, 1)
        val ec1Id = 12345
        val exception = RuntimeException("API Error")

        every { ec1Api.getC1s(ec1Id) } throws exception

        // When/Then
        assertThatThrownBy { c9Adapter.loadEC1(ec1Id, requestDate) }
            .isInstanceOf(RuntimeException::class.java)
            .hasMessage("API Error")

        verify(exactly = 1) { ec1Api.getC1s(ec1Id) }
    }

    @Test
    fun `loadC9 should map C9 response correctly when EC1 attestRef is present`() {
        // Given
        val c9Id = 12345L
        val c9 = C9().apply {
            id = c9Id
            type = "400"
            scanUrl = "http://example.com/scan"
            ssin = "12345678901"
            this.requestDate = LocalDate.of(2021, 1, 1)
            this.introductionDate = LocalDate.of(2021, 1, 1)
            this.dateValid = LocalDate.of(2024, 12, 15)
            this.opKey = "OP123"
            this.sectOp = "SO123"
            this.paymentInstitution = 778899
            this.entityCode = "EC123"
            this.scanNumber = 778866L
            attestRefs = listOf(AttestRef().apply {
                id = 54321
                type = "EC1"
                displayUrl = "http://example.com/display"
            })
        }

        every { c9Api.getC9s(c9Id.toInt()) } returns c9

        // When
        val result = c9Adapter.loadC9(c9Id)

        // Then
        assertThat(result).isNotNull
            .extracting("c9Id", "scanUrl", "ssin")
            .containsExactly(c9Id, "http://example.com/scan", "12345678901")

        assertThat(result.ec1Id)
            .isNotNull
            .isEqualTo(54321)

        assertThat(result.ec1DisplayUrl)
            .isNotNull
            .isEqualTo("http://example.com/display")

        verify(exactly = 1) { c9Api.getC9s(c9Id.toInt()) }
    }

    @Test
    fun `loadC9 should handle C9 response without EC1 attestRef`() {
        // Given
        val c9Id = 12345L
        val c9 = C9().apply {
            id = c9Id
            type = "400"
            scanUrl = "http://example.com/scan"
            ssin = "12345678901"
            this.requestDate = LocalDate.of(2021, 1, 1)
            this.introductionDate = LocalDate.of(2021, 1, 1)
            this.dateValid = LocalDate.of(2021, 1, 1)
            this.opKey = "OP123"
            this.sectOp = "SO123"
            this.paymentInstitution = 778899
            this.entityCode = "EC123"
            this.scanNumber = 778866L
            attestRefs = listOf(AttestRef().apply {
                id = 54321
                type = "OTHER"
                displayUrl = "http://example.com/display"
            })
        }

        every { c9Api.getC9s(c9Id.toInt()) } returns c9

        // When
        val result = c9Adapter.loadC9(c9Id)

        // Then
        assertThat(result).isNotNull
            .extracting("c9Id", "scanUrl", "ssin")
            .containsExactly(c9Id, "http://example.com/scan", "12345678901")

        assertThat(result.ec1Id).isNull()
        assertThat(result.ec1DisplayUrl).isNull()


        verify(exactly = 1) { c9Api.getC9s(c9Id.toInt()) }
    }

    @Test
    fun `loadC9 should handle C9 response with empty attestRefs`() {
        // Given
        val c9Id = 12345L
        val c9 = C9().apply {
            this.id = c9Id
            this.type = "400"
            this.scanUrl = "http://example.com/scan"
            this.ssin = "12345678901"
            attestRefs = emptyList()
            this.requestDate = LocalDate.of(2021, 1, 1)
            this.introductionDate = LocalDate.of(2021, 1, 1)
            this.dateValid = LocalDate.of(2021, 1, 1)
            this.opKey = "OP123"
            this.sectOp = "SO123"
            this.paymentInstitution = 778899
            this.entityCode = "EC123"
            this.scanNumber = 778866L
        }

        every { c9Api.getC9s(c9Id.toInt()) } returns c9

        // When
        val result = c9Adapter.loadC9(c9Id)

        // Then
        assertThat(result).isNotNull
            .extracting("c9Id", "scanUrl", "ssin")
            .containsExactly(c9Id, "http://example.com/scan", "12345678901")

        assertThat(result.ec1Id).isNull()
        assertThat(result.ec1DisplayUrl).isNull()

        verify(exactly = 1) { c9Api.getC9s(c9Id.toInt()) }
    }

    @Test
    fun `loadC9 should propagate exceptions from C9Api`() {
        // Given
        val c9Id = 12345L
        val exception = RuntimeException("API Error")

        every { c9Api.getC9s(c9Id.toInt()) } throws exception

        // When/Then
        assertThatThrownBy { c9Adapter.loadC9(c9Id) }
            .isInstanceOf(RuntimeException::class.java)
            .hasMessage("API Error")

        verify(exactly = 1) { c9Api.getC9s(c9Id.toInt()) }
    }
}