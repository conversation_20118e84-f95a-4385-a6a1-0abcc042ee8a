spring:
  main:
    allow-bean-definition-overriding: true
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://test-auth-server
      client:
        registration:
          keycloak:
            provider: keycloak
            client-id: cu-frontend
            client-secret: test-secret
            scope: [openid,profile,email]
        provider:
          keycloak:
            issuer-uri: http://test-auth-server
            authorization-uri: http://test-auth-server/auth
            token-uri: http://test-auth-server/token
            user-info-uri: http://test-auth-server/userinfo
            jwk-set-uri: http://test-auth-server/certs

keycloak:
  auth-server-url: http://test-auth-server
  realm: test-realm
  checkToken: false

management:
  endpoints:
    enabled-by-default: false
  simple:
    metrics:
      export:
        enabled: false
  prometheus:
    metrics:
      export:
        enabled: false
        pushgateway:
          enabled: false

onerva:
  metrics:
    enabled: false
  observation:
    prometheus:
      enabled: false
logging:
  level:
    org.springframework: INFO
    org.springframework.boot.actuate: off

c51:
  url: http://test-c51-url

regis:
  url: https://regis.test.paas.onemrva.priv/regis/regis/rew.seam