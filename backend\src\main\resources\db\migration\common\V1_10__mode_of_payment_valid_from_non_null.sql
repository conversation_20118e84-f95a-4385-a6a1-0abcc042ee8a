update mode_of_payment
set valid_from = r.request_date
from mode_of_payment mop
         inner join request r on mop.request_id = r.id
where valid_from is null;

update mode_of_payment_aud
set valid_from = r.request_date
from mode_of_payment_aud mop_aud
         inner join mode_of_payment mop on mop.id = mop_aud.id
         inner join request r on mop.request_id = r.id
where mop_aud.valid_from is null

-- Make the column not null
alter table mode_of_payment
    alter column valid_from date not null;
alter table mode_of_payment_aud
    alter column valid_from date not null;