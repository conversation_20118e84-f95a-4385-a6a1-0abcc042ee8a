import {AbstractControl, ValidationErrors, ValidatorFn} from "@angular/forms";
import {DateTime} from "luxon";
import {DateUtils} from "./date.utils";

export default class CustomValidators {
    static dateInPastOrPresent(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (control.errors && !control.errors["isInFuture"]) {
                return null;
            }

            const dateToBeChecked = DateUtils.convertToDateTime(control.value);

            if (!dateToBeChecked) {
                return null;
            }

            let errors = null;
            const currentDate = DateTime.now().setZone('Europe/Brussels');
            const isSameOrBefore = dateToBeChecked <= currentDate;

            if (!isSameOrBefore) {
                errors = { isInFuture: true };
            }

            return errors;
        };
    }


    static countryLookupValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (control.errors && !control.errors["countryLookupNotSelected"]) {
                return null;
            }

            const controlValue = control.value;

            if (!controlValue) {
                return null;
            }

            let errors = null;
            if (!controlValue.code) {
                errors = {countryLookupNotSelected: true};
            }

            return errors;
        };
    }

    static bicValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (control.errors && !control.errors["invalidBic"]) {
                return null;
            }

            const bic = control.value;

            if (!bic) {
                return null;
            }

            // Match the same regex pattern as in the backend
            const BIC_PATTERN = /^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/;

            let errors = null;
            if (!BIC_PATTERN.test(bic)) {
                errors = { invalidBic: true };
            }

            return errors;
        };
    }

}

export function validateNiss(
    control: AbstractControl,
): ValidationErrors | null {
    let niss = control.value;
    if (!niss || niss.length !== 11) {
        return null;
    }
    const partOneNissBefore2000 = parseInt(niss.substring(0, 9));
    const partOneNissAfter2000 = parseInt("2" + niss.substring(0, 9));
    const expectedCheckSum = parseInt(niss.substring(9, niss.length + 1));
    const actualCheckSumAfter2000 = 97 - (partOneNissAfter2000 % 97);
    const actualCheckSumBefore2000 = 97 - (partOneNissBefore2000 % 97);
    if (
        !(
            actualCheckSumAfter2000 === expectedCheckSum ||
            actualCheckSumBefore2000 === expectedCheckSum
        )
    ) {
        return {
            checkSum: true,
        };
    } else {
        return null;
    }
}
