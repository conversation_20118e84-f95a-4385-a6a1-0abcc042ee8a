package be.fgov.onerva.cu.backend.adapter.out.external.barema

import java.time.LocalDate
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import be.fgov.onerva.barema.api.BaremeApi
import be.fgov.onerva.barema.rest.model.ArticleTypeDTO
import be.fgov.onerva.barema.rest.model.BaremeSearchTypeDTO
import be.fgov.onerva.barema.rest.model.DateCompareOperatorDTO
import be.fgov.onerva.barema.rest.model.SortDirectionDTO
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.port.out.BaremaPort
import be.fgov.onerva.cu.common.utils.logger

@Service
class BaremaAdapter(private val baremaApi: BaremeApi) : BaremaPort {
    private val log = logger

    override fun getLatestBarema(citizenId: Int, requestDate: LocalDate): Barema? {
        try {
            val baremaResponse = baremaApi.getBaremesByCitizenId(
                citizenId, BaremeSearchTypeDTO.ALL,
                requestDate, SortDirectionDTO.DESC, DateCompareOperatorDTO.LESS_OR_EQUAL
            )
            log.debug("Barema received for citizen {}: {}", citizenId, baremaResponse)

            val firstBarema = baremaResponse.baremes.firstOrNull()

            return firstBarema?.items?.firstOrNull()?.let {
                Barema(
                    barema = it.code,
                    article = it.articles.firstOrNull { it.type == ArticleTypeDTO.ADMISSIBILITY }?.code
                )
            }
        } catch (e: HttpClientErrorException.NotFound) {
            log.warn("Error when getting barema for citizen - NotFound")
            return null
        }
    }
}