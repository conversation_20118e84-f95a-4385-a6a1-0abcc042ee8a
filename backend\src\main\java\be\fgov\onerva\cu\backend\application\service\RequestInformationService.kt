package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.port.`in`.RequestInformationUseCase
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.common.utils.logger

@Service
@BusinessTransaction
class RequestInformationService(
    private val requestInformationPort: RequestInformationPort,
) : RequestInformationUseCase {
    private val log = logger

    override fun getRequestInformation(requestId: UUID): RequestInformation {
        return requestInformationPort.getRequestInformation(requestId)
    }

    override fun updateRequestInformation(requestId: UUID, newRequestInformation: RequestInformation) {
        val existingRequestInformation = try {
            requestInformationPort.getRequestInformation(requestId)
        } catch (e: Exception) {
            null
        }

        if (existingRequestInformation != null) {
            log.info("Updating request information for request ID: $requestId")
            requestInformationPort.updateRequestInformation(requestId, newRequestInformation)
        } else {
            log.info("Creating new request information for request ID: $requestId")
            requestInformationPort.persistRequestInformation(requestId, newRequestInformation)
        }
    }
}