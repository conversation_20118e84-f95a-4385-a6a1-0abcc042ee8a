import { TestBed, ComponentFixture } from '@angular/core/testing';
import { HttpHeaders } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { BaseWebComponent } from './base-web-component';
import { ConfigService } from '../../config/config.service';
import { GeoLookupService } from '../../http/geo-lookup.service';
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'test-component',
    template: '',
    standalone: true,
    imports: [CommonModule]
})
class TestWebComponent extends BaseWebComponent {
    constructor(
        translate: TranslateService,
        geoLookupService: GeoLookupService,
        configService: ConfigService
    ) {
        super(translate, geoLookupService, configService);
    }

    protected initializeComponentServices(token: string): void {
    }

    protected async getWoTaskById(requestId: string): Promise<void> {
    }
}

describe('BaseWebComponent', () => {
    let component: TestWebComponent;
    let fixture: ComponentFixture<TestWebComponent>;
    let translateService: jest.Mocked<TranslateService>;
    let geoLookupService: jest.Mocked<GeoLookupService>;
    let configService: jest.Mocked<ConfigService>;

    beforeEach(() => {
        const translateServiceMock = {
            setDefaultLang: jest.fn(),
            use: jest.fn(),
            instant: jest.fn()
        };

        const geoLookupServiceMock = {
            initializeService: jest.fn()
        };

        const configServiceMock = {
            isOnWO: jest.fn().mockReturnValue(false)
        };

        TestBed.configureTestingModule({
            imports: [TestWebComponent],
            providers: [
                { provide: TranslateService, useValue: translateServiceMock },
                { provide: GeoLookupService, useValue: geoLookupServiceMock },
                { provide: ConfigService, useValue: configServiceMock }
            ]
        });

        translateService = TestBed.inject(TranslateService) as jest.Mocked<TranslateService>;
        geoLookupService = TestBed.inject(GeoLookupService) as jest.Mocked<GeoLookupService>;
        configService = TestBed.inject(ConfigService) as jest.Mocked<ConfigService>;

        fixture = TestBed.createComponent(TestWebComponent);
        component = fixture.componentInstance;
    });

    afterEach(() => {
        fixture.destroy();
        TestBed.resetTestingModule();
    });

    describe('initialization', () => {
        it('should create', () => {
            expect(component).toBeTruthy();
        });

        it('should set default language', () => {
            expect(translateService.setDefaultLang).toHaveBeenCalledWith('NL');
        });

        it('should set headers ready when not on WO', () => {
            expect(component._headersReady()).toBe(true);
        });

        it('should not set headers ready when on WO', () => {
            configService.isOnWO.mockReturnValue(true);
            const newFixture = TestBed.createComponent(TestWebComponent);
            const newComponent = newFixture.componentInstance;
            expect(newComponent._headersReady()).toBe(false);
        });
    });

    describe('ngOnInit', () => {
        it('should emit appReady action', () => {
            const emitSpy = jest.spyOn((component as any).action, 'emit');
            component.ngOnInit();
            expect(emitSpy).toHaveBeenCalledWith({ messageType: 'appReady' });
        });

        it('should set language if available', () => {
            component.language = 'nl';
            component.ngOnInit();
            expect(translateService.use).toHaveBeenCalledWith('NL');
        });
    });

    describe('ngOnDestroy', () => {
        it('should complete destroy subject', () => {
            const nextSpy = jest.spyOn(component.destroy$, 'next');
            const completeSpy = jest.spyOn(component.destroy$, 'complete');

            component.ngOnDestroy();

            expect(nextSpy).toHaveBeenCalled();
            expect(completeSpy).toHaveBeenCalled();
        });
    });

    describe('input properties', () => {
        it('should set and get requestId', () => {
            component.requestId = 'test-123';
            expect(component.requestId).toBe('test-123');
            expect(component._requestIdSignal()).toBe('test-123');
        });

        it('should set and get taskId', () => {
            component.taskId = 'task-456';
            expect(component.taskId).toBe('task-456');
        });

        it('should set and get language', () => {
            component.language = 'en';
            expect(component.language).toBe('en');
        });

        it('should set and get token', () => {
            component.token = 'token-789';
            expect(component.token).toBe('token-789');
            expect(component._tokenSignal()).toBe('token-789');
        });

        it('should parse JSON task if valid', () => {
            const taskObject = { id: 1, name: 'test' };
            component.task = JSON.stringify(taskObject);
            expect(component.task).toEqual(taskObject);
        });

        it('should keep task as string if invalid JSON', () => {
            component.task = 'invalid-json';
            expect(component.task).toBe('invalid-json');
        });

        it('should set and get status', () => {
            component.status = 'active';
            expect(component.status).toBe('active');
        });

        it('should set and get taskStep', () => {
            component.taskStep = 'step1';
            expect(component.taskStep).toBe('step1');
        });

        it('should set and get decisionType', () => {
            component.decisionType = 'automatic';
            expect(component.decisionType).toBe('automatic');
        });

        it('should set and get decisionBarema', () => {
            component.decisionBarema = 'barema1';
            expect(component.decisionBarema).toBe('barema1');
        });
    });

    describe('error handling', () => {
        it('should handle loading error with custom message', () => {
            const error = { message: 'Network error' };
            component['handleLoadingError'](error, 'Custom error message');

            expect(component.loadingError()).toEqual({
                hasError: true,
                message: 'Custom error message'
            });
        });

        it('should handle loading error with error detail', () => {
            const error = { error: { detail: 'Server error detail' } };
            component['handleLoadingError'](error);

            expect(component.loadingError()).toEqual({
                hasError: true,
                message: 'Server error detail'
            });
        });

        it('should clear loading error', () => {
            component['handleLoadingError']({}, 'Error');
            component['clearLoadingError']();

            expect(component.loadingError()).toEqual({
                hasError: false,
                message: ''
            });
        });
    });
});