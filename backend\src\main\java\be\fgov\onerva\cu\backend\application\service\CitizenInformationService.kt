package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenInformation
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.mapper.toDomainAddress
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.CitizenInformationUseCase
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.backend.application.port.out.FieldSourcePort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.RequestPort
import be.fgov.onerva.cu.backend.application.validation.UniqueFieldSources
import be.fgov.onerva.cu.backend.application.validation.ValidFieldNames
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.utils.logger

/**
 * Service responsible for processing received change of address requests from citizens.
 *
 * This service implements the [ChangePersonalDataRequestUseCase] and handles the business logic for:
 * - Validating citizen existence via their SSIN (Social Security Identification Number)
 * - Persisting change of address requests
 * - Creating corresponding tasks in the Wave system
 *
 * @property persistChangePersonalDataPort Port for persisting change of address records
 * @property createChangePersonalDataTaskPort Port for creating tasks in the Wave system
 * @property loadCitizenPort Port for loading citizen information
 *
 * @throws RuntimeException if the citizen cannot be found using their SSIN
 *
 * @see ChangePersonalDataRequestCommand Input model containing the change of address details
 * @see ChangePersonalDataPersistCommand Model representing the data to be persisted
 */
@Service
@BusinessTransaction
@Validated
class CitizenInformationService(
    val loadCitizenPort: LoadCitizenPort,
    val citizenInformationPort: CitizenInformationPort,
    val requestPort: RequestPort,
    val fieldSourcePort: FieldSourcePort,
    val historicalInformationService: HistoricalInformationService,
) : CitizenInformationUseCase {
    private val log = logger

    // Entity type constant for field source tracking
    companion object {
        const val ENTITY_TYPE = "citizen_information"
    }

    @LogMethodCall
    override fun updateCitizenInformation(
        requestId: UUID,
        @Valid updateCitizenInformation: UpdateCitizenInformation,
    ) {
        val existingCitizenInformation = citizenInformationPort.getCitizenInformation(requestId)

        val citizenInformation = if (existingCitizenInformation == null) {
            val request = requestPort.getRequest(requestId)
            val citizenBySsin = loadCitizenPort.getCitizenBySsin(request.ssin)

            CitizenInformation(
                firstName = citizenBySsin.firstName,
                lastName = citizenBySsin.lastName,
                birthDate = updateCitizenInformation.birthDate,
                nationalityCode = updateCitizenInformation.nationalityCode,
                address = updateCitizenInformation.address
                // source field removed
            )
        } else {
            CitizenInformation(
                firstName = existingCitizenInformation.firstName,
                lastName = existingCitizenInformation.lastName,
                birthDate = updateCitizenInformation.birthDate,
                nationalityCode = updateCitizenInformation.nationalityCode,
                address = updateCitizenInformation.address
                // source field removed
            )
        }

        // Persist the citizen information
        citizenInformationPort.persistCitizenInformation(requestId, citizenInformation)
    }

    /**
     * Get citizen information based on the request ID.
     * @param requestId The unique identifier of the request.
     * @return The citizen information associated with the request. null if no citizen information is found.
     */
    override fun getCitizenInformation(requestId: UUID): CitizenInformation? =
        citizenInformationPort.getCitizenInformation(requestId)

    /**
     * Get all field sources for a citizen information entity
     */
    override fun getCitizenInformationFieldSources(requestId: UUID): List<FieldSource> {
        val entityId = citizenInformationPort.getEntityId(requestId)
        return fieldSourcePort.getAllFieldSources(ENTITY_TYPE, entityId)
    }

    /**
     * Select field sources for citizen information fields
     */
    @LogMethodCall
    override fun selectCitizenInformationFieldSources(
        requestId: UUID,
        @Valid
        @UniqueFieldSources
        @ValidFieldNames(names = ["birthDate", "nationality", "address"])
        fieldSources: List<FieldSource>,
    ) {
        val citizenInformation = getCitizenInformation(requestId)
            ?: throw RequestIdNotFoundException("Citizen information not found for request ID: $requestId")
        val c1CitizenInformation = historicalInformationService.getHistoricalCitizenC1(requestId)
        val onemCitizenInformation = historicalInformationService.getHistoricalCitizenOnem(requestId)
        val authenticCitizenInformation = try {
            if (fieldSources.any { it.source == ExternalSource.AUTHENTIC_SOURCES }) {
                historicalInformationService.getHistoricalCitizenAuthenticSources(requestId)
            } else {
                null
            }
        } catch (e: Exception) {
            log.warn("Failed to get authentic sources for citizen: ${e.message}", e)
            if (fieldSources.any { it.source == ExternalSource.AUTHENTIC_SOURCES }) {
                throw InvalidExternalDataException("Authentic sources were not found - it is not possible to use it as a source")
            }
            null
        }
        
        var birtDate: LocalDate? = null
        var nationality: Int? = null
        var address: Address? = null

        fieldSources.forEach { fieldSource ->
            when (fieldSource.fieldName) {
                "birthDate" -> {
                    birtDate = when (fieldSource.source) {
                        ExternalSource.C1 -> c1CitizenInformation.birthDate
                        ExternalSource.ONEM -> throw InvalidExternalDataException("ONEM does not have birthDate")
                        ExternalSource.AUTHENTIC_SOURCES -> authenticCitizenInformation?.birthDate
                    }
                }

                "nationality" -> {
                    nationality = when (fieldSource.source) {
                        ExternalSource.C1 -> c1CitizenInformation.nationalityCode
                        ExternalSource.ONEM -> onemCitizenInformation.nationalityCode
                        ExternalSource.AUTHENTIC_SOURCES -> authenticCitizenInformation?.nationalityCode
                    }
                }

                "address" -> {
                    address = when (fieldSource.source) {
                        ExternalSource.C1 -> c1CitizenInformation.address.toDomainAddress()
                        ExternalSource.ONEM -> onemCitizenInformation.address.toDomainAddress()
                        ExternalSource.AUTHENTIC_SOURCES -> authenticCitizenInformation?.address?.toDomainAddress()
                    }
                }
            }
        }

        citizenInformationPort.persistCitizenInformation(
            requestId, CitizenInformation(
                firstName = citizenInformation.firstName,
                lastName = citizenInformation.lastName,
                birthDate = birtDate ?: citizenInformation.birthDate,
                nationalityCode = nationality ?: citizenInformation.nationalityCode,
                address = address ?: citizenInformation.address,
            )
        )

        val entityId = citizenInformationPort.getEntityId(requestId)
        fieldSourcePort.setMultipleFieldSources(ENTITY_TYPE, entityId, fieldSources)
    }
}