package be.fgov.onerva.cu.bff.controller

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.groups.Tuple.tuple
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.bff.lookup.CityDTO
import be.fgov.onerva.cu.bff.lookup.CountryDTO
import be.fgov.onerva.cu.bff.lookup.LookupService
import be.fgov.onerva.cu.bff.lookup.NationalityDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class LookupControllerTest {

    @MockK
    private lateinit var lookupService: LookupService

    @InjectMockKs
    private lateinit var controller: LookupController

    @Test
    fun `searchCountry should return all countries when search query is null`() {
        // Given
        val countries = listOf(
            CountryDTO(1, "BE", "Belgium", "België"),
            CountryDTO(2, "FR", "France", "Frankrijk"),
            CountryDTO(3, "NL", "Netherlands", "Nederland")
        )

        every { lookupService.lookupCountry(null) } returns countries

        // When
        val response = controller.searchCountry(null)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .hasSize(3)
            .extracting("code", "descFr", "descNl")
            .containsExactly(
                tuple("BE", "Belgium", "België"),
                tuple("FR", "France", "Frankrijk"),
                tuple("NL", "Netherlands", "Nederland")
            )

        verify(exactly = 1) { lookupService.lookupCountry(null) }
    }

    @Test
    fun `searchNationality should return all nationalities when search query is null`() {
        // Given
        val nationalities = listOf(
            NationalityDTO(1, "BE", "Belgian", "Belgisch", "1"),
            NationalityDTO(2, "FR", "French", "Frans", "4"),
            NationalityDTO(3, "NL", "Dutch", "Nederlands", "2")
        )

        every { lookupService.lookupNationality(null) } returns nationalities

        // When
        val response = controller.searchNationality(null)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .hasSize(3)
            .extracting("code", "descFr", "descNl")
            .containsExactly(
                tuple("BE", "Belgian", "Belgisch"),
                tuple("FR", "French", "Frans"),
                tuple("NL", "Dutch", "Nederlands")
            )

        verify(exactly = 1) { lookupService.lookupNationality(null) }
    }

    @Test
    fun `searchCountry should handle empty service response`() {
        // Given
        val searchQuery = "test"
        every { lookupService.lookupCountry(searchQuery) } returns emptyList()

        // When
        val response = controller.searchCountry(searchQuery)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .isEmpty()

        verify(exactly = 1) { lookupService.lookupCountry(searchQuery) }
    }

    @Test
    fun `searchNationality should handle empty service response`() {
        // Given
        val searchQuery = "test"
        every { lookupService.lookupNationality(searchQuery) } returns emptyList()

        // When
        val response = controller.searchNationality(searchQuery)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .isEmpty()

        verify(exactly = 1) { lookupService.lookupNationality(searchQuery) }
    }

    @Test
    fun `searchCity should return all cities when search query is null`() {
        // Given
        val cities = listOf(
            CityDTO(
                beginDate = LocalDate.of(2024, 1, 1),
                nisCode = "1000",
                lookupId = 1,
                code = "BRU",
                descFr = "Bruxelles",
                descNl = "Brussel"
            ),
            CityDTO(
                beginDate = LocalDate.of(2024, 1, 1),
                nisCode = "2000",
                lookupId = 2,
                code = "ANT",
                descFr = "Anvers",
                descNl = "Antwerpen"
            )
        )

        every { lookupService.lookupCity(null) } returns cities

        // When
        val response = controller.searchCity(null)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .hasSize(2)
            .extracting("code", "descFr", "descNl", "nisCode")
            .containsExactly(
                tuple("BRU", "Bruxelles", "Brussel", "1000"),
                tuple("ANT", "Anvers", "Antwerpen", "2000")
            )

        verify(exactly = 1) { lookupService.lookupCity(null) }
    }

    @Test
    fun `searchCity should return filtered cities when search query is provided`() {
        // Given
        val searchQuery = "brux"
        val cities = listOf(
            CityDTO(
                beginDate = LocalDate.of(2024, 1, 1),
                nisCode = "1000",
                lookupId = 1,
                code = "BRU",
                descFr = "Bruxelles",
                descNl = "Brussel"
            )
        )

        every { lookupService.lookupCity(searchQuery) } returns cities

        // When
        val response = controller.searchCity(searchQuery)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .hasSize(1)
            .extracting("code", "descFr", "descNl", "nisCode")
            .containsExactly(
                tuple("BRU", "Bruxelles", "Brussel", "1000")
            )

        verify(exactly = 1) { lookupService.lookupCity(searchQuery) }
    }

    @Test
    fun `searchCity should handle empty service response`() {
        // Given
        val searchQuery = "nonexistent"
        every { lookupService.lookupCity(searchQuery) } returns emptyList()

        // When
        val response = controller.searchCity(searchQuery)

        // Then
        assertThat(response.statusCode.value()).isEqualTo(200)
        assertThat(response.body)
            .isNotNull
            .isEmpty()

        verify(exactly = 1) { lookupService.lookupCity(searchQuery) }
    }
}