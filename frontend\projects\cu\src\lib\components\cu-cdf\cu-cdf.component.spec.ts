import {ComponentFixture, fakeAsync, TestBed, tick} from "@angular/core/testing";
import {FormControl, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {InputSignal, signal} from "@angular/core";
import {NoopAnimationsModule} from "@angular/platform-browser/animations";
import {of} from "rxjs";

import {CuCdfComponent} from "./cu-cdf.component";
import {GeoLookupService} from "../../http/geo-lookup.service";
import {TranslateModule} from "@ngx-translate/core";
import {
  CitizenInformationDetailResponse,
  ModeOfPaymentDetailResponse,
  RequestBasicInfoResponse,
  UnionContributionDetailResponse,
} from "@rest-client/cu-config";
import {CitizenInformationDetailNullableResponse} from "@rest-client/cu-bff";

class MockGeoLookupService {
  setupSearchControl() {
    return of([]);
  }
  searchNationalityByCode(_code: string) {
    return of(null);
  }
  searchCountryByCode(_code: string) {
    return of(null);
  }
}

describe('CuCdfComponent', () => {
  let fixture: ComponentFixture<CuCdfComponent>;
  let component: CuCdfComponent;

  const mockRequestBasicInfoResponse: RequestBasicInfoResponse = {
    requestDate: '2025-02-01',
    lastName: 'Doe',
    firstName: 'John',
    ssin: '***********',
    c9Id: "123",
    documentType: RequestBasicInfoResponse.DocumentTypeEnum.Electronic,
  };

  const mockEmployeeData: CitizenInformationDetailResponse = {
    birthDate: '1990-10-10',
    nationalityCode: 150,
    address: {
      street: 'Main St',
      houseNumber: '12',
      boxNumber: 'A',
      zipCode: '1000',
      city: 'Bruxelles',
      countryCode: 150,
      validFrom: '2024-01-01'
    }
  };

  const mockPaymentData: ModeOfPaymentDetailResponse = {
    iban: 'BE68 5390 0754 7034',
    bic: '',
    otherPersonName: ""
  };

  const mockUnionData: UnionContributionDetailResponse = {
    authorized: true,
    effectiveDate: '2025-02-01'
  };

  const geoLookupServiceMock = {
    searchNationalityByCode: jest.fn().mockReturnValue(of(null)),
    searchCountryByCode: jest.fn().mockReturnValue(of(null)),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CuCdfComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
        TranslateModule.forRoot(),
      ],
      providers: [
        { provide: GeoLookupService, useClass: MockGeoLookupService },
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CuCdfComponent);
    component = fixture.componentInstance;

    component.requestBasicInfoResponse = signal<RequestBasicInfoResponse>(mockRequestBasicInfoResponse) as unknown as InputSignal<RequestBasicInfoResponse>;
    component.citizenInformation = signal<CitizenInformationDetailNullableResponse>(mockEmployeeData) as unknown as InputSignal<CitizenInformationDetailNullableResponse>;
    component.paymentData = signal<ModeOfPaymentDetailResponse>(mockPaymentData) as unknown as InputSignal<ModeOfPaymentDetailResponse>;
    component.unionData = signal<UnionContributionDetailResponse>(mockUnionData) as unknown as InputSignal<UnionContributionDetailResponse>;

    component.language = 'fr';
    component.taskStatus = 'OPEN';

    // Updated form structure with new bank panel fields
    component.cdfForm = new FormGroup({
      requestPanel: new FormGroup({
        startDate: new FormControl(null, [Validators.required]),
      }),
      employeePanel: new FormGroup({
        niss: new FormControl('', [Validators.required]),
        birthDate: new FormControl('', [Validators.required]),
        lastName: new FormControl(''),
        firstName: new FormControl(''),
        nationalityCode: new FormControl(''),
        street: new FormControl('', [Validators.required]),
        streetNbr: new FormControl('', [Validators.required]),
        streetBox: new FormControl(''),
        postCode: new FormControl('', [Validators.required]),
        countryCode: new FormControl('', [Validators.required]),
        city: new FormControl('', [Validators.required]),
        addressFromDate: new FormControl('', [Validators.required]),
      }),
      bankPanel: new FormGroup({
        isMyBankAccount: new FormControl('is_my_bank_account'),
        otherPersonName: new FormControl(""),
        iban: new FormControl(''),
        bic: new FormControl(''),
        bankFromDate: new FormControl('', [Validators.required]),
      }),
      syndicatePanel: new FormGroup({
        syndicateContribution: new FormControl('CONTRIBUTION'),
        contributionDeductionFromTheMonth: new FormControl(''),
        stopContributionDeductionFromTheMonth: new FormControl(''),
      }),
    });

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should patch form data in ngOnChanges', () => {
    component.ngOnChanges({
      requestBasicInfoResponse: {
        currentValue: mockRequestBasicInfoResponse,
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true
      }
    });

    expect(component.startDateCtrl.value).toBe('2025-02-01');
    expect(component.nissCtrl.value).toBe('***********');
    expect(component.birthDateCtrl.value).toBe('1990-10-10');
  });

  it('should mark requestPanel invalid if startDate is empty', () => {
    component.startDateCtrl.setValue(null);
    fixture.detectChanges();

    expect(component.isPanelValid('requestPanel')).toBe(false);
  });

  it('should mark requestPanel valid if startDate is provided', () => {
    component.startDateCtrl.setValue('2025-02-01');
    fixture.detectChanges();

    expect(component.isPanelValid('requestPanel')).toBe(true);
  });

  // Bank Panel Tests - Updated for new IBAN approach
  describe('Bank Panel Validation', () => {
    it('should detect Belgian IBAN correctly', () => {
      const belgianIban = '****************';
      (component as any).updateIbanValidation(belgianIban);

      expect(component.isBelgianIban).toBe(true);
      expect(component.bicCtrl.hasValidator(Validators.required)).toBe(false);
    });

    it('should detect non-Belgian IBAN correctly', () => {
      const frenchIban = '***************************';
      (component as any).updateIbanValidation(frenchIban);

      expect(component.isBelgianIban).toBe(false);
      expect(component.bicCtrl.hasValidator(Validators.required)).toBe(true);
    });

    it('should handle empty IBAN value', () => {
      (component as any).updateIbanValidation('');

      expect(component.isBelgianIban).toBe(false);
      expect(component.bicCtrl.hasValidator(Validators.required)).toBe(true);
    });

    it('should handle IBAN value with spaces and lowercase', () => {
      const belgianIban = 'be 68 5390 0754 7034';
      (component as any).updateIbanValidation(belgianIban);

      expect(component.isBelgianIban).toBe(true);
      expect(component.bicCtrl.hasValidator(Validators.required)).toBe(false);
    });

    it('should correctly patch bank form values for Belgian IBAN', () => {
      component.paymentData = signal<ModeOfPaymentDetailResponse>({
        iban: '****************',
        bic: '',
        otherPersonName: ''
      }) as unknown as InputSignal<ModeOfPaymentDetailResponse>;

      component.ngOnChanges({
        paymentData: {
          currentValue: {
            iban: '****************',
            bic: '',
            otherPersonName: ''
          },
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true
        }
      });

      expect(component.ibanCtrl.value).toBe('****************');
      expect(component.bicCtrl.value).toBe('');
      expect(component.isBelgianIban).toBe(true);
    });

    it('should correctly patch bank form values for non-Belgian IBAN', () => {
      component.paymentData = signal<ModeOfPaymentDetailResponse>({
        iban: '***************************',
        bic: 'AGRIFRPP',
        otherPersonName: ''
      }) as unknown as InputSignal<ModeOfPaymentDetailResponse>;

      component.ngOnChanges({
        paymentData: {
          currentValue: {
            iban: '***************************',
            bic: 'AGRIFRPP',
            otherPersonName: ''
          },
          previousValue: null,
          firstChange: true,
          isFirstChange: () => true
        }
      });

      expect(component.ibanCtrl.value).toBe('***************************');
      expect(component.bicCtrl.value).toBe('AGRIFRPP');
      expect(component.isBelgianIban).toBe(false);
    });

    it('should validate isMyBankAccount selection properly', () => {
      component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
      fixture.detectChanges();

      expect(component.otherPersonNameCtrl.hasValidator(Validators.required)).toBeTruthy();

      component.isMyBankAccountCtrl.setValue('is_my_bank_account');
      fixture.detectChanges();

      expect(component.otherPersonNameCtrl.hasValidator(Validators.required)).toBeFalsy();
    });

    it('should test debounced IBAN value changes', fakeAsync(() => {
      const updateIbanValidationSpy = jest.spyOn(component as any, 'updateIbanValidation');

      component.ibanCtrl.setValue('****************');
      tick(100); // Not enough time for debounce

      expect(updateIbanValidationSpy).not.toHaveBeenCalledWith('****************');

      tick(200); // Complete the debounce

      expect(updateIbanValidationSpy).toHaveBeenCalledWith('****************');
    }));

    it('should mark bank panel valid with valid iban', () => {
      component.ibanCtrl.setValue('****************');
      component.bankFromDateCtrl.setValue('2023-01-01');
      (component as any).updateIbanValidation('****************');
      fixture.detectChanges();

      expect(component.isPanelValid('bankPanel')).toBeTruthy();
    });

    it('should mark bank panel invalid when foreign IBAN without BIC', () => {
      component.ibanCtrl.setValue('***************************');
      component.bankFromDateCtrl.setValue('2023-01-01');
      (component as any).updateIbanValidation('***************************');
      component.bicCtrl.setValue(''); // Missing BIC
      fixture.detectChanges();

      expect(component.isPanelValid('bankPanel')).toBeFalsy();
    });

    it('should mark bank panel valid when foreign IBAN with BIC', () => {
      component.ibanCtrl.setValue('***************************');
      component.bankFromDateCtrl.setValue('2023-01-01');
      (component as any).updateIbanValidation('***************************');
      component.bicCtrl.setValue('AGRIFRPP');
      fixture.detectChanges();

      expect(component.isPanelValid('bankPanel')).toBeTruthy();
    });
  });

  describe('Syndicate Panel Validation', () => {
    it('should require contributionDeductionFromTheMonth when CONTRIBUTION is selected', () => {
      component.syndicateContributionCtrl.setValue('CONTRIBUTION');
      fixture.detectChanges();

      expect(component.contributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeTruthy();
      expect(component.stopContributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeFalsy();
    });

    it('should require stopContributionDeductionFromTheMonth when STOP_CONTRIBUTION is selected', () => {
      component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
      fixture.detectChanges();

      expect(component.stopContributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeTruthy();
      expect(component.contributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeFalsy();
    });

    it('should clear both date fields and remove validators when CONTRIBUTION_NOT_AUTHORIZED is selected', () => {
      component.syndicateContributionCtrl.setValue('CONTRIBUTION');
      fixture.detectChanges();

      component.syndicateContributionCtrl.setValue('CONTRIBUTION');
      component.contributionDeductionFromTheMonthCtrl.setValue('2025-02-01');
      component.stopContributionDeductionFromTheMonthCtrl.setValue('2025-03-01');
      fixture.detectChanges();

      component.syndicateContributionCtrl.setValue('CONTRIBUTION_NOT_AUTHORIZED');
      component.ngOnChanges({});
      fixture.detectChanges();

      expect(component.syndicateContributionCtrl.valid).toBeTruthy();
      expect(component.isPanelValid('syndicatePanel')).toBeTruthy();
      expect(component.contributionDeductionFromTheMonthCtrl.errors).toBeNull();
      expect(component.stopContributionDeductionFromTheMonthCtrl.errors).toBeNull();
    });

    it('should mark syndicatePanel as valid when CONTRIBUTION_NOT_AUTHORIZED is selected', () => {
      component.syndicateContributionCtrl.setValue('CONTRIBUTION_NOT_AUTHORIZED');
      fixture.detectChanges();

      expect(component.isPanelValid('syndicatePanel')).toBeTruthy();
    });
  });

  describe('Lifecycle Methods', () => {
    it('should clean up subscriptions on destroy', () => {
      const nextSpy = jest.fn();
      const completeSpy = jest.fn();
      component['destroyed$'].next = nextSpy;
      component['destroyed$'].complete = completeSpy;

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should setup search effects after view init', () => {
      const setupSearchEffectsSpy = jest.spyOn(component as any, 'setupSearchEffects');
      component.ngAfterViewInit();
      expect(setupSearchEffectsSpy).toHaveBeenCalled();
    });
  });

  describe('Search and Lookup Functionality', () => {
    it('should filter cities based on search value', fakeAsync(() => {
      component.cityCtrl.setValue('bru');
      tick(300);
      fixture.detectChanges();

      expect(component.citiesList.length).toBe(1);
      expect(component.citiesList[0].code).toBe('92250');
    }));

    it('should not filter cities when value is an object', fakeAsync(() => {
      const initialLength = component.citiesList.length;
      component.cityCtrl.setValue({ code: '92250', descFr: 'Bruxelles', descNl: 'Brussel' });
      tick(300);
      fixture.detectChanges();

      expect(component.citiesList.length).toBe(initialLength);
    }));

    it('should display lookup value in correct language', () => {
      const lookup = { code: '92250', descFr: 'Bruxelles', descNl: 'Brussel' };

      component.language = 'fr';
      expect(component.displayLookup(lookup)).toBe('Bruxelles');

      component.language = 'nl';
      expect(component.displayLookup(lookup)).toBe('Brussel');
    });

    it('should handle empty lookup value', () => {
      expect(component.displayLookup(undefined)).toBe('');
    });
  });

  describe('Panel State Management', () => {

    it('should open invalid panels', () => {
      const openSpy = jest.fn();
      component.panels = {
        first: { open: openSpy, id: 'cdk-accordion-child-0' },
        length: 1,
        forEach: function(fn: (item: any) => void) {
          fn({ open: openSpy, id: 'cdk-accordion-child-0' });
        }
      } as any;

      component.startDateCtrl.setValue(null);
      fixture.detectChanges();

      component.openInvalidPanels();
      fixture.detectChanges();

      expect(openSpy).toHaveBeenCalled();
    });

    it('should close valid panels', () => {
      const closeSpy = jest.fn();
      component.panels = {
        first: { close: closeSpy, id: 'cdk-accordion-child-0' },
        length: 1,
        forEach: function(fn: (item: any) => void) {
          fn({ close: closeSpy, id: 'cdk-accordion-child-0' });
        }
      } as any;

      component.startDateCtrl.setValue('2025-02-01');
      fixture.detectChanges();

      component.openInvalidPanels();
      fixture.detectChanges();

      expect(closeSpy).toHaveBeenCalled();
    });
  });

  describe('Form Control Updates', () => {
    it('should update control validity after closing', fakeAsync(() => {
      const updateValueAndValiditySpy = jest.spyOn(component.startDateCtrl, 'updateValueAndValidity');
      component.onClosed(component.startDateCtrl);
      tick(0);
      expect(updateValueAndValiditySpy).toHaveBeenCalled();
    }));
  });

  describe('hasNoSearchedNationalities and hasNoSearchedCountries', () => {
    it('should return true when no nationalities are found and search was performed', () => {
      component['nationalitySearched'] = true;
      component.nationalitiesList = [];
      expect(component.hasNoSearchedNationalities()).toBeTruthy();
    });

    it('should return true when no countries are found and search was performed', () => {
      component['countrySearched'] = true;
      component.countriesList = [];
      expect(component.hasNoSearchedCountries()).toBeTruthy();
    });
  });

  it('should handle panel toggle correctly', () => {
    const openSpy = jest.fn();
    const closeSpy = jest.fn();

    component.panels = {
      first: { open: openSpy, close: closeSpy, id: 'cdk-accordion-child-0' },
      length: 1,
      forEach: function(fn: (item: any) => void) {
        fn({ open: openSpy, close: closeSpy, id: 'cdk-accordion-child-0' });
      }
    } as any;

    component.openInvalidPanels();
    expect(openSpy).toHaveBeenCalled();
    expect(closeSpy).not.toHaveBeenCalled();

    component.startDateCtrl.setValue('2025-02-01');
    fixture.detectChanges();

    component.openInvalidPanels();
    expect(closeSpy).toHaveBeenCalled();
  });

  it('should properly handle changes in syndicate contribution selection', () => {
    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    fixture.detectChanges();

    expect(component.stopContributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeTruthy();
    expect(component.contributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeFalsy();
  });

  it('should handle otherPersonName validators', () => {
    const otherPersonNameControl = component.otherPersonNameCtrl;
    const setValidatorsSpy = jest.spyOn(otherPersonNameControl, 'setValidators');

    component.paymentData = signal<ModeOfPaymentDetailResponse>({
      ...mockPaymentData,
      otherPersonName: "Jane Doe",
    }) as unknown as InputSignal<ModeOfPaymentDetailResponse>;

    component.ngOnChanges({
      paymentData: {
        currentValue: {...mockPaymentData, otherPersonName: "Jane Doe"},
        previousValue: mockPaymentData,
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(setValidatorsSpy).toHaveBeenCalled();
  });

  it('should update form validity on closing panel', fakeAsync(() => {
    const updateSpy = jest.spyOn(component.startDateCtrl, 'updateValueAndValidity');
    component.onClosed(component.startDateCtrl);
    tick();
    expect(updateSpy).toHaveBeenCalled();
  }));

  it('should call setupSubscriptions on init', () => {
    const setupSubscriptionsSpy = jest.spyOn(component as any, 'setupSubscriptions');
    component.ngOnInit();
    expect(setupSubscriptionsSpy).toHaveBeenCalled();
  });

  it('should call setupSearchEffects after view init', () => {
    const setupSearchEffectsSpy = jest.spyOn(component as any, 'setupSearchEffects');
    component.ngAfterViewInit();
    expect(setupSearchEffectsSpy).toHaveBeenCalled();
  });

  it('should clean up subscriptions on destroy', () => {
    const nextSpy = jest.fn();
    const completeSpy = jest.fn();
    component['destroyed$'].next = nextSpy;
    component['destroyed$'].complete = completeSpy;

    component.ngOnDestroy();

    expect(nextSpy).toHaveBeenCalled();
    expect(completeSpy).toHaveBeenCalled();
  });

  it("should check if the start date field is enabled", () => {
    const startDateInput = fixture.nativeElement.querySelector("[data-cy=\"inputStartDate\"]");
    expect(startDateInput.disabled).toBeFalsy();
  });

  // it('should handle updateLookupDisplays for city, nationality and country', () => {
  //   const cityControl = component.cdfForm.get('employeePanel.city');
  //   const nationalityControl = component.cdfForm.get('employeePanel.nationalityCode');
  //   const countryControl = component.cdfForm.get('employeePanel.countryCode');
  //
  //   cityControl?.setValue('Brussels');
  //   nationalityControl?.setValue({ code: 'BE', descFr: 'Belgique', descNl: 'België' });
  //   countryControl?.setValue({ code: 'BE', descFr: 'Belgique', descNl: 'België' });
  //
  //   const citySetValueSpy = jest.spyOn(cityControl as any, 'setValue');
  //   const nationalitySetValueSpy = jest.spyOn(nationalityControl as any, 'setValue');
  //   const countrySetValueSpy = jest.spyOn(countryControl as any, 'setValue');
  //
  //   component['updateLookupDisplays']();
  //
  //   expect(citySetValueSpy).toHaveBeenCalledWith('Brussels', { emitEvent: false });
  //   expect(nationalitySetValueSpy).toHaveBeenCalledWith({ code: 'BE', descFr: 'Belgique', descNl: 'België' }, { emitEvent: false });
  //   expect(countrySetValueSpy).toHaveBeenCalledWith({ code: 'BE', descFr: 'Belgique', descNl: 'België' }, { emitEvent: false });
  // });

  it('should handle setupSearchEffects for nationality and country', () => {
    const geoLookupService = TestBed.inject(GeoLookupService);
    const setupSearchControlSpy = jest.spyOn(geoLookupService, 'setupSearchControl').mockReturnValue(of([]));

    component['setupSearchEffects']();

    expect(setupSearchControlSpy).toHaveBeenCalledWith(component.nationalityCtrl, 'nationality');
    expect(setupSearchControlSpy).toHaveBeenCalledWith(component.countryCtrl, 'nationality');
  });

  it('should handle bankPanel form group updates', () => {
    const bankPanel = component.cdfForm.get('bankPanel') as FormGroup;
    const updateValueAndValiditySpy = jest.spyOn(bankPanel, 'updateValueAndValidity');

    component.ngOnChanges({
      paymentData: {
        currentValue: mockPaymentData,
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true
      }
    });

    expect(updateValueAndValiditySpy).toHaveBeenCalled();
  });

  it('should handle cityCtrl.valueChanges when value is null', fakeAsync(() => {
    component.cityCtrl.setValue(null);
    tick(300);
    fixture.detectChanges();

    expect(component.citiesList.length).toBe(0);
  }));

  it('should update syndicate validation when syndicateContribution value changes', () => {
    const updateSyndicateValidationSpy = jest.spyOn(component as any, 'updateSyndicateValidation');

    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    fixture.detectChanges();

    expect(updateSyndicateValidationSpy).toHaveBeenCalledWith('STOP_CONTRIBUTION');
  });

  it('should handle no panelsIdMap entry', () => {
    const panel = component.cdfForm?.get('nonExistentPanel');
    expect(component.isPanelValid('nonExistentPanel')).toBeFalsy();
  });

  it('should disable and reset otherPersonName when isMyBankAccount is selected', () => {
    const disableSpy = jest.spyOn(component.otherPersonNameCtrl, 'disable');
    const resetSpy = jest.spyOn(component.otherPersonNameCtrl, 'reset');
    const clearValidatorsSpy = jest.spyOn(component.otherPersonNameCtrl, 'clearValidators');

    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    component.otherPersonNameCtrl.setValue('John Doe');
    fixture.detectChanges();

    expect(component.otherPersonNameCtrl.disabled).toBe(false);
    expect(component.otherPersonNameCtrl.hasValidator(Validators.required)).toBeTruthy();

    component.isMyBankAccountCtrl.setValue('is_my_bank_account');
    fixture.detectChanges();

    expect(disableSpy).toHaveBeenCalled();
    expect(resetSpy).toHaveBeenCalled();
    expect(clearValidatorsSpy).toHaveBeenCalled();

    expect(component.otherPersonNameCtrl.disabled).toBe(true);
    expect(component.otherPersonNameCtrl.value).toBeNull();
  });

  it('should enable otherPersonName when bank_account_for_other_person_name is selected', () => {
    const enableSpy = jest.spyOn(component.otherPersonNameCtrl, 'enable');
    const setValidatorsSpy = jest.spyOn(component.otherPersonNameCtrl, 'setValidators');

    component.isMyBankAccountCtrl.setValue('is_my_bank_account');
    fixture.detectChanges();

    expect(component.otherPersonNameCtrl.disabled).toBe(true);

    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    fixture.detectChanges();

    expect(enableSpy).toHaveBeenCalled();
    expect(setValidatorsSpy).toHaveBeenCalled();

    expect(component.otherPersonNameCtrl.disabled).toBe(false);
    expect(component.otherPersonNameCtrl.hasValidator(Validators.required)).toBeTruthy();

    const validators = component.otherPersonNameCtrl.validator;
    expect(validators).toBeTruthy();

    component.otherPersonNameCtrl.setValue('a'.repeat(300));
    expect(component.otherPersonNameCtrl.valid).toBeFalsy();
    expect(component.otherPersonNameCtrl.errors?.['maxlength']).toBeTruthy();

    component.otherPersonNameCtrl.setValue('Valid Name');
    expect(component.otherPersonNameCtrl.valid).toBeTruthy();
  });

  it('should maintain validation state when switching between account types', () => {
    component.isMyBankAccountCtrl.setValue('is_my_bank_account');

    component.ibanCtrl.setValue('****************');
    component.bankFromDateCtrl.setValue('2023-01-01');
    (component as any).updateIbanValidation('****************');

    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeTruthy();

    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeFalsy();

    component.otherPersonNameCtrl.setValue('Jane Doe');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeTruthy();

    component.isMyBankAccountCtrl.setValue('is_my_bank_account');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeTruthy();
    expect(component.otherPersonNameCtrl.value).toBeNull();
  });



  it('should handle initial form state correctly', () => {
    const testFixture = TestBed.createComponent(CuCdfComponent);
    const testComponent = testFixture.componentInstance;

    // Set up required input signals
    testComponent.requestBasicInfoResponse = signal<RequestBasicInfoResponse>(mockRequestBasicInfoResponse) as unknown as InputSignal<RequestBasicInfoResponse>;
    testComponent.citizenInformation = signal<CitizenInformationDetailNullableResponse>(mockEmployeeData) as unknown as InputSignal<CitizenInformationDetailNullableResponse>;
    testComponent.paymentData = signal<ModeOfPaymentDetailResponse>(mockPaymentData) as unknown as InputSignal<ModeOfPaymentDetailResponse>;
    testComponent.unionData = signal<UnionContributionDetailResponse>(mockUnionData) as unknown as InputSignal<UnionContributionDetailResponse>;

    testComponent.language = 'fr';
    testComponent.taskStatus = 'OPEN';

    testComponent.cdfForm = new FormGroup({
      requestPanel: new FormGroup({
        startDate: new FormControl(null, [Validators.required]),
      }),
      employeePanel: new FormGroup({
        niss: new FormControl('', [Validators.required]),
        birthDate: new FormControl('', [Validators.required]),
        lastName: new FormControl(''),
        firstName: new FormControl(''),
        nationalityCode: new FormControl(''),
        street: new FormControl('', [Validators.required]),
        streetNbr: new FormControl('', [Validators.required]),
        streetBox: new FormControl(''),
        postCode: new FormControl('', [Validators.required]),
        countryCode: new FormControl('', [Validators.required]),
        city: new FormControl('', [Validators.required]),
        addressFromDate: new FormControl('', [Validators.required]),
      }),
      bankPanel: new FormGroup({
        isMyBankAccount: new FormControl('is_my_bank_account'),
        otherPersonName: new FormControl(""),  // NOT disabled initially
        iban: new FormControl('****************'),
        bic: new FormControl(''),
        bankFromDate: new FormControl('', [Validators.required]),
      }),
      syndicatePanel: new FormGroup({
        syndicateContribution: new FormControl('CONTRIBUTION'),
        contributionDeductionFromTheMonth: new FormControl(''),
        stopContributionDeductionFromTheMonth: new FormControl(''),
      })
    });

    const disableSpy = jest.spyOn(testComponent.otherPersonNameCtrl, 'disable');

    testComponent['setupBankPanelValidation']();
    testComponent.isMyBankAccountCtrl.setValue('is_my_bank_account', {emitEvent: true});
    testFixture.detectChanges();

    expect(disableSpy).toHaveBeenCalled();
    expect(testComponent.otherPersonNameCtrl.disabled).toBe(true);

    const testFixture2 = TestBed.createComponent(CuCdfComponent);
    const testComponent2 = testFixture2.componentInstance;

    // Set up required input signals for second component
    testComponent2.requestBasicInfoResponse = signal<RequestBasicInfoResponse>(mockRequestBasicInfoResponse) as unknown as InputSignal<RequestBasicInfoResponse>;
    testComponent2.citizenInformation = signal<CitizenInformationDetailNullableResponse>(mockEmployeeData) as unknown as InputSignal<CitizenInformationDetailNullableResponse>;
    testComponent2.paymentData = signal<ModeOfPaymentDetailResponse>(mockPaymentData) as unknown as InputSignal<ModeOfPaymentDetailResponse>;
    testComponent2.unionData = signal<UnionContributionDetailResponse>(mockUnionData) as unknown as InputSignal<UnionContributionDetailResponse>;

    testComponent2.language = 'fr';
    testComponent2.taskStatus = 'OPEN';

    testComponent2.cdfForm = new FormGroup({
      requestPanel: new FormGroup({
        startDate: new FormControl(null, [Validators.required]),
      }),
      employeePanel: new FormGroup({
        niss: new FormControl('', [Validators.required]),
        birthDate: new FormControl('', [Validators.required]),
        lastName: new FormControl(''),
        firstName: new FormControl(''),
        nationalityCode: new FormControl(''),
        street: new FormControl('', [Validators.required]),
        streetNbr: new FormControl('', [Validators.required]),
        streetBox: new FormControl(''),
        postCode: new FormControl('', [Validators.required]),
        countryCode: new FormControl('', [Validators.required]),
        city: new FormControl('', [Validators.required]),
        addressFromDate: new FormControl('', [Validators.required]),
      }),
      bankPanel: new FormGroup({
        isMyBankAccount: new FormControl('bank_account_for_other_person_name'),
        otherPersonName: new FormControl({value: "", disabled: true}),  // Explicitly disabled
        iban: new FormControl('****************'),
        bic: new FormControl(''),
        bankFromDate: new FormControl('', [Validators.required]),
      }),
      syndicatePanel: new FormGroup({
        syndicateContribution: new FormControl('CONTRIBUTION'),
        contributionDeductionFromTheMonth: new FormControl(''),
        stopContributionDeductionFromTheMonth: new FormControl(''),
      })
    });

    const enableSpy = jest.spyOn(testComponent2.otherPersonNameCtrl, 'enable');

    testComponent2['setupBankPanelValidation']();
    testComponent2.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name', {emitEvent: true});
    testFixture2.detectChanges();

    expect(enableSpy).toHaveBeenCalled();
    expect(testComponent2.otherPersonNameCtrl.disabled).toBe(false);
  });

  it('should enable contributionDeductionFromTheMonth and disable stopContributionDeductionFromTheMonth when CONTRIBUTION is selected', () => {
    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    fixture.detectChanges();

    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    fixture.detectChanges();

    expect(component.contributionDeductionFromTheMonthCtrl.enabled).toBeTruthy();
    expect(component.stopContributionDeductionFromTheMonthCtrl.disabled).toBeTruthy();
    expect(component.contributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeTruthy();
  });

  it('should enable stopContributionDeductionFromTheMonth and disable contributionDeductionFromTheMonth when STOP_CONTRIBUTION is selected', () => {
    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    fixture.detectChanges();

    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    fixture.detectChanges();

    expect(component.stopContributionDeductionFromTheMonthCtrl.enabled).toBeTruthy();
    expect(component.contributionDeductionFromTheMonthCtrl.disabled).toBeTruthy();
    expect(component.stopContributionDeductionFromTheMonthCtrl.hasValidator(Validators.required)).toBeTruthy();
  });

  it('should disable both date fields when CONTRIBUTION_NOT_AUTHORIZED is selected', () => {
    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    fixture.detectChanges();

    component.syndicateContributionCtrl.setValue('CONTRIBUTION_NOT_AUTHORIZED');
    fixture.detectChanges();

    expect(component.contributionDeductionFromTheMonthCtrl.disabled).toBeTruthy();
    expect(component.stopContributionDeductionFromTheMonthCtrl.disabled).toBeTruthy();
  });

  it('should reset the value of the disabled date field when changing syndicate contribution type', () => {
    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    component.contributionDeductionFromTheMonthCtrl.setValue('2025-03-01');
    fixture.detectChanges();

    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    fixture.detectChanges();

    expect(component.contributionDeductionFromTheMonthCtrl.value).toBeNull();

    component.stopContributionDeductionFromTheMonthCtrl.setValue('2025-04-01');
    fixture.detectChanges();
    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    fixture.detectChanges();

    expect(component.stopContributionDeductionFromTheMonthCtrl.value).toBeNull();
  });

  it('should maintain panel validity when switching between contribution types with valid values', () => {
    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    component.contributionDeductionFromTheMonthCtrl.setValue('2025-03-01');
    fixture.detectChanges();

    expect(component.isPanelValid('syndicatePanel')).toBeTruthy();

    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    component.stopContributionDeductionFromTheMonthCtrl.setValue('2025-04-01');
    fixture.detectChanges();

    expect(component.isPanelValid('syndicatePanel')).toBeTruthy();

    component.syndicateContributionCtrl.setValue('CONTRIBUTION_NOT_AUTHORIZED');
    fixture.detectChanges();

    expect(component.isPanelValid('syndicatePanel')).toBeTruthy();
  });

  it('should respect current form values during updates', () => {
    component.startDateCtrl.setValue('2025-03-15');
    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    component.syndicateContributionCtrl.setValue('STOP_CONTRIBUTION');
    component['isInitialLoad'] = false;
    const originalStartDate = component.startDateCtrl.value;
    const originalBankAccountType = component.isMyBankAccountCtrl.value;
    const originalSyndicateContribution = component.syndicateContributionCtrl.value;
    const getCurrentFormValuesSpy = jest.spyOn(component as any, 'getCurrentFormValues');
    jest.spyOn(component as any, 'allRequiredDataAvailable').mockReturnValue(true);

    component.ngOnChanges({
      language: {
        currentValue: 'fr',
        previousValue: 'nl',
        firstChange: false,
        isFirstChange: () => false
      }
    });

    expect(component.startDateCtrl.value).toBe(originalStartDate);
    expect(component.isMyBankAccountCtrl.value).toBe(originalBankAccountType);
    expect(component.syndicateContributionCtrl.value).toBe(originalSyndicateContribution);

    getCurrentFormValuesSpy.mockRestore();
  });

  it('should clear all validators when task status is CLOSED', () => {
    const clearAllValidatorsSpy = jest.spyOn(component as any, 'clearAllValidators');
    component.taskStatus = 'CLOSED';
    component['runValidators']();
    expect(clearAllValidatorsSpy).toHaveBeenCalled();
    clearAllValidatorsSpy.mockRestore();
  });

  it('should handle short/invalid IBAN inputs correctly', () => {
    (component as any).updateIbanValidation('BE12');
    expect(component.isBelgianIban).toBe(true);
    expect(component.bicCtrl.hasValidator(Validators.required)).toBe(false);
    component.ibanCtrl.setValue('BE12');
    expect(component.ibanCtrl.valid).toBe(false);
  });

  it('should properly reset form fields when changing syndicate contribution type', () => {
    component.syndicateContributionCtrl.setValue('CONTRIBUTION');
    component.contributionDeductionFromTheMonthCtrl.setValue('2025-03-01');
    component.stopContributionDeductionFromTheMonthCtrl.setValue('2025-04-01');
    fixture.detectChanges();
    component.syndicateContributionCtrl.setValue('CONTRIBUTION_NOT_AUTHORIZED');
    fixture.detectChanges();

    expect(component.contributionDeductionFromTheMonthCtrl.value).toBeNull();
    expect(component.stopContributionDeductionFromTheMonthCtrl.value).toBeNull();
    expect(component.contributionDeductionFromTheMonthCtrl.disabled).toBe(true);
    expect(component.stopContributionDeductionFromTheMonthCtrl.disabled).toBe(true);
  });

  it('should properly handle switching between bank account types multiple times', () => {
    component.ibanCtrl.setValue('****************');
    component.bankFromDateCtrl.setValue('2023-01-01');
    (component as any).updateIbanValidation('****************');
    component.isMyBankAccountCtrl.setValue('is_my_bank_account');
    fixture.detectChanges();
    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    fixture.detectChanges();
    component.otherPersonNameCtrl.setValue('John Smith');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeTruthy();

    component.isMyBankAccountCtrl.setValue('is_my_bank_account');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeTruthy();
    expect(component.otherPersonNameCtrl.value).toBeNull();

    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeFalsy();

    component.otherPersonNameCtrl.setValue('Jane Doe');
    fixture.detectChanges();

    expect(component.isPanelValid('bankPanel')).toBeTruthy();
  });

  it('should validate other person name length', () => {
    component.isMyBankAccountCtrl.setValue('bank_account_for_other_person_name');
    fixture.detectChanges();
    component.otherPersonNameCtrl.setValue('Valid Name');

    expect(component.otherPersonNameCtrl.valid).toBeTruthy();

    component.otherPersonNameCtrl.setValue('a'.repeat(256));

    expect(component.otherPersonNameCtrl.valid).toBeFalsy();
    expect(component.otherPersonNameCtrl.errors?.['maxlength']).toBeTruthy();
  });

  it('should validate BIC format for non-Belgian IBANs', () => {
    component.ibanCtrl.setValue('***************************');
    (component as any).updateIbanValidation('***************************');
    fixture.detectChanges();
    component.bicCtrl.setValue('');

    expect(component.bicCtrl.valid).toBeFalsy();

    component.bicCtrl.setValue('AGRIFRPP');

    expect(component.bicCtrl.valid).toBeTruthy();
  });

  it('should display lookup values in the correct language', () => {
    const lookup = { code: '92250', descFr: 'Bruxelles', descNl: 'Brussel' };
    component.language = 'fr';

    expect(component.displayLookup(lookup)).toBe('Bruxelles');

    component.language = 'nl';

    expect(component.displayLookup(lookup)).toBe('Brussel');

    component.language = 'FR';

    expect(component.displayLookup(lookup)).toBe('Bruxelles');
  });

  it('should not update panels when not all required data is available', () => {
    const updateRequestPanelSpy = jest.spyOn(component as any, 'updateRequestPanel');
    const updateEmployeePanelSpy = jest.spyOn(component as any, 'updateEmployeePanel');

    jest.spyOn(component as any, 'allRequiredDataAvailable').mockReturnValue(false);

    component.ngOnChanges({
      requestBasicInfoResponse: {
        currentValue: mockRequestBasicInfoResponse,
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true
      }
    });

    expect(updateRequestPanelSpy).not.toHaveBeenCalled();
    expect(updateEmployeePanelSpy).not.toHaveBeenCalled();
  });
});