interface MultiLangLabel {
    fr?: string;
    nl?: string;
}

interface TechnicalInformation {
    creationDate?: string | null;
    creationSource?: string;
    creationUser?: string;
    lastUpdateDate?: string | null;
    lastUpdateSource?: string;
    lastUpdateUser?: string;
    closureDate?: string | null;
    closureSource?: string;
    closedDate?: string | null;
    technicalStatus?: string;
    readFromJson?: boolean;
}

interface BusinessIdentifiers {
    ssin?: string;
}

interface Employee {
    name?: string;
    language?: string;
}

interface Group {
    name?: MultiLangLabel;
    nature?: string;
}

interface Node {
    id?: string;
    nodeId?: string;
    type?: string;
    businessIdentifiers?: BusinessIdentifiers;
    employee?: Employee;
    group?: Group;
    memberOf?: Node[];
    _children?: any;
    nature?: string;
    name?: MultiLangLabel;
    technicalInformation?: TechnicalInformation;
    isFavorite?: boolean;
    agentLanguage?: string;
    agentSsin?: string;
}

interface Priority {
    code?: string;
    label?: MultiLangLabel;
}

interface BusinessDataType {
    code?: string;
}

interface BusinessDataUsageType {
    businessDataType?: BusinessDataType;
}

interface BusinessDataItem {
    id?: string;
    businessDataUsageType?: BusinessDataUsageType;
    internalValue?: string;
}

interface BusinessDataList {
    items?: BusinessDataItem[];
}

interface ActionType {
    code?: string;
    label?: MultiLangLabel;
}

interface NoteType {
}

interface Note {
    id?: string;
    content?: string;
    type?: NoteType;
}

interface ActionHistoryItem {
    actionId?: string;
    actionType?: ActionType;
    targetNodeId?: Node;
    originalNodeId?: Node;
    note?: Note;
    technicalInformation?: TechnicalInformation;
}

interface ActionHistory {
    items?: ActionHistoryItem[];
}

interface Role {
    code?: string;
    label?: MultiLangLabel;
}

interface Quality {
    code?: string;
    label?: MultiLangLabel;
}

interface ConcernedEntity {
    sourceId?: string;
    sourceName?: string;
    roleId?: string;
    role?: Role;
    quality?: Quality;
}

interface ConcernedEntities {
    items?: ConcernedEntity[];
}

interface AcceptedAction {
    code?: string;
    label?: MultiLangLabel;
}

interface State {
    code?: string;
    label?: MultiLangLabel;
}

interface Notes {
    items?: Note[];
}

interface ItemsCollection<T> {
    items?: T[];
}

interface BusinessDomain {
    code?: string;
}

interface ProcessType {
    code?: string;
    label?: MultiLangLabel;
    description?: MultiLangLabel;
    legalContext?: any;
    businessDomainTypes?: ItemsCollection<any>;
    milestoneTypes?: ItemsCollection<any>;
}

interface ParentProcess {
    processId?: string;
    dueDate?: string | null;
    actionHistory?: ActionHistory;
    associatedProcess?: ItemsCollection<any>;
    businessDataList?: BusinessDataList;
    concernedEntities?: ConcernedEntities;
    acceptedActions?: ItemsCollection<AcceptedAction>;
    correlations?: any[];
    milestones?: ItemsCollection<any>;
    notes?: Notes;
    subprocesses?: ItemsCollection<any>;
    tasks?: ItemsCollection<any>;
    processType?: ProcessType;
    technicalInformation?: TechnicalInformation;
    documents?: ItemsCollection<any>;
}

export interface WaveTask {
    taskId?: string;
    title?: MultiLangLabel;
    priority?: Priority;
    dueDate?: string;
    businessDataList?: BusinessDataList;
    actionHistory?: ActionHistory;
    concernedEntities?: ConcernedEntities;
    responsible?: Node;
    acceptedAction?: ItemsCollection<AcceptedAction>;
    state?: State;
    correlations?: any[];
    notes?: Notes;
    shipments?: ItemsCollection<any>;
    matchingKeys?: ItemsCollection<any>;
    taskType?: {
        code?: string;
        label?: MultiLangLabel;
        description?: MultiLangLabel | any;
    };
    businessDomain?: BusinessDomain;
    parentProcess?: ParentProcess;
    technicalInformation?: TechnicalInformation;
}