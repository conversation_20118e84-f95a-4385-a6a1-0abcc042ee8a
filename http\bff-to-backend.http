
### Get basic request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/requests/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get employee information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json


### Update employee information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "source": "ONEM",
  "birthDate": "1980-01-01",
  "nationalityCode": 150,
  "address": {
    "street": "Main Street",
    "city": "Springfield",
    "zipCode": "12345",
    "countryCode": 150,
    "houseNumber": "333",
    "boxNumber": "4444"
  }
}

### Update employee information for a request - select C1
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "C1"
    },
    {
      "fieldName": "address",
      "source": "C1"
    }
  ]
}


### Update employee information for a request - select ONEM
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "ONEM"
    },
    {
      "fieldName": "address",
      "source": "ONEM"
    }
  ]
}

### Update employee information for a request - select - AUTHENIC_SOURCES
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "ONEM"
    },
    {
      "fieldName": "address",
      "source": "AUTHENTIC_SOURCES"
    }
  ]
}

### Update mode of payment for a request - select - C1
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/mode-of-payment/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "account",
      "source": "C1"
    },
    {
      "fieldName": "otherPersonName",
      "source": "C1"
    }
  ]
}

### Update mode of payment for a request - select
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/mode-of-payment/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "account",
      "source": "C1"
    },
    {
      "fieldName": "otherPersonName",
      "source": "ONEM"
    }
  ]
}

### Close the CHANGE_PERSONAL_DATA_CAPTURE task for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/CHANGE_PERSONAL_DATA_CAPTURE/close
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Close the VALIDATION_DATA task for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/VALIDATION_DATA/close
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json


### GET Routing Decisions
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{bff-url}}/bff/api/requests/{{latestRequestId}}/routing-decision
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### PUT Routing Decisions - All OK (all false)
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/routing-decision
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

[
  {
    "type": "CITIZEN_OVER_65_YEARS_OLD",
    "value": false
  },
  {
    "type": "RELEVANT_TO_PORT_WORKER",
    "value": false
  },
  {
    "type": "NON_BELGIAN_RESIDENT",
    "value": false
  },
  {
    "type": "TRANSFER_BETWEEN_OP_OR_OC",
    "value": false
  },
  {
    "type": "REQUEST_FOR_ECONOMIC_REASON",
    "value": false
  },
  {
    "type": "RELEVANT_TO_APPRENTICESHIP",
    "value": false
  },
  {
    "type": "CASE_OF_IMPULSION",
    "value": false
  }
]


### PUT Routing Decisions - NOT All OK (one is true)
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{bff-url}}/bff/api/requests/{{latestRequestId}}/routing-decision
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

[
  {
    "type": "CITIZEN_OVER_65_YEARS_OLD",
    "value": false
  },
  {
    "type": "RELEVANT_TO_PORT_WORKER",
    "value": true
  },
  {
    "type": "NON_BELGIAN_RESIDENT",
    "value": false
  },
  {
    "type": "TRANSFER_BETWEEN_OP_OR_OC",
    "value": false
  },
  {
    "type": "REQUEST_FOR_ECONOMIC_REASON",
    "value": false
  },
  {
    "type": "RELEVANT_TO_APPRENTICESHIP",
    "value": false
  },
  {
    "type": "CASE_OF_IMPULSION",
    "value": false
  }
]
