package be.fgov.onerva.cu.backend.cucumber.steps

import java.sql.Date
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.core.JdbcTemplate
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase
import be.fgov.onerva.cu.backend.application.port.out.FeatureFlagPort
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.unemployment.c9.api.Ec1Api
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef
import be.fgov.onerva.unemployment.c9.rest.model.BelgianCommunity
import be.fgov.onerva.unemployment.c9.rest.model.C9
import be.fgov.onerva.unemployment.c9.rest.model.EC1
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment
import be.fgov.onerva.unemployment.c9.rest.model.EC1ReasonIntroduction
import be.fgov.onerva.unemployment.c9.rest.model.EC1TradeUnionContribution
import be.fgov.onerva.unemployment.c9.rest.model.NationalityBCSS
import be.fgov.onerva.wo.facade.rest.model.CreatableTaskDTO
import be.fgov.onerva.wo.facade.rest.model.InputMetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.InputThirdPartyDTO
import be.fgov.onerva.wo.facade.rest.model.StateDTO
import be.fgov.onerva.wo.facade.rest.model.StatusDTO
import be.fgov.onerva.wo.facade.rest.model.TaskDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyTypeDTO
import be.fgov.onerva.wo.organizational.chart.rest.model.Node
import dev.openfeature.sdk.Client
import io.cucumber.java.en.And
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import io.mockk.every
import io.mockk.slot

class ChangePersonalDataSteps : BaseSteps() {

    @Autowired
    private lateinit var changePersonalDataRequestUseCase: ChangePersonalDataRequestUseCase

    @Autowired
    private lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    private lateinit var jdbcHelper: JdbcHelper

    @Autowired
    private lateinit var c9Api: be.fgov.onerva.unemployment.c9.api.C9Api

    @Autowired
    private lateinit var citizenApi: be.fgov.onerva.person.api.CitizenApi

    @Autowired
    private lateinit var facadeControllerApi: be.fgov.onerva.wo.facade.api.FacadeControllerApi

    @Autowired
    private lateinit var defaultApi: be.fgov.onerva.wo_thirdparty.api.DefaultApi

    @Autowired
    private lateinit var nodeApi: be.fgov.onerva.wo.organizational.chart.api.NodeApi

    @Autowired
    private lateinit var currentUserPort: be.fgov.onerva.cu.backend.application.port.out.CurrentUserPort

    @Autowired
    private lateinit var client: Client

    @Autowired
    private lateinit var ec1Api: Ec1Api

    private val c9id: Int = 12345
    private val ssin = "123456789"
    private var changePersonalDataCommand: ChangePersonalDataRequestReceivedCommand? = null
    private val creatableTaskDTO = slot<CreatableTaskDTO>()

    @Given("We receive a change of personal data message {word}")
    fun weReceiveAChangeOfPersonalDataMessage(typeC1: String) {
        changePersonalDataCommand = when (typeC1) {
            "C1" -> ChangePersonalDataRequestReceivedCommand(
                c9Id = c9id.toLong(),
                c9Type = "400",
                ssin = ssin,
                ec1Id = null,
                ec1DisplayUrl = null,
                requestDate = LocalDate.of(2021, 1, 1),
                paymentInstitution = 778899,
                entityCode = "EC123",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = "http://example.com/scan1",
                unemploymentOffice = 123,
                introductionDate = LocalDate.of(2021, 1, 2),
                dateValid = LocalDate.of(2024, 12, 15),
                scanNumber = 778866L,
                operatorCode = 123,
                introductionType = be.fgov.onerva.cu.backend.application.domain.IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.of(2025, 1, 1),
            )

            "EC1" -> ChangePersonalDataRequestReceivedCommand(
                c9Id = c9id.toLong(),
                c9Type = "410",
                ssin = ssin,
                ec1Id = 1234,
                ec1DisplayUrl = "http://ec1-display-url",
                requestDate = LocalDate.of(2021, 1, 1),
                paymentInstitution = 778899,
                entityCode = "EC123",
                opKey = "OP123",
                sectOp = "SO123",
                scanUrl = "http://example.com/scan1",
                unemploymentOffice = 123,
                introductionDate = LocalDate.of(2021, 1, 2),
                dateValid = LocalDate.of(2024, 12, 15),
                scanNumber = 778866L,
                operatorCode = 123,
                introductionType = be.fgov.onerva.cu.backend.application.domain.IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.of(2025, 1, 1),
            )

            else -> throw RuntimeException("Unknown type $typeC1")
        }

        // Setup test data
        val assignee = "cu_user"

        // Setup mocks
        val expectedNumbox = 42
        val citizenDTO = CitizenDTO(numbox = expectedNumbox)
        val taskDto = TaskDTO(
            concernedEntities = emptyList(),
            assignee = "test-assignee",
            taskTypeCode = "test-type",
            processId = 87876,
            taskId = 65544,
            assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
            status = StatusDTO(
                task = StateDTO.OPEN,
                process = StateDTO.OPEN
            )
        )
        val nodeResult = Node()

        val ec1 = EC1().also { eC1 ->
            eC1.identity = EC1Identity().also {
                it.firstName = "John"
                it.lastName = "Doe"
                it.dateOfBirth = "1990-01-01"
                it.nationality = NationalityBCSS().apply {
                    code = "111"
                }
                it.street = "the-street"
                it.houseNumber = "the-housenumber"
                it.boxNumber = "the-box-number"
                it.zipCode = BelgianCommunity().also {
                    it.zipCode = "1000"
                }
                it.city = "Brussels"
                it.country = NationalityBCSS().also {
                    it.code = "150"
                }
            }
            eC1.modeOfPayment = EC1ModeOfPayment().also { eC1ModeOfPayment ->
                eC1ModeOfPayment.isMyBankAccount = true
                eC1ModeOfPayment.foreignBankAccountIBAN = null
                eC1ModeOfPayment.bankAccountForOtherPersonName = null
                eC1ModeOfPayment.belgianSEPABankAccount = "BE776655443232"
                eC1ModeOfPayment.foreignBankAccountBIC = null
            }
            eC1.tradeUnionContribution = EC1TradeUnionContribution().also {
                it.stopContributionDeductionFromTheMonth = LocalDate.of(2025, 2, 1)
                it.contributionDeductionFromTheMonth = null
            }
            eC1.reasonIntroduction = EC1ReasonIntroduction().also {
                it.changeMyAddressFromDate = LocalDate.of(2025, 1, 1)
                it.changeModeOfPaymentOrAccountNrFromDate = LocalDate.of(2025, 1, 1)
            }
        }

        every { client?.getBooleanValue(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
        every { client?.getBooleanValue(FeatureFlagPort.SUPPORTED_C9_TYPES, false) } returns true
        every { client?.getStringValue(FeatureFlagPort.SUPPORTED_C9_TYPES, "") } returns "400,410"

        every { c9Api.getC9s(c9id) } returns C9().also { c9 ->
            c9.id = c9id.toLong()
            c9.ssin = ssin
            c9.type = "400"
            c9.requestDate = LocalDate.of(2021, 1, 1)
            c9.introductionDate = LocalDate.of(2021, 1, 2)
            c9.dateValid = LocalDate.of(2024, 12, 15)
            c9.opKey = "OP123"
            c9.sectOp = "SO123"
            c9.paymentInstitution = 778899
            c9.entityCode = "EC123"
            c9.scanNumber = 778866L
            c9.attestRefs = if (typeC1 == "EC1") listOf(AttestRef().also {
                it.type = "EC1"
                it.id = 1234
                it.displayUrl = "http://example.com"
            }) else emptyList()
        }

        every { ec1Api.getC1s(1234) } returns ec1
        every { citizenApi.getByNiss(ssin) } returns citizenDTO
        every { facadeControllerApi.createTask(capture(creatableTaskDTO)) } returns taskDto
        every { defaultApi.createParty(any(), isNull()) } returns null
        every { nodeApi.createNode(any()) } returns nodeResult
        every { currentUserPort.getCurrentUsername() } returns assignee
    }

    @When("The message is processed")
    fun theMessageIsProcessed() {
        val changePersonalDataCommand = requireNotNull(changePersonalDataCommand)
        // Execute the use case
        changePersonalDataRequestUseCase.receivedChangePersonalData(changePersonalDataCommand)
    }

    @Then("The database is updated with the change of personal data request {word}")
    fun theDatabaseIsUpdatedWithTheRequest(typeC1: String) {
        // Verify database records
        val requestMap = jdbcHelper.getRequestByC9Id(c9id.toLong())

        assertThat(requestMap)
            .containsEntry("ssin", "123456789")
            .containsEntry("c9_id", c9id.toLong())
            .containsEntry("sect_op", "SO123")
            .containsEntry("op_key", "OP123")
            .containsEntry("document_type", if (typeC1 == "C1") "PAPER" else "ELECTRONIC")
            .containsEntry("type", "CHANGE_PERSONAL_DATA")

        val requestId = UUID.fromString(requestMap["id"]!!.toString())

        when (typeC1) {
            "C1" -> {
                // Verify the absence of related records
                assertThat(
                    jdbcTemplate.queryForList(
                        """
                select * from citizen_information where request_id = ?
                """.trimIndent(),
                        requestId
                    )
                ).isEmpty()

                assertThat(
                    jdbcTemplate.queryForList(
                        """
                select * from mode_of_payment where request_id = ?
                """.trimIndent(),
                        requestId
                    )
                ).isEmpty()

                assertThat(
                    jdbcTemplate.queryForList(
                        """
                select * from union_contribution where request_id = ?
                """.trimIndent(),
                        requestId
                    )
                ).isEmpty()
            }

            "EC1" -> {
                val citizenInformationMap = jdbcHelper.getCitizenInformation(requestId)
                assertThat(citizenInformationMap)
                    .containsEntry("request_id", requestId.toString().uppercase())
                    .containsEntry("birth_date", Date.valueOf("1990-01-01"))
                    .containsEntry("nationality_code", 111)
                    .containsEntry("street", "the-street")
                    .containsEntry("box_number", "the-box-number")
                    .containsEntry("house_number", "the-housenumber")
                    .containsEntry("zip_code", "1000")
                    .containsEntry("city", "Brussels")
                    .containsEntry("country_code", 150)
                    .containsEntry("update_status", "FROM_C9")

                val modeOfPaymentMap = jdbcHelper.getModeOfPayment(requestId)
                assertThat(modeOfPaymentMap)
                    .containsEntry("request_id", requestId.toString().uppercase())
                    .containsEntry("other_person_name", null)
                    .containsEntry("iban", "BE776655443232")
                    .containsEntry("bic", null)
                    .containsEntry("update_status", "FROM_C9")

                val unionContributionMap = jdbcHelper.getUnionContribution(requestId)
                println(unionContributionMap)
                assertThat(unionContributionMap)
                    .containsEntry("request_id", requestId.toString().uppercase())
                    .containsEntry("authorized", false)
                    .containsEntry("effective_date", Date.valueOf("2025-02-01"))
                    .containsEntry("update_status", "FROM_C9")
            }

            else -> error("Unknown type $typeC1")
        }
    }

    @And("The wave task is created {word}")
    fun theWaveTaskIsCreated(typeC1: String) {
        val requestId = jdbcHelper.getRequestIdByC9Id(c9id.toLong())
        assertThat(creatableTaskDTO.captured).isNotNull.extracting(
            "processId",
            "assignee",
            "taskTypeCode",
            "concernedEntities",
            "processMetadata",
            "metadata"

        ).containsExactly(
            null,
            "EEC123",
            "CHANGE_PERSONAL_DATA_CAPTURE",
            listOf(InputThirdPartyDTO(id = "42", type = ThirdPartyTypeDTO.CITIZEN, quality = null)),
            listOf(
                InputMetaDataDTO(code = "CU_REQUEST_ID", value = requestId.toString()),
                InputMetaDataDTO(code = "CU_C9_TYPE", value = if (typeC1 == "C1") "400" else "410"),
                InputMetaDataDTO(code = "CU_RECEPTION_DATE", value = "2021-01-02"),
                InputMetaDataDTO(code = "CU_REQUEST_DATE", value = "2021-01-01"),
                InputMetaDataDTO(code = "CU_DOSSIER_ID", value = "OP123SO123"),
                InputMetaDataDTO(code = "CU_ENTITY", value = "EC123"),
                InputMetaDataDTO(code = "CU_PAYMENT_INSTITUTION", value = "SO123"),
                InputMetaDataDTO(code = "CU_DECISION_BAREMA", value = ""),
                InputMetaDataDTO(code = "CU_DECISION_TYPE", value = "")
            ),
            listOf(
                InputMetaDataDTO(code = "CU_REQUEST_ID", value = requestId.toString()),
                InputMetaDataDTO(code = "CU_RECEPTION_DATE", value = "2021-01-02"),
                InputMetaDataDTO(code = "CU_ENTITY", value = "EC123"),
                InputMetaDataDTO(code = "CU_C9_TYPE", value = if (typeC1 == "C1") "400" else "410")
            )
        )

        val waveTaskMap = jdbcHelper.getWaveTask(requestId)
        assertThat(waveTaskMap).isNotNull.extracting("request_id", "process_id", "task_id", "status")
            .containsExactly(requestId.toString().uppercase(), "87876", "65544", "OPEN")
    }
}