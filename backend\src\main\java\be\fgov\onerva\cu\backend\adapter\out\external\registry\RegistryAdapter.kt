package be.fgov.onerva.cu.backend.adapter.out.external.registry

import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.adapter.out.mapper.toCitizenInfoWithAddress
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.RegistryPort
import be.fgov.onerva.cu.backend.lookup.BaseLookupDTO
import be.fgov.onerva.cu.backend.lookup.LookupService
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.registerproxyservice.api.CitizenApi
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress

@Service
class RegistryAdapter(
    private val registryService: CitizenApi,
    private val lookupService: LookupService,
) : RegistryPort {
    private val log = logger

    @LogMethodCall
    override fun getRegistryInformationForCitizen(@SensitiveParam ssin: String): HistoricalCitizenAuthenticSources {
        val registerPerson =
            registryService.getCitizenInfosBySsin(ssin, null, true, false, false, null, true, true, false)
        log.debug("getRegistryInformationForCitizen: Citizen found for ssin {}: {}", ssin, registerPerson)

        if (registerPerson == null || registerPerson.ssin == null) {
            throw CitizenNotFoundException("No valid address found for SSIN (not found in registry)")
        }

        val citizenInfoWithAddress = registerPerson.toCitizenInfoWithAddress()

        val residentialAddress =
            registerPerson.addresses.firstOrNull { it.atType == RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS && it.radiated == false } as? ResidentialAddress

        if (residentialAddress == null) {
            return citizenInfoWithAddress
        }

        val streetByCode = lookupService.getStreetNameByStreetCode(
            residentialAddress.postalCode, residentialAddress.streetCode
        )

        val isDutch =
            lookupService.lookupPostalCodeLanguageRegime(residentialAddress.postalCode)?.languageRegimeCode == "N0"

        // Determine which language description to use
        val languageSelector: (BaseLookupDTO) -> String = { lookup ->
            if (isDutch) lookup.descNl else lookup.descFr
        }

        // Lookup necessary values
        val cityByPostalCode =
            lookupService.getCityByPostalCode(residentialAddress.postalCode, residentialAddress.cityCode)
        val countryCode = residentialAddress.countryCode
        val countryByCode = lookupService.getNationalityByCode(countryCode)
        if (cityByPostalCode == null || countryByCode == null || streetByCode == null) {
            log.warn("No valid address found for SSIN (lookup) - city: $cityByPostalCode, country: $countryByCode, street: $streetByCode")
            throw CitizenNotFoundException("No valid address found for SSIN (lookup)")
        }
        val updatedStreet = languageSelector(streetByCode)
        val updatedCity = languageSelector(
            cityByPostalCode
        )
        val valueDate = LocalDate.ofInstant(
            Instant.ofEpochMilli(residentialAddress.validityPeriod.beginDate), ZoneId.of("Europe/Brussels")
        )

        // Create new CitizenInfoWithAddress with updated address
        return citizenInfoWithAddress.copy(
            address = citizenInfoWithAddress.address.copy(
                street = updatedStreet,
                city = updatedCity,
                countryCode = countryCode,
                valueDate = valueDate,
            ),
        )
    }
}