import {ComponentFixture, TestBed} from "@angular/core/testing";
import {MatDialog} from "@angular/material/dialog";
import {NoopAnimationsModule} from "@angular/platform-browser/animations";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {of} from "rxjs";
import {Origin} from "../../../model/types";
import {ConsistencyTableComponent, ConsistencyTableElement} from "./consistency-table.component";
import {GeoLookupService} from "../../../http/geo-lookup.service";
import {DateUtils} from "../../../date.utils";
import {SimpleChange} from "@angular/core";
import {MatTableDataSource} from "@angular/material/table";
import {printFormat as formatIban} from "iban-ts";
import { StringUtils } from "../../../string.utils";

// Mock iban-ts module
jest.mock("iban-ts", () => ({
    printFormat: jest.fn().mockImplementation(iban => iban),
}));

describe("ConsistencyTableComponent", () => {
    let component: ConsistencyTableComponent;
    let fixture: ComponentFixture<ConsistencyTableComponent>;
    let geoLookupServiceMock: Partial<GeoLookupService>;
    let translateServiceMock: Partial<TranslateService>;
    let mockMatDialog: any;

    function getMockDataConsistencyData() {
        return {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                dateOfBirth: "1990-01-01",
                nationalityCode: 150,
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                },
                unionDue: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                },
                iban: "****************",
            },
            onemCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                birthDate: "1990-01-01",
                nationalityCode: 150,
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                },
                unionDue: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                },
                iban: "****************",
            },
            authenticCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                birthDate: "1990-01-01",
                nationalityCode: 150,
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                },
                iban: "****************",
            },
            citizenInformation: {
                address: {
                    validFrom: null
                },
                fieldSources: []
            },
            modeOfPayment: {
                validFrom: null,
                fieldSources: []
            },
            unionContribution: {
                authorized: false,
                effectiveDate: "2025-01-01",
                fieldSources: []
            }
        };
    }

    beforeEach(async () => {
        geoLookupServiceMock = {
            getEntityDescription: jest.fn().mockImplementation((code, lang) => {
                const descriptions: Record<string, Record<string, string>> = {
                    "150": {"NL": "België", "FR": "Belgique"},
                    "111": {"NL": "Frankrijk", "FR": "France"},
                };
                return descriptions[code]?.[lang] || code;
            }),
        };

        translateServiceMock = {
            use: jest.fn(),
            currentLang: "NL",
            instant: jest.fn((key: string) => key),
        };

        mockMatDialog = {
            open: jest.fn().mockReturnValue({
                afterClosed: jest.fn().mockReturnValue(of(Origin.Employee)),
            }),
        };

        await TestBed.configureTestingModule({
            imports: [
                NoopAnimationsModule,
                ConsistencyTableComponent,
                TranslateModule.forRoot(),
            ],
            providers: [
                {provide: GeoLookupService, useValue: geoLookupServiceMock},
                {provide: TranslateService, useValue: translateServiceMock},
                {provide: MatDialog, useValue: mockMatDialog},
            ],
        }).compileComponents();

        fixture = TestBed.createComponent(ConsistencyTableComponent);
        component = fixture.componentInstance;

        component.displayedColumns = ["label", "encodedValue", "dbValue", "sourceValue", "actions"];
        component.dataConsistencySelectedValues = {};

        jest.spyOn(DateUtils, "formatDateTo").mockImplementation((date, format) => {
            if (!date) {
                return "";
            }
            return date instanceof Date ? date.toISOString().split("T")[0] : date;
        });
    });

    it("should create", () => {
        expect(component).toBeTruthy();
    });

    it("should initialize with default values", () => {
        expect(component.language).toBe("NL");
        expect(component.tableDataSource).toBeDefined();
        expect(component.tableDataSource.data).toEqual([]);
    });

    it("should set language and update translations", () => {
        component.language = "FR";

        expect(component.language).toBe("FR");
        expect(translateServiceMock.use).toHaveBeenCalledWith("FR");
    });

    it("should prepare table data on initialization when data is provided", () => {
        const prepareTableDataSpy = jest.spyOn(component, "prepareTableData");

        component.dataConsistencyData = getMockDataConsistencyData();
        component.dataSource = [];

        component.ngOnInit();

        expect(prepareTableDataSpy).toHaveBeenCalled();
        expect(component.tableDataSource.data.length).toBeGreaterThan(0);
    });

    it("should update on data source changes", () => {
        const prepareTableDataSpy = jest.spyOn(component, "prepareTableData");

        component.dataConsistencyData = getMockDataConsistencyData();
        component.dataSource = [];

        component.ngOnChanges({
            dataSource: new SimpleChange(null, [], true),
        });

        expect(prepareTableDataSpy).toHaveBeenCalled();
    });

    it("should update nationality displays when language changes", () => {
        const updateNationalitySpy = jest.spyOn<any, any>(component, "updateNationalityDisplays");

        component.dataConsistencyData = getMockDataConsistencyData();
        component.dataSource = [];
        component.prepareTableData();

        component.ngOnChanges({
            language: new SimpleChange("NL", "FR", false),
        });

        expect(updateNationalitySpy).toHaveBeenCalled();
    });

    it("should correctly prepare table data with citizen information", () => {
        component.dataConsistencyData = getMockDataConsistencyData();
        component.prepareTableData();

        const tableData = component.tableDataSource.data;

        expect(tableData.length).toBe(6);
        expect(tableData[0].id).toBe("birthDate");
        expect(tableData[1].id).toBe("address");
        expect(tableData[2].id).toBe("nationality");
        expect(tableData.some(item => item.id === "bankAccount")).toBe(true);
        expect(tableData.some(item => item.id === "otherPersonName")).toBe(true);
        expect(tableData.some(item => item.id === "cotisation")).toBe(true);
    });

    describe("Private utility methods", () => {
        it("should normalize text by removing accents and converting to uppercase", () => {
            const normalizeText = component["normalizeText"].bind(component);

            expect(normalizeText("Hélène")).toBe("HELENE");
            expect(normalizeText("  Jürgen ")).toBe("JURGEN");
            expect(normalizeText("")).toBe("");
            expect(normalizeText(null as any)).toBe("");
        });

        it("should compare dates correctly", () => {
            const compareDates = component["compareDates"].bind(component);

            expect(compareDates(["2023-01-01", "2023-01-01", "-"])).toBe(true);
            expect(compareDates(["2023-01-01", "2023-01-02", "-"])).toBe(false);
            expect(compareDates(["2023-01-01", "-", "-"])).toBe(true);
            expect(compareDates(["-", "-", "-"])).toBe(true);

            // Test with invalid date format that should be handled gracefully
            expect(compareDates(["invalid-date", "-", "-"])).toBe(true);
        });

        it("should compare addresses correctly", () => {
            const compareAddresses = component["compareAddresses"].bind(component);

            const citizenInfo1 = {
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            const onemInfo1 = {
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            expect(compareAddresses(citizenInfo1, onemInfo1)).toBe(true);

            // Different house number
            const onemInfo2 = {
                address: {
                    street: "Main Street",
                    houseNumber: "124",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            expect(compareAddresses(citizenInfo1, onemInfo2)).toBe(false);

            // Test with accents
            const citizenInfo2 = {
                address: {
                    street: "Chaussée de Gand",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            const onemInfo3 = {
                address: {
                    street: "CHAUSSEE DE GAND",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            expect(compareAddresses(citizenInfo2, onemInfo3)).toBe(true);

            // Test with old format properties
            const citizenInfo3 = {
                street: "Main Street",
                housNbr: "123",
                postBox: "A",
                zipCode: "1000",
            };

            expect(compareAddresses(citizenInfo3, onemInfo1)).toBe(true);

            // Handle missing onemInfo
            expect(compareAddresses(citizenInfo1, null)).toBe(true);
            expect(compareAddresses(citizenInfo1, {})).toBe(true);
            expect(compareAddresses(null, onemInfo1)).toBe(true);
        });

        it("should compare nationality codes correctly", () => {
            const compareNationality = component["compareNationality"].bind(component);

            // Both have same code
            const citizenInfo1 = {nationalityCode: 150,};
            const onemInfo1 = {nationalityCode: 150,};
            const authenticInfo = {nationalityCode: 150,};
            expect(compareNationality(citizenInfo1, onemInfo1, authenticInfo)).toBe(true);

            // Different codes
            const onemInfo2 = {nationalityCode: 111};
            expect(compareNationality(citizenInfo1, onemInfo2, authenticInfo)).toBe(false);

            // Handle missing data
            expect(compareNationality(null, onemInfo1, authenticInfo)).toBe(true);
            expect(compareNationality(citizenInfo1, null, authenticInfo)).toBe(true);
            expect(compareNationality({}, {}, {})).toBe(true);
            expect(compareNationality({}, {nationality: "BE"}, {})).toBe(true);
            expect(compareNationality({nationality: "BE"}, {}, {})).toBe(true);
        });

        it("should compare bank accounts correctly", () => {
            const compareAccounts = component["compareAccounts"].bind(component);

            const belgianAccount1 = {iban: "****************", bic: "KREDBEBB", isForeign: false};
            const belgianAccount2 = {iban: "BE68 5390 0754 7034", bic: "KREDBEBB", isForeign: false};
            expect(compareAccounts(belgianAccount1, belgianAccount2)).toBe(true);

            const belgianAccount3 = {iban: "****************", bic: "KREDBEBB", isForeign: false};
            expect(compareAccounts(belgianAccount1, belgianAccount3)).toBe(false);

            const foreignAccount1 = {iban: "***************************", bic: "BNPAFRPP", isForeign: true};
            expect(compareAccounts(belgianAccount1, foreignAccount1)).toBe(false);

            const foreignAccount2 = {iban: "***************************", bic: "BNPAFRPP", isForeign: true};
            expect(compareAccounts(foreignAccount1, foreignAccount2)).toBe(true);

            const foreignAccount3 = {iban: "***************************", bic: "ABCDEFGH", isForeign: true};
            expect(compareAccounts(foreignAccount1, foreignAccount3)).toBe(false);

            const foreignAccountNoBic = {iban: "***************************", bic: "", isForeign: true};
            expect(compareAccounts(foreignAccount1, foreignAccountNoBic)).toBe(false);

            expect(compareAccounts({iban: "", bic: "", isForeign: false}, belgianAccount1)).toBe(true);
            expect(compareAccounts(belgianAccount1, {iban: "", bic: "", isForeign: false})).toBe(true);
        });

        it("should correctly format bank accounts with BIC for foreign accounts", () => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                onemCitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                authenticCitizenInformation: {},
                citizenInformation: {
                    address: {
                        validFrom: null
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: null,
                    fieldSources: []
                },
                unionContribution: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                    fieldSources: []
                }
            };

            component.prepareTableData();

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");

            expect(bankAccountRow?.encodedValue).toContain("BIC: BNPAFRPP");
            expect(bankAccountRow?.dbValue).toContain("BIC: BNPAFRPP");
            expect(bankAccountRow?.isConsistent).toBe(true);
        });

        it("should handle mixed account types correctly", () => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    iban: "****************",
                    bic: "KREDBEBB",
                },
                onemCitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                authenticCitizenInformation: {},
                citizenInformation: {
                    address: {
                        validFrom: null
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: null,
                    fieldSources: []
                },
                unionContribution: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                    fieldSources: []
                }
            };

            component.prepareTableData();

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");

            expect(bankAccountRow?.isConsistent).toBe(false);
        });

        it("should handle foreign accounts without BIC", () => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    iban: "***************************",
                    bic: "",  // missing BIC
                },
                onemCitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                authenticCitizenInformation: {},
                citizenInformation: {
                    address: {
                        validFrom: null
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: null,
                    fieldSources: []
                },
                unionContribution: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                    fieldSources: []
                }
            };

            component.prepareTableData();

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");

            expect(bankAccountRow?.isConsistent).toBe(false);
        });

        it("should compare names correctly", () => {
            const compareNames = component["compareNames"].bind(component);

            expect(compareNames(["John Doe", "JOHN DOE", "-"])).toBe(true);
            expect(compareNames(["John Doe", "Jane Doe", "-"])).toBe(false);
            expect(compareNames(["Jean-François", "JEAN-FRANCOIS", "-"])).toBe(true);
            expect(compareNames(["", "-", "-"])).toBe(true);
        });
    });

    describe("Date values in table data", () => {
        it("should correctly set date values from provided data", () => {
            const testDate = "01/01/2025";
            const addressValueDate = "02/01/2025";
            const bankAccountValueDate = "03/01/2025";
            const unionValueDate = "04/01/2025";

            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: testDate,
                },
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationalityCode: 150,
                    iban: "****************",
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-01",
                    nationalityCode: 150,
                    addressValueDate: addressValueDate,
                    bankAccountValueDate: bankAccountValueDate,
                    unionContributionValueDate: unionValueDate,
                    iban: "****************",
                },
                authenticCitizenInformation: {},
                citizenInformation: {
                    address: {
                        validFrom: testDate
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: testDate,
                    fieldSources: []
                },
                unionContribution: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                    fieldSources: []
                }
            };

            component.prepareTableData();

            const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
            expect(birthDateRow?.encodedDate).toBe(testDate);
            expect(birthDateRow?.dbDate).toBe(addressValueDate);
            expect(birthDateRow?.sourceDate).toBe("-");

            const addressRow = component.tableDataSource.data.find(row => row.id === "address");
            expect(addressRow?.encodedDate).toBe(testDate);
            expect(addressRow?.dbDate).toBe(addressValueDate);
            expect(addressRow?.sourceDate).toBe("-");

            const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");
            expect(nationalityRow?.encodedDate).toBe(testDate);
            expect(nationalityRow?.dbDate).toBe(addressValueDate);
            expect(nationalityRow?.sourceDate).toBe("-");

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");
            expect(bankAccountRow?.encodedDate).toBe(testDate);
            expect(bankAccountRow?.dbDate).toBe(bankAccountValueDate);
            expect(bankAccountRow?.sourceDate).toBe("-");

            const otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");
            expect(otherPersonNameRow?.encodedDate).toBe(testDate);
            expect(otherPersonNameRow?.dbDate).toBe(bankAccountValueDate);
            expect(otherPersonNameRow?.sourceDate).toBe("-");

            const unionRow = component.tableDataSource.data.find(row => row.id === "cotisation");
            expect(unionRow?.encodedDate).toBe(testDate);
            expect(unionRow?.dbDate).toBe(unionValueDate);
            expect(unionRow?.sourceDate).toBe("-");
        });

        it("should handle missing date values by using default \"-\"", () => {
            component.dataConsistencyData = {
                basicInfo: {},
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationalityCode: 150,
                    iban: "****************",
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-01",
                    nationalityCode: 150,
                    iban: "****************",
                },
                authenticCitizenInformation: {},
                citizenInformation: {
                    address: {
                        validFrom: null
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: null,
                    fieldSources: []
                },
                unionContribution: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                    fieldSources: []
                }
            };

            component.prepareTableData();

            // Check that default value '-' is used for all missing dates
            const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
            expect(birthDateRow?.encodedDate).toBe("-");
            expect(birthDateRow?.dbDate).toBe("-");
            expect(birthDateRow?.sourceDate).toBe("-");

            const addressRow = component.tableDataSource.data.find(row => row.id === "address");
            expect(addressRow?.encodedDate).toBe("-");
            expect(addressRow?.dbDate).toBe("-");
            expect(addressRow?.sourceDate).toBe("-");
        });

        it("should pass date values to table elements consistently", () => {
            const testDate = "01/01/2025";
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: testDate,
                },
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationalityCode: 150,
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-01",
                    nationalityCode: 150,
                    addressValueDate: "02/01/2025",
                },
                authenticCitizenInformation: {},
                citizenInformation: {
                    address: {
                        validFrom: null
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: null,
                    fieldSources: []
                },
                unionContribution: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                    fieldSources: []
                }
            };

            component.prepareTableData();

            expect(component.tableDataSource.data.length).toBeGreaterThan(0);

            component.tableDataSource.data.forEach((element: ConsistencyTableElement) => {
                expect(element).toHaveProperty("encodedDate");
                expect(element).toHaveProperty("dbDate");
                expect(element).toHaveProperty("sourceDate");
            });
        });
    });

    it("should get the correct icon based on consistency", () => {
        expect(component.getIcon(true)).toBe("check_circle");
        expect(component.getIcon(false)).toBe("warning");
    });

    it("should return the value from getModifiedValue", () => {
        const selectedValue = {
            fieldName: "nationality",
            origin: "onem",
            value: "BE",
        };

        expect(component.getModifiedValue(selectedValue)).toBe("BE");
        expect(component.getModifiedValue(null)).toBe(null);
    });

    it("should update nationality displays correctly", () => {
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                nationalityCode: 150,
            },
            onemCitizenInformation: {
                nationalityCode: 111,
            },
            citizenInformation: {
                address: {
                    validFrom: null
                },
                fieldSources: []
            },
            modeOfPayment: {
                validFrom: null,
                fieldSources: []
            },
            unionContribution: {
                authorized: false,
                effectiveDate: "2025-01-01",
                fieldSources: []
            }
        };

        component.tableDataSource = new MatTableDataSource([
            {
                id: "nationality",
                label: "Nationality",
                encodedValue: "Unknown",
                dbValue: "Unknown",
                sourceValue: "-",
                valueToKeep: "",

                encodedDate: "-",
                dbDate: "-",
                sourceDate: "-",
            },
        ]);

        component["updateNationalityDisplays"]();

        const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");
        expect(nationalityRow?.encodedValue).toBe("België");
        expect(nationalityRow?.dbValue).toBe("Frankrijk");
    });

    it("should handle otherPersonName from otherPersonName", () => {
        // Test with otherPersonName present
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                otherPersonName: "Jane Smith",
            },
            onemCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                otherPersonName: "Jane Smith",
            },
            authenticCitizenInformation: {},
            citizenInformation: {
                address: {
                    validFrom: null
                },
                fieldSources: []
            },
            modeOfPayment: {
                validFrom: null,
                fieldSources: []
            },
            unionContribution: {
                authorized: false,
                effectiveDate: "2025-01-01",
                fieldSources: []
            }
        };

        component.prepareTableData();
        let otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");
        expect(otherPersonNameRow?.encodedValue).toBe("Jane Smith");

        // Test with only basic info
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                firstName: "John",
                lastName: "Doe",
            },
            onemCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
            },
            authenticCitizenInformation: {},
            citizenInformation: {
                address: {
                    validFrom: null
                },
                fieldSources: []
            },
            modeOfPayment: {
                validFrom: null,
                fieldSources: []
            },
            unionContribution: {
                authorized: false,
                effectiveDate: "2025-01-01",
                fieldSources: []
            }
        };

        component.prepareTableData();
        otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");
        expect(otherPersonNameRow?.encodedValue).toBeUndefined;
    });

    it("should initialize dataConsistencySelectedValues when undefined", () => {
        component.dataConsistencySelectedValues = undefined as any;
        component.ngOnInit();
        expect(component.dataConsistencySelectedValues).toEqual({});
    });

    it("should format IBAN using iban-ts library", () => {
        const rawIban = "****************";
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                iban: rawIban,
            },
            onemCitizenInformation: {
                iban: "",
            },
            authenticCitizenInformation: {},
            citizenInformation: {
                address: {
                    validFrom: null
                },
                fieldSources: []
            },
            modeOfPayment: {
                validFrom: null,
                fieldSources: []
            },
            unionContribution: {
                authorized: false,
                effectiveDate: "2025-01-01",
                fieldSources: []
            }
        };

        component.prepareTableData();

        expect(formatIban).toHaveBeenCalledWith(rawIban);

        // Check foreign IBAN handling
        const foreignIban = "***************************";
        component.dataConsistencyData.c1CitizenInformation = {
            iban: foreignIban,
        };

        component.prepareTableData();

        expect(formatIban).toHaveBeenCalledWith(foreignIban);
    });

    it("should format bank account objects in getModifiedValue", () => {
        const belgianAccount = {
            fieldName: "bankAccount",
            origin: "Employee",
            value: {
                iban: "****************",
                bic: "KREDBEBB",
            },
        };

        expect(component.getModifiedValue(belgianAccount)).toBe("****************");
        const foreignAccount = {
            fieldName: "bankAccount",
            origin: "Employee",
            value: {
                iban: "***************************",
                bic: "BNPAFRPP",
            },
        };

        expect(component.getModifiedValue(foreignAccount))
            .toBe("*************************** <span class=\"bicToCompair\">BIC: BNPAFRPP</span>");
        const regularValue = {
            fieldName: "otherPersonName",
            origin: "Employee",
            value: "John Doe",
        };

        expect(component.getModifiedValue(regularValue)).toBe("John Doe");
    });

    describe("Prefill logic", () => {
        beforeEach(() => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationalityCode: 150,
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        zipCode: "1000",
                        city: "Brussels",
                    },
                    iban: "****************",
                    otherPersonName: "John Doe",
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-02", // Different from EC1
                    nationalityCode: 111, // Different from EC1
                    address: {
                        street: "Other Street",
                        houseNumber: "456",
                        zipCode: "2000",
                        city: "Antwerp",
                    },
                    iban: "****************",
                    otherPersonName: "Jane Smith",
                },
                authenticCitizenInformation: {
                    birthDate: "1990-01-01", // Same as EC1
                    nationalityCode: 150, // Same as EC1
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        zipCode: "1000",
                        city: "Brussels",
                    },
                    valueDate: "01/01/2025",
                },
                unionContribution: {
                    authorized: true,
                    effectiveDate: "2025-01-01",
                },
                citizenInformation: {
                    address: {
                        validFrom: null
                    },
                    fieldSources: []
                },
                modeOfPayment: {
                    validFrom: null,
                    fieldSources: []
                },
            };
        });

        describe("shouldPrefillValue", () => {
            it("should return true when EC1 == SA and EC1 != MF", () => {
                const shouldPrefillValue = component["shouldPrefillValue"].bind(component);

                const entry: ConsistencyTableElement = {
                    id: "birthDate",
                    label: "Birth Date",
                    encodedValue: "1990-01-01", // EC1
                    dbValue: "1990-01-02", // MF (different)
                    sourceValue: "1990-01-01", // SA (same as EC1)
                    valueToKeep: "",
                    encodedDate: "01/01/2025",
                    dbDate: "01/01/2025",
                    sourceDate: "01/01/2025",
                    isConsistent: false,
                };

                expect(shouldPrefillValue(entry)).toBe(true);
            });

            it("should return false when EC1 != SA", () => {
                const shouldPrefillValue = component["shouldPrefillValue"].bind(component);

                const entry: ConsistencyTableElement = {
                    id: "birthDate",
                    label: "Birth Date",
                    encodedValue: "1990-01-01", // EC1
                    dbValue: "1990-01-02", // MF
                    sourceValue: "1990-01-03", // SA (different from EC1)
                    valueToKeep: "",
                    encodedDate: "01/01/2025",
                    dbDate: "01/01/2025",
                    sourceDate: "01/01/2025",
                    isConsistent: false,
                };

                expect(shouldPrefillValue(entry)).toBe(false);
            });

            it("should return false when EC1 == MF", () => {
                const shouldPrefillValue = component["shouldPrefillValue"].bind(component);

                const entry: ConsistencyTableElement = {
                    id: "birthDate",
                    label: "Birth Date",
                    encodedValue: "1990-01-01", // EC1
                    dbValue: "1990-01-01", // MF (same as EC1)
                    sourceValue: "1990-01-01", // SA
                    valueToKeep: "",
                    encodedDate: "01/01/2025",
                    dbDate: "01/01/2025",
                    sourceDate: "01/01/2025",
                    isConsistent: false,
                };

                expect(shouldPrefillValue(entry)).toBe(false);
            });

            it("should return false when entry is already consistent", () => {
                const shouldPrefillValue = component["shouldPrefillValue"].bind(component);

                const entry: ConsistencyTableElement = {
                    id: "birthDate",
                    label: "Birth Date",
                    encodedValue: "1990-01-01",
                    dbValue: "1990-01-02",
                    sourceValue: "1990-01-01",
                    valueToKeep: "",
                    encodedDate: "01/01/2025",
                    dbDate: "01/01/2025",
                    sourceDate: "01/01/2025",
                    isConsistent: true,
                };

                expect(shouldPrefillValue(entry)).toBe(false);
            });

            it("should return false when any value is missing", () => {
                const shouldPrefillValue = component["shouldPrefillValue"].bind(component);

                const entry: ConsistencyTableElement = {
                    id: "birthDate",
                    label: "Birth Date",
                    encodedValue: "1990-01-01",
                    dbValue: "-", // Missing value
                    sourceValue: "1990-01-01",
                    valueToKeep: "",
                    encodedDate: "01/01/2025",
                    dbDate: "01/01/2025",
                    sourceDate: "01/01/2025",
                    isConsistent: false,
                };

                expect(shouldPrefillValue(entry)).toBe(false);
            });
        });

        describe("compareEntryValues", () => {
            it("should compare birth dates correctly", () => {
                const compareEntryValues = component["compareEntryValues"].bind(component);

                expect(compareEntryValues("birthDate", "1990-01-01", "1990-01-01")).toBe(true);
                expect(compareEntryValues("birthDate", "1990-01-01", "1990-01-02")).toBe(false);
            });

            it("should compare addresses with normalized text", () => {
                const compareEntryValues = component["compareEntryValues"].bind(component);

                expect(compareEntryValues("address", "Rue de la Paix", "RUE DE LA PAIX")).toBe(true);
                expect(compareEntryValues("address", "Chaussée de Gand", "CHAUSSEE DE GAND")).toBe(true);
                expect(compareEntryValues("address", "Main Street", "Other Street")).toBe(false);
            });

            it("should compare nationality with normalized text", () => {
                const compareEntryValues = component["compareEntryValues"].bind(component);

                expect(compareEntryValues("nationality", "België", "BELGIE")).toBe(true);
                expect(compareEntryValues("nationality", "France", "Belgium")).toBe(false);
            });

            it("should compare cotisation with normalized text", () => {
                const compareEntryValues = component["compareEntryValues"].bind(component);

                expect(compareEntryValues("cotisation", "Autorisé", "AUTORISE")).toBe(true);
                expect(compareEntryValues("cotisation", "Yes", "No")).toBe(false);
            });

            it("should return false for unsupported fields", () => {
                const compareEntryValues = component["compareEntryValues"].bind(component);

                expect(compareEntryValues("bankAccount", "BE123", "BE123")).toBe(false);
                expect(compareEntryValues("otherPersonName", "John", "John")).toBe(false);
            });
        });

        describe("getEC1Value", () => {
            it("should return formatted birth date", () => {
                const getEC1Value = component["getEC1Value"].bind(component);

                const value = getEC1Value("birthDate");
                expect(value).toBe("1990-01-01");
            });

            it("should return formatted address", () => {
                const getEC1Value = component["getEC1Value"].bind(component);

                const value = getEC1Value("address");
                expect(value).toContain("Main Street");
                expect(value).toContain("123");
                expect(value).toContain("1000");
                expect(value).toContain("Brussels");
            });

            it("should return nationality description", () => {
                const getEC1Value = component["getEC1Value"].bind(component);

                const value = getEC1Value("nationality");
                expect(value).toBe("België");
            });

            it("should return union contribution formatted string", () => {
                const getEC1Value = component["getEC1Value"].bind(component);

                const value = getEC1Value("cotisation");
                expect(value).toContain("CONTRIBUTION_AUTHORIZATION");
                expect(value).toContain("2025-01-01");
            });

            it("should return null for unsupported fields", () => {
                const getEC1Value = component["getEC1Value"].bind(component);

                expect(getEC1Value("unknownField")).toBeNull();
            });

            it("should handle missing data gracefully", () => {
                const getEC1Value = component["getEC1Value"].bind(component);

                component.dataConsistencyData.c1CitizenInformation.address = null;
                expect(getEC1Value("address")).toBe("-");

                component.dataConsistencyData.c1CitizenInformation.nationalityCode = null;
                expect(getEC1Value("nationality")).toBe("-");

                component.dataConsistencyData.unionContribution = null;
                expect(getEC1Value("cotisation")).toBe("-");
            });
        });

        describe("prepareTableData with prefill", () => {
            it("should prefill values when EC1 == SA and EC1 != MF", () => {
                component.prepareTableData();

                const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
                const addressRow = component.tableDataSource.data.find(row => row.id === "address");
                const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");

                // Birth date should be prefilled (EC1 == SA, EC1 != MF)
                expect(birthDateRow?.selectedValue).toBeTruthy();
                expect(birthDateRow?.selectedValue?.origin).toBe("Employee");
                expect(birthDateRow?.selectedValue?.value).toBe("1990-01-01");

                // Address should be prefilled (EC1 == SA, EC1 != MF)
                expect(addressRow?.selectedValue).toBeTruthy();
                expect(addressRow?.selectedValue?.origin).toBe("Employee");
                expect(addressRow?.selectedValue?.value).toContain("Main Street");

                // Nationality should be prefilled (EC1 == SA, EC1 != MF)
                expect(nationalityRow?.selectedValue).toBeTruthy();
                expect(nationalityRow?.selectedValue?.origin).toBe("Employee");
                expect(nationalityRow?.selectedValue?.value).toBe("België");
            });

            it("should not prefill values when EC1 != SA", () => {
                component.dataConsistencyData.authenticCitizenInformation.birthDate = "1990-01-03";
                component.dataConsistencyData.authenticCitizenInformation.nationalityCode = 111;

                component.prepareTableData();

                const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
                const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");

                // Should not be prefilled because EC1 != SA
                expect(birthDateRow?.selectedValue).toBeFalsy();
                expect(nationalityRow?.selectedValue).toBeFalsy();
            });

            it("should not prefill values when EC1 == MF", () => {
                // Change ONEM values to be same as EC1
                component.dataConsistencyData.onemCitizenInformation.birthDate = "1990-01-01";
                component.dataConsistencyData.onemCitizenInformation.nationalityCode = 150;

                component.prepareTableData();

                const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
                const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");

                // Should not be prefilled because EC1 == MF (values are consistent)
                expect(birthDateRow?.isConsistent).toBe(true);
                expect(nationalityRow?.isConsistent).toBe(true);
                expect(birthDateRow?.selectedValue).toBeFalsy();
                expect(nationalityRow?.selectedValue).toBeFalsy();
            });

            it("should only prefill allowed fields", () => {
                component.prepareTableData();

                const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");
                const otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");

                // These fields should never be prefilled
                expect(bankAccountRow?.selectedValue).toBeFalsy();
                expect(otherPersonNameRow?.selectedValue).toBeFalsy();
            });
        });
    });



    describe("Address with Country functionality", () => {
        afterEach(() => {
            // Restore all mocks after each test
            jest.restoreAllMocks();
        });

        describe("formatAddressValue with country", () => {
            it("should include country name in formatted address", () => {
                const formatAddressValue = component["formatAddressValue"].bind(component);

                const addressWithCountry = {
                    street: "Main Street",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                    city: "Brussels",
                    countryCode: 150
                };

                // Mock StringUtils.formatAddress to return a basic formatted address
                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Main Street 123/A, 1000 Brussels");

                const result = formatAddressValue(addressWithCountry);

                expect(result).toBe("Main Street 123/A, 1000 Brussels, België");
                expect(geoLookupServiceMock.getEntityDescription).toHaveBeenCalledWith("150", "NL");
            });

            it("should handle address without country", () => {
                const formatAddressValue = component["formatAddressValue"].bind(component);

                const addressWithoutCountry = {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels"
                };

                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Main Street 123, 1000 Brussels");

                const result = formatAddressValue(addressWithoutCountry);

                expect(result).toBe("Main Street 123, 1000 Brussels");
            });

            it("should not append country if country name is empty or dash", () => {
                const formatAddressValue = component["formatAddressValue"].bind(component);

                const address = {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                    countryCode: "XX" // Unknown country code
                };

                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Main Street 123, 1000 Brussels");
                geoLookupServiceMock.getEntityDescription = jest.fn().mockReturnValue("-");

                const result = formatAddressValue(address);

                expect(result).toBe("Main Street 123, 1000 Brussels");
            });

            it("should use correct language for country name", () => {
                const formatAddressValue = component["formatAddressValue"].bind(component);

                const address = {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                    countryCode: 111
                };

                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Main Street 123, 1000 Brussels");

                // Test with Dutch
                component.language = "NL";
                let result = formatAddressValue(address);
                expect(result).toBe("Main Street 123, 1000 Brussels, Frankrijk");

                // Test with French
                component.language = "FR";
                result = formatAddressValue(address);
                expect(result).toBe("Main Street 123, 1000 Brussels, France");
            });
        });

        describe("compareAddresses with country", () => {
            it("should return false when countries are different", () => {
                const compareAddresses = component["compareAddresses"].bind(component);

                const citizenInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        boxNumber: "A",
                        zipCode: "1000",
                        countryCode: 150
                    }
                };

                const onemInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        boxNumber: "A",
                        zipCode: "1000",
                        countryCode: 111
                    }
                };

                expect(compareAddresses(citizenInfo, onemInfo)).toBe(false);
            });

            it("should return true when countries match", () => {
                const compareAddresses = component["compareAddresses"].bind(component);

                const citizenInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        boxNumber: "A",
                        zipCode: "1000",
                        countryCode: 150
                    }
                };

                const onemInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        boxNumber: "A",
                        zipCode: "1000",
                        countryCode: 150
                    }
                };

                expect(compareAddresses(citizenInfo, onemInfo)).toBe(true);
            });

            it("should handle missing country gracefully", () => {
                const compareAddresses = component["compareAddresses"].bind(component);

                const citizenInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        zipCode: "1000"
                        // no country field
                    }
                };

                const onemInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        zipCode: "1000",
                        countryCode: 150
                    }
                };

                expect(compareAddresses(citizenInfo, onemInfo)).toBe(false);
            });

            it("should return true when both addresses have no country", () => {
                const compareAddresses = component["compareAddresses"].bind(component);

                const citizenInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        zipCode: "1000"
                    }
                };

                const onemInfo = {
                    address: {
                        street: "Main Street",
                        houseNumber: "123",
                        zipCode: "1000"
                    }
                };

                expect(compareAddresses(citizenInfo, onemInfo)).toBe(true);
            });
        });

        describe("Table data with country in addresses", () => {
            beforeEach(() => {
                jest.spyOn(StringUtils, 'formatAddress').mockImplementation((address) => {
                    if (!address) return '';
                    return `${address.street} ${address.houseNumber}, ${address.zipCode} ${address.city || ''}`.trim();
                });
            });

            it("should display country in all address columns", () => {
                component.dataConsistencyData = {
                    basicInfo: {
                        requestDate: "01/01/2025"
                    },
                    c1CitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    onemCitizenInformation: {
                        address: {
                            street: "Other Street",
                            houseNumber: "456",
                            zipCode: "2000",
                            city: "Antwerp",
                            countryCode: 150
                        }
                    },
                    authenticCitizenInformation: {
                        address: {
                            street: "Third Street",
                            houseNumber: "789",
                            zipCode: "3000",
                            city: "Leuven",
                            countryCode: 150
                        }
                    },
                    citizenInformation: {
                        address: {
                            validFrom: null
                        },
                        fieldSources: []
                    },
                    modeOfPayment: {
                        validFrom: null,
                        fieldSources: []
                    },
                    unionContribution: {
                        authorized: false,
                        effectiveDate: "2025-01-01",
                        fieldSources: []
                    }
                };

                component.prepareTableData();

                const addressRow = component.tableDataSource.data.find(row => row.id === "address");

                expect(addressRow?.encodedValue).toBe("Main Street 123, 1000 Brussels, België");
                expect(addressRow?.dbValue).toBe("Other Street 456, 2000 Antwerp, België");
                expect(addressRow?.sourceValue).toBe("Third Street 789, 3000 Leuven, België");
            });

            it("should mark addresses as inconsistent when countries differ", () => {
                component.dataConsistencyData = {
                    basicInfo: {
                        requestDate: "01/01/2025"
                    },
                    c1CitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    onemCitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 111 // Different country
                        }
                    },
                    authenticCitizenInformation: {},
                    citizenInformation: {
                        address: {
                            validFrom: null
                        },
                        fieldSources: []
                    },
                    modeOfPayment: {
                        validFrom: null,
                        fieldSources: []
                    },
                    unionContribution: {
                        authorized: false,
                        effectiveDate: "2025-01-01",
                        fieldSources: []
                    }
                };

                component.prepareTableData();

                const addressRow = component.tableDataSource.data.find(row => row.id === "address");

                expect(addressRow?.isConsistent).toBe(false);
                expect(addressRow?.encodedValue).toContain("België");
                expect(addressRow?.dbValue).toContain("Frankrijk");
            });

            it("should update country names when language changes", () => {
                component.dataConsistencyData = {
                    basicInfo: {
                        requestDate: "01/01/2025"
                    },
                    c1CitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    onemCitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 111
                        }
                    },
                    authenticCitizenInformation: {},
                    citizenInformation: {
                        address: {
                            validFrom: null
                        },
                        fieldSources: []
                    },
                    modeOfPayment: {
                        validFrom: null,
                        fieldSources: []
                    },
                    unionContribution: {
                        authorized: false,
                        effectiveDate: "2025-01-01",
                        fieldSources: []
                    }
                };

                // Initial preparation in Dutch
                component.language = "NL";
                component.prepareTableData();

                let addressRow = component.tableDataSource.data.find(row => row.id === "address");
                expect(addressRow?.encodedValue).toContain("België");
                expect(addressRow?.dbValue).toContain("Frankrijk");

                // Change to French and update
                component.language = "FR";
                component.prepareTableData();

                addressRow = component.tableDataSource.data.find(row => row.id === "address");
                expect(addressRow?.encodedValue).toContain("Belgique");
                expect(addressRow?.dbValue).toContain("France");
            });
        });

        describe("Selected value with country", () => {
            it("should include country in selected address value", () => {
                const mockAddressWithCountry = {
                    street: "Selected Street",
                    houseNumber: "999",
                    zipCode: "4000",
                    city: "Liège",
                    countryCode: 150
                };

                component.dataConsistencyData = {
                    basicInfo: { requestDate: "01/01/2025" },
                    c1CitizenInformation: { address: mockAddressWithCountry },
                    onemCitizenInformation: {
                        address: {
                            street: "Different Street",
                            houseNumber: "111",
                            zipCode: "5000",
                            city: "Namur",
                            country: 111
                        }
                    },
                    authenticCitizenInformation: {},
                    citizenInformation: {
                        address: {
                            validFrom: null
                        },
                        fieldSources: [
                            { fieldName: "address", source: "C1" }
                        ]
                    },
                    modeOfPayment: {
                        validFrom: null,
                        fieldSources: []
                    },
                    unionContribution: {
                        authorized: false,
                        effectiveDate: "2025-01-01",
                        fieldSources: []
                    }
                };

                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Selected Street 999, 4000 Liège");

                component.prepareTableData();

                const addressRow = component.tableDataSource.data.find(row => row.id === "address");

                expect(addressRow?.selectedValue).toBeTruthy();
                expect(addressRow?.selectedValue?.value).toBe("Selected Street 999, 4000 Liège, België");
            });

            it("should format selected address value correctly in getModifiedValue", () => {
                const selectedValue = {
                    fieldName: "address",
                    origin: "Employee",
                    value: "Main Street 123, 1000 Brussels, België"
                };

                const result = component.getModifiedValue(selectedValue);

                expect(result).toBe("Main Street 123, 1000 Brussels, België");
            });
        });

        describe("Address comparison edge cases", () => {
            it("should handle address with all fields including country", () => {
                const fullAddress = {
                    street: "Rue de la Loi",
                    houseNumber: "16",
                    boxNumber: "B12",
                    zipCode: "1000",
                    city: "Brussels",
                    countryCode: 150
                };

                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Rue de la Loi 16/B12, 1000 Brussels");

                const formatAddressValue = component["formatAddressValue"].bind(component);
                const result = formatAddressValue(fullAddress);

                expect(result).toBe("Rue de la Loi 16/B12, 1000 Brussels, België");
            });

            it("should handle edge case where country code is empty string", () => {
                const address = {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                    countryCode: ""
                };

                jest.spyOn(StringUtils, 'formatAddress').mockReturnValue("Main Street 123, 1000 Brussels");

                const formatAddressValue = component["formatAddressValue"].bind(component);
                const result = formatAddressValue(address);

                expect(result).toBe("Main Street 123, 1000 Brussels");
            });
        });

        describe("Prefill functionality with country", () => {
            beforeEach(() => {
                jest.spyOn(StringUtils, 'formatAddress').mockImplementation((address) => {
                    if (!address) return '';
                    return `${address.street} ${address.houseNumber}, ${address.zipCode} ${address.city || ''}`.trim();
                });
            });

            it("should prefill address with country when EC1 == SA and EC1 != MF", () => {
                component.dataConsistencyData = {
                    basicInfo: { requestDate: "01/01/2025" },
                    c1CitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    onemCitizenInformation: {
                        address: {
                            street: "Different Street",
                            houseNumber: "456",
                            zipCode: "2000",
                            city: "Antwerp",
                            countryCode: 150
                        }
                    },
                    authenticCitizenInformation: {
                        address: {
                            street: "Main Street",
                            houseNumber: "123",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    citizenInformation: {
                        address: {
                            validFrom: null
                        },
                        fieldSources: []
                    },
                    modeOfPayment: {
                        validFrom: null,
                        fieldSources: []
                    },
                    unionContribution: {
                        authorized: false,
                        effectiveDate: "2025-01-01",
                        fieldSources: []
                    }
                };

                component.prepareTableData();

                const addressRow = component.tableDataSource.data.find(row => row.id === "address");

                expect(addressRow?.selectedValue).toBeTruthy();
                expect(addressRow?.selectedValue?.value).toBe("Main Street 123, 1000 Brussels, België");
                expect(addressRow?.selectedValue?.origin).toBe("Employee");
            });

            it("should compare addresses including country for prefill logic", () => {
                const compareEntryValues = component["compareEntryValues"].bind(component);

                // Same address with country
                const address1 = "Main Street 123, 1000 Brussels, België";
                const address2 = "MAIN STREET 123, 1000 BRUSSELS, BELGIE";

                expect(compareEntryValues("address", address1, address2)).toBe(true);

                // Different addresses due to country
                const address3 = "Main Street 123, 1000 Brussels, België";
                const address4 = "Main Street 123, 1000 Brussels, Frankrijk";

                expect(compareEntryValues("address", address3, address4)).toBe(false);
            });
        });

        describe("Integration test for country display", () => {
            it("should correctly handle complete address flow with country", () => {
                jest.spyOn(StringUtils, 'formatAddress').mockImplementation((address) => {
                    if (!address) return '';
                    const parts = [];
                    if (address.street && address.houseNumber) {
                        parts.push(`${address.street} ${address.houseNumber}`);
                        if (address.boxNumber) {
                            parts[parts.length - 1] += `/${address.boxNumber}`;
                        }
                    }
                    if (address.zipCode) {
                        parts.push(address.zipCode);
                        if (address.city) {
                            parts[parts.length - 1] += ` ${address.city}`;
                        }
                    }
                    return parts.join(', ');
                });

                const testData = {
                    basicInfo: { requestDate: "01/01/2025" },
                    c1CitizenInformation: {
                        address: {
                            street: "Rue de la Loi",
                            houseNumber: "16",
                            boxNumber: "A2",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    onemCitizenInformation: {
                        address: {
                            street: "Rue de la Loi",
                            houseNumber: "16",
                            boxNumber: "A2",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    authenticCitizenInformation: {
                        address: {
                            street: "Rue de la Loi",
                            houseNumber: "16",
                            boxNumber: "A2",
                            zipCode: "1000",
                            city: "Brussels",
                            countryCode: 150
                        }
                    },
                    citizenInformation: {
                        address: {
                            validFrom: null
                        },
                        fieldSources: []
                    },
                    modeOfPayment: {
                        validFrom: null,
                        fieldSources: []
                    },
                    unionContribution: {
                        authorized: false,
                        effectiveDate: "2025-01-01",
                        fieldSources: []
                    }
                };

                component.dataConsistencyData = testData;
                component.prepareTableData();

                const addressRow = component.tableDataSource.data.find(row => row.id === "address");

                // All addresses should be consistent and include country
                expect(addressRow?.isConsistent).toBe(true);
                expect(addressRow?.encodedValue).toBe("Rue de la Loi 16/A2, 1000 Brussels, België");
                expect(addressRow?.dbValue).toBe("Rue de la Loi 16/A2, 1000 Brussels, België");
                expect(addressRow?.sourceValue).toBe("Rue de la Loi 16/A2, 1000 Brussels, België");
            });
        });
    });



});