{"version": 3, "sources": ["../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-tooltip.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Input, Component, HostListener, Directive, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/overlay';\nfunction OnemrvaMatTooltipComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.text, \" \");\n  }\n}\nfunction OnemrvaMatTooltipComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nclass OnemrvaMatTooltipComponent {\n  static {\n    this.ɵfac = function OnemrvaMatTooltipComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatTooltipComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OnemrvaMatTooltipComponent,\n      selectors: [[\"onemrva-mat-tooltip\"]],\n      inputs: {\n        text: \"text\",\n        contentTemplate: \"contentTemplate\"\n      },\n      decls: 5,\n      vars: 1,\n      consts: [[\"simpleText\", \"\"], [1, \"tooltip-container\"], [4, \"ngTemplateOutlet\"]],\n      template: function OnemrvaMatTooltipComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"div\", 1);\n          i0.ɵɵtemplate(2, OnemrvaMatTooltipComponent_ng_template_2_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, OnemrvaMatTooltipComponent_ng_container_4_Template, 1, 0, \"ng-container\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const simpleText_r2 = i0.ɵɵreference(3);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate || simpleText_r2);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      styles: [\".tooltip-container[_ngcontent-%COMP%]{background:#fff;color:#000;box-shadow:0 2px 6px #00000052;border-radius:4px;margin:2px;padding:8px;line-height:normal}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatTooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'onemrva-mat-tooltip',\n      template: `\n    <div>\n      <div class=\"tooltip-container\">\n        <ng-template #simpleText>\n          {{ text }}\n        </ng-template>\n        <ng-container *ngTemplateOutlet=\"contentTemplate || simpleText\">\n        </ng-container>\n      </div>\n    </div>\n  `,\n      standalone: true,\n      imports: [NgTemplateOutlet],\n      styles: [\".tooltip-container{background:#fff;color:#000;box-shadow:0 2px 6px #00000052;border-radius:4px;margin:2px;padding:8px;line-height:normal}\\n\"]\n    }]\n  }], null, {\n    text: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }]\n  });\n})();\nclass OnemrvaMatTooltipDirective {\n  constructor(_overlay, _overlayPositionBuilder, _elementRef) {\n    this._overlay = _overlay;\n    this._overlayPositionBuilder = _overlayPositionBuilder;\n    this._elementRef = _elementRef;\n    this.showToolTip = true;\n    this.position = 'bottom';\n    this.positionOffset = 5;\n  }\n  ngOnInit() {\n    if (!this.showToolTip) {\n      return;\n    }\n    const positionStrategy = this._overlayPositionBuilder.flexibleConnectedTo(this._elementRef).withPositions(this.defineStrategy());\n    this._overlayRef = this._overlay.create({\n      positionStrategy\n    });\n  }\n  defineStrategy() {\n    switch (this.position) {\n      case 'top':\n        return [{\n          originX: 'center',\n          originY: 'top',\n          overlayX: 'center',\n          overlayY: 'bottom',\n          offsetY: -this.positionOffset\n        }];\n      case 'right':\n        return [{\n          originX: 'end',\n          originY: 'center',\n          overlayX: 'start',\n          overlayY: 'center',\n          offsetX: this.positionOffset\n        }];\n      case 'bottom':\n        return [{\n          originX: 'center',\n          originY: 'bottom',\n          overlayX: 'center',\n          overlayY: 'top',\n          offsetY: this.positionOffset\n        }];\n      case 'left':\n        return [{\n          originX: 'start',\n          originY: 'center',\n          overlayX: 'end',\n          overlayY: 'center',\n          offsetX: -this.positionOffset\n        }];\n    }\n  }\n  show() {\n    if (this._overlayRef && !this._overlayRef.hasAttached()) {\n      const tooltipRef = this._overlayRef.attach(new ComponentPortal(OnemrvaMatTooltipComponent));\n      tooltipRef.instance.text = this.customToolTip;\n      tooltipRef.instance.contentTemplate = this.contentTemplate;\n    }\n  }\n  hide() {\n    this.closeToolTip();\n  }\n  ngOnDestroy() {\n    this.closeToolTip();\n  }\n  closeToolTip() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n  }\n  static {\n    this.ɵfac = function OnemrvaMatTooltipDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatTooltipDirective)(i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i1.OverlayPositionBuilder), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OnemrvaMatTooltipDirective,\n      selectors: [[\"\", \"OnemrvaMatTooltip\", \"\"]],\n      hostBindings: function OnemrvaMatTooltipDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function OnemrvaMatTooltipDirective_mouseenter_HostBindingHandler() {\n            return ctx.show();\n          })(\"mouseleave\", function OnemrvaMatTooltipDirective_mouseleave_HostBindingHandler() {\n            return ctx.hide();\n          });\n        }\n      },\n      inputs: {\n        showToolTip: \"showToolTip\",\n        customToolTip: \"customToolTip\",\n        contentTemplate: \"contentTemplate\",\n        position: \"position\",\n        positionOffset: \"positionOffset\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatTooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[OnemrvaMatTooltip]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Overlay\n  }, {\n    type: i1.OverlayPositionBuilder\n  }, {\n    type: i0.ElementRef\n  }], {\n    showToolTip: [{\n      type: Input\n    }],\n    customToolTip: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    positionOffset: [{\n      type: Input\n    }],\n    show: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    hide: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nclass OnemrvaMatTooltipModule {\n  static {\n    this.ɵfac = function OnemrvaMatTooltipModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatTooltipModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaMatTooltipModule,\n      imports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent],\n      exports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      imports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent],\n      exports: [OnemrvaMatTooltipDirective, OnemrvaMatTooltipComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaMatTooltipComponent, OnemrvaMatTooltipDirective, OnemrvaMatTooltipModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAK,OAAO,MAAM,GAAG;AAAA,EAC7C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAC9E,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC;AAC3M,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,gBAAmB,YAAY,CAAC;AACtC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,IAAI,mBAAmB,aAAa;AAAA,QACxE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAgB;AAAA,MAC/B,QAAQ,CAAC,8JAA8J;AAAA,IACzK,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWV,YAAY;AAAA,MACZ,SAAS,CAAC,gBAAgB;AAAA,MAC1B,QAAQ,CAAC,6IAA6I;AAAA,IACxJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,UAAU,yBAAyB,aAAa;AAC1D,SAAK,WAAW;AAChB,SAAK,0BAA0B;AAC/B,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,mBAAmB,KAAK,wBAAwB,oBAAoB,KAAK,WAAW,EAAE,cAAc,KAAK,eAAe,CAAC;AAC/H,SAAK,cAAc,KAAK,SAAS,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,YAAQ,KAAK,UAAU;AAAA,MACrB,KAAK;AACH,eAAO,CAAC;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,UACV,SAAS,CAAC,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,KAAK;AACH,eAAO,CAAC;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,UACV,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,KAAK;AACH,eAAO,CAAC;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,UACV,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,KAAK;AACH,eAAO,CAAC;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,UACV,UAAU;AAAA,UACV,SAAS,CAAC,KAAK;AAAA,QACjB,CAAC;AAAA,IACL;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,YAAY,GAAG;AACvD,YAAM,aAAa,KAAK,YAAY,OAAO,IAAI,gBAAgB,0BAA0B,CAAC;AAC1F,iBAAW,SAAS,OAAO,KAAK;AAChC,iBAAW,SAAS,kBAAkB,KAAK;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe;AACb,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,OAAO,GAAM,kBAAqB,sBAAsB,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACrL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,SAAS,2DAA2D;AAC9F,mBAAO,IAAI,KAAK;AAAA,UAClB,CAAC,EAAE,cAAc,SAAS,2DAA2D;AACnF,mBAAO,IAAI,KAAK;AAAA,UAClB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,4BAA4B,0BAA0B;AAAA,MAChE,SAAS,CAAC,4BAA4B,0BAA0B;AAAA,IAClE,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC;AAAA,MACf,SAAS,CAAC,4BAA4B,0BAA0B;AAAA,MAChE,SAAS,CAAC,4BAA4B,0BAA0B;AAAA,IAClE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}