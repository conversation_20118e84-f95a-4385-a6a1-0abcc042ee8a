<h2 mat-dialog-title data-cy="dialog-title">{{ "CU_DATA_CONSISTENCY.DC.TABLE.COLUMN.ACTION" | translate }}</h2>
<mat-dialog-content data-cy="dialog-content">
    <p data-cy="dialog-body">{{ "CU_DATA_CONSISTENCY.DC.DIALOG.BODY" | translate }}</p>
    <p data-cy="dialog-subtitle">{{ "CU_DATA_CONSISTENCY.DC.DIALOG.SUBTITLE" | translate }}</p>
    <div [formGroup]="resultSelectedForm">
        <mat-radio-group
                formControlName="resultSelected"
                data-cy="value-radio-group"
        >

            <div *ngIf="hasValue(employeeValue)" data-cy="employee-value-option">
                <lib-cu-consistency-card
                        [origin]="Origins.Employee"
                        [value]="employeeValue"
                        [valueDate]="employeeValueDate"
                        [language]="data.language"
                        data-cy="employee-card"
                ></lib-cu-consistency-card>
            </div>
            <div *ngIf="hasValue(onemValue)">
                <lib-cu-consistency-card
                        [origin]="Origins.Onem"
                        [value]="onemValue"
                        [valueDate]="OnemValueDate"
                        [language]="data.language"
                        data-cy="onem-card"
                ></lib-cu-consistency-card>
            </div>
            <div *ngIf="hasValue(sourceAuthentiquesValue)">
                <lib-cu-consistency-card
                        [origin]="Origins.SourceAuthentiques"
                        [value]="sourceAuthentiquesValue"
                        [valueDate]="sourceAuthentiquesValueDate"
                        [language]="data.language"
                        data-cy="source-card"
                ></lib-cu-consistency-card>
            </div>

        </mat-radio-group>
    </div>
</mat-dialog-content>
<mat-dialog-actions align="end" data-cy="dialog-actions">
    <button mat-button color="primary"
            mat-dialog-close data-cy="cancel-button">{{ "CU_DATA_CONSISTENCY.DC.DIALOG.ACTION.CANCEL" | translate }}
    </button>
    <button mat-flat-button color="accent" [disabled]="resultSelected.value == null"
            data-cy="confirm-button"
            (click)="onYesClick()">{{ "CU_DATA_CONSISTENCY.DC.DIALOG.ACTION.VALIDATE" | translate }}
    </button>
</mat-dialog-actions>
