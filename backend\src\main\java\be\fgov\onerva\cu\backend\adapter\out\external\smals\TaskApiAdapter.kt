package be.fgov.onerva.cu.backend.adapter.out.external.smals

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.port.out.TaskApiPort
import be.fgov.onerva.cu.backend.rest.client.wo.smals.rest.model.TaskDTO
import be.fgov.onerva.cu.backend.taskapi.TaskApiService

@Service
class TaskApiAdapter(val taskApiService: TaskApiService): TaskApiPort {

    override fun softDeleteTask(taskId: Long, reason: String?): TaskDTO? {
        return taskApiService.softDeleteTask(taskId, reason);
    }
}