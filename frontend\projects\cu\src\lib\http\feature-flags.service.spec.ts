import { TestBed } from '@angular/core/testing';
import { HttpClient } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { FeatureFlagsService } from './feature-flags.service';
import { ConfigService } from '../config/config.service';

// Mock flagsmith directly in the jest.mock call
jest.mock('flagsmith', () => ({
    __esModule: true,
    default: {
        initialised: false,
        init: jest.fn().mockResolvedValue(undefined),
        hasFeature: jest.fn()
    }
}));

// Mock @rest-client/cu-config - ConfigService is aliased as CuConfigService in the actual service
jest.mock('@rest-client/cu-config', () => ({
    ConfigService: jest.fn().mockImplementation(() => ({
        getFlagsmithConfig: jest.fn().mockReturnValue(
            of({
                environmentID: 'test-env',
                identity: 'test-identity',
                api: 'http://flagsmith.test'
            })
        ),
        defaultHeaders: null
    })),
    Configuration: jest.fn()
}));

describe('FeatureFlagsService', () => {
    let service: FeatureFlagsService;
    let mockHttpClient: jest.Mocked<HttpClient>;
    let mockConfigService: jest.Mocked<ConfigService>;
    let mockFlagsmith: any;
    let mockCuConfigService: any;
    let mockConfiguration: any;

    beforeEach(async () => {
        // Get references to the mocked modules
        const flagsmithModule = await import('flagsmith');
        mockFlagsmith = flagsmithModule.default;

        const cuConfigModule = await import('@rest-client/cu-config');
        mockConfiguration = cuConfigModule.Configuration;

        // Get the mock instance that will be created
        mockCuConfigService = {
            getFlagsmithConfig: jest.fn().mockReturnValue(
                of({
                    environmentID: 'test-env',
                    identity: 'test-identity',
                    api: 'http://flagsmith.test'
                })
            ),
            defaultHeaders: null
        };

        // Update the ConfigService mock to return our mock instance
        (cuConfigModule.ConfigService as jest.Mock).mockImplementation(() => mockCuConfigService);

        // Reset mocks
        mockFlagsmith.initialised = false;
        mockFlagsmith.init.mockClear();
        mockFlagsmith.hasFeature.mockClear();
        mockCuConfigService.getFlagsmithConfig.mockClear();
        mockConfiguration.mockClear();

        mockHttpClient = {} as any;
        mockConfigService = {
            getEnvironmentVariable: jest.fn((key) => {
                if (key === 'apiBaseUrl') return 'http://api.test';
                if (key === 'bffBaseUrl') return 'http://bff.test';
                return '';
            })
        } as any;

        TestBed.configureTestingModule({
            providers: [
                FeatureFlagsService,
                { provide: HttpClient, useValue: mockHttpClient },
                { provide: ConfigService, useValue: mockConfigService }
            ]
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should create', () => {
        service = TestBed.inject(FeatureFlagsService);
        expect(service).toBeTruthy();
    });

    it('should have REOPEN_TASK constant', () => {
        expect(FeatureFlagsService.REOPEN_TASK).toBe('reopen-task-enabled');
    });

    it('should initialize flagsmith on construction', (done) => {
        service = TestBed.inject(FeatureFlagsService);

        setTimeout(() => {
            expect(mockFlagsmith.init).toHaveBeenCalledWith({
                environmentID: 'test-env',
                identity: 'test-identity',
                api: 'http://flagsmith.test',
                cacheFlags: false,
                onChange: expect.any(Function)
            });
            done();
        }, 100);
    });

    it('should handle initialization error', (done) => {
        // Mock error before creating service
        mockCuConfigService.getFlagsmithConfig.mockReturnValue(
            throwError(() => new Error('Config error'))
        );

        service = TestBed.inject(FeatureFlagsService);

        setTimeout(() => {
            expect(mockFlagsmith.init).not.toHaveBeenCalled();
            done();
        }, 100);
    });

    describe('isFeatureEnabled', () => {
        beforeEach(() => {
            service = TestBed.inject(FeatureFlagsService);
        });

        it('should return true for enabled feature', () => {
            mockFlagsmith.initialised = true;
            mockFlagsmith.hasFeature.mockReturnValue(true);

            expect(service.isFeatureEnabled('test-feature')).toBe(true);
            expect(mockFlagsmith.hasFeature).toHaveBeenCalledWith('test-feature');
        });

        it('should return false for disabled feature', () => {
            mockFlagsmith.initialised = true;
            mockFlagsmith.hasFeature.mockReturnValue(false);

            expect(service.isFeatureEnabled('test-feature')).toBe(false);
        });

        it('should return false when flagsmith not initialized', () => {
            mockFlagsmith.initialised = false;

            expect(service.isFeatureEnabled('test-feature')).toBe(false);
            expect(mockFlagsmith.hasFeature).not.toHaveBeenCalled();
        });
    });

    describe('initializeServices', () => {
        beforeEach(() => {
            service = TestBed.inject(FeatureFlagsService);
        });

        it('should initialize with token', () => {
            service.initializeServices('test-token');

            expect(mockConfigService.getEnvironmentVariable).toHaveBeenCalledWith('apiBaseUrl', true);
            expect(mockConfigService.getEnvironmentVariable).toHaveBeenCalledWith('bffBaseUrl', true);
        });

        it('should initialize without token', () => {
            service.initializeServices();

            expect(mockConfigService.getEnvironmentVariable).toHaveBeenCalledWith('apiBaseUrl', true);
            expect(mockConfigService.getEnvironmentVariable).toHaveBeenCalledWith('bffBaseUrl', true);
        });
    });
});