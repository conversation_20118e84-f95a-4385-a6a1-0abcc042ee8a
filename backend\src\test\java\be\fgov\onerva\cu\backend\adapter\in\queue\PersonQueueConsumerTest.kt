package be.fgov.onerva.cu.backend.adapter.`in`.queue

import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.`in`.queue.model.PersonUpdated
import be.fgov.onerva.cu.backend.adapter.`in`.queue.model.PersonUpdatedPayload
import be.fgov.onerva.cu.backend.application.port.`in`.PersonUpdatedUseCase
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.runs
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class PersonQueueConsumerTest {

    @MockK
    lateinit var personUpdatedUseCase: PersonUpdatedUseCase

    @InjectMockKs
    lateinit var personQueueConsumer: PersonQueueConsumer

    @Nested
    inner class OnPersonEventReceivedFromQueue {

        @Test
        fun `should process PersonUpdated event and call use case`() {
            // Given
            val correlationId = "test-correlation-id"
            val personUpdatedPayload = PersonUpdatedPayload(
                id = "123",
                type = "be.fgov.onerva.person.msg.v1.PersonUpdated",
                data = PersonUpdated(
                    id = 123,
                    correlationId = correlationId,
                    ssin = "12345678901",
                    success = true,
                    names = "John Doe",
                    errorCode = 0,
                ),
                source = "test-source",
                specversion = "1.0",
                datacontenttype = "application/json",
                dataschema = "test-schema",
                subject = "test-subject",
                time = "2024-01-01T12:00:00Z"
            )

            every {
                personUpdatedUseCase.receivedPersonUpdated(
                    // capture
                    match {
                        it.correlationId == correlationId
                        it.success == true
                        it.names == "John Doe"
                        it.ssin == "12345678901"
                    }
                )
            } just runs

            // When
            personQueueConsumer.onPersonEventReceivedFromQueue(personUpdatedPayload, null)

            // Then
            verify(exactly = 1) { personUpdatedUseCase.receivedPersonUpdated(any()) }
        }

        @Test
        fun `should propagate exceptions when processing fails`() {
            // Given
            val correlationId = "test-correlation-id"
            val personUpdatedPayload = PersonUpdatedPayload(
                id = "123",
                type = "be.fgov.onerva.person.msg.v1.PersonUpdated",
                data = PersonUpdated(
                    id = 123,
                    correlationId = correlationId,
                    ssin = "12345678901",
                    success = true,
                    names = "John Doe",
                    errorCode = 0,
                ),
                source = "test-source",
                specversion = "1.0",
                datacontenttype = "application/json",
                dataschema = "test-schema",
                subject = "test-subject",
                time = "2024-01-01T12:00:00Z"
            )

            val exception = RuntimeException("Test exception")
            every { personUpdatedUseCase.receivedPersonUpdated(any()) } throws exception

            // When/Then
            assertThatThrownBy {
                personQueueConsumer.onPersonEventReceivedFromQueue(personUpdatedPayload, null)
            }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")

            verify(exactly = 1) { personUpdatedUseCase.receivedPersonUpdated(any()) }
        }

        @Test
        fun `should not process message when type is not PersonUpdated`() {
            // Given
            val correlationId = "test-correlation-id"
            val personUpdatedPayload = PersonUpdatedPayload(
                id = "123",
                type = "be.fgov.onerva.person.msg.v1.SomeOtherType",
                data = PersonUpdated(
                    id = 123,
                    correlationId = correlationId,
                    ssin = "12345678901",
                    success = true,
                    names = "John Doe",
                    errorCode = 0,
                ),
                source = "test-source",
                specversion = "1.0",
                datacontenttype = "application/json",
                dataschema = "test-schema",
                subject = "test-subject",
                time = "2024-01-01T12:00:00Z"
            )

            // When
            personQueueConsumer.onPersonEventReceivedFromQueue(personUpdatedPayload, null)

            // Then
            verify(exactly = 0) { personUpdatedUseCase.receivedPersonUpdated(any()) }
        }
    }
}