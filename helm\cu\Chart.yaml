---
apiVersion: "v2"
name: "cu"
description: "A Helm chart for Kubernetes"
type: "application"
version: "0.1.0"
appVersion: "0.0.0"
dependencies:
- name: "onerva-metadata"
  version: "2.0.0"
  alias: "onerva-metadata"
  condition: "onerva-metadata.enabled"
  repository: "https://nexusprod.onemrva.priv/repository/helm-release"
- name: "spring-boot"
  version: "4.1.0"
  alias: "backend"
  condition: "backend.enabled"
  repository: "https://nexusprod.onemrva.priv/repository/helm-release"
- name: "spring-boot"
  version: "4.1.0"
  alias: "bff"
  condition: "bff.enabled"
  repository: "https://nexusprod.onemrva.priv/repository/helm-release"
- name: "angular"
  version: "4.6.0"
  alias: "cu"
  condition: "cu.enabled"
  repository: "https://nexusprod.onemrva.priv/repository/helm-release"
- name: "fake-onem-infra"
  version: "5.2.0"
  alias: "infra"
  condition: "infra.enabled"
  repository: "https://nexusprod.onemrva.priv/repository/helm-release"
- name: "keycloak-config"
  version: "28.0.0"
  alias: "kcconfig"
  condition: "kcconfig.enabled"
  repository: "https://nexusprod.onemrva.priv/repository/helm-release"
- name: wo-configurator
  alias: woconfig
  version: "58.0.3"
  condition: woconfig.enabled
  repository: https://nexusprod.onemrva.priv/repository/helm-release
- name: microcks-uber
  version: 11.0.0
  alias: microcks
  condition: microcks.enabled
  repository: https://nexusprod.onemrva.priv/repository/helm-release
- name: wiremock
  version: "0.0.8"  # Use the version you found in Nexus
  alias: wiremock
  condition: wiremock.enabled
  repository: https://nexusprod.onemrva.priv/repository/helm