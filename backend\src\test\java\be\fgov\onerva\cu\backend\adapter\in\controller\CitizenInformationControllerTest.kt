package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.backend.application.port.`in`.CitizenInformationUseCase
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class CitizenInformationControllerTest {

    @MockK
    private lateinit var citizenInformationUseCase: CitizenInformationUseCase

    @InjectMockKs
    private lateinit var citizenInformationController: CitizenInformationController

    @Test
    fun `getCitizenInformation should return citizen information when it exists`() {
        // Given
        val requestId = UUID.randomUUID()
        val address = Address(
            street = "Main Street",
            houseNumber = "42",
            boxNumber = "A",
            countryCode = 150,
            city = "Brussels",
            zipCode = "1000",
            validFrom = LocalDate.of(2022, 1, 1),
        )

        val citizenInfo = CitizenInformation(
            firstName = "John",
            lastName = "Doe",
            birthDate = LocalDate.of(1980, 1, 1),
            nationalityCode = 111,
            address = address
        )

        val fieldSources = listOf(
            FieldSource("birthDate", ExternalSource.C1), FieldSource("nationality", ExternalSource.ONEM)
        )

        every { citizenInformationUseCase.getCitizenInformation(requestId) } returns citizenInfo
        every { citizenInformationUseCase.getCitizenInformationFieldSources(requestId) } returns fieldSources

        // When
        val result = citizenInformationController.getCitizenInformation(requestId)

        // Then
        assertThat(result).isNotNull
        assertThat(result.birthDate).isEqualTo(LocalDate.of(1980, 1, 1))
        assertThat(result.nationalityCode).isEqualTo(111)
        assertThat(result.address.street).isEqualTo("Main Street")
        assertThat(result.address.houseNumber).isEqualTo("42")
        assertThat(result.address.boxNumber).isEqualTo("A")
        assertThat(result.address.countryCode).isEqualTo(150)
        assertThat(result.address.city).isEqualTo("Brussels")
        assertThat(result.address.zipCode).isEqualTo("1000")
        assertThat(result.fieldSources).hasSize(2)

        verify(exactly = 1) { citizenInformationUseCase.getCitizenInformation(requestId) }
        verify(exactly = 1) { citizenInformationUseCase.getCitizenInformationFieldSources(requestId) }
    }

    @Test
    fun `getCitizenInformation should throw InformationNotFoundException when citizen information not found`() {
        // Given
        val requestId = UUID.randomUUID()

        every { citizenInformationUseCase.getCitizenInformation(requestId) } returns null

        // When/Then
        assertThatThrownBy { citizenInformationController.getCitizenInformation(requestId) }.isInstanceOf(
                InformationNotFoundException::class.java
            ).hasMessage("Citizen information not found for request $requestId")

        verify(exactly = 1) { citizenInformationUseCase.getCitizenInformation(requestId) }
        verify(exactly = 0) { citizenInformationUseCase.getCitizenInformationFieldSources(any()) }
    }

    @Test
    fun `updateCitizenInformation should update citizen information when request valid`() {
        // Given
        val requestId = UUID.randomUUID()
        val addressValidFrom = LocalDate.of(2022, 1, 1)
        val updateRequest = UpdateCitizenInformationRequest().apply {
            birthDate = LocalDate.of(1980, 1, 1)
            nationalityCode = 151
            address = be.fgov.onerva.cu.rest.priv.model.Address().apply {
                street = "Main Street"
                houseNumber = "42"
                boxNumber = "A"
                countryCode = 150
                city = "Brussels"
                zipCode = "1000"
                validFrom = addressValidFrom
            }
        }

        every {
            citizenInformationUseCase.updateCitizenInformation(eq(requestId), any())
        } returns Unit

        // When
        citizenInformationController.updateCitizenInformation(requestId, updateRequest)

        // Then
        verify(exactly = 1) {
            citizenInformationUseCase.updateCitizenInformation(eq(requestId), any())
        }
    }

    @Test
    fun `updateCitizenInformation should throw InvalidInputException when request is null`() {
        // Given
        val requestId = UUID.randomUUID()

        // When/Then
        assertThatThrownBy { citizenInformationController.updateCitizenInformation(requestId, null) }.isInstanceOf(
                InvalidInputException::class.java
            ).hasMessage("updateCitizenInformationRequest is required")

        verify(exactly = 0) { citizenInformationUseCase.updateCitizenInformation(any(), any()) }
    }

    @Test
    fun `selectCitizenInformationSources should select field sources when request is valid`() {
        // Given
        val requestId = UUID.randomUUID()
        val fieldSourcesRequest = SelectFieldSourcesRequest().apply {
            fieldSources = listOf(be.fgov.onerva.cu.rest.priv.model.FieldSource().apply {
                fieldName = "birthDate"
                source = be.fgov.onerva.cu.rest.priv.model.ExternalSource.C1
            }, be.fgov.onerva.cu.rest.priv.model.FieldSource().apply {
                fieldName = "nationality"
                source = be.fgov.onerva.cu.rest.priv.model.ExternalSource.ONEM
            })
        }

        every {
            citizenInformationUseCase.selectCitizenInformationFieldSources(eq(requestId), any())
        } returns Unit

        // When
        citizenInformationController.selectCitizenInformationSources(requestId, fieldSourcesRequest)

        // Then
        verify(exactly = 1) {
            citizenInformationUseCase.selectCitizenInformationFieldSources(eq(requestId), any())
        }
    }

    @Test
    fun `selectCitizenInformationSources should throw NullPointerException when request is null`() {
        // Given
        val requestId = UUID.randomUUID()

        // When/Then
        assertThatThrownBy {
            citizenInformationController.selectCitizenInformationSources(requestId, null)
        }.isInstanceOf(NullPointerException::class.java)

        verify(exactly = 0) {
            citizenInformationUseCase.selectCitizenInformationFieldSources(any(), any())
        }
    }
}