package be.fgov.onerva.cu.backend.application.port.out

import java.time.LocalDate
import be.fgov.onerva.cu.backend.application.domain.C9Info
import be.fgov.onerva.cu.backend.application.domain.EC1Info

/**
 * Port interface for loading EC1 form data from the C9 system.
 * This interface defines the contract for retrieving consolidated EC1 information.
 */
interface C9Port {
    /**
     * Loads EC1 form information for a given EC1 identifier.
     *
     * @param ec1Id The unique identifier of the EC1 form in the C9 system
     * @param defaultValidFrom The default valid from date to use if not specified in the EC1 form
     * @return [EC1Info] containing the employee information, payment details, and union contribution data
     * @throws RuntimeException if the EC1 form cannot be found or loaded
     */
    fun loadEC1(ec1Id: Int, defaultValidFrom: LocalDate): EC1Info

    fun loadC9(c9Id: Long): C9Info
}