import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SimpleChange } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ErrorDisplayComponent } from './error-display.component';
import { OnemRvaCDNService } from '@onemrvapublic/design-system/shared';
import { FormUtilsService } from '../../../services/form-utils.service';

describe('ErrorDisplayComponent', () => {
    let component: ErrorDisplayComponent;
    let fixture: ComponentFixture<ErrorDisplayComponent>;
    let mockCdnService: jest.Mocked<OnemRvaCDNService>;

    beforeEach(async () => {
        mockCdnService = {
            getImg: jest.fn().mockReturnValue('mocked-image-url')
        } as any;

        FormUtilsService.getWaveProcessUrl = jest.fn().mockReturnValue('mocked-process-url');

        await TestBed.configureTestingModule({
            imports: [
                ErrorDisplayComponent,
                TranslateModule.forRoot()
            ],
            providers: [
                { provide: OnemRvaCDNService, useValue: mockCdnService }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(ErrorDisplayComponent);
        component = fixture.componentInstance;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should get image URL from CDN service', () => {
        expect(mockCdnService.getImg).toHaveBeenCalledWith('ori/alone.svg');
        expect(component.imgUrl).toBe('mocked-image-url');
    });

    it('should accept message input', () => {
        component.message = 'Test error message';
        fixture.detectChanges();

        expect(component.message).toBe('Test error message');
    });

    it('should accept task input', () => {
        const task = { id: 1, name: 'Test Task' };
        component.task = task;
        fixture.detectChanges();

        expect(component.task).toBe(task);
    });

    it('should update processLink when task changes', () => {
        const task = { id: 1, name: 'Test Task' };
        component.task = task;

        const changes = {
            task: new SimpleChange(null, task, true)
        };

        component.ngOnChanges(changes);

        expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledWith(task);
        expect(component.processLink).toBe('mocked-process-url');
    });

    it('should not update processLink when task is unchanged', () => {
        const changes = {
            message: new SimpleChange('old', 'new', false)
        };

        component.ngOnChanges(changes);

        expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
    });
});