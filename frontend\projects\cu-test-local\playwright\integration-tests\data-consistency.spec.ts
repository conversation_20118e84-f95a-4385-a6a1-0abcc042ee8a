const fs = require('fs');

// // These are used for coverage
// test.beforeEach(async ({ page }) => {
//     if (!fs.existsSync('coverage')) {
//         fs.mkdirSync('coverage', { recursive: true });
//     }
//   await page.coverage.startJSCoverage();
// });
//
// test.afterEach(async ({ page }) => {
//   const coverage = await page.coverage.stopJSCoverage();
//   fs.writeFileSync('coverage/coverage.json', JSON.stringify(coverage, null, 2));
// });

// test('test mock', async ({ page }) => {
//
//   await page.route('**/api/lookup/nationality**', route =>
//     route.fulfill({
//       status: 200,
//       contentType: 'application/json',
//       body: JSON.stringify(
//         [{"code":150,"descFr":"Belgique ","descNl":"België "}]
//       )
//       }
//     )
//   )
//   await page.route('**/api/config/keycloak', route =>
//     route.fulfill({
//       status: 200,
//        contentType: 'application/json',
//       body: JSON.stringify(
//         {
//           "config": {
//             "url": "http://localhost:8082",
//             "clientId": "cu-frontend",
//             "realm": "onemrva-agents"
//           },
//           "initOptions": {
//             "redirectUri": "http://localhost:4300",
//             "checkLoginIframe": false,
//             "onLoad": "login-required"
//           }
//         }
//       )
//     })
//   )
//
//   await page.route('**/aggregate-requests/change-personal-data/**', route =>
//     route.fulfill({
//       status: 200,
//       contentType: 'application/json',
//       body: JSON.stringify(
//         {
//           "requestId": "457437d4-4b76-48a2-badd-390767fdf25f",
//           "basicInfo": {
//             "requestDate": "2022-09-22",
//             "firstName": "GERTRUDE",
//             "lastName": "GRUSS",
//             "ssin": "18031307065",
//             "annexes": [
//               {
//                 "type": "EC1",
//                 "url": "https://c9.test.paas.onemrva.priv/ec1/223EC100000029/61060350648/91123028210"
//               }
//             ]
//           },
//           "citizenInformation": {
//             "birthDate": "1961-06-03",
//             "nationality": 150,
//             "address": {
//               "country": 150,
//               "street": "DORP OOST",
//               "houseNumber": "6",
//               "zipCode": "2070",
//               "city": "92250",
//               "boxNumber": "B011"
//             }
//           },
//           "modeOfPayment": {
//             "otherPersonName": null,
//             "belgianSEPABankAccount": "****************",
//             "foreignBankAccountIBAN": null,
//             "foreignBankAccountBIC": null
//           },
//           "unionContribution": {
//             "authorized": false,
//             "effectiveDate": "2024-01-01"
//           }
//         }
//       ),
//
//     })
//   );
//
//
//
//   await page.goto('/DataCapture?language=NL&requestId=bdd8d117-e577-41ba-bef7-0335544b955a');
//
//   await expect(page.locator('mat-expansion-panel#requestPanel')).toContainText('Compleet');
//   await expect(page.locator('mat-expansion-panel#employeePanel')).toContainText('Compleet');
//   await expect(page.locator('mat-expansion-panel#bankPanel')).toContainText('Compleet');
//   await expect(page.locator('mat-expansion-panel#syndicatePanel')).toContainText('Compleet');
//
//
// });
