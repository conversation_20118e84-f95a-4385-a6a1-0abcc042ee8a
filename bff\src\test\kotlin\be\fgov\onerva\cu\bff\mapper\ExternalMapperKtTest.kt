package be.fgov.onerva.cu.bff.mapper

import java.math.BigDecimal
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import be.fgov.onerva.cu.bff.rest.client.citizen.model.BankAccountDTO
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoDTO
import be.fgov.onerva.cu.bff.rest.client.citizen.model.ForeignAddressDTO

@Execution(ExecutionMode.SAME_THREAD)
class CitizenInfoMapperTest {

    @Nested
    @DisplayName("toCitizenInfoWithAddress")
    inner class ToCitizenInfoWithAddress {

        @Test
        @DisplayName("should map DTO with complete address correctly")
        fun shouldMapDtoWithCompleteAddressCorrectly() {
            // Given
            val address = "Rue de la Loi 16"

            val dto = CitizenInfoDTO().apply {
                this.firstName = "John"
                this.lastName = "Doe"
                this.numBox = BigDecimal.valueOf(42)
                this.flagNation = BigDecimal.valueOf(111)
                this.iban = "****************"
                this.address = address
                this.postalCode = "1000"
                this.paymentMode = 1
                this.bankAccount = BankAccountDTO().apply {
                    this.iban = "****************"
                    this.bic = "THEBIC"
                    this.holder = "The Holder"
                }
                this.addressObj = ForeignAddressDTO().apply {
                    this.city = "Brussels"
                    this.street = "Rue de la Loi"
                    this.box = null
                    this.countryCode = 1
                    this.zip = "1000"
                    this.number = "16"
                }
            }

            // When
            val result = dto.toCitizenInfoWithAddress(150)

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("John")
            assertThat(result.lastName).isEqualTo("Doe")
            assertThat(result.numbox).isEqualTo(42)
            assertThat(result.nationalityCode).isEqualTo(111)
            assertThat(result.iban).isEqualTo("****************")

            assertThat(result.address).isNotNull
            assertThat(result.address.street).isEqualTo("Rue de la Loi")
            assertThat(result.address.houseNumber).isEqualTo("16")
            assertThat(result.address.boxNumber).isNull()
            assertThat(result.address.countryCode).isEqualTo(150)
            assertThat(result.address.zipCode).isEqualTo("1000")
            assertThat(result.address.city).isEqualTo("Brussels")
        }

        @Test
        @DisplayName("should map DTO with complete address and box number correctly")
        fun shouldMapDtoWithCompleteAddressAndBoxNumberCorrectly() {
            // Given
            val address = "Avenue Louise 149/B24"

            val dto = CitizenInfoDTO().apply {
                this.firstName = "Jane"
                this.lastName = "Smith"
                this.numBox = BigDecimal.valueOf(42)
                this.flagNation = BigDecimal.valueOf(111)
                this.iban = "****************"
                this.address = address
                this.postalCode = "1050"
                this.paymentMode = 1
                this.bankAccount = BankAccountDTO().apply {
                    this.iban = "****************"
                    this.bic = "THEBIC"
                    this.holder = "The Holder"
                }
                this.addressObj = null
            }

            // When
            val result = dto.toCitizenInfoWithAddress(null)

            // Then
            assertThat(result).isNotNull
            assertThat(result.firstName).isEqualTo("Jane")
            assertThat(result.lastName).isEqualTo("Smith")
            assertThat(result.numbox).isEqualTo(42)
            assertThat(result.nationalityCode).isEqualTo(111)
            assertThat(result.iban).isEqualTo("****************")

            assertThat(result.address).isNotNull
            assertThat(result.address.street).isNull()
            assertThat(result.address.houseNumber).isNull()
            assertThat(result.address.boxNumber).isNull()
            assertThat(result.address.countryCode).isNull()
            assertThat(result.address.zipCode).isNull()
            assertThat(result.address.city).isNull()
        }
    }
}