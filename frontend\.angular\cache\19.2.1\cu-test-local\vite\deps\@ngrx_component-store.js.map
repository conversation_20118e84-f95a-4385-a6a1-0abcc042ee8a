{"version": 3, "sources": ["../../../../../../node_modules/@ngrx/component-store/fesm2022/ngrx-component-store.mjs"], "sourcesContent": ["import { Observable, Subscription, asapScheduler, ReplaySubject, isObservable, of, queueScheduler, EMPTY, throwError, scheduled, combineLatest, Subject } from 'rxjs';\nimport { take, takeUntil, observeOn, tap, withLatestFrom, map, catchError, distinctUntilChanged, shareReplay } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, computed, isDevMode, Injectable, Optional, Inject } from '@angular/core';\nimport { toSignal } from '@angular/core/rxjs-interop';\n\n/**\n * @license MIT License\n *\n * Copyright (c) 2017-2020 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nfunction debounceSync() {\n  return source => new Observable(observer => {\n    let actionSubscription;\n    let actionValue;\n    const rootSubscription = new Subscription();\n    rootSubscription.add(source.subscribe({\n      complete: () => {\n        if (actionSubscription) {\n          observer.next(actionValue);\n        }\n        observer.complete();\n      },\n      error: error => {\n        observer.error(error);\n      },\n      next: value => {\n        actionValue = value;\n        if (!actionSubscription) {\n          actionSubscription = asapScheduler.schedule(() => {\n            observer.next(actionValue);\n            actionSubscription = undefined;\n          });\n          rootSubscription.add(actionSubscription);\n        }\n      }\n    }));\n    return rootSubscription;\n  });\n}\n\n/**\n * Checks to see if the OnInitStore lifecycle hook\n * is defined on the ComponentStore.\n *\n * @param cs ComponentStore type\n * @returns boolean\n */\nfunction isOnStoreInitDefined(cs) {\n  return typeof cs.ngrxOnStoreInit === 'function';\n}\n/**\n * Checks to see if the OnInitState lifecycle hook\n * is defined on the ComponentStore.\n *\n * @param cs ComponentStore type\n * @returns boolean\n */\nfunction isOnStateInitDefined(cs) {\n  return typeof cs.ngrxOnStateInit === 'function';\n}\n/**\n * @description\n *\n * Function that returns the ComponentStore\n * class registered as a provider,\n * and uses a factory provider to instantiate the\n * ComponentStore and run the lifecycle hooks\n * defined on the ComponentStore.\n *\n * @param componentStoreClass The ComponentStore with lifecycle hooks\n * @returns Provider[]\n *\n * @usageNotes\n *\n * ```ts\n * @Injectable()\n * export class MyStore\n *    extends ComponentStore<{ init: boolean }>\n *    implements OnStoreInit, OnStateInit\n *   {\n *\n *   constructor() {\n *     super({ init: true });\n *   }\n *\n *   ngrxOnStoreInit() {\n *     // runs once after store has been instantiated\n *   }\n *\n *   ngrxOnStateInit() {\n *     // runs once after store state has been initialized\n *   }\n * }\n *\n * @Component({\n *   providers: [\n *     provideComponentStore(MyStore)\n *   ]\n * })\n * export class MyComponent {\n *   constructor(private myStore: MyStore) {}\n * }\n * ```\n */\nfunction provideComponentStore(componentStoreClass) {\n  const CS_WITH_HOOKS = new InjectionToken('@ngrx/component-store ComponentStore with Hooks');\n  return [{\n    provide: CS_WITH_HOOKS,\n    useClass: componentStoreClass\n  }, {\n    provide: componentStoreClass,\n    useFactory: () => {\n      const componentStore = inject(CS_WITH_HOOKS);\n      // Set private property that CS has been provided with lifecycle hooks\n      componentStore['ɵhasProvider'] = true;\n      if (isOnStoreInitDefined(componentStore)) {\n        componentStore.ngrxOnStoreInit();\n      }\n      if (isOnStateInitDefined(componentStore)) {\n        componentStore.state$.pipe(take(1)).subscribe(() => componentStore.ngrxOnStateInit());\n      }\n      return componentStore;\n    }\n  }];\n}\nconst INITIAL_STATE_TOKEN = new InjectionToken('@ngrx/component-store Initial State');\nclass ComponentStore {\n  constructor(defaultState) {\n    // Should be used only in ngOnDestroy.\n    this.destroySubject$ = new ReplaySubject(1);\n    // Exposed to any extending Store to be used for the teardown.\n    this.destroy$ = this.destroySubject$.asObservable();\n    this.stateSubject$ = new ReplaySubject(1);\n    this.isInitialized = false;\n    // Needs to be after destroy$ is declared because it's used in select.\n    this.state$ = this.select(s => s);\n    this.state = toSignal(this.stateSubject$.pipe(takeUntil(this.destroy$)), {\n      requireSync: false,\n      manualCleanup: true\n    });\n    this.ɵhasProvider = false;\n    // State can be initialized either through constructor or setState.\n    if (defaultState) {\n      this.initState(defaultState);\n    }\n    this.checkProviderForHooks();\n  }\n  /** Completes all relevant Observable streams. */\n  ngOnDestroy() {\n    this.stateSubject$.complete();\n    this.destroySubject$.next();\n  }\n  /**\n   * Creates an updater.\n   *\n   * Throws an error if updater is called with synchronous values (either\n   * imperative value or Observable that is synchronous) before ComponentStore\n   * is initialized. If called with async Observable before initialization then\n   * state will not be updated and subscription would be closed.\n   *\n   * @param updaterFn A static updater function that takes 2 parameters (the\n   * current state and an argument object) and returns a new instance of the\n   * state.\n   * @return A function that accepts one argument which is forwarded as the\n   *     second argument to `updaterFn`. Every time this function is called\n   *     subscribers will be notified of the state change.\n   */\n  updater(updaterFn) {\n    return observableOrValue => {\n      // We need to explicitly throw an error if a synchronous error occurs.\n      // This is necessary to make synchronous errors catchable.\n      let isSyncUpdate = true;\n      let syncError;\n      // We can receive either the value or an observable. In case it's a\n      // simple value, we'll wrap it with `of` operator to turn it into\n      // Observable.\n      const observable$ = isObservable(observableOrValue) ? observableOrValue : of(observableOrValue);\n      const subscription = observable$.pipe(\n      // Push the value into queueScheduler\n      observeOn(queueScheduler),\n      // If the state is not initialized yet, we'll throw an error.\n      tap(() => this.assertStateIsInitialized()), withLatestFrom(this.stateSubject$),\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      map(([value, currentState]) => updaterFn(currentState, value)), tap(newState => this.stateSubject$.next(newState)), catchError(error => {\n        if (isSyncUpdate) {\n          syncError = error;\n          return EMPTY;\n        }\n        return throwError(error);\n      }), takeUntil(this.destroy$)).subscribe();\n      if (syncError) {\n        throw syncError;\n      }\n      isSyncUpdate = false;\n      return subscription;\n    };\n  }\n  /**\n   * Initializes state. If it was already initialized then it resets the\n   * state.\n   */\n  initState(state) {\n    scheduled([state], queueScheduler).subscribe(s => {\n      this.isInitialized = true;\n      this.stateSubject$.next(s);\n    });\n  }\n  /**\n   * Sets the state specific value.\n   * @param stateOrUpdaterFn object of the same type as the state or an\n   * updaterFn, returning such object.\n   */\n  setState(stateOrUpdaterFn) {\n    if (typeof stateOrUpdaterFn !== 'function') {\n      this.initState(stateOrUpdaterFn);\n    } else {\n      this.updater(stateOrUpdaterFn)();\n    }\n  }\n  /**\n   * Patches the state with provided partial state.\n   *\n   * @param partialStateOrUpdaterFn a partial state or a partial updater\n   * function that accepts the state and returns the partial state.\n   * @throws Error if the state is not initialized.\n   */\n  patchState(partialStateOrUpdaterFn) {\n    const patchedState = typeof partialStateOrUpdaterFn === 'function' ? partialStateOrUpdaterFn(this.get()) : partialStateOrUpdaterFn;\n    this.updater((state, partialState) => ({\n      ...state,\n      ...partialState\n    }))(patchedState);\n  }\n  get(projector) {\n    this.assertStateIsInitialized();\n    let value;\n    this.stateSubject$.pipe(take(1)).subscribe(state => {\n      value = projector ? projector(state) : state;\n    });\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return value;\n  }\n  select(...args) {\n    const {\n      observablesOrSelectorsObject,\n      projector,\n      config\n    } = processSelectorArgs(args);\n    const source$ = hasProjectFnOnly(observablesOrSelectorsObject, projector) ? this.stateSubject$ : combineLatest(observablesOrSelectorsObject);\n    return source$.pipe(config.debounce ? debounceSync() : noopOperator(), projector ? map(projectorArgs =>\n    // projectorArgs could be an Array in case where the entire state is an Array, so adding this check\n    observablesOrSelectorsObject.length > 0 && Array.isArray(projectorArgs) ? projector(...projectorArgs) : projector(projectorArgs)) : noopOperator(), distinctUntilChanged(config.equal), shareReplay({\n      refCount: true,\n      bufferSize: 1\n    }), takeUntil(this.destroy$));\n  }\n  selectSignal(...args) {\n    const selectSignalArgs = [...args];\n    const options = typeof selectSignalArgs[args.length - 1] === 'object' ? selectSignalArgs.pop() : {};\n    const projector = selectSignalArgs.pop();\n    const signals = selectSignalArgs;\n    const computation = signals.length === 0 ? () => projector(this.state()) : () => {\n      const values = signals.map(signal => signal());\n      return projector(...values);\n    };\n    return computed(computation, options);\n  }\n  /**\n   * Creates an effect.\n   *\n   * This effect is subscribed to throughout the lifecycle of the ComponentStore.\n   * @param generator A function that takes an origin Observable input and\n   *     returns an Observable. The Observable that is returned will be\n   *     subscribed to for the life of the component.\n   * @return A function that, when called, will trigger the origin Observable.\n   */\n  effect(generator) {\n    const origin$ = new Subject();\n    generator(origin$)\n    // tied to the lifecycle 👇 of ComponentStore\n    .pipe(takeUntil(this.destroy$)).subscribe();\n    return observableOrValue => {\n      const observable$ = isObservable(observableOrValue) ? observableOrValue : of(observableOrValue);\n      return observable$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n        // any new 👇 value is pushed into a stream\n        origin$.next(value);\n      });\n    };\n  }\n  /**\n   * Used to check if lifecycle hooks are defined\n   * but not used with provideComponentStore()\n   */\n  checkProviderForHooks() {\n    asapScheduler.schedule(() => {\n      if (isDevMode() && (isOnStoreInitDefined(this) || isOnStateInitDefined(this)) && !this.ɵhasProvider) {\n        const warnings = [isOnStoreInitDefined(this) ? 'OnStoreInit' : '', isOnStateInitDefined(this) ? 'OnStateInit' : ''].filter(defined => defined);\n        console.warn(`@ngrx/component-store: ${this.constructor.name} has the ${warnings.join(' and ')} ` + 'lifecycle hook(s) implemented without being provided using the ' + `provideComponentStore(${this.constructor.name}) function. ` + `To resolve this, provide the component store via provideComponentStore(${this.constructor.name})`);\n      }\n    });\n  }\n  assertStateIsInitialized() {\n    if (!this.isInitialized) {\n      throw new Error(`${this.constructor.name} has not been initialized yet. ` + `Please make sure it is initialized before updating/getting.`);\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function ComponentStore_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ComponentStore)(i0.ɵɵinject(INITIAL_STATE_TOKEN, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ComponentStore,\n      factory: ComponentStore.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ComponentStore, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [INITIAL_STATE_TOKEN]\n    }]\n  }], null);\n})();\nfunction processSelectorArgs(args) {\n  const selectorArgs = Array.from(args);\n  const defaultEqualityFn = (previous, current) => previous === current;\n  // Assign default values.\n  let config = {\n    debounce: false,\n    equal: defaultEqualityFn\n  };\n  // Last argument is either config or projector or selectorsObject\n  if (isSelectConfig(selectorArgs[selectorArgs.length - 1])) {\n    config = {\n      ...config,\n      ...selectorArgs.pop()\n    };\n  }\n  // At this point selectorArgs is either projector, selectors with projector or selectorsObject\n  if (selectorArgs.length === 1 && typeof selectorArgs[0] !== 'function') {\n    // this is a selectorsObject\n    return {\n      observablesOrSelectorsObject: selectorArgs[0],\n      projector: undefined,\n      config\n    };\n  }\n  const projector = selectorArgs.pop();\n  // The Observables to combine, if there are any left.\n  const observables = selectorArgs;\n  return {\n    observablesOrSelectorsObject: observables,\n    projector,\n    config\n  };\n}\nfunction isSelectConfig(arg) {\n  const typedArg = arg;\n  return typeof typedArg.debounce !== 'undefined' || typeof typedArg.equal !== 'undefined';\n}\nfunction hasProjectFnOnly(observablesOrSelectorsObject, projector) {\n  return Array.isArray(observablesOrSelectorsObject) && observablesOrSelectorsObject.length === 0 && projector;\n}\nfunction noopOperator() {\n  return source$ => source$;\n}\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ComponentStore, INITIAL_STATE_TOKEN, provideComponentStore };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SAAS,eAAe;AACtB,SAAO,YAAU,IAAI,WAAW,cAAY;AAC1C,QAAI;AACJ,QAAI;AACJ,UAAM,mBAAmB,IAAI,aAAa;AAC1C,qBAAiB,IAAI,OAAO,UAAU;AAAA,MACpC,UAAU,MAAM;AACd,YAAI,oBAAoB;AACtB,mBAAS,KAAK,WAAW;AAAA,QAC3B;AACA,iBAAS,SAAS;AAAA,MACpB;AAAA,MACA,OAAO,WAAS;AACd,iBAAS,MAAM,KAAK;AAAA,MACtB;AAAA,MACA,MAAM,WAAS;AACb,sBAAc;AACd,YAAI,CAAC,oBAAoB;AACvB,+BAAqB,cAAc,SAAS,MAAM;AAChD,qBAAS,KAAK,WAAW;AACzB,iCAAqB;AAAA,UACvB,CAAC;AACD,2BAAiB,IAAI,kBAAkB;AAAA,QACzC;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AACF,WAAO;AAAA,EACT,CAAC;AACH;AASA,SAAS,qBAAqB,IAAI;AAChC,SAAO,OAAO,GAAG,oBAAoB;AACvC;AAQA,SAAS,qBAAqB,IAAI;AAChC,SAAO,OAAO,GAAG,oBAAoB;AACvC;AA6CA,SAAS,sBAAsB,qBAAqB;AAClD,QAAM,gBAAgB,IAAI,eAAe,iDAAiD;AAC1F,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,YAAM,iBAAiB,OAAO,aAAa;AAE3C,qBAAe,cAAc,IAAI;AACjC,UAAI,qBAAqB,cAAc,GAAG;AACxC,uBAAe,gBAAgB;AAAA,MACjC;AACA,UAAI,qBAAqB,cAAc,GAAG;AACxC,uBAAe,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM,eAAe,gBAAgB,CAAC;AAAA,MACtF;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,IAAM,sBAAsB,IAAI,eAAe,qCAAqC;AACpF,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,cAAc;AAExB,SAAK,kBAAkB,IAAI,cAAc,CAAC;AAE1C,SAAK,WAAW,KAAK,gBAAgB,aAAa;AAClD,SAAK,gBAAgB,IAAI,cAAc,CAAC;AACxC,SAAK,gBAAgB;AAErB,SAAK,SAAS,KAAK,OAAO,OAAK,CAAC;AAChC,SAAK,QAAQ,SAAS,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ,CAAC,GAAG;AAAA,MACvE,aAAa;AAAA,MACb,eAAe;AAAA,IACjB,CAAC;AACD,SAAK,eAAe;AAEpB,QAAI,cAAc;AAChB,WAAK,UAAU,YAAY;AAAA,IAC7B;AACA,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,QAAQ,WAAW;AACjB,WAAO,uBAAqB;AAG1B,UAAI,eAAe;AACnB,UAAI;AAIJ,YAAM,cAAc,aAAa,iBAAiB,IAAI,oBAAoB,GAAG,iBAAiB;AAC9F,YAAM,eAAe,YAAY;AAAA;AAAA,QAEjC,UAAU,cAAc;AAAA;AAAA,QAExB,IAAI,MAAM,KAAK,yBAAyB,CAAC;AAAA,QAAG,eAAe,KAAK,aAAa;AAAA;AAAA,QAE7E,IAAI,CAAC,CAAC,OAAO,YAAY,MAAM,UAAU,cAAc,KAAK,CAAC;AAAA,QAAG,IAAI,cAAY,KAAK,cAAc,KAAK,QAAQ,CAAC;AAAA,QAAG,WAAW,WAAS;AACtI,cAAI,cAAc;AAChB,wBAAY;AACZ,mBAAO;AAAA,UACT;AACA,iBAAO,WAAW,KAAK;AAAA,QACzB,CAAC;AAAA,QAAG,UAAU,KAAK,QAAQ;AAAA,MAAC,EAAE,UAAU;AACxC,UAAI,WAAW;AACb,cAAM;AAAA,MACR;AACA,qBAAe;AACf,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,cAAU,CAAC,KAAK,GAAG,cAAc,EAAE,UAAU,OAAK;AAChD,WAAK,gBAAgB;AACrB,WAAK,cAAc,KAAK,CAAC;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,kBAAkB;AACzB,QAAI,OAAO,qBAAqB,YAAY;AAC1C,WAAK,UAAU,gBAAgB;AAAA,IACjC,OAAO;AACL,WAAK,QAAQ,gBAAgB,EAAE;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,yBAAyB;AAClC,UAAM,eAAe,OAAO,4BAA4B,aAAa,wBAAwB,KAAK,IAAI,CAAC,IAAI;AAC3G,SAAK,QAAQ,CAAC,OAAO,iBAAkB,kCAClC,QACA,aACH,EAAE,YAAY;AAAA,EAClB;AAAA,EACA,IAAI,WAAW;AACb,SAAK,yBAAyB;AAC9B,QAAI;AACJ,SAAK,cAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAClD,cAAQ,YAAY,UAAU,KAAK,IAAI;AAAA,IACzC,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EACA,UAAU,MAAM;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,oBAAoB,IAAI;AAC5B,UAAM,UAAU,iBAAiB,8BAA8B,SAAS,IAAI,KAAK,gBAAgB,cAAc,4BAA4B;AAC3I,WAAO,QAAQ,KAAK,OAAO,WAAW,aAAa,IAAI,aAAa,GAAG,YAAY,IAAI;AAAA;AAAA,MAEvF,6BAA6B,SAAS,KAAK,MAAM,QAAQ,aAAa,IAAI,UAAU,GAAG,aAAa,IAAI,UAAU,aAAa;AAAA,KAAC,IAAI,aAAa,GAAG,qBAAqB,OAAO,KAAK,GAAG,YAAY;AAAA,MAClM,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAAA,EAC9B;AAAA,EACA,gBAAgB,MAAM;AACpB,UAAM,mBAAmB,CAAC,GAAG,IAAI;AACjC,UAAM,UAAU,OAAO,iBAAiB,KAAK,SAAS,CAAC,MAAM,WAAW,iBAAiB,IAAI,IAAI,CAAC;AAClG,UAAM,YAAY,iBAAiB,IAAI;AACvC,UAAM,UAAU;AAChB,UAAM,cAAc,QAAQ,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC,IAAI,MAAM;AAC/E,YAAM,SAAS,QAAQ,IAAI,YAAU,OAAO,CAAC;AAC7C,aAAO,UAAU,GAAG,MAAM;AAAA,IAC5B;AACA,WAAO,SAAS,aAAa,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,WAAW;AAChB,UAAM,UAAU,IAAI,QAAQ;AAC5B,cAAU,OAAO,EAEhB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU;AAC1C,WAAO,uBAAqB;AAC1B,YAAM,cAAc,aAAa,iBAAiB,IAAI,oBAAoB,GAAG,iBAAiB;AAC9F,aAAO,YAAY,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAEnE,gBAAQ,KAAK,KAAK;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,kBAAc,SAAS,MAAM;AAC3B,UAAI,UAAU,MAAM,qBAAqB,IAAI,KAAK,qBAAqB,IAAI,MAAM,CAAC,KAAK,cAAc;AACnG,cAAM,WAAW,CAAC,qBAAqB,IAAI,IAAI,gBAAgB,IAAI,qBAAqB,IAAI,IAAI,gBAAgB,EAAE,EAAE,OAAO,aAAW,OAAO;AAC7I,gBAAQ,KAAK,0BAA0B,KAAK,YAAY,IAAI,YAAY,SAAS,KAAK,OAAO,CAAC,yFAAmG,KAAK,YAAY,IAAI,sFAA2F,KAAK,YAAY,IAAI,GAAG;AAAA,MAC3U;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,2BAA2B;AACzB,QAAI,CAAC,KAAK,eAAe;AACvB,YAAM,IAAI,MAAM,GAAG,KAAK,YAAY,IAAI,4FAAiG;AAAA,IAC3I;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,SAAS,qBAAqB,CAAC,CAAC;AAAA,IACtF;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,oBAAoB,MAAM;AACjC,QAAM,eAAe,MAAM,KAAK,IAAI;AACpC,QAAM,oBAAoB,CAAC,UAAU,YAAY,aAAa;AAE9D,MAAI,SAAS;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,EACT;AAEA,MAAI,eAAe,aAAa,aAAa,SAAS,CAAC,CAAC,GAAG;AACzD,aAAS,kCACJ,SACA,aAAa,IAAI;AAAA,EAExB;AAEA,MAAI,aAAa,WAAW,KAAK,OAAO,aAAa,CAAC,MAAM,YAAY;AAEtE,WAAO;AAAA,MACL,8BAA8B,aAAa,CAAC;AAAA,MAC5C,WAAW;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,aAAa,IAAI;AAEnC,QAAM,cAAc;AACpB,SAAO;AAAA,IACL,8BAA8B;AAAA,IAC9B;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,eAAe,KAAK;AAC3B,QAAM,WAAW;AACjB,SAAO,OAAO,SAAS,aAAa,eAAe,OAAO,SAAS,UAAU;AAC/E;AACA,SAAS,iBAAiB,8BAA8B,WAAW;AACjE,SAAO,MAAM,QAAQ,4BAA4B,KAAK,6BAA6B,WAAW,KAAK;AACrG;AACA,SAAS,eAAe;AACtB,SAAO,aAAW;AACpB;", "names": []}