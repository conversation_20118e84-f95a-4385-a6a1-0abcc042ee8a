FROM docker-proxy.onemrva.priv/openapitools/openapi-generator-cli:v7.1.0 as openapigenerator

COPY api/public /usr/src/api
COPY api/private /usr/src/api
COPY scripts/generate_apis.sh /usr/src/api
WORKDIR /usr/src/api

RUN chmod +x generate_apis.sh
RUN ./generate_apis.sh

FROM docker-proxy.onemrva.priv/cypress/included:13.13.1 as nodecompiler

ENV no_proxy=localhost,nexusprod,kubernetes.docker.internal,.onemrva.priv,.paas.onemrva.priv,bitbucket,servicestest,nexusprod.onemrva.priv,*.neocloud.be,neocloud.be
ENV https_proxy=http://proxy-intra-prod.onemrva.priv:8080
ENV http_proxy=http://proxy-intra-prod.onemrva.priv:8080
ENV NPM_CONFIG_REGISTRY=https://nexusprod.onemrva.priv/repository/npm-group
ENV NODE_EXTRA_CA_CERTS=/usr/src/app/rva.crt

ARG NEXUS_USER
ARG NEXUS_PASSWORD
WORKDIR /usr/src/app
RUN chmod go+rwx / /usr/src/app
RUN umask -S go+rwX

RUN apt update && apt install curl -y

COPY ./crt/rva.crt ./rva.crt
COPY frontend/package.json frontend/package-lock.json ./

RUN npm i -g npm@8.3.0
RUN npm ci --no-fund --no-audit

COPY frontend/ .
COPY frontend/projects/cu/src/lib/environments/environment.e2e.ts ./projects/cu/src/lib/environments/environment.ts

COPY --from=openapigenerator /usr/src/api/rest-client ./projects/cu/src/rest-client

RUN npm run build cu-test-local
RUN npm run build:components

RUN chmod ug=rx e2e.sh
RUN chmod -R g+rwX projects/cu-test-local/cypress/

RUN mkdir -p /.config && chmod -R g=u /.config \
    && mkdir -p projects/cu-test-local/cypress/reports && chmod -R g+rwX projects/cu-test-local/cypress/reports \
    && mkdir -p projects/cu-test-local/cypress/videos && chmod -R g+rwX projects/cu-test-local/cypress/videos \
    && mkdir -p projects/cu-test-local/cypress/screenshots && chmod -R g+rwX projects/cu-test-local/cypress/screenshots \
    && mkdir -p projects/cu-test-local/cypress/cucumber-json && chmod -R g+rwX projects/cu-test-local/cypress/cucumber-json \
    && mkdir -p coverage && chmod -R g+rwX coverage \
    && mkdir -p .nyc_output && chmod -R g+rwX .nyc_output
ENV XDG_CONFIG_HOME=/.config

ENV NEXUS_USER=$NEXUS_USER
ENV NEXUS_PASSWORD=$NEXUS_PASSWORD
ENV http_proxy="http://proxy-intra-test.onemrva.priv:8080"
ENV https_proxy="http://proxy-intra-test.onemrva.priv:8080"

ENTRYPOINT [ "/usr/src/app/e2e.sh" ]
