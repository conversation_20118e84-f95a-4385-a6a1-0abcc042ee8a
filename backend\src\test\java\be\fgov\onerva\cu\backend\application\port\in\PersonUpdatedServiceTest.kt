package be.fgov.onerva.cu.backend.application.port.`in`

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.PersonUpdated
import be.fgov.onerva.cu.backend.application.port.out.SyncFollowUpPort
import be.fgov.onerva.cu.backend.application.service.PersonUpdatedService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class PersonUpdatedServiceTest {

    @MockK
    lateinit var syncFollowUpPort: SyncFollowUpPort

    @InjectMockKs
    lateinit var personUpdatedService: PersonUpdatedService

    @Test
    fun `receivedPersonUpdated should call updateSyncFollowUpAsOk with correct correlation id`() {
        // Given
        val correlationId = "test-correlation-id"
        every { syncFollowUpPort.updateSyncFollowUp(any(), true, 0) } returns Unit

        // When
        personUpdatedService.receivedPersonUpdated(
            PersonUpdated(
                id = 123,
                correlationId = correlationId,
                ssin = "12345678901",
                success = true,
                names = "John Doe",
                errorCode = 0,
            )
        )

        // Then
        verify(exactly = 1) { syncFollowUpPort.updateSyncFollowUp(correlationId, true, 0) }
    }
}