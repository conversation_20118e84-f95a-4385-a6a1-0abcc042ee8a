import {FormUtilsService} from "./form-utils.service";
import {FormControl, Validators} from "@angular/forms";

describe("FormUtilsService", () => {
    let formUtilsService: FormUtilsService;

    beforeEach(() => {
        formUtilsService = new FormUtilsService();
    });

    it("should be created", () => {
        expect(formUtilsService).toBeTruthy();
    });

    describe("checkLengthOfInput", () => {
        it("should return null if FormControl has no maxlength error", () => {
            const ctrl = new FormControl("", [Validators.required]);
            expect(FormUtilsService.checkLengthOfInput(ctrl)).toBeNull();
        });

        it("should return maxlength error details if present", () => {
            const ctrl = new FormControl("toolongvalue", [Validators.maxLength(5)]);
            const expectedError = {requiredLength: 5, actualLength: 12};
            expect(FormUtilsService.checkLengthOfInput(ctrl)).toEqual(expectedError);
        });
    });

    describe("isClosedOrWaiting", () => {
        it("should return true when status is CLOSED", () => {
            expect(FormUtilsService.isClosedOrWaiting("CLOSED", {})).toBe(true);
        });

        it("should return true when task state code is Wait", () => {
            const task = {state: {code: "Wait"}};
            expect(FormUtilsService.isClosedOrWaiting("OPEN", task)).toBe(true);
        });

        it("should return false when status is not CLOSED and task state code is not Wait", () => {
            const task = {state: {code: "InProgress"}};
            expect(FormUtilsService.isClosedOrWaiting("OPEN", task)).toBe(false);
        });

        it("should return false when status is not CLOSED and task is null", () => {
            expect(FormUtilsService.isClosedOrWaiting("OPEN", undefined)).toBe(false);
        });

        it("should return false when status is not CLOSED and task state is undefined", () => {
            const task = {};
            expect(FormUtilsService.isClosedOrWaiting("OPEN", task)).toBe(false);
        });
    });

    describe("isLogiclyDeleted", () => {
        it("should return true when technicalStatus is DELETED", () => {
            const task = {
                technicalInformation: {
                    technicalStatus: "DELETED",
                },
            };
            expect(FormUtilsService.isLogiclyDeleted(task)).toBe(true);
        });

        it("should return false when technicalStatus is not DELETED", () => {
            const task = {
                technicalInformation: {
                    technicalStatus: "ACTIVE",
                },
            };
            expect(FormUtilsService.isLogiclyDeleted(task)).toBe(false);
        });

        it("should return false when task is null", () => {
            expect(FormUtilsService.isLogiclyDeleted(undefined)).toBe(false);
        });

        it("should return false when task is undefined", () => {
            expect(FormUtilsService.isLogiclyDeleted(undefined)).toBe(false);
        });

        it("should return false when technicalInformation is undefined", () => {
            const task = {};
            expect(FormUtilsService.isLogiclyDeleted(task)).toBe(false);
        });

        it("should return false when technicalStatus is undefined", () => {
            const task = {
                technicalInformation: {},
            };
            expect(FormUtilsService.isLogiclyDeleted(task)).toBe(false);
        });

        it("should return false when technicalStatus is empty string", () => {
            const task = {
                technicalInformation: {
                    technicalStatus: "",
                },
            };
            expect(FormUtilsService.isLogiclyDeleted(task)).toBe(false);
        });
    });

    describe("getWaveProcessUrl", () => {
        it("should return correct URL when processId exists", () => {
            const task = {
                parentProcess: {
                    processId: "12345",
                },
            };
            const expectedUrl = "/processes-page/process/(process-detail/12345!!sidemenu:process-detail/12345)";
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe(expectedUrl);
        });

        it("should return empty string when task is null", () => {
            expect(FormUtilsService.getWaveProcessUrl(undefined)).toBe("");
        });

        it("should return empty string when task is undefined", () => {
            expect(FormUtilsService.getWaveProcessUrl(undefined)).toBe("");
        });

        it("should return empty string when parentProcess is undefined", () => {
            const task = {};
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe("");
        });

        it("should return empty string when processId is undefined", () => {
            const task = {
                parentProcess: {},
            };
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe("");
        });

        it("should return empty string when processId is null", () => {
            const task = {
                parentProcess: {
                    processId: undefined,
                },
            };
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe("");
        });

        it("should return empty string when processId is empty string", () => {
            const task = {
                parentProcess: {
                    processId: "",
                },
            };
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe("");
        });

        it("should handle numeric processId", () => {
            const task = {
                parentProcess: {
                    processId: "98765",
                },
            };
            const expectedUrl = "/processes-page/process/(process-detail/98765!!sidemenu:process-detail/98765)";
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe(expectedUrl);
        });

        it("should handle string processId with special characters", () => {
            const task = {
                parentProcess: {
                    processId: "ABC-123_test",
                },
            };
            const expectedUrl = "/processes-page/process/(process-detail/ABC-123_test!!sidemenu:process-detail/ABC-123_test)";
            expect(FormUtilsService.getWaveProcessUrl(task)).toBe(expectedUrl);
        });
    });
});
