{"version": 3, "sources": ["../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-toast.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Inject, Component, NgModule } from '@angular/core';\nimport { MAT_SNACK_BAR_DATA, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { NgClass } from '@angular/common';\nvar ToastType;\n(function (ToastType) {\n  ToastType[\"info\"] = \"info\";\n  ToastType[\"success\"] = \"success\";\n  ToastType[\"warn\"] = \"warn\";\n  ToastType[\"error\"] = \"error\";\n  ToastType[\"accent\"] = \"accent\";\n  ToastType[\"primary\"] = \"primary\";\n})(ToastType || (ToastType = {}));\nclass OnemrvaMatToastComponent {\n  constructor(data) {\n    this.data = data;\n    this.dataCy = data.dataCy ? data.dataCy : 'onemrva-mat-toast';\n  }\n  static {\n    this.ɵfac = function OnemrvaMatToastComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatToastComponent)(i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OnemrvaMatToastComponent,\n      selectors: [[\"onemrva-mat-toast\"]],\n      decls: 2,\n      vars: 3,\n      consts: [[\"role\", \"alert\", \"aria-atomic\", \"true\", 1, \"onemrva-mat-toast\", 3, \"ngClass\"]],\n      template: function OnemrvaMatToastComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.data.type);\n          i0.ɵɵattribute(\"data-cy\", ctx.dataCy);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \" \");\n        }\n      },\n      dependencies: [NgClass],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatToastComponent, [{\n    type: Component,\n    args: [{\n      selector: 'onemrva-mat-toast',\n      template: `\n    <div\n      class=\"onemrva-mat-toast\"\n      [ngClass]=\"data.type\"\n      [attr.data-cy]=\"dataCy\"\n      role=\"alert\"\n      aria-atomic=\"true\"\n    >\n      {{ data.message }}\n    </div>\n  `,\n      standalone: true,\n      imports: [NgClass]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SNACK_BAR_DATA]\n    }]\n  }], null);\n})();\nclass OnemrvaMatToastModule {\n  static {\n    this.ɵfac = function OnemrvaMatToastModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatToastModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaMatToastModule,\n      imports: [OnemrvaMatToastComponent],\n      exports: [OnemrvaMatToastComponent, MatSnackBarModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatSnackBarModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatToastModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OnemrvaMatToastComponent],\n      exports: [OnemrvaMatToastComponent, MatSnackBarModule]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of mat-toast\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaMatToastComponent, OnemrvaMatToastModule, ToastType };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI;AAAA,CACH,SAAUA,YAAW;AACpB,EAAAA,WAAU,MAAM,IAAI;AACpB,EAAAA,WAAU,SAAS,IAAI;AACvB,EAAAA,WAAU,MAAM,IAAI;AACpB,EAAAA,WAAU,OAAO,IAAI;AACrB,EAAAA,WAAU,QAAQ,IAAI;AACtB,EAAAA,WAAU,SAAS,IAAI;AACzB,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,SAAS,KAAK,SAAS,KAAK,SAAS;AAAA,EAC5C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAkB,kBAAkB,CAAC;AAAA,IACrG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,SAAS,eAAe,QAAQ,GAAG,qBAAqB,GAAG,SAAS,CAAC;AAAA,MACvF,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,OAAO,CAAC;AACX,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,KAAK,IAAI;AACtC,UAAG,YAAY,WAAW,IAAI,MAAM;AACpC,UAAG,UAAU;AACb,UAAG,mBAAmB,KAAK,IAAI,KAAK,SAAS,GAAG;AAAA,QAClD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO;AAAA,MACtB,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWV,YAAY;AAAA,MACZ,SAAS,CAAC,OAAO;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,wBAAwB;AAAA,MAClC,SAAS,CAAC,0BAA0B,iBAAiB;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,wBAAwB;AAAA,MAClC,SAAS,CAAC,0BAA0B,iBAAiB;AAAA,IACvD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ToastType"]}