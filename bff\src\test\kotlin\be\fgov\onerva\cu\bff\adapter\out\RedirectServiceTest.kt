package be.fgov.onerva.cu.bff.adapter.out

import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.test.util.ReflectionTestUtils
import org.springframework.web.client.RestTemplate
import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import be.fgov.onerva.cu.bff.exceptions.CitizenNotFoundException
import be.fgov.onerva.cu.bff.exceptions.InvalidC9Exception
import be.fgov.onerva.cu.bff.exceptions.RequestNotFoundException
import be.fgov.onerva.cu.bff.exceptions.WaveUserNotFoundException
import be.fgov.onerva.cu.bff.model.Address
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking

@ExtendWith(MockKExtension::class)
class RedirectServiceTest {

    @MockK
    lateinit var restTemplate: RestTemplate

    @MockK
    lateinit var citizenInfoService: CitizenInfoService

    @MockK
    lateinit var waveUserService: WaveUserService

    @MockK
    lateinit var c9NavigateMainframeToS24Service: C9NavigateMainframeToS24Service

    @MockK
    private lateinit var restTemplateUtil: RestTemplateUtil

    @MockK
    lateinit var c9Service: C9Service

    private val backendBaseUrl = "http://localhost:9091"

    private val c51BaseUrl = "https://proc51.test.paas.onemrva.priv/proc51/home.jsf"

    private val regisUrl = "https://regis.test.paas.onemrva.priv/regis/regis/rew.seam"

    @InjectMockKs
    lateinit var redirectService: RedirectService

    private val defaultSsin = "85050599890"
    private val testAuthHeader = "Bearer test-token"

    @BeforeEach
    fun setup() {
        ReflectionTestUtils.setField(redirectService, "backendBaseUrl", backendBaseUrl)
        ReflectionTestUtils.setField(redirectService, "c51BaseUrl", c51BaseUrl)
        ReflectionTestUtils.setField(redirectService, "regisUrl", regisUrl)
        every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
    }

    private fun withSecurityContext(ssin: String = defaultSsin, block: suspend () -> Unit) = runBlocking {
        val originalContext = SecurityContextHolder.getContext()
        try {
            val jwt = mockk<Jwt>()
            every { jwt.claims } returns mapOf("ssin" to ssin)

            val authentication = mockk<Authentication>()
            every { authentication.principal } returns jwt

            val securityContext = mockk<SecurityContext>()
            every { securityContext.authentication } returns authentication

            SecurityContextHolder.setContext(securityContext)

            block()
        } finally {
            SecurityContextHolder.setContext(originalContext)
        }
    }

    private fun prepareMocks(
        requestId: UUID,
        basicInfo: RequestBasicInfoResponse?,
        citizen: CitizenInfoWithAddress?,
        operatorCode: String? = "OP123",
        throwWaveException: Boolean = false,
        c9Details: C9Service.C9Details? = null
    ) {
        every {
            restTemplate.getForObject(
                "$backendBaseUrl/api/requests/$requestId",
                RequestBasicInfoResponse::class.java
            )
        } returns basicInfo

        if (basicInfo != null) {
            every { citizenInfoService.getCitizenInfo(basicInfo.ssin, testAuthHeader) } returns citizen
        }

        if (c9Details != null) {
            every { c9Service.getC9Details(any()) } returns c9Details
        } else if (basicInfo?.c9Id != null) {
            every { c9Service.getC9Details(any()) } returns C9Service.C9Details(
                unemploymentOfficeCode = "123",
                opKey = "OP456"
            )
        }

        if (throwWaveException) {
            every { waveUserService.getOperatorCode(any()) } throws
                    WaveUserNotFoundException("Wave user not found for SSIN $defaultSsin")
        } else if (operatorCode != null) {
            every { waveUserService.getOperatorCode(any()) } returns operatorCode
        }
    }

    private fun createBasicInfo(
        ssin: String = defaultSsin,
        dateValid: LocalDate? = LocalDate.of(2025, 1, 1),
        introductionDate: LocalDate? = LocalDate.of(2023, 1, 1),
        c9Id: String = "12345"
    ) = RequestBasicInfoResponse(
        ssin = ssin,
        firstName = "John",
        lastName = "Doe",
        dateValid = dateValid,
        introductionDate = introductionDate,
        requestDate = LocalDate.of(2023, 1, 1),
        annexes = emptyList(),
        c9Id = c9Id,
        documentType = RequestBasicInfoResponse.DocumentType.ELECTRONIC,
    )

    private fun createCitizen(numbox: Int = 12345) = CitizenInfoWithAddress(
        numbox = numbox,
        firstName = "John",
        lastName = "Doe",
        address = Address(
            city = "New York",
            countryCode = 150,
            houseNumber = "123",
            street = "Main St",
            zipCode = "10001",
            boxNumber = "A"
        ),
        iban = "test",
        nationalityCode = 111,
        bic = "1234",
        otherPersonName = "test",
        paymentMode = 1,
        unionDue = null
    )

    @Test
    fun `getC51Link should return correct URL when all data is available`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()

            prepareMocks(requestId, basicInfo, citizen)

            // When
            val result = redirectService.getC51Link(requestId)

            // Then
            assertEquals(
                "https://proc51.test.paas.onemrva.priv/proc51/home.jsf?numbox=12345&validityDate=20250101&type=1&operatorCode=OP123",
                result
            )
        }
    }

    @Test
    fun `getC51Link should use introductionDate when dateValid is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo(dateValid = null)
            val citizen = createCitizen()

            prepareMocks(requestId, basicInfo, citizen)

            // When
            val result = redirectService.getC51Link(requestId)

            // Then
            assertEquals(
                "https://proc51.test.paas.onemrva.priv/proc51/home.jsf?numbox=12345&validityDate=20230101&type=1&operatorCode=OP123",
                result
            )
        }
    }

    @Test
    fun `getC51Link should throw RequestNotFoundException when basic info is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            prepareMocks(requestId, null, null)

            // When & Then
            val exception = assertThrows<RequestNotFoundException> {
                redirectService.getC51Link(requestId)
            }
            assertEquals("Basic info not found for request $requestId", exception.message)
        }
    }

    @Test
    fun `getC51Link should throw CitizenNotFoundException when citizen info is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()

            prepareMocks(requestId, basicInfo, null)

            // When & Then
            val exception = assertThrows<CitizenNotFoundException> {
                redirectService.getC51Link(requestId)
            }
            assertEquals("Citizen not found for SSIN $defaultSsin", exception.message)
        }
    }

    @Test
    fun `getC51Link should throw WaveUserNotFoundException when wave user is not found`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()

            prepareMocks(requestId, basicInfo, citizen, throwWaveException = true)

            // When & Then
            val exception = assertThrows<WaveUserNotFoundException> {
                redirectService.getC51Link(requestId)
            }
            assertEquals("Wave user not found for SSIN $defaultSsin", exception.message)
        }
    }

    @Test
    fun `openS24Session should call c9NavigateMainframeToS24Service with correct parameters`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            prepareMocks(requestId, basicInfo, null)
            every { c9NavigateMainframeToS24Service.openS24Session(any(), any()) } returns Unit

            // When
            redirectService.openS24Session(requestId)

            // Then
            verify(exactly = 1) {
                c9NavigateMainframeToS24Service.openS24Session(
                    basicInfo.ssin,
                    basicInfo.dateValid ?: basicInfo.introductionDate!!
                )
            }
        }
    }

    @Test
    fun `openS24Session should throw RequestNotFoundException when basic info is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            prepareMocks(requestId, null, null)

            // When & Then
            val exception = assertThrows<RequestNotFoundException> {
                redirectService.openS24Session(requestId)
            }
            assertEquals("Basic info not found for request $requestId", exception.message)
        }
    }

    @Test
    fun `getRegisRedirectUrl should return correct URL when all data is available`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo(c9Id = "12345")
            val citizen = createCitizen()
            val c9Details = C9Service.C9Details(
                unemploymentOfficeCode = "123",
                opKey = "OP456"
            )
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen, c9Details = c9Details)

            // When
            val result = redirectService.getRegisRedirectUrl(requestId, languageCode)

            // Then
            val expectedUrl = "https://regis.test.paas.onemrva.priv/regis/regis/rew.seam" +
                    "?type=REW" +
                    "&opCode=OP123" +
                    "&userid=$defaultSsin" +
                    "&l=1" +
                    "&niss=$defaultSsin" +
                    "&numbox=12345" +
                    "&date=20230101" +
                    "&dateS24=20230101" +
                    "&WB=123" +
                    "&numop=OP456"

            assertEquals(expectedUrl, result)
            verify(exactly = 1) { citizenInfoService.getCitizenInfo(defaultSsin, testAuthHeader) }
            verify(exactly = 1) { waveUserService.getOperatorCode(defaultSsin) }
            verify(exactly = 1) { c9Service.getC9Details("12345") }
        }
    }

    @Test
    fun `getRegisRedirectUrl should throw InvalidC9Exception when C9 details not found`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo(c9Id = "12345")
            val citizen = createCitizen()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen)

            every { c9Service.getC9Details("12345") } throws
                    C9NotFoundException("C9 details not found")

            // When & Then
            val exception = assertThrows<InvalidC9Exception> {
                redirectService.getRegisRedirectUrl(requestId, languageCode)
            }
            assertEquals("Failed to retrieve C9 details for C9 ID: 12345", exception.message)
        }
    }

    @Test
    fun `getRegisRedirectUrl should return correct URL for Dutch language`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo(c9Id = "12345")
            val citizen = createCitizen()
            val c9Details = C9Service.C9Details(
                unemploymentOfficeCode = "123",
                opKey = "OP456"
            )
            val languageCode = "nl"

            prepareMocks(requestId, basicInfo, citizen, c9Details = c9Details)

            // When
            val result = redirectService.getRegisRedirectUrl(requestId, languageCode)

            // Then
            val expectedUrl = "https://regis.test.paas.onemrva.priv/regis/regis/rew.seam" +
                    "?type=REW" +
                    "&opCode=OP123" +
                    "&userid=$defaultSsin" +
                    "&l=2" +
                    "&niss=$defaultSsin" +
                    "&numbox=12345" +
                    "&date=20230101" +
                    "&dateS24=20230101" +
                    "&WB=123" +
                    "&numop=OP456"

            assertEquals(expectedUrl, result)
        }
    }

    @Test
    fun `getRegisRedirectUrl should throw RequestNotFoundException when basic info is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            prepareMocks(requestId, null, null)
            val languageCode = "fr"

            // When & Then
            val exception = assertThrows<RequestNotFoundException> {
                redirectService.getRegisRedirectUrl(requestId, languageCode)
            }
            assertEquals("Basic info not found for request $requestId", exception.message)
        }
    }

    @Test
    fun `getRegisRedirectUrl should throw CitizenNotFoundException when citizen info is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, null)

            // When & Then
            val exception = assertThrows<CitizenNotFoundException> {
                redirectService.getRegisRedirectUrl(requestId, languageCode)
            }
            assertEquals("Citizen not found for SSIN $defaultSsin", exception.message)
        }
    }

    @Test
    fun `getRegisRedirectUrl should throw WaveUserNotFoundException when wave user is not found`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen, throwWaveException = true)

            // When & Then
            val exception = assertThrows<WaveUserNotFoundException> {
                redirectService.getRegisRedirectUrl(requestId, languageCode)
            }
            assertEquals("Wave user not found for SSIN $defaultSsin", exception.message)
        }
    }

    @Test
    fun `getRegisRedirectUrl should throw IllegalArgumentException for unsupported language code`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()
            val languageCode = "de" // Unsupported language code

            prepareMocks(requestId, basicInfo, citizen)

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                redirectService.getRegisRedirectUrl(requestId, languageCode)
            }
            assertEquals("Unsupported language code: $languageCode", exception.message)
        }
    }

    @Test
    fun `getRegisRedirectUrl should use dateValid as dateS24 if introductionDate is null`() = runBlocking {
        withSecurityContext {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo(
                c9Id = "12345",
                introductionDate = null,
                dateValid = LocalDate.of(2023, 2, 2)
            )
            val citizen = createCitizen()
            val c9Details = C9Service.C9Details(
                unemploymentOfficeCode = "123",
                opKey = "OP456"
            )
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen, c9Details = c9Details)

            // When
            val result = redirectService.getRegisRedirectUrl(requestId, languageCode)

            // Then
            val expectedUrl = "https://regis.test.paas.onemrva.priv/regis/regis/rew.seam" +
                    "?type=REW" +
                    "&opCode=OP123" +
                    "&userid=$defaultSsin" +
                    "&l=1" +
                    "&niss=$defaultSsin" +
                    "&numbox=12345" +
                    "&date=20230101" +
                    "&dateS24=20230202" +
                    "&WB=123" +
                    "&numop=OP456"

            assertEquals(expectedUrl, result)
        }
    }

    @Test
    fun `getRegisRedirectUrl should handle SSIN as string correctly`() = runBlocking {
        // Test with SSIN that has leading zeros as a string
        val ssinWithLeadingZeros = "00012345678"
        withSecurityContext(ssin = ssinWithLeadingZeros) {
            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo(ssin = "85050599890") // Different SSIN in basic info
            val citizen = createCitizen()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen)

            // When
            val result = redirectService.getRegisRedirectUrl(requestId, languageCode)

            // Then
            // Verify that the SSIN with leading zeros is preserved in the URL
            assert(result.contains("userid=$ssinWithLeadingZeros"))
            verify { waveUserService.getOperatorCode(ssinWithLeadingZeros) }
        }
    }

    @Test
    fun `getRegisRedirectUrl should pad SSIN when stored as number`() = runBlocking {
        // Create a mock where SSIN is stored as a number
        val originalContext = SecurityContextHolder.getContext()
        try {
            val jwt = mockk<Jwt>()
            // Store SSIN as a number (will lose leading zeros)
            every { jwt.claims } returns mapOf("ssin" to 12345678) // Number type

            val authentication = mockk<Authentication>()
            every { authentication.principal } returns jwt

            val securityContext = mockk<SecurityContext>()
            every { securityContext.authentication } returns authentication

            SecurityContextHolder.setContext(securityContext)

            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen)

            // Mock for the padded SSIN
            every { waveUserService.getOperatorCode("00012345678") } returns "OP123"

            // When
            val result = redirectService.getRegisRedirectUrl(requestId, languageCode)

            // Then
            // Verify that the SSIN was padded to 11 digits
            assert(result.contains("userid=00012345678"))
            verify { waveUserService.getOperatorCode("00012345678") }
        } finally {
            SecurityContextHolder.setContext(originalContext)
        }
    }

    @Test
    fun `getRegisRedirectUrl should throw IllegalArgumentException for invalid SSIN type`() = runBlocking {
        // Create a mock where SSIN is stored as an invalid type
        val originalContext = SecurityContextHolder.getContext()
        try {
            val jwt = mockk<Jwt>()
            // Store SSIN as a boolean (invalid type)
            every { jwt.claims } returns mapOf("ssin" to true)

            val authentication = mockk<Authentication>()
            every { authentication.principal } returns jwt

            val securityContext = mockk<SecurityContext>()
            every { securityContext.authentication } returns authentication

            SecurityContextHolder.setContext(securityContext)

            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen)

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                redirectService.getRegisRedirectUrl(requestId, languageCode)
            }
            assertEquals("Invalid SSIN format", exception.message)
        } finally {
            SecurityContextHolder.setContext(originalContext)
        }
    }

    @Test
    fun `getRegisRedirectUrl should handle SSIN as Long correctly`() = runBlocking {
        // Test with SSIN as Long (another Number type)
        val originalContext = SecurityContextHolder.getContext()
        try {
            val jwt = mockk<Jwt>()
            // Store SSIN as Long
            every { jwt.claims } returns mapOf("ssin" to 99999999999L) // 11 digits, no padding needed

            val authentication = mockk<Authentication>()
            every { authentication.principal } returns jwt

            val securityContext = mockk<SecurityContext>()
            every { securityContext.authentication } returns authentication

            SecurityContextHolder.setContext(securityContext)

            // Given
            val requestId = UUID.randomUUID()
            val basicInfo = createBasicInfo()
            val citizen = createCitizen()
            val languageCode = "fr"

            prepareMocks(requestId, basicInfo, citizen)

            // Mock for the SSIN (no padding needed for this one)
            every { waveUserService.getOperatorCode("99999999999") } returns "OP123"

            // When
            val result = redirectService.getRegisRedirectUrl(requestId, languageCode)

            // Then
            assert(result.contains("userid=99999999999"))
            verify { waveUserService.getOperatorCode("99999999999") }
        } finally {
            SecurityContextHolder.setContext(originalContext)
        }
    }
}