package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RoutingDecisionItemEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RoutingDecisionItemRepository

import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.RoutingDecisionPort

@Component
@Transactional
class RoutingDecisionPersistenceAdapter(
    private val routingDecisionItemRepository: RoutingDecisionItemRepository,
    private val changePersonalDataRepository: ChangePersonalDataRepository,
) : RoutingDecisionPort {

    override fun getRoutingDecisions(requestId: UUID): Set<RoutingDecisionItem> {
        val routingDecisionEntities = routingDecisionItemRepository.findByRequestId(requestId)
        return routingDecisionEntities.map { entity ->
            RoutingDecisionItem(
                type = entity.type,
                value = entity.value
            )
        }.toSet()
    }

    override fun saveRoutingDecisions(
        requestId: UUID,
        routingDecisions: Set<RoutingDecisionItem>,
        processInWave: Boolean,
    ) {
        val requestEntity = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        // Delete existing routing decisions for this request
        routingDecisionItemRepository.deleteByRequestId(requestId)
        routingDecisionItemRepository.flush()

        // Create new routing decision entities
        val routingDecisionEntities = routingDecisions.map { decision ->
            RoutingDecisionItemEntity(
                request = requestEntity,
                type = decision.type,
                value = decision.value
            )
        }

        // Save new routing decisions
        routingDecisionItemRepository.saveAll(routingDecisionEntities)

        // Update the processInWave field in the parent request entity
        requestEntity.processInWave = processInWave
    }

    override fun deleteRoutingDecisions(requestId: UUID) {
        routingDecisionItemRepository.deleteByRequestId(requestId)
    }
}