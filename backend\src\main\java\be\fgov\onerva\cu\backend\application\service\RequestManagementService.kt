package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.Annex
import be.fgov.onerva.cu.backend.application.domain.AnnexType
import be.fgov.onerva.cu.backend.application.domain.C9Info
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataUpdateCommand
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.RequestBasicInfo
import be.fgov.onerva.cu.backend.application.domain.UpdateCitizenCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.backend.application.port.`in`.AssignTaskToUserUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.CloseRequestTaskUseCase
import be.fgov.onerva.cu.backend.application.port.`in`.RequestBasicInfoUseCase
import be.fgov.onerva.cu.backend.application.port.out.C9Port
import be.fgov.onerva.cu.backend.application.port.out.CurrentUserPort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.backend.application.port.out.SnapshotPort
import be.fgov.onerva.cu.backend.application.port.out.SyncFollowUpPort
import be.fgov.onerva.cu.backend.application.port.out.UpdateCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.application.service.helpers.WaveTaskHelper
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.utils.logger

@Service
@BusinessTransaction
class RequestManagementService(
    private val persistChangePersonalDataPort: PersistChangePersonalDataPort,
    private val loadCitizenPort: LoadCitizenPort,
    private val updateCitizenPort: UpdateCitizenPort,
    private val c9Port: C9Port,
    private val currentUserPort: CurrentUserPort,
    private val waveTaskHelper: WaveTaskHelper,
    private val syncFollowUpPort: SyncFollowUpPort,
    private val snapshotPort: SnapshotPort,
) :
    RequestBasicInfoUseCase, CloseRequestTaskUseCase, AssignTaskToUserUseCase {
    private val log = logger

    /**
     * Retrieves basic information for a specific request.
     *
     * @param requestId The unique identifier of the request
     * @return The basic information of the request
     * @throws RequestIdNotFoundException if the specified request ID does not exist
     */
    @LogMethodCall
    override fun getRequestBasicInfo(requestId: UUID): RequestBasicInfo {
        val changePersonalData =
            persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        val c9Info = getOrPopulateC9Info(requestId, changePersonalData)
        val ec1Info = c9Info.ec1Id?.let { c9Port.loadEC1(it, changePersonalData.requestDate) }

        val c1Annex = if (c9Info.ec1Id != null && c9Info.ec1DisplayUrl != null) {
            Annex(
                type = AnnexType.EC1,
                url = c9Info.ec1DisplayUrl,
            )
        } else null
        val scannedDocumentsAnnex = if (c9Info.scanUrl != null) {
            Annex(
                type = AnnexType.SCANNED_DOCUMENTS,
                url = c9Info.scanUrl,
            )
        } else null

        var firstName: String?
        var lastName: String?

        if (ec1Info?.citizenInformation != null) {
            firstName = ec1Info.citizenInformation.firstName
            lastName = ec1Info.citizenInformation.lastName
        } else {
            val citizen = loadCitizenPort.getCitizenBySsin(changePersonalData.ssin)
            firstName = citizen.firstName
            lastName = citizen.lastName
        }

        val syncFollowUpStatus = syncFollowUpPort.getSyncFollowUpStatusByRequestId(requestId)

        return RequestBasicInfo(
            requestDate = changePersonalData.requestDate,
            ssin = changePersonalData.ssin,
            firstName = firstName,
            lastName = lastName,
            introductionDate = c9Info.introductionDate,
            dateValid = c9Info.dateValid,
            annexes = listOfNotNull(c1Annex, scannedDocumentsAnnex),
            decisionType = changePersonalData.decisionType,
            decisionBarema = changePersonalData.decisionBarema,
            c9Id = changePersonalData.c9id,
            pushbackStatus = syncFollowUpStatus,
            documentType = c9Info.documentType,
        )
    }

    @LogMethodCall
    override fun closeTaskForRequestAndCreateNext(requestId: UUID, taskCode: String): WaveTask? {
        val currentUserName = currentUserPort.getCurrentUsername()
        val changePersonalDataRequest =
            persistChangePersonalDataPort.getChangePersonalDataById(requestId)
                ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        val task = when (taskCode) {
            WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE -> changePersonalDataRequest.changePersonalDataCaptureWaveTask
            WaveTaskPort.VALIDATION_DATA -> changePersonalDataRequest.changePersonalDataValidateWaveTask
            else -> throw WaveTaskNotFoundException("Task not found: $taskCode for request $requestId")
        } ?: throw WaveTaskNotFoundException("Task not found: $taskCode for request $requestId")

        val request = persistChangePersonalDataPort.getChangePersonalDataByC9Id(changePersonalDataRequest.c9id)
            ?: throw RequestIdNotFoundException("Request with C9 ID ${changePersonalDataRequest.c9id} not found")

        val c9Info = getOrPopulateC9Info(requestId, request)
        val numbox = request.numbox ?: loadCitizenPort.getCitizenNumbox(request.ssin)

        val waveTask = when (taskCode) {
            WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE -> {
                validateCloseDataCaptureTask(changePersonalDataRequest)
                val taskWasClosed = waveTaskHelper.assignAndSleepDataCaptureTask(
                    requestId,
                    task.taskId,
                    currentUserName,
                )
                if (taskWasClosed) {
                    val createChangePersonalDataTaskCommand = CreateChangePersonalDataTaskCommand(
                        c9id = c9Info.c9Id,
                        c9Type = c9Info.type,
                        ssin = c9Info.ssin,
                        numbox = numbox,
                        introductionDate = c9Info.introductionDate,
                        entityCode = c9Info.entityCode,
                        dossierId = "${c9Info.sectOp}-${c9Info.opKey}",
                        paymentInstitution = c9Info.paymentInstitution,
                        requestDate = c9Info.requestDate,
                        sectOp = c9Info.sectOp,
                        dueDate = c9Info.dueDate
                    )

                    waveTaskHelper.createChangePersonalDataValidateTask(
                        requestId,
                        task.processId,
                        createChangePersonalDataTaskCommand,
                        currentUserName,
                    )
                } else {
                    null
                }
            }

            WaveTaskPort.VALIDATION_DATA -> {
                val unemploymentOffice = changePersonalDataRequest.unemploymentOffice
                    ?: throw RequestInvalidStateException("Unemployment office is not filled in for request $requestId")
                snapshotPort.makeBaremaSnapshotReadonly(requestId)
                snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.ONEM)
                snapshotPort.makeCitizenInformationSnapshotReadonly(requestId, ExternalSource.AUTHENTIC_SOURCES)

                val citizenInformation = requireNotNull(changePersonalDataRequest.citizenInformation)
                val unionContribution = requireNotNull(changePersonalDataRequest.unionContribution)
                val modeOfPayment = requireNotNull(changePersonalDataRequest.modeOfPayment)
                val correlationId = requestId.toString()
                syncFollowUpPort.persistSyncFollowUpAsPending(requestId, correlationId)

                updateCitizenPort.updateCitizenInformation(
                    requestId, UpdateCitizenCommand(
                        ssin = changePersonalDataRequest.ssin,
                        address = citizenInformation.address,
                        nationalityCode = citizenInformation.nationalityCode,
                        correlationId = correlationId,
                        userName = currentUserName,
                        requestDate = changePersonalDataRequest.requestDate,
                        unionContribution = unionContribution,
                        modeOfPayment = modeOfPayment,
                        birthDate = citizenInformation.birthDate,
                        valueDate = changePersonalDataRequest.requestDate,
                        unemploymentOffice = unemploymentOffice,
                    )
                )

                waveTaskHelper.assignAndSleepValidateDataTask(
                    requestId,
                    task.taskId,
                    currentUserName,
                )
                
                null
            }

            else -> error("Task code $taskCode is not supported - should not come here")
        }

        return waveTask
    }

    private fun validateCloseDataCaptureTask(changePersonalDataRequest: ChangePersonalDataRequest) {
        if (changePersonalDataRequest.citizenInformation == null) {
            throw RequestInvalidStateException("Citizen information is not filled in for request ${changePersonalDataRequest.id}")
        }
        if (changePersonalDataRequest.modeOfPayment == null) {
            throw RequestInvalidStateException("Mode of payment is not filled in for request ${changePersonalDataRequest.id}")
        }
    }

    @LogMethodCall
    override fun assignTaskToUser(requestId: UUID) {
        val currentUsername = currentUserPort.getCurrentUsername()
        waveTaskHelper.assignTaskToUser(requestId, currentUsername)
    }

    private fun getOrPopulateC9Info(requestId: UUID, changePersonalData: ChangePersonalDataRequest): C9Info =
        if (changePersonalData.paymentInstitution == null || changePersonalData.entityCode == null
            || changePersonalData.unemploymentOffice == null || changePersonalData.introductionDate == null
            || changePersonalData.dateValid == null || changePersonalData.operatorCode == null
        ) {
            log.warn("Payment institution is not populated for request $requestId")
            val c9Info = c9Port.loadC9(changePersonalData.c9id)
            val updateCommand = ChangePersonalDataUpdateCommand(
                paymentInstitution = changePersonalData.paymentInstitution ?: c9Info.paymentInstitution,
                entityCode = changePersonalData.entityCode ?: c9Info.entityCode,
                unemploymentOffice = changePersonalData.unemploymentOffice ?: c9Info.unemploymentOffice,
                introductionDate = changePersonalData.introductionDate ?: c9Info.introductionDate,
                dateValid = changePersonalData.dateValid ?: c9Info.dateValid,
                operatorCode = changePersonalData.operatorCode ?: c9Info.operatorCode,
                scanUrl = changePersonalData.scanUrl ?: c9Info.scanUrl,
                scanNumber = changePersonalData.scanNumber ?: c9Info.scanNumber,
                ec1Id = changePersonalData.ec1Id ?: c9Info.ec1Id,
                ec1DisplayUrl = changePersonalData.ec1DisplayUrl ?: c9Info.ec1DisplayUrl,
                documentType = changePersonalData.documentType,
                introductionType = changePersonalData.introductionType ?: c9Info.introductionType,
            )
            persistChangePersonalDataPort.updateChangePersonalDataWithC9EnvelopeData(requestId, updateCommand)
            c9Info
        } else {
            C9Info(
                c9Id = changePersonalData.c9id,
                type = changePersonalData.c9Type,
                ssin = changePersonalData.ssin,
                requestDate = changePersonalData.requestDate,
                opKey = changePersonalData.opKey,
                sectOp = changePersonalData.sectOp,
                introductionDate = changePersonalData.introductionDate,
                dateValid = changePersonalData.dateValid,
                paymentInstitution = changePersonalData.paymentInstitution,
                entityCode = changePersonalData.entityCode,
                unemploymentOffice = changePersonalData.unemploymentOffice,
                scanUrl = changePersonalData.scanUrl,
                scanNumber = changePersonalData.scanNumber,
                ec1Id = changePersonalData.ec1Id,
                ec1DisplayUrl = changePersonalData.ec1DisplayUrl,
                documentType = changePersonalData.documentType,
                introductionType = changePersonalData.introductionType,
                operatorCode = changePersonalData.operatorCode,
                dueDate = changePersonalData.dueDate,
            )
        }
}