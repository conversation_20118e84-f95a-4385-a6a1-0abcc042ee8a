{"version": 3, "sources": ["../../../../../../node_modules/@ngx-translate/core/fesm2022/ngx-translate-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\nclass TranslateLoader {}\n/**\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\n */\nclass TranslateFakeLoader extends TranslateLoader {\n  getTranslation(lang) {\n    void lang;\n    return of({});\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTranslateFakeLoader_BaseFactory;\n    return function TranslateFakeLoader_Factory(__ngFactoryType__) {\n      return (ɵTranslateFakeLoader_BaseFactory || (ɵTranslateFakeLoader_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateFakeLoader)))(__ngFactoryType__ || TranslateFakeLoader);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateFakeLoader,\n    factory: TranslateFakeLoader.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeLoader, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MissingTranslationHandler {}\n/**\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\n */\nclass FakeMissingTranslationHandler {\n  handle(params) {\n    return params.key;\n  }\n  static ɵfac = function FakeMissingTranslationHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FakeMissingTranslationHandler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FakeMissingTranslationHandler,\n    factory: FakeMissingTranslationHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FakeMissingTranslationHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Determines if two objects or two values are equivalent.\n *\n * Two objects or values are considered equivalent if at least one of the following is true:\n *\n * * Both objects or values pass `===` comparison.\n * * Both objects or values are of the same type and all of their properties are equal by\n *   comparing them with `equals`.\n *\n * @param o1 Object or value to compare.\n * @param o2 Object or value to compare.\n * @returns true if arguments are equal.\n */\nfunction equals(o1, o2) {\n  if (o1 === o2) return true;\n  if (o1 === null || o2 === null) return false;\n  if (o1 !== o1 && o2 !== o2) return true; // NaN === NaN\n  const t1 = typeof o1,\n    t2 = typeof o2;\n  let length, key, keySet;\n  if (t1 == t2 && t1 == 'object') {\n    if (Array.isArray(o1)) {\n      if (!Array.isArray(o2)) return false;\n      if ((length = o1.length) == o2.length) {\n        for (key = 0; key < length; key++) {\n          if (!equals(o1[key], o2[key])) return false;\n        }\n        return true;\n      }\n    } else {\n      if (Array.isArray(o2)) {\n        return false;\n      }\n      keySet = Object.create(null);\n      for (key in o1) {\n        if (!equals(o1[key], o2[key])) {\n          return false;\n        }\n        keySet[key] = true;\n      }\n      for (key in o2) {\n        if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n}\nfunction isDefined(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isDict(value) {\n  return isObject(value) && !isArray(value) && value !== null;\n}\nfunction isObject(value) {\n  return typeof value === 'object';\n}\nfunction isArray(value) {\n  return Array.isArray(value);\n}\nfunction isString(value) {\n  return typeof value === 'string';\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction mergeDeep(target, source) {\n  const output = Object.assign({}, target);\n  if (!isObject(target)) {\n    return mergeDeep({}, source);\n  }\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isDict(source[key])) {\n        if (key in target) {\n          output[key] = mergeDeep(target[key], source[key]);\n        } else {\n          Object.assign(output, {\n            [key]: source[key]\n          });\n        }\n      } else {\n        Object.assign(output, {\n          [key]: source[key]\n        });\n      }\n    });\n  }\n  return output;\n}\n/**\n * Gets a value from an object by composed key\n * getValue({ key1: { keyA: 'valueI' }}, 'key1.keyA') ==> 'valueI'\n * @param target\n * @param key\n */\nfunction getValue(target, key) {\n  const keys = key.split(\".\");\n  key = \"\";\n  do {\n    key += keys.shift();\n    if (isDefined(target) && isDefined(target[key]) && (isDict(target[key]) || isArray(target[key]) || !keys.length)) {\n      target = target[key];\n      key = \"\";\n    } else if (!keys.length) {\n      target = undefined;\n    } else {\n      key += \".\";\n    }\n  } while (keys.length);\n  return target;\n}\n/**\n * Gets a value from an object by composed key\n * parser.setValue({a:{b:{c: \"test\"}}}, 'a.b.c', \"test2\") ==> {a:{b:{c: \"test2\"}}}\n * @param target an object\n * @param key E.g. \"a.b.c\"\n * @param value to set\n */\nfunction setValue(target, key, value) {\n  const keys = key.split('.');\n  let current = target;\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    // If we're at the last key, set the value\n    if (i === keys.length - 1) {\n      current[key] = value;\n    } else {\n      // If the key doesn't exist or isn't an object, create an empty object\n      if (!current[key] || !isDict(current[key])) {\n        current[key] = {};\n      }\n      current = current[key];\n    }\n  }\n}\nclass TranslateParser {}\nclass TranslateDefaultParser extends TranslateParser {\n  templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n  interpolate(expr, params) {\n    if (isString(expr)) {\n      return this.interpolateString(expr, params);\n    } else if (isFunction(expr)) {\n      return this.interpolateFunction(expr, params);\n    }\n    return undefined;\n  }\n  interpolateFunction(fn, params) {\n    return fn(params);\n  }\n  interpolateString(expr, params) {\n    if (!params) {\n      return expr;\n    }\n    return expr.replace(this.templateMatcher, (substring, b) => {\n      const r = getValue(params, b);\n      return isDefined(r) ? r : substring;\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTranslateDefaultParser_BaseFactory;\n    return function TranslateDefaultParser_Factory(__ngFactoryType__) {\n      return (ɵTranslateDefaultParser_BaseFactory || (ɵTranslateDefaultParser_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateDefaultParser)))(__ngFactoryType__ || TranslateDefaultParser);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateDefaultParser,\n    factory: TranslateDefaultParser.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDefaultParser, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateCompiler {}\n/**\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n  compile(value, lang) {\n    void lang;\n    return value;\n  }\n  compileTranslations(translations, lang) {\n    void lang;\n    return translations;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵTranslateFakeCompiler_BaseFactory;\n    return function TranslateFakeCompiler_Factory(__ngFactoryType__) {\n      return (ɵTranslateFakeCompiler_BaseFactory || (ɵTranslateFakeCompiler_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateFakeCompiler)))(__ngFactoryType__ || TranslateFakeCompiler);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateFakeCompiler,\n    factory: TranslateFakeCompiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeCompiler, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateStore {\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   */\n  defaultLang;\n  /**\n   * The lang currently used\n   */\n  currentLang = this.defaultLang;\n  /**\n   * a list of translations per lang\n   */\n  translations = {};\n  /**\n   * an array of langs\n   */\n  langs = [];\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n   */\n  onTranslationChange = new EventEmitter();\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  onLangChange = new EventEmitter();\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  onDefaultLangChange = new EventEmitter();\n}\nconst ISOLATE_TRANSLATE_SERVICE = new InjectionToken('ISOLATE_TRANSLATE_SERVICE');\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\nconst makeObservable = value => {\n  return isObservable(value) ? value : of(value);\n};\nclass TranslateService {\n  store;\n  currentLoader;\n  compiler;\n  parser;\n  missingTranslationHandler;\n  useDefaultLang;\n  extend;\n  loadingTranslations;\n  pending = false;\n  _translationRequests = {};\n  lastUseLanguage = null;\n  /**\n   * An EventEmitter to listen to translation change events\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onTranslationChange() {\n    return this.store.onTranslationChange;\n  }\n  /**\n   * An EventEmitter to listen to lang change events\n   * onLangChange.subscribe((params: LangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onLangChange() {\n    return this.store.onLangChange;\n  }\n  /**\n   * An EventEmitter to listen to default lang change events\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\n     *     // do something\n     * });\n   */\n  get onDefaultLangChange() {\n    return this.store.onDefaultLangChange;\n  }\n  /**\n   * The default lang to fallback when translations are missing on the current lang\n   */\n  get defaultLang() {\n    return this.store.defaultLang;\n  }\n  set defaultLang(defaultLang) {\n    this.store.defaultLang = defaultLang;\n  }\n  /**\n   * The lang currently used\n   */\n  get currentLang() {\n    return this.store.currentLang;\n  }\n  set currentLang(currentLang) {\n    this.store.currentLang = currentLang;\n  }\n  /**\n   * an array of langs\n   */\n  get langs() {\n    return this.store.langs;\n  }\n  set langs(langs) {\n    this.store.langs = langs;\n  }\n  /**\n   * a list of translations per lang\n   */\n  get translations() {\n    return this.store.translations;\n  }\n  set translations(translations) {\n    this.store.translations = translations;\n  }\n  /**\n   *\n   * @param store an instance of the store (that is supposed to be unique)\n   * @param currentLoader An instance of the loader currently used\n   * @param compiler An instance of the compiler currently used\n   * @param parser An instance of the parser currently used\n   * @param missingTranslationHandler A handler for missing translations.\n   * @param useDefaultLang whether we should use default language translation when current language translation is missing.\n   * @param isolate whether this service should use the store or not\n   * @param extend To make a child module extend (and use) translations from parent modules.\n   * @param defaultLanguage Set the default language using configuration\n   */\n  constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n    this.store = store;\n    this.currentLoader = currentLoader;\n    this.compiler = compiler;\n    this.parser = parser;\n    this.missingTranslationHandler = missingTranslationHandler;\n    this.useDefaultLang = useDefaultLang;\n    this.extend = extend;\n    if (isolate) {\n      this.store = new TranslateStore();\n    }\n    if (defaultLanguage) {\n      this.setDefaultLang(defaultLanguage);\n    }\n  }\n  /**\n   * Sets the default language to use as a fallback\n   */\n  setDefaultLang(lang) {\n    if (lang === this.defaultLang) {\n      return;\n    }\n    const pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the defaultLang immediately\n      if (this.defaultLang == null) {\n        this.defaultLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(() => {\n        this.changeDefaultLang(lang);\n      });\n    } else {\n      // we already have this language\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Gets the default language used\n   */\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  /**\n   * Changes the lang currently used\n   */\n  use(lang) {\n    // remember the language that was called\n    // we need this with multiple fast calls to use()\n    // where translation loads might complete in random order\n    this.lastUseLanguage = lang;\n    // don't change the language if the language given is already selected\n    if (lang === this.currentLang) {\n      return of(this.translations[lang]);\n    }\n    // on init set the currentLang immediately\n    if (!this.currentLang) {\n      this.currentLang = lang;\n    }\n    const pending = this.retrieveTranslations(lang);\n    if (isObservable(pending)) {\n      pending.pipe(take(1)).subscribe(() => {\n        this.changeLang(lang);\n      });\n      return pending;\n    } else {\n      // we have this language, return an Observable\n      this.changeLang(lang);\n      return of(this.translations[lang]);\n    }\n  }\n  /**\n   * Changes the current lang\n   */\n  changeLang(lang) {\n    // received a new language file\n    // but this was not the one requested last\n    if (lang !== this.lastUseLanguage) {\n      return;\n    }\n    this.currentLang = lang;\n    this.onLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n    // if there is no default lang, use the one that we just set\n    if (this.defaultLang == null) {\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\n   * Retrieves the given translations\n   */\n  retrieveTranslations(lang) {\n    // if this language is unavailable or extend is true, ask for it\n    if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n      this._translationRequests[lang] = this._translationRequests[lang] || this.loadAndCompileTranslations(lang);\n      return this._translationRequests[lang];\n    }\n    return undefined;\n  }\n  /**\n   * Gets an object of translations for a given language with the current loader\n   * and passes it through the compiler\n   *\n   * @deprecated This function is meant for internal use. There should\n   * be no reason to use outside this service. You can plug into this\n   * functionality by using a customer TranslateLoader or TranslateCompiler.\n   * To load a new language use setDefaultLang() and/or use()\n   */\n  getTranslation(lang) {\n    return this.loadAndCompileTranslations(lang);\n  }\n  loadAndCompileTranslations(lang) {\n    this.pending = true;\n    const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n    this.loadingTranslations = loadingTranslations.pipe(map(res => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n    this.loadingTranslations.subscribe({\n      next: res => {\n        this.translations[lang] = this.extend && this.translations[lang] ? {\n          ...res,\n          ...this.translations[lang]\n        } : res;\n        this.updateLangs();\n        this.pending = false;\n      },\n      error: err => {\n        void err;\n        this.pending = false;\n      }\n    });\n    return loadingTranslations;\n  }\n  /**\n   * Manually sets an object of translations for a given language\n   * after passing it through the compiler\n   */\n  setTranslation(lang, translations, shouldMerge = false) {\n    const interpolatableTranslations = this.compiler.compileTranslations(translations, lang);\n    if ((shouldMerge || this.extend) && this.translations[lang]) {\n      this.translations[lang] = mergeDeep(this.translations[lang], interpolatableTranslations);\n    } else {\n      this.translations[lang] = interpolatableTranslations;\n    }\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Returns an array of currently available langs\n   */\n  getLangs() {\n    return this.langs;\n  }\n  /**\n   * Add available languages\n   */\n  addLangs(langs) {\n    const newLangs = langs.filter(lang => !this.langs.includes(lang));\n    if (newLangs.length > 0) {\n      this.langs = [...this.langs, ...newLangs];\n    }\n  }\n  /**\n   * Update the list of available languages\n   */\n  updateLangs() {\n    this.addLangs(Object.keys(this.translations));\n  }\n  getParsedResultForKey(translations, key, interpolateParams) {\n    let res;\n    if (translations) {\n      res = this.runInterpolation(getValue(translations, key), interpolateParams);\n    }\n    if (res === undefined && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n      res = this.runInterpolation(getValue(this.translations[this.defaultLang], key), interpolateParams);\n    }\n    if (res === undefined) {\n      const params = {\n        key,\n        translateService: this\n      };\n      if (typeof interpolateParams !== 'undefined') {\n        params.interpolateParams = interpolateParams;\n      }\n      res = this.missingTranslationHandler.handle(params);\n    }\n    return res !== undefined ? res : key;\n  }\n  runInterpolation(translations, interpolateParams) {\n    if (isArray(translations)) {\n      return translations.map(translation => this.runInterpolation(translation, interpolateParams));\n    } else if (isDict(translations)) {\n      const result = {};\n      for (const key in translations) {\n        const res = this.runInterpolation(translations[key], interpolateParams);\n        if (res !== undefined) {\n          result[key] = res;\n        }\n      }\n      return result;\n    } else {\n      return this.parser.interpolate(translations, interpolateParams);\n    }\n  }\n  /**\n   * Returns the parsed result of the translations\n   */\n  getParsedResult(translations, key, interpolateParams) {\n    // handle a bunch of keys\n    if (key instanceof Array) {\n      const result = {};\n      let observables = false;\n      for (const k of key) {\n        result[k] = this.getParsedResultForKey(translations, k, interpolateParams);\n        observables = observables || isObservable(result[k]);\n      }\n      if (!observables) {\n        return result;\n      }\n      const sources = key.map(k => makeObservable(result[k]));\n      return forkJoin(sources).pipe(map(arr => {\n        const obj = {};\n        arr.forEach((value, index) => {\n          obj[key[index]] = value;\n        });\n        return obj;\n      }));\n    }\n    return this.getParsedResultForKey(translations, key, interpolateParams);\n  }\n  /**\n   * Gets the translated value of a key (or an array of keys)\n   * @returns the translated key, or an object of translated keys\n   */\n  get(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" is required and cannot be empty`);\n    }\n    // check if we are loading a new translation to use\n    if (this.pending) {\n      return this.loadingTranslations.pipe(concatMap(res => {\n        return makeObservable(this.getParsedResult(res, key, interpolateParams));\n      }));\n    }\n    return makeObservable(this.getParsedResult(this.translations[this.currentLang], key, interpolateParams));\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the translation changes.\n   * @returns A stream of the translated key, or an object of translated keys\n   */\n  getStreamOnTranslationChange(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" is required and cannot be empty`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return makeObservable(res);\n    })));\n  }\n  /**\n   * Returns a stream of translated values of a key (or an array of keys) which updates\n   * whenever the language changes.\n   * @returns A stream of the translated key, or an object of translated keys\n   */\n  stream(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return makeObservable(res);\n    })));\n  }\n  /**\n   * Returns a translation instantly from the internal state of loaded translation.\n   * All rules regarding the current language, the preferred language of even fallback languages\n   * will be used except any promise handling.\n   */\n  instant(key, interpolateParams) {\n    if (!isDefined(key) || key.length === 0) {\n      throw new Error('Parameter \"key\" is required and cannot be empty');\n    }\n    const result = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n    if (isObservable(result)) {\n      if (Array.isArray(key)) {\n        return key.reduce((acc, currKey) => {\n          acc[currKey] = currKey;\n          return acc;\n        }, {});\n      }\n      return key;\n    }\n    return result;\n  }\n  /**\n   * Sets the translated value of a key, after compiling it\n   */\n  set(key, translation, lang = this.currentLang) {\n    setValue(this.translations[lang], key, isString(translation) ? this.compiler.compile(translation, lang) : this.compiler.compileTranslations(translation, lang));\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Changes the default lang\n   */\n  changeDefaultLang(lang) {\n    this.defaultLang = lang;\n    this.onDefaultLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\n   * Allows to reload the lang file from the file\n   */\n  reloadLang(lang) {\n    this.resetLang(lang);\n    return this.loadAndCompileTranslations(lang);\n  }\n  /**\n   * Deletes inner translation\n   */\n  resetLang(lang) {\n    delete this._translationRequests[lang];\n    delete this.translations[lang];\n  }\n  /**\n   * Returns the language code name from the browser, e.g. \"de\"\n   */\n  getBrowserLang() {\n    if (typeof window === 'undefined' || !window.navigator) {\n      return undefined;\n    }\n    const browserLang = this.getBrowserCultureLang();\n    return browserLang ? browserLang.split(/[-_]/)[0] : undefined;\n  }\n  /**\n   * Returns the culture language code name from the browser, e.g. \"de-DE\"\n   */\n  getBrowserCultureLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    return window.navigator.languages ? window.navigator.languages[0] : window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n  }\n  static ɵfac = function TranslateService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateService)(i0.ɵɵinject(TranslateStore), i0.ɵɵinject(TranslateLoader), i0.ɵɵinject(TranslateCompiler), i0.ɵɵinject(TranslateParser), i0.ɵɵinject(MissingTranslationHandler), i0.ɵɵinject(USE_DEFAULT_LANG), i0.ɵɵinject(ISOLATE_TRANSLATE_SERVICE), i0.ɵɵinject(USE_EXTEND), i0.ɵɵinject(DEFAULT_LANGUAGE));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslateService,\n    factory: TranslateService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: TranslateStore\n  }, {\n    type: TranslateLoader\n  }, {\n    type: TranslateCompiler\n  }, {\n    type: TranslateParser\n  }, {\n    type: MissingTranslationHandler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [USE_DEFAULT_LANG]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [ISOLATE_TRANSLATE_SERVICE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [USE_EXTEND]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DEFAULT_LANGUAGE]\n    }]\n  }], null);\n})();\nclass TranslateDirective {\n  translateService;\n  element;\n  _ref;\n  key;\n  lastParams;\n  currentParams;\n  onLangChangeSub;\n  onDefaultLangChangeSub;\n  onTranslationChangeSub;\n  set translate(key) {\n    if (key) {\n      this.key = key;\n      this.checkNodes();\n    }\n  }\n  set translateParams(params) {\n    if (!equals(this.currentParams, params)) {\n      this.currentParams = params;\n      this.checkNodes(true);\n    }\n  }\n  constructor(translateService, element, _ref) {\n    this.translateService = translateService;\n    this.element = element;\n    this._ref = _ref;\n    // subscribe to onTranslationChange event, in case the translations of the current lang change\n    if (!this.onTranslationChangeSub) {\n      this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe(event => {\n        if (event.lang === this.translateService.currentLang) {\n          this.checkNodes(true, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChangeSub) {\n      this.onLangChangeSub = this.translateService.onLangChange.subscribe(event => {\n        this.checkNodes(true, event.translations);\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe(event => {\n        void event;\n        this.checkNodes(true);\n      });\n    }\n  }\n  ngAfterViewChecked() {\n    this.checkNodes();\n  }\n  checkNodes(forceUpdate = false, translations) {\n    let nodes = this.element.nativeElement.childNodes;\n    // if the element is empty\n    if (!nodes.length) {\n      // we add the key as content\n      this.setContent(this.element.nativeElement, this.key);\n      nodes = this.element.nativeElement.childNodes;\n    }\n    nodes.forEach(n => {\n      const node = n;\n      if (node.nodeType === 3) {\n        // node type 3 is a text node\n        let key;\n        if (forceUpdate) {\n          node.lastKey = null;\n        }\n        if (isDefined(node.lookupKey)) {\n          key = node.lookupKey;\n        } else if (this.key) {\n          key = this.key;\n        } else {\n          const content = this.getContent(node);\n          const trimmedContent = content.trim();\n          if (trimmedContent.length) {\n            node.lookupKey = trimmedContent;\n            // we want to use the content as a key, not the translation value\n            if (content !== node.currentValue) {\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            } else if (node.originalContent) {\n              // the content seems ok, but the lang has changed\n              // the current content is the translation, not the key, use the last real content as key\n              key = node.originalContent.trim();\n            }\n          }\n        }\n        this.updateValue(key, node, translations);\n      }\n    });\n  }\n  updateValue(key, node, translations) {\n    if (key) {\n      if (node.lastKey === key && this.lastParams === this.currentParams) {\n        return;\n      }\n      this.lastParams = this.currentParams;\n      const onTranslation = res => {\n        if (res !== key || !node.lastKey) {\n          node.lastKey = key;\n        }\n        if (!node.originalContent) {\n          node.originalContent = this.getContent(node);\n        }\n        node.currentValue = isDefined(res) ? res : node.originalContent || key;\n        // we replace in the original content to preserve spaces that we might have trimmed\n        this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n        this._ref.markForCheck();\n      };\n      if (isDefined(translations)) {\n        const res = this.translateService.getParsedResult(translations, key, this.currentParams);\n        if (isObservable(res)) {\n          res.subscribe({\n            next: onTranslation\n          });\n        } else {\n          onTranslation(res);\n        }\n      } else {\n        this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n      }\n    }\n  }\n  getContent(node) {\n    return isDefined(node.textContent) ? node.textContent : node.data;\n  }\n  setContent(node, content) {\n    if (isDefined(node.textContent)) {\n      node.textContent = content;\n    } else {\n      node.data = content;\n    }\n  }\n  ngOnDestroy() {\n    if (this.onLangChangeSub) {\n      this.onLangChangeSub.unsubscribe();\n    }\n    if (this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub.unsubscribe();\n    }\n    if (this.onTranslationChangeSub) {\n      this.onTranslationChangeSub.unsubscribe();\n    }\n  }\n  static ɵfac = function TranslateDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateDirective)(i0.ɵɵdirectiveInject(TranslateService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TranslateDirective,\n    selectors: [[\"\", \"translate\", \"\"], [\"\", \"ngx-translate\", \"\"]],\n    inputs: {\n      translate: \"translate\",\n      translateParams: \"translateParams\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[translate],[ngx-translate]',\n      standalone: true\n    }]\n  }], () => [{\n    type: TranslateService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    translate: [{\n      type: Input\n    }],\n    translateParams: [{\n      type: Input\n    }]\n  });\n})();\nclass TranslatePipe {\n  translate;\n  _ref;\n  value = '';\n  lastKey = null;\n  lastParams = [];\n  onTranslationChange;\n  onLangChange;\n  onDefaultLangChange;\n  constructor(translate, _ref) {\n    this.translate = translate;\n    this._ref = _ref;\n  }\n  updateValue(key, interpolateParams, translations) {\n    const onTranslation = res => {\n      this.value = res !== undefined ? res : key;\n      this.lastKey = key;\n      this._ref.markForCheck();\n    };\n    if (translations) {\n      const res = this.translate.getParsedResult(translations, key, interpolateParams);\n      if (isObservable(res)) {\n        res.subscribe(onTranslation);\n      } else {\n        onTranslation(res);\n      }\n    }\n    this.translate.get(key, interpolateParams).subscribe(onTranslation);\n  }\n  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */\n  transform(query, ...args) {\n    if (!query || !query.length) {\n      return query;\n    }\n    // if we ask another time for the same key, return the last value\n    if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n      return this.value;\n    }\n    let interpolateParams = undefined;\n    if (isDefined(args[0]) && args.length) {\n      if (isString(args[0]) && args[0].length) {\n        // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n        // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n        const validArgs = args[0].replace(/(')?([a-zA-Z0-9_]+)(')?(\\s)?:/g, '\"$2\":').replace(/:(\\s)?(')(.*?)(')/g, ':\"$3\"');\n        try {\n          interpolateParams = JSON.parse(validArgs);\n        } catch (e) {\n          void e;\n          throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n        }\n      } else if (isDict(args[0])) {\n        interpolateParams = args[0];\n      }\n    }\n    // store the query, in case it changes\n    this.lastKey = query;\n    // store the params, in case they change\n    this.lastParams = args;\n    // set the value\n    this.updateValue(query, interpolateParams);\n    // if there is a subscription to onLangChange, clean it\n    this._dispose();\n    // subscribe to onTranslationChange event, in case the translations change\n    if (!this.onTranslationChange) {\n      this.onTranslationChange = this.translate.onTranslationChange.subscribe(event => {\n        if (this.lastKey && event.lang === this.translate.currentLang) {\n          this.lastKey = null;\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChange) {\n      this.onLangChange = this.translate.onLangChange.subscribe(event => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChange) {\n      this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams);\n        }\n      });\n    }\n    return this.value;\n  }\n  /**\n   * Clean any existing subscription to change events\n   */\n  _dispose() {\n    if (typeof this.onTranslationChange !== 'undefined') {\n      this.onTranslationChange.unsubscribe();\n      this.onTranslationChange = undefined;\n    }\n    if (typeof this.onLangChange !== 'undefined') {\n      this.onLangChange.unsubscribe();\n      this.onLangChange = undefined;\n    }\n    if (typeof this.onDefaultLangChange !== 'undefined') {\n      this.onDefaultLangChange.unsubscribe();\n      this.onDefaultLangChange = undefined;\n    }\n  }\n  ngOnDestroy() {\n    this._dispose();\n  }\n  static ɵfac = function TranslatePipe_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslatePipe)(i0.ɵɵdirectiveInject(TranslateService, 16), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"translate\",\n    type: TranslatePipe,\n    pure: false\n  });\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslatePipe,\n    factory: TranslatePipe.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslatePipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'translate',\n      standalone: true,\n      pure: false // required to update the value when the promise is resolved\n    }]\n  }], () => [{\n    type: TranslateService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nfunction _(key) {\n  return key;\n}\nconst provideTranslateService = (config = {}) => {\n  return makeEnvironmentProviders([config.loader || {\n    provide: TranslateLoader,\n    useClass: TranslateFakeLoader\n  }, config.compiler || {\n    provide: TranslateCompiler,\n    useClass: TranslateFakeCompiler\n  }, config.parser || {\n    provide: TranslateParser,\n    useClass: TranslateDefaultParser\n  }, config.missingTranslationHandler || {\n    provide: MissingTranslationHandler,\n    useClass: FakeMissingTranslationHandler\n  }, TranslateStore, {\n    provide: ISOLATE_TRANSLATE_SERVICE,\n    useValue: config.isolate\n  }, {\n    provide: USE_DEFAULT_LANG,\n    useValue: config.useDefaultLang\n  }, {\n    provide: USE_EXTEND,\n    useValue: config.extend\n  }, {\n    provide: DEFAULT_LANGUAGE,\n    useValue: config.defaultLanguage\n  }, TranslateService]);\n};\nclass TranslateModule {\n  /**\n   * Use this method in your root module to provide the TranslateService\n   */\n  static forRoot(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, TranslateStore, {\n        provide: ISOLATE_TRANSLATE_SERVICE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  /**\n   * Use this method in your other (non-root) modules to import the directive/pipe\n   */\n  static forChild(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, {\n        provide: ISOLATE_TRANSLATE_SERVICE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  static ɵfac = function TranslateModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslateModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslateModule,\n    imports: [TranslatePipe, TranslateDirective],\n    exports: [TranslatePipe, TranslateDirective]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateModule, [{\n    type: NgModule,\n    args: [{\n      imports: [TranslatePipe, TranslateDirective],\n      exports: [TranslatePipe, TranslateDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, ISOLATE_TRANSLATE_SERVICE, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, _, equals, getValue, isArray, isDefined, isDict, isFunction, isObject, isString, mergeDeep, provideTranslateService, setValue };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,kBAAN,MAAsB;AAAC;AAIvB,IAAM,sBAAN,MAAM,6BAA4B,gBAAgB;AAAA,EAChD,eAAe,MAAM;AACnB,SAAK;AACL,WAAO,GAAG,CAAC,CAAC;AAAA,EACd;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAgC;AAAC;AAIjC,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO,QAAQ;AACb,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,OAAO,SAAS,sCAAsC,mBAAmB;AAC9E,WAAO,KAAK,qBAAqB,gCAA+B;AAAA,EAClE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,+BAA8B;AAAA,EACzC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAgBH,SAAS,OAAO,IAAI,IAAI;AACtB,MAAI,OAAO,GAAI,QAAO;AACtB,MAAI,OAAO,QAAQ,OAAO,KAAM,QAAO;AACvC,MAAI,OAAO,MAAM,OAAO,GAAI,QAAO;AACnC,QAAM,KAAK,OAAO,IAChB,KAAK,OAAO;AACd,MAAI,QAAQ,KAAK;AACjB,MAAI,MAAM,MAAM,MAAM,UAAU;AAC9B,QAAI,MAAM,QAAQ,EAAE,GAAG;AACrB,UAAI,CAAC,MAAM,QAAQ,EAAE,EAAG,QAAO;AAC/B,WAAK,SAAS,GAAG,WAAW,GAAG,QAAQ;AACrC,aAAK,MAAM,GAAG,MAAM,QAAQ,OAAO;AACjC,cAAI,CAAC,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAG,QAAO;AAAA,QACxC;AACA,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,UAAI,MAAM,QAAQ,EAAE,GAAG;AACrB,eAAO;AAAA,MACT;AACA,eAAS,uBAAO,OAAO,IAAI;AAC3B,WAAK,OAAO,IAAI;AACd,YAAI,CAAC,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG;AAC7B,iBAAO;AAAA,QACT;AACA,eAAO,GAAG,IAAI;AAAA,MAChB;AACA,WAAK,OAAO,IAAI;AACd,YAAI,EAAE,OAAO,WAAW,OAAO,GAAG,GAAG,MAAM,aAAa;AACtD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,UAAU,eAAe,UAAU;AACnD;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,SAAS,KAAK,KAAK,CAAC,QAAQ,KAAK,KAAK,UAAU;AACzD;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,QAAQ,KAAK;AAC5B;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,UAAU,QAAQ,QAAQ;AACjC,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM;AACvC,MAAI,CAAC,SAAS,MAAM,GAAG;AACrB,WAAO,UAAU,CAAC,GAAG,MAAM;AAAA,EAC7B;AACA,MAAI,SAAS,MAAM,KAAK,SAAS,MAAM,GAAG;AACxC,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,UAAI,OAAO,OAAO,GAAG,CAAC,GAAG;AACvB,YAAI,OAAO,QAAQ;AACjB,iBAAO,GAAG,IAAI,UAAU,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,QAClD,OAAO;AACL,iBAAO,OAAO,QAAQ;AAAA,YACpB,CAAC,GAAG,GAAG,OAAO,GAAG;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,eAAO,OAAO,QAAQ;AAAA,UACpB,CAAC,GAAG,GAAG,OAAO,GAAG;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAOA,SAAS,SAAS,QAAQ,KAAK;AAC7B,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,QAAM;AACN,KAAG;AACD,WAAO,KAAK,MAAM;AAClB,QAAI,UAAU,MAAM,KAAK,UAAU,OAAO,GAAG,CAAC,MAAM,OAAO,OAAO,GAAG,CAAC,KAAK,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,SAAS;AAChH,eAAS,OAAO,GAAG;AACnB,YAAM;AAAA,IACR,WAAW,CAAC,KAAK,QAAQ;AACvB,eAAS;AAAA,IACX,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,SAAS,KAAK;AACd,SAAO;AACT;AAQA,SAAS,SAAS,QAAQ,KAAK,OAAO;AACpC,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAMA,OAAM,KAAK,CAAC;AAElB,QAAI,MAAM,KAAK,SAAS,GAAG;AACzB,cAAQA,IAAG,IAAI;AAAA,IACjB,OAAO;AAEL,UAAI,CAAC,QAAQA,IAAG,KAAK,CAAC,OAAO,QAAQA,IAAG,CAAC,GAAG;AAC1C,gBAAQA,IAAG,IAAI,CAAC;AAAA,MAClB;AACA,gBAAU,QAAQA,IAAG;AAAA,IACvB;AAAA,EACF;AACF;AACA,IAAM,kBAAN,MAAsB;AAAC;AACvB,IAAM,yBAAN,MAAM,gCAA+B,gBAAgB;AAAA,EACnD,kBAAkB;AAAA,EAClB,YAAY,MAAM,QAAQ;AACxB,QAAI,SAAS,IAAI,GAAG;AAClB,aAAO,KAAK,kBAAkB,MAAM,MAAM;AAAA,IAC5C,WAAW,WAAW,IAAI,GAAG;AAC3B,aAAO,KAAK,oBAAoB,MAAM,MAAM;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,IAAI,QAAQ;AAC9B,WAAO,GAAG,MAAM;AAAA,EAClB;AAAA,EACA,kBAAkB,MAAM,QAAQ;AAC9B,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,KAAK,iBAAiB,CAAC,WAAW,MAAM;AAC1D,YAAM,IAAI,SAAS,QAAQ,CAAC;AAC5B,aAAO,UAAU,CAAC,IAAI,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,+BAA+B,mBAAmB;AAChE,cAAQ,wCAAwC,sCAAyC,sBAAsB,uBAAsB,IAAI,qBAAqB,uBAAsB;AAAA,IACtL;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAwB;AAAC;AAIzB,IAAM,wBAAN,MAAM,+BAA8B,kBAAkB;AAAA,EACpD,QAAQ,OAAO,MAAM;AACnB,SAAK;AACL,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,cAAc,MAAM;AACtC,SAAK;AACL,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,8BAA8B,mBAAmB;AAC/D,cAAQ,uCAAuC,qCAAwC,sBAAsB,sBAAqB,IAAI,qBAAqB,sBAAqB;AAAA,IAClL;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAInB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,KAAK;AAAA;AAAA;AAAA;AAAA,EAInB,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhB,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,sBAAsB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvC,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhC,sBAAsB,IAAI,aAAa;AACzC;AACA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAChF,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,aAAa,IAAI,eAAe,YAAY;AAClD,IAAM,iBAAiB,WAAS;AAC9B,SAAO,aAAa,KAAK,IAAI,QAAQ,GAAG,KAAK;AAC/C;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,uBAAuB,CAAC;AAAA,EACxB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,IAAI,sBAAsB;AACxB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eAAe;AACjB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,sBAAsB;AACxB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,cAAc;AAChB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,MAAM,cAAc;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,cAAc;AAChB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,MAAM,cAAc;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACV,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,MAAM,QAAQ;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACjB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,aAAa,cAAc;AAC7B,SAAK,MAAM,eAAe;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,OAAO,eAAe,UAAU,QAAQ,2BAA2B,iBAAiB,MAAM,UAAU,OAAO,SAAS,OAAO,iBAAiB;AACtJ,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,4BAA4B;AACjC,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,QAAI,SAAS;AACX,WAAK,QAAQ,IAAI,eAAe;AAAA,IAClC;AACA,QAAI,iBAAiB;AACnB,WAAK,eAAe,eAAe;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,MAAM;AACnB,QAAI,SAAS,KAAK,aAAa;AAC7B;AAAA,IACF;AACA,UAAM,UAAU,KAAK,qBAAqB,IAAI;AAC9C,QAAI,OAAO,YAAY,aAAa;AAElC,UAAI,KAAK,eAAe,MAAM;AAC5B,aAAK,cAAc;AAAA,MACrB;AACA,cAAQ,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACpC,aAAK,kBAAkB,IAAI;AAAA,MAC7B,CAAC;AAAA,IACH,OAAO;AAEL,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM;AAIR,SAAK,kBAAkB;AAEvB,QAAI,SAAS,KAAK,aAAa;AAC7B,aAAO,GAAG,KAAK,aAAa,IAAI,CAAC;AAAA,IACnC;AAEA,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc;AAAA,IACrB;AACA,UAAM,UAAU,KAAK,qBAAqB,IAAI;AAC9C,QAAI,aAAa,OAAO,GAAG;AACzB,cAAQ,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACpC,aAAK,WAAW,IAAI;AAAA,MACtB,CAAC;AACD,aAAO;AAAA,IACT,OAAO;AAEL,WAAK,WAAW,IAAI;AACpB,aAAO,GAAG,KAAK,aAAa,IAAI,CAAC;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AAGf,QAAI,SAAS,KAAK,iBAAiB;AACjC;AAAA,IACF;AACA,SAAK,cAAc;AACnB,SAAK,aAAa,KAAK;AAAA,MACrB;AAAA,MACA,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAED,QAAI,KAAK,eAAe,MAAM;AAC5B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,MAAM;AAEzB,QAAI,OAAO,KAAK,aAAa,IAAI,MAAM,eAAe,KAAK,QAAQ;AACjE,WAAK,qBAAqB,IAAI,IAAI,KAAK,qBAAqB,IAAI,KAAK,KAAK,2BAA2B,IAAI;AACzG,aAAO,KAAK,qBAAqB,IAAI;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,MAAM;AACnB,WAAO,KAAK,2BAA2B,IAAI;AAAA,EAC7C;AAAA,EACA,2BAA2B,MAAM;AAC/B,SAAK,UAAU;AACf,UAAM,sBAAsB,KAAK,cAAc,eAAe,IAAI,EAAE,KAAK,YAAY,CAAC,GAAG,KAAK,CAAC,CAAC;AAChG,SAAK,sBAAsB,oBAAoB,KAAK,IAAI,SAAO,KAAK,SAAS,oBAAoB,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,GAAG,KAAK,CAAC,CAAC;AACrI,SAAK,oBAAoB,UAAU;AAAA,MACjC,MAAM,SAAO;AACX,aAAK,aAAa,IAAI,IAAI,KAAK,UAAU,KAAK,aAAa,IAAI,IAAI,kCAC9D,MACA,KAAK,aAAa,IAAI,KACvB;AACJ,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,OAAO,SAAO;AACZ,aAAK;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM,cAAc,cAAc,OAAO;AACtD,UAAM,6BAA6B,KAAK,SAAS,oBAAoB,cAAc,IAAI;AACvF,SAAK,eAAe,KAAK,WAAW,KAAK,aAAa,IAAI,GAAG;AAC3D,WAAK,aAAa,IAAI,IAAI,UAAU,KAAK,aAAa,IAAI,GAAG,0BAA0B;AAAA,IACzF,OAAO;AACL,WAAK,aAAa,IAAI,IAAI;AAAA,IAC5B;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,KAAK;AAAA,MAC5B;AAAA,MACA,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO;AACd,UAAM,WAAW,MAAM,OAAO,UAAQ,CAAC,KAAK,MAAM,SAAS,IAAI,CAAC;AAChE,QAAI,SAAS,SAAS,GAAG;AACvB,WAAK,QAAQ,CAAC,GAAG,KAAK,OAAO,GAAG,QAAQ;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,SAAS,OAAO,KAAK,KAAK,YAAY,CAAC;AAAA,EAC9C;AAAA,EACA,sBAAsB,cAAc,KAAK,mBAAmB;AAC1D,QAAI;AACJ,QAAI,cAAc;AAChB,YAAM,KAAK,iBAAiB,SAAS,cAAc,GAAG,GAAG,iBAAiB;AAAA,IAC5E;AACA,QAAI,QAAQ,UAAa,KAAK,eAAe,QAAQ,KAAK,gBAAgB,KAAK,eAAe,KAAK,gBAAgB;AACjH,YAAM,KAAK,iBAAiB,SAAS,KAAK,aAAa,KAAK,WAAW,GAAG,GAAG,GAAG,iBAAiB;AAAA,IACnG;AACA,QAAI,QAAQ,QAAW;AACrB,YAAM,SAAS;AAAA,QACb;AAAA,QACA,kBAAkB;AAAA,MACpB;AACA,UAAI,OAAO,sBAAsB,aAAa;AAC5C,eAAO,oBAAoB;AAAA,MAC7B;AACA,YAAM,KAAK,0BAA0B,OAAO,MAAM;AAAA,IACpD;AACA,WAAO,QAAQ,SAAY,MAAM;AAAA,EACnC;AAAA,EACA,iBAAiB,cAAc,mBAAmB;AAChD,QAAI,QAAQ,YAAY,GAAG;AACzB,aAAO,aAAa,IAAI,iBAAe,KAAK,iBAAiB,aAAa,iBAAiB,CAAC;AAAA,IAC9F,WAAW,OAAO,YAAY,GAAG;AAC/B,YAAM,SAAS,CAAC;AAChB,iBAAW,OAAO,cAAc;AAC9B,cAAM,MAAM,KAAK,iBAAiB,aAAa,GAAG,GAAG,iBAAiB;AACtE,YAAI,QAAQ,QAAW;AACrB,iBAAO,GAAG,IAAI;AAAA,QAChB;AAAA,MACF;AACA,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,OAAO,YAAY,cAAc,iBAAiB;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,cAAc,KAAK,mBAAmB;AAEpD,QAAI,eAAe,OAAO;AACxB,YAAM,SAAS,CAAC;AAChB,UAAI,cAAc;AAClB,iBAAW,KAAK,KAAK;AACnB,eAAO,CAAC,IAAI,KAAK,sBAAsB,cAAc,GAAG,iBAAiB;AACzE,sBAAc,eAAe,aAAa,OAAO,CAAC,CAAC;AAAA,MACrD;AACA,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,MACT;AACA,YAAM,UAAU,IAAI,IAAI,OAAK,eAAe,OAAO,CAAC,CAAC,CAAC;AACtD,aAAO,SAAS,OAAO,EAAE,KAAK,IAAI,SAAO;AACvC,cAAM,MAAM,CAAC;AACb,YAAI,QAAQ,CAAC,OAAO,UAAU;AAC5B,cAAI,IAAI,KAAK,CAAC,IAAI;AAAA,QACpB,CAAC;AACD,eAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,KAAK,sBAAsB,cAAc,KAAK,iBAAiB;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK,mBAAmB;AAC1B,QAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,QAAQ;AAClC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AAEA,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,oBAAoB,KAAK,UAAU,SAAO;AACpD,eAAO,eAAe,KAAK,gBAAgB,KAAK,KAAK,iBAAiB,CAAC;AAAA,MACzE,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,eAAe,KAAK,gBAAgB,KAAK,aAAa,KAAK,WAAW,GAAG,KAAK,iBAAiB,CAAC;AAAA,EACzG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,KAAK,mBAAmB;AACnD,QAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,QAAQ;AAClC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AACA,WAAO,OAAO,MAAM,MAAM,KAAK,IAAI,KAAK,iBAAiB,CAAC,GAAG,KAAK,oBAAoB,KAAK,UAAU,WAAS;AAC5G,YAAM,MAAM,KAAK,gBAAgB,MAAM,cAAc,KAAK,iBAAiB;AAC3E,aAAO,eAAe,GAAG;AAAA,IAC3B,CAAC,CAAC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK,mBAAmB;AAC7B,QAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,QAAQ;AAClC,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,WAAO,OAAO,MAAM,MAAM,KAAK,IAAI,KAAK,iBAAiB,CAAC,GAAG,KAAK,aAAa,KAAK,UAAU,WAAS;AACrG,YAAM,MAAM,KAAK,gBAAgB,MAAM,cAAc,KAAK,iBAAiB;AAC3E,aAAO,eAAe,GAAG;AAAA,IAC3B,CAAC,CAAC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,KAAK,mBAAmB;AAC9B,QAAI,CAAC,UAAU,GAAG,KAAK,IAAI,WAAW,GAAG;AACvC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AACA,UAAM,SAAS,KAAK,gBAAgB,KAAK,aAAa,KAAK,WAAW,GAAG,KAAK,iBAAiB;AAC/F,QAAI,aAAa,MAAM,GAAG;AACxB,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAO,IAAI,OAAO,CAAC,KAAK,YAAY;AAClC,cAAI,OAAO,IAAI;AACf,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAAA,MACP;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK,aAAa,OAAO,KAAK,aAAa;AAC7C,aAAS,KAAK,aAAa,IAAI,GAAG,KAAK,SAAS,WAAW,IAAI,KAAK,SAAS,QAAQ,aAAa,IAAI,IAAI,KAAK,SAAS,oBAAoB,aAAa,IAAI,CAAC;AAC9J,SAAK,YAAY;AACjB,SAAK,oBAAoB,KAAK;AAAA,MAC5B;AAAA,MACA,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,MAAM;AACtB,SAAK,cAAc;AACnB,SAAK,oBAAoB,KAAK;AAAA,MAC5B;AAAA,MACA,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACf,SAAK,UAAU,IAAI;AACnB,WAAO,KAAK,2BAA2B,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM;AACd,WAAO,KAAK,qBAAqB,IAAI;AACrC,WAAO,KAAK,aAAa,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,OAAO,WAAW,eAAe,CAAC,OAAO,WAAW;AACtD,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,sBAAsB;AAC/C,WAAO,cAAc,YAAY,MAAM,MAAM,EAAE,CAAC,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACtB,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,cAAc,aAAa;AAC5E,aAAO;AAAA,IACT;AACA,WAAO,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU,CAAC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,mBAAmB,OAAO,UAAU;AAAA,EACxJ;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAqB,SAAS,cAAc,GAAM,SAAS,eAAe,GAAM,SAAS,iBAAiB,GAAM,SAAS,eAAe,GAAM,SAAS,yBAAyB,GAAM,SAAS,gBAAgB,GAAM,SAAS,yBAAyB,GAAM,SAAS,UAAU,GAAM,SAAS,gBAAgB,CAAC;AAAA,EACnV;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,UAAU,KAAK;AACjB,QAAI,KAAK;AACP,WAAK,MAAM;AACX,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,IAAI,gBAAgB,QAAQ;AAC1B,QAAI,CAAC,OAAO,KAAK,eAAe,MAAM,GAAG;AACvC,WAAK,gBAAgB;AACrB,WAAK,WAAW,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EACA,YAAY,kBAAkB,SAAS,MAAM;AAC3C,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,QAAI,CAAC,KAAK,wBAAwB;AAChC,WAAK,yBAAyB,KAAK,iBAAiB,oBAAoB,UAAU,WAAS;AACzF,YAAI,MAAM,SAAS,KAAK,iBAAiB,aAAa;AACpD,eAAK,WAAW,MAAM,MAAM,YAAY;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,KAAK,iBAAiB,aAAa,UAAU,WAAS;AAC3E,aAAK,WAAW,MAAM,MAAM,YAAY;AAAA,MAC1C,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,wBAAwB;AAChC,WAAK,yBAAyB,KAAK,iBAAiB,oBAAoB,UAAU,WAAS;AACzF,aAAK;AACL,aAAK,WAAW,IAAI;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW,cAAc,OAAO,cAAc;AAC5C,QAAI,QAAQ,KAAK,QAAQ,cAAc;AAEvC,QAAI,CAAC,MAAM,QAAQ;AAEjB,WAAK,WAAW,KAAK,QAAQ,eAAe,KAAK,GAAG;AACpD,cAAQ,KAAK,QAAQ,cAAc;AAAA,IACrC;AACA,UAAM,QAAQ,OAAK;AACjB,YAAM,OAAO;AACb,UAAI,KAAK,aAAa,GAAG;AAEvB,YAAI;AACJ,YAAI,aAAa;AACf,eAAK,UAAU;AAAA,QACjB;AACA,YAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,gBAAM,KAAK;AAAA,QACb,WAAW,KAAK,KAAK;AACnB,gBAAM,KAAK;AAAA,QACb,OAAO;AACL,gBAAM,UAAU,KAAK,WAAW,IAAI;AACpC,gBAAM,iBAAiB,QAAQ,KAAK;AACpC,cAAI,eAAe,QAAQ;AACzB,iBAAK,YAAY;AAEjB,gBAAI,YAAY,KAAK,cAAc;AACjC,oBAAM;AAEN,mBAAK,kBAAkB,WAAW,KAAK;AAAA,YACzC,WAAW,KAAK,iBAAiB;AAG/B,oBAAM,KAAK,gBAAgB,KAAK;AAAA,YAClC;AAAA,UACF;AAAA,QACF;AACA,aAAK,YAAY,KAAK,MAAM,YAAY;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,KAAK,MAAM,cAAc;AACnC,QAAI,KAAK;AACP,UAAI,KAAK,YAAY,OAAO,KAAK,eAAe,KAAK,eAAe;AAClE;AAAA,MACF;AACA,WAAK,aAAa,KAAK;AACvB,YAAM,gBAAgB,SAAO;AAC3B,YAAI,QAAQ,OAAO,CAAC,KAAK,SAAS;AAChC,eAAK,UAAU;AAAA,QACjB;AACA,YAAI,CAAC,KAAK,iBAAiB;AACzB,eAAK,kBAAkB,KAAK,WAAW,IAAI;AAAA,QAC7C;AACA,aAAK,eAAe,UAAU,GAAG,IAAI,MAAM,KAAK,mBAAmB;AAEnE,aAAK,WAAW,MAAM,KAAK,MAAM,KAAK,eAAe,KAAK,gBAAgB,QAAQ,KAAK,KAAK,YAAY,CAAC;AACzG,aAAK,KAAK,aAAa;AAAA,MACzB;AACA,UAAI,UAAU,YAAY,GAAG;AAC3B,cAAM,MAAM,KAAK,iBAAiB,gBAAgB,cAAc,KAAK,KAAK,aAAa;AACvF,YAAI,aAAa,GAAG,GAAG;AACrB,cAAI,UAAU;AAAA,YACZ,MAAM;AAAA,UACR,CAAC;AAAA,QACH,OAAO;AACL,wBAAc,GAAG;AAAA,QACnB;AAAA,MACF,OAAO;AACL,aAAK,iBAAiB,IAAI,KAAK,KAAK,aAAa,EAAE,UAAU,aAAa;AAAA,MAC5E;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,MAAM;AACf,WAAO,UAAU,KAAK,WAAW,IAAI,KAAK,cAAc,KAAK;AAAA,EAC/D;AAAA,EACA,WAAW,MAAM,SAAS;AACxB,QAAI,UAAU,KAAK,WAAW,GAAG;AAC/B,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,YAAY;AAAA,IACnC;AACA,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB,YAAY;AAAA,IAC1C;AACA,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAuB,kBAAkB,gBAAgB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,EAC9K;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IAC5D,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,iBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa,CAAC;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,WAAW,MAAM;AAC3B,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,YAAY,KAAK,mBAAmB,cAAc;AAChD,UAAM,gBAAgB,SAAO;AAC3B,WAAK,QAAQ,QAAQ,SAAY,MAAM;AACvC,WAAK,UAAU;AACf,WAAK,KAAK,aAAa;AAAA,IACzB;AACA,QAAI,cAAc;AAChB,YAAM,MAAM,KAAK,UAAU,gBAAgB,cAAc,KAAK,iBAAiB;AAC/E,UAAI,aAAa,GAAG,GAAG;AACrB,YAAI,UAAU,aAAa;AAAA,MAC7B,OAAO;AACL,sBAAc,GAAG;AAAA,MACnB;AAAA,IACF;AACA,SAAK,UAAU,IAAI,KAAK,iBAAiB,EAAE,UAAU,aAAa;AAAA,EACpE;AAAA;AAAA,EAEA,UAAU,UAAU,MAAM;AACxB,QAAI,CAAC,SAAS,CAAC,MAAM,QAAQ;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,OAAO,KAAK,OAAO,KAAK,OAAO,MAAM,KAAK,UAAU,GAAG;AAChE,aAAO,KAAK;AAAA,IACd;AACA,QAAI,oBAAoB;AACxB,QAAI,UAAU,KAAK,CAAC,CAAC,KAAK,KAAK,QAAQ;AACrC,UAAI,SAAS,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,QAAQ;AAGvC,cAAM,YAAY,KAAK,CAAC,EAAE,QAAQ,kCAAkC,OAAO,EAAE,QAAQ,sBAAsB,OAAO;AAClH,YAAI;AACF,8BAAoB,KAAK,MAAM,SAAS;AAAA,QAC1C,SAAS,GAAG;AACV,eAAK;AACL,gBAAM,IAAI,YAAY,wEAAwE,KAAK,CAAC,CAAC,EAAE;AAAA,QACzG;AAAA,MACF,WAAW,OAAO,KAAK,CAAC,CAAC,GAAG;AAC1B,4BAAoB,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,SAAK,UAAU;AAEf,SAAK,aAAa;AAElB,SAAK,YAAY,OAAO,iBAAiB;AAEzC,SAAK,SAAS;AAEd,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB,KAAK,UAAU,oBAAoB,UAAU,WAAS;AAC/E,YAAI,KAAK,WAAW,MAAM,SAAS,KAAK,UAAU,aAAa;AAC7D,eAAK,UAAU;AACf,eAAK,YAAY,OAAO,mBAAmB,MAAM,YAAY;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,eAAe,KAAK,UAAU,aAAa,UAAU,WAAS;AACjE,YAAI,KAAK,SAAS;AAChB,eAAK,UAAU;AACf,eAAK,YAAY,OAAO,mBAAmB,MAAM,YAAY;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB,KAAK,UAAU,oBAAoB,UAAU,MAAM;AAC5E,YAAI,KAAK,SAAS;AAChB,eAAK,UAAU;AACf,eAAK,YAAY,OAAO,iBAAiB;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,QAAI,OAAO,KAAK,wBAAwB,aAAa;AACnD,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,OAAO,KAAK,iBAAiB,aAAa;AAC5C,WAAK,aAAa,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,OAAO,KAAK,wBAAwB,aAAa;AACnD,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAkB,kBAAkB,kBAAkB,EAAE,GAAM,kBAAqB,mBAAmB,EAAE,CAAC;AAAA,EAC5I;AAAA,EACA,OAAO,QAA0B,aAAa;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,MAAM;AAAA;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,EAAE,KAAK;AACd,SAAO;AACT;AACA,IAAM,0BAA0B,CAAC,SAAS,CAAC,MAAM;AAC/C,SAAO,yBAAyB,CAAC,OAAO,UAAU;AAAA,IAChD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,OAAO,YAAY;AAAA,IACpB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,OAAO,UAAU;AAAA,IAClB,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,OAAO,6BAA6B;AAAA,IACrC,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,gBAAgB;AAAA,IACjB,SAAS;AAAA,IACT,UAAU,OAAO;AAAA,EACnB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,OAAO;AAAA,EACnB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,OAAO;AAAA,EACnB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,OAAO;AAAA,EACnB,GAAG,gBAAgB,CAAC;AACtB;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,OAAO,UAAU;AAAA,QAC3B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,OAAO,YAAY;AAAA,QACpB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,OAAO,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,OAAO,6BAA6B;AAAA,QACrC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,gBAAgB;AAAA,QACjB,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG,gBAAgB;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,SAAS,CAAC,GAAG;AAC3B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,OAAO,UAAU;AAAA,QAC3B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,OAAO,YAAY;AAAA,QACpB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,OAAO,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG,OAAO,6BAA6B;AAAA,QACrC,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,GAAG,gBAAgB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,kBAAkB;AAAA,IAC3C,SAAS,CAAC,eAAe,kBAAkB;AAAA,EAC7C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,kBAAkB;AAAA,MAC3C,SAAS,CAAC,eAAe,kBAAkB;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["key"]}