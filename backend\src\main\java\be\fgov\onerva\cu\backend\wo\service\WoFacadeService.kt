package be.fgov.onerva.cu.backend.wo.service

import java.time.LocalDate
import be.fgov.onerva.cu.backend.wo.dto.WoMetadataDTO
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO

/**
 * A Facade service for the Wo API Facade to simplify interface with the WO Api.
 * The use cases will be task creation, task close, task assignment.
 */
interface WoFacadeService {
    /**
     * Create a task in the WO API for a given process. The process can be null to create a new process.
     *
     * @param processId                The id of the process or null to create a new process.
     * @param typeTaskCode             The code of the task type.
     * @param numbox                   The numbox of the citizen associated with task.
     * @param assignee                 The assignee of the task.
     * @param dueDate                  The due date of the tasks.
     * @param woProcessMetadataDTOList The metadata of the process.
     * @param woTaskMetadataDTOList    The metadata of the task.
     * @return The created task as a [WoTaskDTO] containing basic information about the created task (processId,
     * taskId).
     */
    fun createTask(
        processId: Long?,
        typeTaskCode: String,
        numbox: Int?,
        assignee: String,
        dueDate: LocalDate?,
        woProcessMetadataDTOList: List<WoMetadataDTO>,
        woTaskMetadataDTOList: List<WoMetadataDTO>
    ): WoTaskDTO

    /**
     * Check if a task can be closed. A task can be closed if it is not already in a closed state. The process
     * should not be in the closed state neither.
     *
     * @param taskId The id of the task to check
     * @return True if the task can be closed, false otherwise.
     */
    fun checkTaskCanBeUpdated(taskId: Long): Boolean

    fun awakeTask(taskId: Long, reason: String)
    /**
     * Assign a task to a user specified by the username parameter.
     *
     * @param taskId The id of the task to assign
     * @param user   The username of the user to assign the task to
     */
    fun assignTaskToUser(taskId: Long, user: String)

    /**
     * Update the metadata of a process.
     *
     * @param processId         The id of the process
     * @param woMetadataDTOList The list of metadata to update
     */
    fun updateProcessData(processId: Long, woMetadataDTOList: List<WoMetadataDTO>)

    fun patchProcessData(processId: Long, taskId: Long, woMetadataDTOList: List<WoMetadataDTO>)

    /**
     * Close a task and the associated process.
     *
     * @param taskId The id of the task to close
     */
    fun closeTaskAndProcess(taskId: Long)

    /**
     * Close a task.
     *
     * @param taskId The id of the task to closed
     */
    fun closeTask(taskId: Long)

    fun sleepTask(taskId: Long, reason: String)

    /**
     * Close a process.
     *
     * @param processId The id of the process to close
     */
    fun closeProcess(processId: Long)
}
