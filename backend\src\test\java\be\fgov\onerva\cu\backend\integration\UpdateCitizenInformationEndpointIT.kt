package be.fgov.onerva.cu.backend.integration

import java.sql.Date
import java.time.LocalDate
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.CuBaseIntegration
import be.fgov.onerva.cu.backend.lookup.NationalityDTO
import be.fgov.onerva.cu.backend.security.Roles
import be.fgov.onerva.cu.backend.security.WithMockedJwtToken
import be.fgov.onerva.cu.rest.priv.model.Address
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every

@Sql(scripts = [
    "classpath:/cleanup.sql",
    "classpath:/data-change-personal-data.sql"
])
class UpdateCitizenInformationEndpointIT : CuBaseIntegration() {
    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    private val requestId = "F1DA3F30-10A5-4124-AA5E-2D4E46A09B15"
    private val c9Id = 12345L
    private val ssin = "18031307065"

    @BeforeEach
    fun setup() {
        // Setup nationality lookup mocks
        setupNationalityLookup(1, "150", "Belgique", "België", "1")
        setupNationalityLookup(2, "111", "France", "Frankrijk", "4")
        every { lookupService?.lookupNationality("999") } returns emptyList()
    }

    private fun setupNationalityLookup(id: Int, code: String, descFr: String, descNl: String, onemCountryCode: String) {
        every { lookupService?.lookupNationality(code) } returns listOf(
            NationalityDTO(
                lookupId = id,
                code = code,
                descFr = descFr,
                descNl = descNl,
                onemCountryCode = onemCountryCode,
            )
        )
    }

    data class TestCase(
        val description: String,
        val request: UpdateCitizenInformationRequest,
        val expectedStatus: Int,
        val shouldValidateDatabase: Boolean = false
    )

    companion object {
        @JvmStatic
        fun testCases(): Stream<TestCase> = Stream.of(
            // Valid case
            TestCase(
                description = "Valid request with all fields properly filled",
                request = UpdateCitizenInformationRequest().apply {
                    birthDate = LocalDate.of(2006, 10, 12)
                    nationalityCode = 111
                    address = Address().apply {
                        street = "Main Street"
                        houseNumber = "123"
                        boxNumber = "A"
                        zipCode = "1000"
                        city = "Brussels"
                        countryCode = 150
                        validFrom = LocalDate.of(2022, 1, 1)
                    }
                },
                expectedStatus = 204,
                shouldValidateDatabase = true
            ),
            // Invalid nationality
            TestCase(
                description = "Invalid nationality code",
                request = UpdateCitizenInformationRequest().apply {
                    birthDate = LocalDate.of(2006, 10, 12)
                    nationalityCode = 999
                    address = Address().apply {
                        street = "Main Street"
                        houseNumber = "123"
                        boxNumber = "A"
                        zipCode = "1000"
                        city = "Brussels"
                        countryCode = 150
                        validFrom = LocalDate.of(2022, 1, 1)
                    }
                },
                expectedStatus = 400
            ),
            // Blank required fields
            TestCase(
                description = "Blank street in address",
                request = UpdateCitizenInformationRequest().apply {
                    birthDate = LocalDate.of(2006, 10, 12)
                    nationalityCode = 111
                    address = Address().apply {
                        street = ""
                        houseNumber = "123"
                        boxNumber = "A"
                        zipCode = "1000"
                        city = "Brussels"
                        countryCode = 150
                        validFrom = LocalDate.of(2022, 1, 1)
                    }
                },
                expectedStatus = 400
            ),
            // Future birth date
            TestCase(
                description = "Future birth date",
                request = UpdateCitizenInformationRequest().apply {
                    birthDate = LocalDate.now().plusYears(1)
                    nationalityCode = 111
                    address = Address().apply {
                        street = "Main Street"
                        houseNumber = "123"
                        boxNumber = "A"
                        zipCode = "1000"
                        city = "Brussels"
                        countryCode = 150
                        validFrom = LocalDate.of(2022, 1, 1)
                    }
                },
                expectedStatus = 400
            )
            // Add more test cases here
        )
    }

    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("testCases")
    @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
    fun `test update citizen information with various inputs`(testCase: TestCase) {
        // When
        val result = mockMvc.perform(
            put("/api/requests/$requestId/citizen-information")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testCase.request))
        )
            .andExpect(status().`is`(testCase.expectedStatus))
            .andReturn()

        // Then
        if (testCase.shouldValidateDatabase) {
            validateDatabaseEntries(testCase.request)
        }
    }

    private fun validateDatabaseEntries(request: UpdateCitizenInformationRequest) {
        val requestMap = jdbcTemplate.queryForMap(
            "SELECT * FROM request WHERE c9_id = ?",
            c9Id
        )
        assertThat(requestMap)
            .containsEntry("ssin", ssin)
            .containsEntry("c9_id", c9Id)

        val citizenInformationMap = jdbcTemplate.queryForMap(
            """
            SELECT citizen_information.*
            FROM citizen_information 
            INNER JOIN request on citizen_information.request_id = request.id
            WHERE c9_id = ?   
            """,
            c9Id
        )

        with(request) {
            assertThat(citizenInformationMap)
                .containsEntry("id", "231F1D46-6EC0-4DFC-AFD2-76A1C37435F8")
                .containsEntry("birth_date", Date.valueOf(birthDate))
                .containsEntry("nationality_code", nationalityCode)
                .containsEntry("street", address.street)
                .containsEntry("box_number", address.boxNumber)
                .containsEntry("house_number", address.houseNumber)
                .containsEntry("zip_code", address.zipCode)
                .containsEntry("city", address.city)
                .containsEntry("country_code", address.countryCode)
                .containsEntry("update_status", "EDITED")
        }
    }
}