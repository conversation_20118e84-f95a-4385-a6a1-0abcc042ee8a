package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainSnapshot
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.BaremaSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotAddress
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotModeOfPayment
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotUnionContribution
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.BaremaSnapshotRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationSnapshotRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.domain.Snapshot
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class SnapshotAdapterTest {

    @MockK
    lateinit var citizenInformationSnapshotRepository: CitizenInformationSnapshotRepository

    @MockK
    lateinit var requestRepository: RequestRepository

    @MockK
    lateinit var baremaSnapshotRepository: BaremaSnapshotRepository

    @InjectMockKs
    lateinit var snapshotAdapter: SnapshotAdapter

    @Test
    fun `saveCitizenInformationSnapshot creates new snapshot when none exists`() {
        // Given
        val requestId = UUID.randomUUID()
        val request = mockRequestEntity()
        val citizenInfo = mockCitizenInfoWithAddress()
        val source = ExternalSource.ONEM
        val snapshotEntitySlot = slot<CitizenInformationSnapshotEntity>()

        every { requestRepository.findByIdOrNull(requestId) } returns request
        every { citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source) } returns null
        every { citizenInformationSnapshotRepository.save(capture(snapshotEntitySlot)) } answers { snapshotEntitySlot.captured }

        // When
        snapshotAdapter.saveCitizenInformationSnapshot(requestId, source, citizenInfo)

        // Then
        with(snapshotEntitySlot.captured) {
            assertThat(firstName).isEqualTo("John")
            assertThat(lastName).isEqualTo("Doe")
            assertThat(nationalityCode).isEqualTo(111)
            assertThat(numbox).isEqualTo(123)
            assertThat(address.street).isEqualTo("Main Street")
            assertThat(address.houseNumber).isEqualTo("42")
            assertThat(address.zipCode).isEqualTo("1000")
            assertThat(address.city).isEqualTo("Brussels")
            assertThat(modeOfPayment.iban).isEqualTo("BE123")
            assertThat(externalSource).isEqualTo(source)
            assertThat(readonly).isFalse()
        }
    }

    @Test
    fun `saveCitizenInformationSnapshot updates existing snapshot`() {
        // Given
        val requestId = UUID.randomUUID()
        val request = mockRequestEntity()
        val existingSnapshot = mockExistingSnapshot(request)
        val updatedCitizenInfo = mockCitizenInfoWithAddress()
        val source = ExternalSource.ONEM

        every { requestRepository.findByIdOrNull(requestId) } returns request
        every {
            citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(
                requestId,
                source
            )
        } returns existingSnapshot

        // When
        snapshotAdapter.saveCitizenInformationSnapshot(requestId, source, updatedCitizenInfo)

        // Then
        with(existingSnapshot) {
            assertThat(firstName).isEqualTo("John")
            assertThat(lastName).isEqualTo("Doe")
            assertThat(nationalityCode).isEqualTo(111)
            assertThat(numbox).isEqualTo(123)
            assertThat(address.street).isEqualTo("Main Street")
            assertThat(address.zipCode).isEqualTo("1000")
            assertThat(modeOfPayment.iban).isEqualTo("BE123")
        }
    }

    @Test
    fun `saveCitizenInformationSnapshot throws when request not found`() {
        // Given
        val requestId = UUID.randomUUID()
        val citizenInfo = mockCitizenInfoWithAddress()
        val source = ExternalSource.ONEM

        every { requestRepository.findByIdOrNull(requestId) } returns null

        // When/Then
        assertThatThrownBy { snapshotAdapter.saveCitizenInformationSnapshot(requestId, source, citizenInfo) }
            .isInstanceOf(RequestIdNotFoundException::class.java)
            .hasMessage("Request with id $requestId not found")
    }

    @Nested
    inner class GetBaremaSnapshot {

        @Test
        fun `getBaremaSnapshot returns NotFound when no snapshot exists`() {
            // Given
            val requestId = UUID.randomUUID()
            every { baremaSnapshotRepository.findByRequestId(requestId) } returns null

            // When
            val result = snapshotAdapter.getBaremaSnapshot(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result).isEqualTo(Snapshot.NotFound)

            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
        }

        @Test
        fun `getBaremaSnapshot returns snapshot when it exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val baremaSnapshotEntity = BaremaSnapshotEntity(
                request = request,
                found = true,
                barema = "A12",
                article = "59",
                readonly = false
            )

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns baremaSnapshotEntity

            // When
            val result = snapshotAdapter.getBaremaSnapshot(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.Found::class.java)

            val foundSnapshot = result as Snapshot.Found<Barema>
            assertThat(foundSnapshot.value.barema).isEqualTo("A12")
            assertThat(foundSnapshot.value.article).isEqualTo("59")
            assertThat(foundSnapshot.readonly).isFalse()

            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
        }

        @Test
        fun `getBaremaSnapshot returns readonly snapshot correctly`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val baremaSnapshotEntity = BaremaSnapshotEntity(
                request = request,
                found = true,
                barema = "B34",
                article = "42",
                readonly = true
            )

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns baremaSnapshotEntity

            // When
            val result = snapshotAdapter.getBaremaSnapshot(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.Found::class.java)

            val foundSnapshot = result as Snapshot.Found<Barema>
            assertThat(foundSnapshot.value.barema).isEqualTo("B34")
            assertThat(foundSnapshot.value.article).isEqualTo("42")
            assertThat(foundSnapshot.readonly).isTrue()

            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
        }

        @Test
        fun `getBaremaSnapshot handles snapshot with no article`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val baremaSnapshotEntity = BaremaSnapshotEntity(
                request = request,
                found = true,
                barema = "C56",
                article = null,
                readonly = false
            )

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns baremaSnapshotEntity

            // When
            val result = snapshotAdapter.getBaremaSnapshot(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result).isInstanceOf(Snapshot.Found::class.java)

            val foundSnapshot = result as Snapshot.Found<Barema>
            assertThat(foundSnapshot.value.barema).isEqualTo("C56")
            assertThat(foundSnapshot.value.article).isNull()
            assertThat(foundSnapshot.readonly).isFalse()

            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
        }
    }

    @Nested
    inner class SaveBaremaSnapshot {

        @Test
        fun `saveBaremaSnapshot creates new entity when none exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val barema = Barema(barema = "A12", article = "59")
            val baremaEntitySlot = slot<BaremaSnapshotEntity>()

            every { requestRepository.findByIdOrNull(requestId) } returns request
            every { baremaSnapshotRepository.findByRequestId(requestId) } returns null
            every { baremaSnapshotRepository.save(capture(baremaEntitySlot)) } answers { baremaEntitySlot.captured }

            // When
            snapshotAdapter.saveBaremaSnapshot(requestId, barema)

            // Then
            verify(exactly = 1) {
                requestRepository.findByIdOrNull(requestId)
                baremaSnapshotRepository.findByRequestId(requestId)
                baremaSnapshotRepository.save(any())
            }

            with(baremaEntitySlot.captured) {
                assertThat(this.request).isEqualTo(request)
                assertThat(this.found).isTrue()
                assertThat(this.barema).isEqualTo("A12")
                assertThat(this.article).isEqualTo("59")
                assertThat(this.readonly).isFalse()
            }
        }

        @Test
        fun `saveBaremaSnapshot updates existing entity`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val barema = Barema(barema = "B34", article = "42")
            val existingEntity = BaremaSnapshotEntity(
                request = request,
                found = false,
                barema = "OldBarema",
                article = "OldArticle",
                readonly = false
            )

            every { requestRepository.findByIdOrNull(requestId) } returns request
            every { baremaSnapshotRepository.findByRequestId(requestId) } returns existingEntity

            // When
            snapshotAdapter.saveBaremaSnapshot(requestId, barema)

            // Then
            verify(exactly = 1) {
                requestRepository.findByIdOrNull(requestId)
                baremaSnapshotRepository.findByRequestId(requestId)
            }
            verify(exactly = 0) { baremaSnapshotRepository.save(any()) }

            with(existingEntity) {
                assertThat(this.found).isTrue()
                assertThat(this.barema).isEqualTo("B34")
                assertThat(this.article).isEqualTo("42")
                assertThat(this.readonly).isFalse()
            }
        }

        @Test
        fun `saveBaremaSnapshot handles null barema`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val baremaEntitySlot = slot<BaremaSnapshotEntity>()

            every { requestRepository.findByIdOrNull(requestId) } returns request
            every { baremaSnapshotRepository.findByRequestId(requestId) } returns null
            every { baremaSnapshotRepository.save(capture(baremaEntitySlot)) } answers { baremaEntitySlot.captured }

            // When
            snapshotAdapter.saveBaremaSnapshot(requestId, null)

            // Then
            verify(exactly = 1) {
                requestRepository.findByIdOrNull(requestId)
                baremaSnapshotRepository.findByRequestId(requestId)
                baremaSnapshotRepository.save(any())
            }

            with(baremaEntitySlot.captured) {
                assertThat(this.request).isEqualTo(request)
                assertThat(this.found).isFalse()
                assertThat(this.barema).isNull()
                assertThat(this.article).isNull()
                assertThat(this.readonly).isFalse()
            }
        }

        @Test
        fun `saveBaremaSnapshot updates existing entity to null barema`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val existingEntity = BaremaSnapshotEntity(
                request = request,
                found = true,
                barema = "OldBarema",
                article = "OldArticle",
                readonly = false
            )

            every { requestRepository.findByIdOrNull(requestId) } returns request
            every { baremaSnapshotRepository.findByRequestId(requestId) } returns existingEntity

            // When
            snapshotAdapter.saveBaremaSnapshot(requestId, null)

            // Then
            verify(exactly = 1) {
                requestRepository.findByIdOrNull(requestId)
                baremaSnapshotRepository.findByRequestId(requestId)
            }
            verify(exactly = 0) { baremaSnapshotRepository.save(any()) }

            with(existingEntity) {
                assertThat(this.found).isFalse()
                assertThat(this.barema).isNull()
                assertThat(this.article).isNull()
                assertThat(this.readonly).isFalse()
            }
        }

        @Test
        fun `saveBaremaSnapshot throws exception when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val barema = Barema(barema = "A12", article = "59")

            every { requestRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { snapshotAdapter.saveBaremaSnapshot(requestId, barema) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")

            verify(exactly = 1) { requestRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) {
                baremaSnapshotRepository.findByRequestId(any())
                baremaSnapshotRepository.save(any())
            }
        }

        @Test
        fun `saveBaremaSnapshot creates entity with null article when barema has null article`() {
            // Given
            val requestId = UUID.randomUUID()
            val request = mockk<RequestEntity>()
            val barema = Barema(barema = "C56", article = null)
            val baremaEntitySlot = slot<BaremaSnapshotEntity>()

            every { requestRepository.findByIdOrNull(requestId) } returns request
            every { baremaSnapshotRepository.findByRequestId(requestId) } returns null
            every { baremaSnapshotRepository.save(capture(baremaEntitySlot)) } answers { baremaEntitySlot.captured }

            // When
            snapshotAdapter.saveBaremaSnapshot(requestId, barema)

            // Then
            verify(exactly = 1) {
                requestRepository.findByIdOrNull(requestId)
                baremaSnapshotRepository.findByRequestId(requestId)
                baremaSnapshotRepository.save(any())
            }

            with(baremaEntitySlot.captured) {
                assertThat(this.request).isEqualTo(request)
                assertThat(this.found).isTrue()
                assertThat(this.barema).isEqualTo("C56")
                assertThat(this.article).isNull()
                assertThat(this.readonly).isFalse()
            }
        }
    }

    @Nested
    inner class MakeCitizenInformationSnapshotReadonly {

        @Test
        fun `makeCitizenInformationSnapshotReadonly sets readonly to true when snapshot exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val source = ExternalSource.ONEM
            val snapshotEntity = CitizenInformationSnapshotEntity(
                request = mockRequestEntity(),
                firstName = "John",
                lastName = "Doe",
                nationalityCode = 111,
                numbox = 123,
                birthDate = LocalDate.of(1990, 1, 1),
                externalSource = source,
                readonly = false,
                address = SnapshotAddress(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = null,
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    valueDate = LocalDate.of(2022, 1, 1)
                ),
                modeOfPayment = SnapshotModeOfPayment(
                    iban = "BE123",
                    bic = null,
                    foreignAccount = false,
                    ownBankAccount = true,
                    valueDate = null,
                    otherPersonName = null,
                    paymentMode = 1,
                ),
                unionContribution = SnapshotUnionContribution(
                    authorized = true,
                    effectiveDate = LocalDate.of(2022, 1, 1),
                )

            )

            every {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            } returns snapshotEntity

            // When
            snapshotAdapter.makeCitizenInformationSnapshotReadonly(requestId, source)

            // Then
            verify(exactly = 1) {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            }
            assertThat(snapshotEntity.readonly).isTrue()
        }

        @Test
        fun `makeCitizenInformationSnapshotReadonly does nothing when snapshot does not exist`() {
            // Given
            val requestId = UUID.randomUUID()
            val source = ExternalSource.ONEM

            every {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            } returns null

            // When
            snapshotAdapter.makeCitizenInformationSnapshotReadonly(requestId, source)

            // Then
            verify(exactly = 1) {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            }
            // No exception should be thrown
        }

        @ParameterizedTest
        @EnumSource(ExternalSource::class)
        fun `makeCitizenInformationSnapshotReadonly works with different sources`(source: ExternalSource) {
            // Given
            val requestId = UUID.randomUUID()
            val snapshotEntity = CitizenInformationSnapshotEntity(
                request = mockRequestEntity(),
                firstName = "John",
                lastName = "Doe",
                nationalityCode = 111,
                numbox = 123,
                birthDate = LocalDate.of(1990, 1, 1),
                externalSource = source,
                readonly = false,
                address = SnapshotAddress(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = null,
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    valueDate = LocalDate.of(2022, 1, 1)
                ),
                modeOfPayment = SnapshotModeOfPayment(
                    iban = "BE123",
                    bic = null,
                    foreignAccount = false,
                    ownBankAccount = true,
                    valueDate = null,
                    otherPersonName = null,
                    paymentMode = 1,
                ),
                unionContribution = SnapshotUnionContribution(
                    authorized = true,
                    effectiveDate = LocalDate.of(2022, 1, 1),
                )
            )

            every {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            } returns snapshotEntity

            // When
            snapshotAdapter.makeCitizenInformationSnapshotReadonly(requestId, source)

            // Then
            verify(exactly = 1) {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            }
            assertThat(snapshotEntity.readonly).isTrue()
        }

        @Test
        fun `makeCitizenInformationSnapshotReadonly keeps readonly true if already set`() {
            // Given
            val requestId = UUID.randomUUID()
            val source = ExternalSource.ONEM
            val snapshotEntity = CitizenInformationSnapshotEntity(
                request = mockRequestEntity(),
                firstName = "John",
                lastName = "Doe",
                nationalityCode = 111,
                numbox = 123,
                birthDate = LocalDate.of(1990, 1, 1),
                externalSource = source,
                readonly = true, // Already set to true
                address = SnapshotAddress(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = null,
                    zipCode = "1000",
                    city = "Brussels",
                    countryCode = 150,
                    valueDate = LocalDate.of(2022, 1, 1)
                ),
                modeOfPayment = SnapshotModeOfPayment(
                    iban = "BE123",
                    bic = null,
                    foreignAccount = false,
                    ownBankAccount = true,
                    valueDate = null,
                    otherPersonName = null,
                    paymentMode = 1,
                ),
                unionContribution = SnapshotUnionContribution(
                    authorized = true,
                    effectiveDate = LocalDate.of(2022, 1, 1),
                )
            )

            every {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            } returns snapshotEntity

            // When
            snapshotAdapter.makeCitizenInformationSnapshotReadonly(requestId, source)

            // Then
            verify(exactly = 1) {
                citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
            }
            assertThat(snapshotEntity.readonly).isTrue()
        }
    }

    @Nested
    inner class MakeBaremaSnapshotReadonly {

        @Test
        fun `makeBaremaSnapshotReadonly sets readonly to true when snapshot exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val baremaSnapshotEntity = BaremaSnapshotEntity(
                request = mockRequestEntity(),
                found = true,
                barema = "A12",
                article = "59",
                readonly = false
            )

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns baremaSnapshotEntity

            // When
            snapshotAdapter.makeBaremaSnapshotReadonly(requestId)

            // Then
            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
            assertThat(baremaSnapshotEntity.readonly).isTrue()
        }

        @Test
        fun `makeBaremaSnapshotReadonly does nothing when snapshot does not exist`() {
            // Given
            val requestId = UUID.randomUUID()

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns null

            // When
            snapshotAdapter.makeBaremaSnapshotReadonly(requestId)

            // Then
            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
            // No exception should be thrown
        }

        @Test
        fun `makeBaremaSnapshotReadonly keeps readonly true if already set`() {
            // Given
            val requestId = UUID.randomUUID()
            val baremaSnapshotEntity = BaremaSnapshotEntity(
                request = mockRequestEntity(),
                found = true,
                barema = "A12",
                article = "59",
                readonly = true // Already set to true
            )

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns baremaSnapshotEntity

            // When
            snapshotAdapter.makeBaremaSnapshotReadonly(requestId)

            // Then
            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
            assertThat(baremaSnapshotEntity.readonly).isTrue()
        }

        @Test
        fun `makeBaremaSnapshotReadonly works with found=false snapshot`() {
            // Given
            val requestId = UUID.randomUUID()
            val baremaSnapshotEntity = BaremaSnapshotEntity(
                request = mockRequestEntity(),
                found = false,
                barema = null,
                article = null,
                readonly = false
            )

            every { baremaSnapshotRepository.findByRequestId(requestId) } returns baremaSnapshotEntity

            // When
            snapshotAdapter.makeBaremaSnapshotReadonly(requestId)

            // Then
            verify(exactly = 1) { baremaSnapshotRepository.findByRequestId(requestId) }
            assertThat(baremaSnapshotEntity.readonly).isTrue()
        }
    }
    
    private fun mockRequestEntity() = mockk<RequestEntity> {
        every { id } returns UUID.randomUUID()
        every { requestDate } returns LocalDate.now()
    }

    private fun mockCitizenInfoWithAddress() = HistoricalCitizenSnapshot(
        firstName = "John",
        lastName = "Doe",
        numbox = 123,
        nationalityCode = 111,
        birthDate = null,
        address = AddressNullable(
            street = "Main Street",
            houseNumber = "42",
            boxNumber = null,
            zipCode = "1000",
            city = "Brussels",
            countryCode = 150,
            valueDate = LocalDate.of(2022, 1, 1)
        ),
        iban = "BE123",
        bic = null,
        otherPersonName = null,
        bankAccountValueDate = LocalDate.of(2022, 1, 1),
        paymentMode = 1,
        authorized = true,
        effectiveDate = LocalDate.of(2022,1,1)
    )

    private fun mockExistingSnapshot(request: RequestEntity) = CitizenInformationSnapshotEntity(
        request = request,
        firstName = "Old Name",
        lastName = "Old Last",
        nationalityCode = 111,
        numbox = 456,
        birthDate = LocalDate.of(1990, 1, 1),
        externalSource = ExternalSource.ONEM,
        readonly = false,
        address = SnapshotAddress(
            street = "Old Street",
            houseNumber = "1",
            boxNumber = null,
            zipCode = "2000",
            city = "Old City",
            countryCode = 150,
            valueDate = LocalDate.of(2022, 1, 1),
        ),
        modeOfPayment = SnapshotModeOfPayment(
            iban = "FR456",
            bic = null,
            foreignAccount = false,
            ownBankAccount = true,
            valueDate = null,
            otherPersonName = null,
            paymentMode = 1,
        ),
        unionContribution = SnapshotUnionContribution(
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1),
        )
    )
}