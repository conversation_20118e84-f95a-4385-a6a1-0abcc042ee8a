afterEach(() => {
    jest.clearAllMocks();
});import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';
import { DataLoaderWrapperComponent } from './data-loader-wrapper.component';
import { LoadingService } from '../../services/loading.service';
import { OnemRvaCDNService } from '@onemrvapublic/design-system/shared';
import { FormUtilsService } from '../../services/form-utils.service';

describe('DataLoaderWrapperComponent', () => {
    let component: DataLoaderWrapperComponent;
    let fixture: ComponentFixture<DataLoaderWrapperComponent>;
    let loadingSubject: BehaviorSubject<boolean>;

    beforeEach(async () => {
        loadingSubject = new BehaviorSubject<boolean>(false);

        // Mock FormUtilsService static method
        FormUtilsService.getWaveProcessUrl = jest.fn().mockReturnValue('mock-url');

        await TestBed.configureTestingModule({
            imports: [
                DataLoaderWrapperComponent,
                HttpClientTestingModule,
                TranslateModule.forRoot()
            ],
            providers: [
                {
                    provide: LoadingService,
                    useValue: { loading$: loadingSubject.asObservable() }
                },
                {
                    provide: OnemRvaCDNService,
                    useValue: { getImg: jest.fn().mockReturnValue('mock-url') }
                }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(DataLoaderWrapperComponent);
        component = fixture.componentInstance;

        // Set required input
        component.error = { hasError: false, message: '' };
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should get loading$ from LoadingService', () => {
        expect(component.loading$).toBeDefined();
    });

    it('should accept error input', () => {
        const error = { hasError: true, message: 'Test error' };
        component.error = error;
        fixture.detectChanges();

        expect(component.error).toBe(error);
    });

    it('should accept task input', () => {
        const task = { id: 1, name: 'Test Task' };
        component.task = task;
        fixture.detectChanges();

        expect(component.task).toBe(task);
    });

    it('should handle loading state changes', (done) => {
        component.loading$.subscribe(loading => {
            expect(loading).toBe(false);
            done();
        });
    });

    it('should update loading state when service emits', (done) => {
        loadingSubject.next(true);

        component.loading$.subscribe(loading => {
            expect(loading).toBe(true);
            done();
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });
});