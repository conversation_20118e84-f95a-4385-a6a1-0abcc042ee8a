<div [ngClass]="{'large-dialog': dialogSize === 'large', 'medium-dialog': dialogSize === 'medium'}">
    <h2 mat-dialog-title class="dialog-title">
      <span class="icon-container" [ngClass]="dialogType">
          <mat-icon *ngIf="dialogType === 'warn'" color="warn">warning</mat-icon>
          <mat-icon *ngIf="dialogType === 'success'" color="accent">check_circle</mat-icon>
          <mat-icon *ngIf="dialogType === 'error'" color="warn">error</mat-icon>
      </span>
        <span [ngClass]="dialogType === 'warn' ? 'warning' : dialogType === 'success' ? 'success' : dialogType === 'error' ? 'error' : ''">{{ title }}</span>
        <span matDialogClose class="close-dialog"><mat-icon>close</mat-icon></span>
    </h2>

    <mat-dialog-content>
        <div [innerHTML]="content"></div>
    </mat-dialog-content>

    <mat-dialog-actions>
        <button mat-button color="primary" aria-label="Basic" id="cancelDialogButton" data-cy="cancelDialogButton" (click)="onSecondaryAction()">{{ secondaryActionText }}</button>
        <button mat-flat-button color="accent" (click)="onPrimaryAction()" cdkFocusInitial >{{ primaryActionText }}</button>
    </mat-dialog-actions>
</div>


