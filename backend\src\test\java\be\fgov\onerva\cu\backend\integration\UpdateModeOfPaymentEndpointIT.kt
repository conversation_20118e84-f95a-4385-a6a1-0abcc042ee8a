package be.fgov.onerva.cu.backend.integration

import java.time.LocalDate
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.CuBaseIntegration
import be.fgov.onerva.cu.backend.security.Roles
import be.fgov.onerva.cu.backend.security.WithMockedJwtToken
import be.fgov.onerva.cu.rest.priv.model.UpdateModeOfPaymentRequest
import com.fasterxml.jackson.databind.ObjectMapper

@Sql(scripts = [
    "classpath:/cleanup.sql",
    "classpath:/data-change-personal-data.sql"
])
class UpdateModeOfPaymentEndpointIT : CuBaseIntegration() {
    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    private val requestId = "F1DA3F30-10A5-4124-AA5E-2D4E46A09B15"

    data class TestCase(
        val description: String,
        val request: UpdateModeOfPaymentRequest,
        val expectedStatus: Int,
        val shouldValidateDatabase: Boolean = false
    )

    companion object {
        @JvmStatic
        fun testCases(): Stream<TestCase> = Stream.of(
            // Valid Belgian SEPA account
            TestCase(
                description = "Valid Belgian SEPA account",
                request = UpdateModeOfPaymentRequest().apply {
                    iban = "****************"
                    validFrom = LocalDate.of(2025, 1, 1)
                },
                expectedStatus = 204,
                shouldValidateDatabase = true
            ),
            // Valid foreign account
            TestCase(
                description = "Valid foreign account with IBAN and BIC",
                request = UpdateModeOfPaymentRequest().apply {
                    iban = "***************************"
                    bic = "SOGEFRPP"
                    validFrom = LocalDate.of(2025, 1, 1)
                },
                expectedStatus = 204,
                shouldValidateDatabase = true
            ),
            // Valid account for other person
            TestCase(
                description = "Valid account for other person",
                request = UpdateModeOfPaymentRequest().apply {
                    iban = "****************"
                    otherPersonName = "John Doe"
                    validFrom = LocalDate.of(2025, 1, 1)
                },
                expectedStatus = 204,
                shouldValidateDatabase = true
            ),
            // Invalid IBAN format
            TestCase(
                description = "Invalid IBAN format",
                request = UpdateModeOfPaymentRequest().apply {
                    iban = "BE685390075470" // Invalid IBAN
                    validFrom = LocalDate.of(2025, 1, 1)
                },
                expectedStatus = 400
            ),
            // Invalid BIC format
            TestCase(
                description = "Invalid BIC format",
                request = UpdateModeOfPaymentRequest().apply {
                    iban = "***************************"
                    bic = "INVALID" // Invalid BIC
                    validFrom = LocalDate.of(2025, 1, 1)
                },
                expectedStatus = 400
            ),
            // Missing required fields for foreign account
            TestCase(
                description = "Missing BIC for foreign account",
                request = UpdateModeOfPaymentRequest().apply {
                    iban = "***************************"
                    validFrom = LocalDate.of(2025, 1, 1)
                    // Missing BIC
                },
                expectedStatus = 400
            ),
            // No payment method specified
            TestCase(
                description = "No payment method specified",
                request = UpdateModeOfPaymentRequest().apply {
                    validFrom = LocalDate.of(2025, 1, 1)
                    // Empty request
                },
                expectedStatus = 400
            )
        )
    }

    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("testCases")
    @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
    fun `test update mode of payment with various inputs`(testCase: TestCase) {
        // When
        val result = mockMvc.perform(
            put("/api/requests/$requestId/mode-of-payment")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testCase.request))
        )
            .andExpect(status().`is`(testCase.expectedStatus))
            .andReturn()

        // Then
        if (testCase.shouldValidateDatabase) {
            validateDatabaseEntries(testCase.request)
        }
    }

    private fun validateDatabaseEntries(request: UpdateModeOfPaymentRequest) {
        val modeOfPaymentMap = jdbcTemplate.queryForMap(
            """
            SELECT mode_of_payment.*
            FROM mode_of_payment 
            INNER JOIN request on mode_of_payment.request_id = request.id
            WHERE request.id = ?   
            """,
            requestId
        )

        with(request) {
            when {
                iban != null -> {
                    assertThat(modeOfPaymentMap)
                        .containsEntry("iban", iban)
                }

                iban != null -> {
                    assertThat(modeOfPaymentMap)
                        .containsEntry("iban", iban)
                        .containsEntry("bic", bic)
                }
            }

            otherPersonName?.let {
                assertThat(modeOfPaymentMap)
                    .containsEntry("other_person_name", it)
            }
        }
    }
}