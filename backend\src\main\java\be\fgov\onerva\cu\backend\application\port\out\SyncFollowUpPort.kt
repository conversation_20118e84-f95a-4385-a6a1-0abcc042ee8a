package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus

/**
 * Output port interface for managing synchronization follow-up operations.
 *
 * This port provides methods to track the status of synchronization operations
 * between the system and external services, allowing the application to monitor
 * and manage the state of these operations.
 */
interface SyncFollowUpPort {
    /**
     * Persists a synchronization follow-up record in a pending state.
     *
     * @param requestId The unique identifier of the request being synchronized
     * @param correlationId The correlation identifier used to track this specific synchronization operation
     * @throws RequestIdNotFoundException if the request is not found
     */
    fun persistSyncFollowUpAsPending(requestId: UUID, correlationId: String)

    /**
     * Updates a synchronization follow-up record to indicate successful completion.
     *
     * @param correlationId The correlation identifier of the synchronization operation to update
     * @param success Whether the synchronization operation was successful or not
     * @param errorCode The error code to set in case of failure (only used if success is false)
     * @throws SyncFollowUpNotFoundException if the synchronization operation is not found
     */
    fun updateSyncFollowUp(correlationId: String, success: Boolean, errorCode: Int)

    /**
     * Retrieves the status of a synchronization follow-up record by its correlation ID.
     *
     * @param correlationId The correlation identifier of the synchronization operation to check
     * @return The status of the synchronization operation
     */
    fun getSyncFollowUpStatusByRequestId(requestId: UUID): SyncFollowUpStatus?
}