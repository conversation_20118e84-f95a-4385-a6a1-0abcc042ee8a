<div class="onemrva-theme">

    <lib-data-loader-wrapper
            [error]="loadingError()"
            [task]="task">

    <div *ngIf="cdfForm()">
        <!-- feature flagged-->
        <button mat-stroked-button
                (click)="reopenTask()"
                *ngIf="isFormClosedOrWaiting() && isReopenTaskFeatureFlagEnabled()"
                data-cy="reopenTask"
                color="primary"
                style="float: right; margin-bottom: 16px;">
            <mat-icon>restart_alt</mat-icon>
            {{ 'CU_DATA_CAPTURE.BUTTONS.REOPEN_TASK' | translate }}
        </button>

            <lib-cu-task-status-message
                    [status]="status" [decisionType]="citizenData()?.decisionType"
                    [taskNr]="1"
                    [task]="task"
                    [decisionBarema]="citizenData()?.decisionBarema"
                    [nextTaskDescription]="'CU_DATA_CAPTURE.NEXT_TASK.DESCRIPTION'"
                    [nextTaskAction]="'CU_DATA_CAPTURE.NEXT_TASK.ACTION'"
                    [pushbackStatus]="citizenData()?.pushbackStatus">
            </lib-cu-task-status-message>

            <lib-cu-c9-annexes [language]="language" [annexes]="citizenData()?.annexes">
            </lib-cu-c9-annexes>


            <lib-cu-cdf
                    [requestBasicInfoResponse]="citizenData()!"
                    [citizenInformation]="citizenInformation()!"
                    [paymentData]="paymentData()!"
                    [unionData]="unionData()!"
                    [language]="language"
                    [taskStatus]="status"
                    [task]="task"
                    [cdfForm]="cdfForm()"
            ></lib-cu-cdf>

            <div class="actions">
                <button mat-button
                        id="saveAsDraftButton"
                        [hidden]="isFormClosedOrWaiting()"
                        (click)="save()"
                        [disabled]="cdfForm().invalid"
                        color="primary"
                        aria-label="Basic"
                        data-cy="saveAsDraftRequestButton"
                >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SAVE_AS_DRAFT' | translate }}
                </button>

                <div class="action-buttons-group">
                    <button mat-stroked-button
                            id="sendC51Button"
                            [hidden]="isFormClosedOrWaiting()"
                            (click)="sendC51()"
                            [disabled]="cdfForm().invalid"
                            color="primary"
                            aria-label="Basic"
                            data-cy="sendC51RequestButton"
                    >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SEND_C51' | translate }}
                    </button>

                    <button mat-flat-button
                            id="sendButton"
                            [hidden]="isFormClosedOrWaiting()"
                            (click)="save(true)"
                            color="accent"
                            aria-label="Basic"
                            [disabled]="cdfForm().invalid"
                            data-cy="validateRequestButton">
                        {{ 'CU_DATA_CAPTURE.BUTTONS.VALIDATE' | translate }}
                    </button>

                    <lib-s24-action-button
                            [requestId]="requestId">
                    </lib-s24-action-button>
                </div>
            </div>
        </div>
    </lib-data-loader-wrapper>
</div>
