ARG ANGULAR_IMAGE_TAG
FROM docker-proxy.onemrva.priv/openapitools/openapi-generator-cli:v7.9.0 AS openapigenerator

COPY api /usr/src/api
COPY scripts/generate_apis.sh /usr/src/api
WORKDIR /usr/src/api

RUN chmod +x generate_apis.sh
RUN ./generate_apis.sh

FROM docker-release.onemrva.priv/onemrva/node:${ANGULAR_IMAGE_TAG} AS nodecompiler
WORKDIR /usr/src/app

COPY frontend/package.json frontend/package-lock.json ./
RUN --mount=type=cache,id=npmcache,target=/root/.npm npm install --no-fund --no-audit
COPY frontend/ .

COPY --from=openapigenerator /usr/src/api/rest-client ./projects/cu/src/rest-client
#COPY --from=openapigenerator /usr/src/api/rest-client/private ./projects/cu/src/rest-client

CMD npm run startSync

EXPOSE 8080
