package be.fgov.onerva.cu.backend.lookup

import java.time.LocalDate
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import jakarta.annotation.PostConstruct
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.api.LookupApi
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.rest.model.DetachedCriteriaParamDTO
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.rest.model.LookupSimpleItemizedDTO
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.rest.model.RestrictionsParamDTO
import be.fgov.onerva.cu.common.utils.logger

@Service
class LookupServiceImpl(val lookupApi: LookupApi) : LookupService {
    private val log = logger

    val COUNTRY_NATIONALITY_BCSS_CLASS_NAME: String =
        "be.fgov.onerva.lookup.wppt.persistence.model.common.NationalityBCSS"
    val COUNTRY_ONEM_CLASS_NAME: String = "be.fgov.onerva.lookup.wppt.persistence.model.common.Country"
    val INS_POST_CLASS_NAME: String = "be.fgov.onerva.lookup.wppt.persistence.model.common.InsPost"
    val STREET_CLASS_NAME: String = "be.fgov.onerva.lookup.wppt.persistence.model.common.StreetInfoAddress"
    val POSTAL_CODE_LANGUAGE_REGIME: String =
        "be.fgov.onerva.lookup.wppt.persistence.model.common.PostalCodeLanguageRegime"

    private val updateLock = ReentrantLock()
    private var countryCache: Map<Int, CountryDTO> = HashMap()
    private var nationalityCache: Map<Int, NationalityDTO> = HashMap()
    private var cityCache: Map<CityKey, CityDTO> = HashMap()
    private var postalCodeLanguageRegime: Map<String, PostalCodeLanguageRegimeDTO> = HashMap()

    @PostConstruct
    fun init() {
        updateFromLookupApi()
    }

    @Scheduled(cron = "\${app.jobs.lookup.cron}")
    fun updateFromLookupApi() {
        log.info("Starting update of lookup caches")
        try {
            //NOPMD
            if (updateLock.tryLock()) {
                try {
                    updateCountry()
                    updateNationality()
                    updateCity()
                    updatePostalCodeLanguageRegime()
                    log.info("Successfully lookup caches")
                } finally {
                    updateLock.unlock()
                }
            } else {
                //NOPMD
                log.info("Update already in progress, skipping this iteration")
            }
        } catch (e: Exception) {
            log.error("Failed to update lookup caches", e)
        }
    }

    private fun ensureCachePopulated() {
        //NOPMD
        if (countryCache.isEmpty() || nationalityCache.isEmpty()) {
            log.info("Cache is empty, attempting to populate it")
            updateFromLookupApi()
        }
    }

    private fun updateCountry() {
        val countryLookupDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(COUNTRY_ONEM_CLASS_NAME).map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<Int, CountryDTO> = ConcurrentHashMap<Int, CountryDTO>()

        countryLookupDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.getEndDate() == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                val entity: CountryDTO =
                    CountryDTO(dto.id, dto.code, dto.descFr, dto.descNl)
                temporaryMap[entity.code.toInt()] = entity
            }

        countryCache = temporaryMap
        log.info(
            "Country cache updated, {} active countries found", temporaryMap.size
        )
    }

    /**
     * Update the city cache. The logic followed is that we use the nis code and postal code as the key
     * and find the last entry for a specific combination.
     */
    private fun updateCity() {
        val cityLookupDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(INS_POST_CLASS_NAME)
                .map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<CityKey, CityDTO> = ConcurrentHashMap<CityKey, CityDTO>()

        cityLookupDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.endDate == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                @Suppress("UNCHECKED_CAST")
                val codeMultiple = if (dto.codeMultiple is Map<*, *>) {
                    dto.codeMultiple as? Map<String, String>
                } else throw InvalidLookupException("Invalid lookup in city with ${dto.id}")
                val entity =
                    CityDTO(
                        LocalDate.parse(dto.beginDate.toString()),
                        codeMultiple?.get("code.nisCode")
                            ?: throw InvalidLookupException("Invalid lookup in city with ${dto.id}"),
                        dto.id,
                        codeMultiple?.get("code.code")
                            ?: throw InvalidLookupException("Invalid lookup in city with ${dto.id}"),
                        dto.descFr,
                        dto.descNl,
                    )
                val key = CityKey(entity.nisCode, entity.code)
                if (temporaryMap.containsKey(key)) {
                    if (temporaryMap[key]?.beginDate?.isBefore(entity.beginDate) == true) {
                        temporaryMap[key] = entity
                    }
                } else {
                    temporaryMap[key] = entity
                }
            }

        cityCache = temporaryMap
        log.info(
            "City cache updated, {} active cities found", temporaryMap.size
        )
    }

    private fun updateNationality() {
        val nationalityLookupDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(COUNTRY_NATIONALITY_BCSS_CLASS_NAME).map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<Int, NationalityDTO> = ConcurrentHashMap<Int, NationalityDTO>()

        nationalityLookupDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.endDate == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                @Suppress("UNCHECKED_CAST", "kotlin:S6530")
                val singleProperties = dto.singlePropertiesMap as Map<String, String>
                val onemCountryCode = singleProperties["countryCode"] ?: ""
                val entity =
                    NationalityDTO(
                        dto.id,
                        dto.code,
                        dto.descFr,
                        dto.descNl,
                        onemCountryCode,
                    )
                temporaryMap[entity.code.toInt()] = entity
            }

        nationalityCache = temporaryMap
        log.info(
            "Nationality cache updated, {} active nationalities found", temporaryMap.size
        )
    }

    fun updatePostalCodeLanguageRegime() {
        val postalCodeLanguageRegimeDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(POSTAL_CODE_LANGUAGE_REGIME).map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<String, PostalCodeLanguageRegimeDTO> =
            ConcurrentHashMap<String, PostalCodeLanguageRegimeDTO>()

        postalCodeLanguageRegimeDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.endDate == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                val entity: PostalCodeLanguageRegimeDTO =
                    PostalCodeLanguageRegimeDTO(
                        dto.beginDate?.let { LocalDate.parse(it.toString()) },
                        dto.endDate?.let { LocalDate.parse(it.toString()) },
                        @Suppress("UNCHECKED_CAST", "kotlin:S6530")
                        (dto.singlePropertiesMap as Map<String, String>)["languageRegimeCode"]
                            ?: throw InvalidLookupException("Invalid lookup in postal code language regime with ${dto.id}"),
                        dto.id,
                        dto.code,
                        dto.descFr,
                        dto.descNl
                    )
                temporaryMap[entity.code] = entity
            }
        postalCodeLanguageRegime = temporaryMap
    }

    override fun getStreetNameByStreetCode(postalCode: String, streetCode: Int): StreetDTO? {
        val searchCriteria = RestrictionsParamDTO()
            .criterion1(streetInfoCodeCriteria(streetCode))
            .criterion2(postalCodeCriteria(postalCode))
            .expression("and")
        val detachedCriteriaParam = DetachedCriteriaParamDTO()
            .entityOrClassName(STREET_CLASS_NAME)
            .restrictions(listOf(searchCriteria))

        val lookupReturnList = lookupApi.lookupGetLookupsByCriteriaPost(detachedCriteriaParam)
        return lookupReturnList.firstOrNull()?.let {
            StreetDTO(it.id, streetCode.toString(), it.descFr, it.descNl)
        }
    }

    override fun getNationalityCodeFromOnemCountryCode(onemCountryCode: String): Int? {
        return nationalityCache.values.filter { it.onemCountryCode == onemCountryCode }
            .minByOrNull { it.code.toInt() }?.code?.toInt()
    }

    override fun getOnemCountryCodeFromNationalityCode(nationalityCode: Int): String? =
        nationalityCache[nationalityCode]?.onemCountryCode

    private fun streetInfoCodeCriteria(code: Int): RestrictionsParamDTO {
        return RestrictionsParamDTO()
            .expression("=")
            .propertyName("code.code")
            .valueClassName(Integer::class.java.name)
            .value(code.toString())
    }

    private fun postalCodeCriteria(postalCode: String): RestrictionsParamDTO {
        return RestrictionsParamDTO()
            .expression("=")
            .propertyName("code.postalCode")
            .valueClassName(Integer::class.java.name)
            .value(postalCode)
    }

    override fun lookupCountry(searchQuery: String?): List<CountryDTO> {
        ensureCachePopulated()
        return countryCache.values.asSequence()
            .filter {
                searchQuery == null ||
                        it.descFr.contains(
                            searchQuery,
                            true
                        ) ||
                        it.descNl.contains(searchQuery, true) ||
                        it.code == searchQuery
            }.toList()
    }

    override fun lookupNationality(searchQuery: String?): List<NationalityDTO> {
        ensureCachePopulated()
        return nationalityCache.values.filter {
            searchQuery == null || it.descFr.contains(
                searchQuery,
                true
            ) ||
                    it.descNl.contains(searchQuery, true) ||
                    it.code == searchQuery
        }
            .toList()
    }

    override fun lookupCity(searchQuery: String?): List<CityDTO> {
        ensureCachePopulated()
        return cityCache.values.filter {
            searchQuery == null || it.descFr.contains(
                searchQuery,
                true
            ) ||
                    it.descNl.contains(searchQuery, true) ||
                    it.code == searchQuery
        }
            .toList()
    }

    override fun lookupPostalCodeLanguageRegime(searchQuery: String?): PostalCodeLanguageRegimeDTO? {
        ensureCachePopulated()
        return postalCodeLanguageRegime.values.firstOrNull { it.code == searchQuery }
    }

    override fun getCityByPostalCode(
        postalCode: String,
        nisCode: Int,
    ): CityDTO? {
        ensureCachePopulated()
        val key = CityKey(nisCode.toString(), postalCode)
        return cityCache[key]
    }

    override fun getNationalityByCode(nationalityCode: Int): NationalityDTO? {
        ensureCachePopulated()
        return nationalityCache[nationalityCode]
    }

    override fun getCountryByCode(countryCode: Int): CountryDTO? {
        ensureCachePopulated()
        return countryCache[countryCode]
    }
}