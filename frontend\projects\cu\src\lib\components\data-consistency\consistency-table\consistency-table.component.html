<div class="table-container" data-cy="consistency-table-container">
    <table mat-table [dataSource]="tableDataSource" class="consistency-table" data-cy="consistency-table">
        <!-- Nom du champ -->
        <ng-container matColumnDef="label">
            <th mat-header-cell *matHeaderCellDef data-cy="column-header-label"></th>
            <td mat-cell *matCellDef="let element" data-cy="cell-label">{{ element.label | translate }}</td>
        </ng-container>

        <!-- Encodé par le citoyen -->
        <ng-container matColumnDef="encodedValue">
            <th mat-header-cell *matHeaderCellDef
                data-cy="column-header-encoded">{{ 'CU_DATA_CONSISTENCY.DC.TABLE.COLUMN.CITIZEN' | translate }}
            </th>
            <td mat-cell *matCellDef="let element" data-cy="cell-encoded-value"
                [class.cell-with-inconsistent-data]="!element.isConsistent && !isFormClosedOrWaiting()"
                [innerHTML]="element.encodedValue || '-'"></td>
        </ng-container>

        <!-- Base de données ONEM -->
        <ng-container matColumnDef="dbValue">
            <th mat-header-cell *matHeaderCellDef
                data-cy="column-header-db">{{ 'CU_DATA_CONSISTENCY.DC.TABLE.COLUMN.ONEM' | translate }}
            </th>
            <td mat-cell *matCellDef="let element" [class.cell-with-inconsistent-data]="!element.isConsistent && !isFormClosedOrWaiting()"
                data-cy="cell-db-value" [innerHTML]="element.dbValue || '-'"></td>
        </ng-container>

        <!-- Sources authentiques -->
        <ng-container matColumnDef="sourceValue">
            <th mat-header-cell *matHeaderCellDef
                data-cy="column-header-source">{{ 'CU_DATA_CONSISTENCY.DC.TABLE.COLUMN.SOURCE_AUTHENTIQUES' | translate }}
            </th>
            <td mat-cell *matCellDef="let element" [class.cell-with-inconsistent-data]="!element.isConsistent && !isFormClosedOrWaiting()"
                data-cy="cell-source-value" [innerHTML]="element.sourceValue || '-'"></td>
        </ng-container>

        <!-- Valeur à maintenir -->
        <ng-container matColumnDef="valueToKeep">
            <th mat-header-cell *matHeaderCellDef
                data-cy="column-header-action">{{ 'CU_DATA_CONSISTENCY.DC.TABLE.COLUMN.ACTION' | translate }}
            </th>

            <td mat-cell *matCellDef="let row; let rowIndex = index" data-cy="cell-action">
                <button mat-button *ngIf="!row.isConsistent && row.selectedValue?.value == null"
                        color="primary"
                        aria-label="Basic"
                        [hidden]="isFormClosedOrWaiting()"
                        (click)="openMaintainValueDialog(row)"
                        [attr.data-cy]="'correct-button-' + rowIndex">{{ 'CU_DATA_CONSISTENCY.BUTTONS.CORRECT' | translate }}
                </button>
                <div *ngIf="row.selectedValue?.value != null" [innerHTML]="getModifiedValue(row.selectedValue)"
                     [attr.data-cy]="'selected-value-' + rowIndex">
                </div>
            </td>
        </ng-container>

        <ng-container matColumnDef="icon">
            <th mat-header-cell *matHeaderCellDef data-cy="column-header-icon"></th>
            <td mat-cell *matCellDef="let row; let rowIndex = index;" data-cy="cell-icon">
                <mat-icon
                        *ngIf="row.isConsistent || row.selectedValue?.value == null || isFormClosedOrWaiting()"
                        class="question-result-icon filled"
                        color="{{(row.isConsistent && row.selectedValue?.value == null) || isFormClosedOrWaiting()? 'success' : 'warn'}}"
                        [attr.data-cy]="'status-icon-' + rowIndex">{{ getIcon(row.isConsistent) }}
                </mat-icon>
                <button
                        mat-stroked-button
                        color="primary small"
                        [hidden]="isFormClosedOrWaiting()"
                        *ngIf="!row.isConsistent && row.selectedValue?.value != null"
                        [attr.data-cy]="'update-button-' + rowIndex"
                        (click)="openMaintainValueDialog(row)"
                >{{ "CU_DATA_CONSISTENCY.BUTTONS.UPDATE" | translate }}
                </button>
            </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns" data-cy="header-row"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;" [attr.data-cy]="'row-' + row.index"></tr>
    </table>
</div>
