import {CommonModule} from "@angular/common";
import {Component, Input} from "@angular/core";
import {Observable} from "rxjs";
import {FormUtilsService} from "../../services/form-utils.service";
import {LoadingService} from "../../services/loading.service";
import {LoadingComponentComponent} from "../loading-component/loading-component.component";
import {ErrorDisplayComponent} from "./error-display/error-display.component";

@Component({
  selector: 'lib-data-loader-wrapper',
  templateUrl: './data-loader-wrapper.component.html',
  styleUrl: './data-loader-wrapper.component.scss',
  standalone: true,
  imports: [CommonModule, ErrorDisplayComponent, LoadingComponentComponent],
})
export class DataLoaderWrapperComponent {
  @Input() error!: {hasError: boolean; message: string};
  @Input() task!: any;


  loading$: Observable<boolean>;

  constructor(private loadingService: LoadingService) {
    this.loading$ = this.loadingService.loading$;
  }

  protected readonly FormUtilsService = FormUtilsService;
}
