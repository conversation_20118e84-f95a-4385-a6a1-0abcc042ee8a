package be.fgov.onerva.cu.backend.adapter.out.smals

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.out.external.smals.TaskApiAdapter
import be.fgov.onerva.cu.backend.rest.client.wo.smals.rest.model.TaskDTO
import be.fgov.onerva.cu.backend.taskapi.TaskApiService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class TaskApiAdapterTest {

    @MockK
    private lateinit var taskApiService: TaskApiService

    @InjectMockKs
    private lateinit var taskApiAdapter: TaskApiAdapter

    @Test
    fun `softDeleteTask should return TaskDTO when task is successfully soft deleted`() {
        val taskId = 123L
        val reason = "Task completed"
        val expectedTaskDTO = TaskDTO()

        every { taskApiService.softDeleteTask(taskId, reason) } returns expectedTaskDTO

        val result = taskApiAdapter.softDeleteTask(taskId, reason)

        assertThat(result).isEqualTo(expectedTaskDTO)
        verify(exactly = 1) { taskApiService.softDeleteTask(taskId, reason) }
    }

    @Test
    fun `softDeleteTask should return null when task is not found`() {
        val taskId = 999L
        val reason = "Soft deletion"

        every { taskApiService.softDeleteTask(taskId, reason) } returns null

        val result = taskApiAdapter.softDeleteTask(taskId, reason)

        assertThat(result).isNull()
        verify(exactly = 1) { taskApiService.softDeleteTask(taskId, reason) }
    }

    @Test
    fun `softDeleteTask should handle null reason parameter`() {
        // Given
        val taskId = 456L
        val reason: String? = null
        val expectedTaskDTO = TaskDTO()

        every { taskApiService.softDeleteTask(taskId, reason) } returns expectedTaskDTO

        val result = taskApiAdapter.softDeleteTask(taskId, reason)

        assertThat(result).isEqualTo(expectedTaskDTO)
        verify(exactly = 1) { taskApiService.softDeleteTask(taskId, reason) }
    }
}