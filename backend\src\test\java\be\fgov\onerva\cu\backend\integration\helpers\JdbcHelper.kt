package be.fgov.onerva.cu.backend.integration.helpers

import java.util.UUID
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Component

@Component
class JdbcHelper(val jdbcTemplate: JdbcTemplate) {

    fun getRequestByC9Id(c9Id: Long): Map<String, Any> {
        return jdbcTemplate.queryForMap(
            "SELECT * FROM request WHERE c9_id = ?",
            c9Id
        )
    }

    fun getRequestIdByC9Id(c9Id: Long): UUID {
        return jdbcTemplate.queryForObject(
            "SELECT id FROM request WHERE c9_id = ?",
            UUID::class.java,
            c9Id
        )!!
    }

    fun getRequest(requestId: UUID): Map<String, Any> {
        return jdbcTemplate.queryForMap(
            "SELECT * FROM request WHERE id = ?",
            requestId
        )
    }

    fun getCitizenInformation(requestId: UUID): Map<String, Any> {
        return jdbcTemplate.queryForMap(
            """
            SELECT citizen_information.*
            FROM citizen_information 
            INNER JOIN request on citizen_information.request_id = request.id
            WHERE request.id = ?   
            """,
            requestId
        )
    }

    fun getModeOfPayment(requestId: UUID): Map<String, Any> {
        return jdbcTemplate.queryForMap(
            """
            SELECT mode_of_payment.*
            FROM mode_of_payment 
            INNER JOIN request on mode_of_payment.request_id = request.id
            WHERE request.id = ?   
            """,
            requestId
        )
    }

    fun getUnionContribution(requestId: UUID): Map<String, Any> {
        return jdbcTemplate.queryForMap(
            """
            SELECT union_contribution.*
            FROM union_contribution 
            INNER JOIN request on union_contribution.request_id = request.id
            WHERE request.id = ?   
            """,
            requestId
        )
    }
    fun getWaveTask(requestId: UUID): Map<String, Any> {
        return jdbcTemplate.queryForMap(
            """
            SELECT wave_task.*
            FROM wave_task 
            INNER JOIN request on wave_task.request_id = request.id
            WHERE request.id = ?   
            """,
            requestId
        )
    }
}