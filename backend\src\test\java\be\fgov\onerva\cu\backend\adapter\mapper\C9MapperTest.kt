package be.fgov.onerva.cu.backend.adapter.mapper

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.exception.InvalidC9Exception
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef
import be.fgov.onerva.unemployment.c9.rest.model.BelgianCommunity
import be.fgov.onerva.unemployment.c9.rest.model.C9
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment
import be.fgov.onerva.unemployment.c9.rest.model.NationalityBCSS

class MapperTest {

    @Nested
    inner class EC1IdentityMapperTest {

        @Test
        fun `toCitizenInformation should map all fields when present`() {
            // Given
            val ec1Identity = EC1Identity().apply {
                inss = "***********"
                lastName = "Doe"
                firstName = "John"
                street = "Main Street"
                houseNumber = "42"
                boxNumber = "A"
                city = "Brussels"
                dateOfBirth = "1990-01-01"
                phoneNumber = "+32123456789"
                email = "<EMAIL>"
                country = NationalityBCSS().apply {
                    code = "150"
                    descFr = "Belge"
                }
                zipCode = BelgianCommunity().apply {
                    zipCode = "1000"
                }
                nationality = NationalityBCSS().apply {
                    code = "111"
                    descFr = "France"
                }
            }

            // When
            val result = ec1Identity.toDomainCitizenInformation(LocalDate.now())

            // Then
            assertThat(result.address.street).isEqualTo("Main Street")
            assertThat(result.address.houseNumber).isEqualTo("42")
            assertThat(result.address.boxNumber).isEqualTo("A")
            assertThat(result.address.city).isEqualTo("Brussels")
            assertThat(result.address.countryCode).isEqualTo(150)
            assertThat(result.nationalityCode).isEqualTo(111)
            assertThat(result.birthDate).isEqualTo(LocalDate.of(1990, 1, 1))
        }

        @Test
        fun `toCitizenInformation should throw InvalidC9Exception when SSIN is missing`() {
            // Given
            val ec1Identity = EC1Identity().apply {
                firstName = "John"
                lastName = "Doe"
            }

            // When/Then
            assertThatThrownBy { ec1Identity.toDomainCitizenInformation(LocalDate.now()) }
                .isInstanceOf(InvalidC9Exception::class.java)
                .hasMessage("The C9 is invalid - Invalid date of birth")
        }
    }

    @Nested
    inner class EC1ModeOfPaymentMapperTest {

        @Test
        fun `toModeOfPayment should map belgian bank account correctly`() {
            // Given
            val ec1ModeOfPayment = EC1ModeOfPayment().apply {
                isMyBankAccount = true
                belgianSEPABankAccount = "****************"
            }

            // When
            val result = ec1ModeOfPayment.toDomainModeOfPayment(LocalDate.now())

            // Then
            assertThat(result.otherPersonName).isNull()
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isNull()
        }

        @Test
        fun `toModeOfPayment should map foreign bank account correctly`() {
            // Given
            val ec1ModeOfPayment = EC1ModeOfPayment().apply {
                isMyBankAccount = true
                foreignBankAccountIBAN = "***************************"
                foreignBankAccountBIC = "BNPAFRPP"
            }

            // When
            val result = ec1ModeOfPayment.toDomainModeOfPayment(LocalDate.now())

            // Then
            assertThat(result.iban).isEqualTo("***************************")
            assertThat(result.bic).isEqualTo("BNPAFRPP")
        }
    }

    @Nested
    inner class C9MapperTest {

        @Test
        fun `toChangePersonalDataReceived should map required fields correctly`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                requestDate = LocalDate.of(2024, 1, 1)
                opKey = "OP123"
                sectOp = "SO123"
                paymentInstitution = 456
                entityCode = "EC456"
                unemploymentOffice = 789
                scanNumber = *********
                scanUrl = "http://the-scan-url"
                introductionDate = LocalDate.of(2024, 1, 2)
            }

            // When
            val result = c9.toChangePersonalDataRequestCommand()

            // Then
            assertThat(result.c9Id).isEqualTo(12345L)
            assertThat(result.ssin).isEqualTo("***********")
        }

        @Test
        fun `hasEC1 should return true when EC1 attestRef exists`() {
            // Given
            val c9 = C9().apply {
                attestRefs = listOf(AttestRef().apply {
                    type = "EC1"
                    url = "http://example.com/ec1"
                })
            }

            // When
            val result = c9.hasEC1()

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `hasEC1 should return false when no EC1 attestRef exists`() {
            // Given
            val c9 = C9().apply {
                attestRefs = listOf(AttestRef().apply {
                    type = "OTHER"
                    url = "http://example.com/other"
                })
            }

            // When
            val result = c9.hasEC1()

            // Then
            assertThat(result).isFalse()
        }

        @Test
        fun `getEC1 should return URL when EC1 attestRef exists`() {
            // Given
            val expectedUrl = "http://example.com/ec1"
            val c9 = C9().apply {
                attestRefs = listOf(AttestRef().apply {
                    type = "EC1"
                    url = expectedUrl
                })
            }

            // When
            val result = c9.getEC1()

            // Then
            assertThat(result).isNotNull()
            assertThat(result?.url).isEqualTo(expectedUrl)
            assertThat(result?.type).isEqualTo("EC1")
        }

        @Test
        fun `getEC1 should return null when no EC1 attestRef exists`() {
            // Given
            val c9 = C9().apply {
                attestRefs = listOf(AttestRef().apply {
                    type = "OTHER"
                    url = "http://example.com/other"
                })
            }

            // When
            val result = c9.getEC1()

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `toChangePersonalDataRequestCommand should set Electronic documentType when EC1 exists`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                requestDate = LocalDate.of(2024, 1, 1)
                opKey = "OP123"
                sectOp = "SO123"
                paymentInstitution = 456
                entityCode = "EC456"
                unemploymentOffice = 789
                introductionDate = LocalDate.of(2024, 1, 2)
                attestRefs = listOf(AttestRef().apply {
                    id = 665544
                    type = "EC1"
                    url = "http://example.com/ec1"
                })
            }

            // When
            val result = c9.toChangePersonalDataRequestCommand()

            // Then
            assertThat(result).extracting("c9Id", "c9Type", "ssin", "ec1Id")
                .containsExactly(12345L, "400", "***********", 665544)
        }

        @Test
        fun `toChangePersonalDataRequestCommand should set Paper documentType when no EC1 exists`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                requestDate = LocalDate.of(2024, 1, 1)
                opKey = "OP123"
                sectOp = "SO123"
                paymentInstitution = 456
                entityCode = "EC456"
                unemploymentOffice = 789
                introductionDate = LocalDate.of(2024, 1, 2)
                attestRefs = listOf(AttestRef().apply {
                    id = 665544
                    type = "EC1"
                    url = "http://example.com/other"
                })
            }

            // When
            val result = c9.toChangePersonalDataRequestCommand()

            // Then
            assertThat(result).extracting("c9Id", "c9Type", "ssin", "ec1Id")
                .containsExactly(12345L, "400", "***********", 665544)
        }

        @Test
        fun `toChangePersonalDataRequestCommand should handle empty attestRefs`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                requestDate = LocalDate.of(2024, 1, 1)
                opKey = "OP123"
                sectOp = "SO123"
                paymentInstitution = 456
                entityCode = "EC456"
                unemploymentOffice = 789
                introductionDate = LocalDate.of(2024, 1, 2)
                attestRefs = emptyList()
            }

            // When
            val result = c9.toChangePersonalDataRequestCommand()

            // Then
            assertThat(result).extracting("c9Id", "c9Type", "ssin", "ec1Id")
                .containsExactly(12345L, "400", "***********", null)
        }
    }

    @Nested
    inner class IsValidForChangePersonalDataTest {

        @Test
        fun `isValidForChangePersonalData should return true when type is 400 and has EC1`() {
            // Given
            val c9 = C9().apply {
                type = "400"
                treatmentStatus = "READY_TO_BE_TREATED"
                attestRefs = listOf(AttestRef().apply {
                    type = "EC1"
                    url = "http://example.com/ec1"
                })
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `isValidForChangePersonalData should return true when type is 410 and has C1`() {
            // Given
            val c9 = C9().apply {
                type = "410"
                treatmentStatus = "READY_TO_BE_TREATED"
                attestRefs = listOf(AttestRef().apply {
                    type = "C1"
                    url = "http://example.com/c1"
                })
                scanNumber = 88776655
                scanUrl = "http://the-scan-url"
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `isValidForChangePersonalData should return false when type is invalid`() {
            // Given
            val c9 = C9().apply {
                type = "300"
                treatmentStatus = "READY_TO_BE_TREATED"
                attestRefs = listOf(AttestRef().apply {
                    type = "EC1"
                    url = "http://example.com/ec1"
                })
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isFalse()
        }

        @Test
        fun `isValidForChangePersonalData should return false when treatment status is invalid`() {
            // Given
            val c9 = C9().apply {
                type = "400"
                treatmentStatus = "PENDING"
                attestRefs = listOf(AttestRef().apply {
                    type = "EC1"
                    url = "http://example.com/ec1"
                })
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isFalse()
        }

        @Test
        fun `isValidForChangePersonalData should return false when neither EC1 nor C1 exist`() {
            // Given
            val c9 = C9().apply {
                type = "400"
                treatmentStatus = "READY_TO_BE_TREATED"
                attestRefs = listOf(AttestRef().apply {
                    type = "OTHER"
                    url = "http://example.com/other"
                })
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isFalse()
        }

        @Test
        fun `isValidForChangePersonalData should return false with empty attestRefs`() {
            // Given
            val c9 = C9().apply {
                type = "400"
                treatmentStatus = "READY_TO_BE_TREATED"
                attestRefs = emptyList()
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isFalse()
        }

        @Test
        fun `isValidForChangePersonalData should return true when both EC1 and C1 exist`() {
            // Given
            val c9 = C9().apply {
                type = "400"
                treatmentStatus = "READY_TO_BE_TREATED"
                attestRefs = listOf(
                    AttestRef().apply {
                        type = "EC1"
                        url = "http://example.com/ec1"
                    },
                    AttestRef().apply {
                        type = "C1"
                        url = "http://example.com/c1"
                    }
                )
            }

            // When
            val result = c9.isChangedOfPersonalDataReady()

            // Then
            assertThat(result).isTrue()
        }
    }

    @Nested
    inner class ToChangePersonalDataRequestTreatedCommandTest {

        @Test
        fun `should map C9 to ChangePersonalDataRequestTreatedCommand correctly`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                decisionDate = LocalDate.of(2024, 4, 15)
                decisionType = "C2Y"
                decisionWindowsUser = "testUser"
            }

            // When
            val result = c9.toChangePersonalDataRequestTreatedCommand()

            // Then
            assertThat(result).isNotNull()
            assertThat(result.c9Id).isEqualTo(12345L)
            assertThat(result.type).isEqualTo("400")
            assertThat(result.ssin).isEqualTo("***********")
            assertThat(result.decisionDate).isEqualTo(LocalDate.of(2024, 4, 15))
            assertThat(result.decisionType).isEqualTo(DecisionType.C2Y)
            assertThat(result.user).isEqualTo("testUser")
        }

        @ParameterizedTest
        @CsvSource(
            "C2Y", "C2N", "C2F", "C2P", "C51", "C9B", "C2", "C9NA"
        )
        fun `should map different decision types correctly`(decisionTypeStr: String) {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                decisionDate = LocalDate.of(2024, 4, 15)
                decisionType = decisionTypeStr
                decisionWindowsUser = "testUser"
            }

            // When
            val result = c9.toChangePersonalDataRequestTreatedCommand()

            // Then
            assertThat(result).isNotNull()
            assertThat(result.decisionType).isEqualTo(DecisionType.valueOf(decisionTypeStr))
        }

        @Test
        fun `should properly map different C9 type values`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "410"
                ssin = "***********"
                decisionDate = LocalDate.of(2024, 4, 15)
                decisionType = "C2Y"
                decisionWindowsUser = "testUser"
            }

            // When
            val result = c9.toChangePersonalDataRequestTreatedCommand()

            // Then
            assertThat(result).isNotNull()
            assertThat(result.type).isEqualTo("410")
        }

        @Test
        fun `should map C9 with minimal required fields`() {
            // Given
            val c9 = C9().apply {
                id = 12345L
                type = "400"
                ssin = "***********"
                decisionDate = LocalDate.of(2024, 4, 15)
                decisionType = "C2Y"
                decisionWindowsUser = "testUser"
            }

            // When
            val result = c9.toChangePersonalDataRequestTreatedCommand()

            // Then
            assertThat(result).isNotNull()
            assertThat(result.c9Id).isEqualTo(12345L)
            assertThat(result.ssin).isEqualTo("***********")
            assertThat(result.user).isEqualTo("testUser")
        }
    }
}