package be.fgov.onerva.cu.bff.mapper

import be.fgov.onerva.cu.bff.lookup.CityDTO
import be.fgov.onerva.cu.bff.lookup.CountryDTO
import be.fgov.onerva.cu.bff.lookup.NationalityDTO
import be.fgov.onerva.cu.bff.model.Address
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import be.fgov.onerva.cu.bff.rest.server.priv.model.AddressNullable
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.CountryResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.NationalityResponse

fun CountryDTO.toCountryResponse() = CountryResponse(
    code = this.code,
    descFr = this.descFr,
    descNl = this.descNl,
)

fun NationalityDTO.toNationalityResponse() = NationalityResponse(
    code = this.code,
    descFr = this.descFr,
    descNl = this.descNl,
)

fun CityDTO.toCityResponse() = be.fgov.onerva.cu.bff.rest.server.priv.model.CityResponse(
    code = this.code,
    descFr = this.descFr,
    descNl = this.descNl,
    nisCode = this.nisCode,
)

fun Address.toAddressNullable() = AddressNullable(
    boxNumber = this.boxNumber,
    city = this.city,
    countryCode = this.countryCode,
    houseNumber = this.houseNumber,
    street = this.street,
    zipCode = this.zipCode,
)

fun CitizenInfoWithAddress.toCitizenInformationDetailNullableResponse() = CitizenInformationDetailNullableResponse(
    birthDate = null,
    nationalityCode = this.nationalityCode,
    address = this.address.toAddressNullable(),
)