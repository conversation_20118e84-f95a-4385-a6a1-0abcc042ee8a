import {CommonModule} from "@angular/common";
import {HttpClient} from "@angular/common/http";
import {Component, signal, ViewEncapsulation} from "@angular/core";
import {ReactiveFormsModule} from "@angular/forms";
import {LuxonDateAdapter, MAT_LUXON_DATE_ADAPTER_OPTIONS} from "@angular/material-luxon-adapter";
import {MatButton} from "@angular/material/button";
import {DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE} from "@angular/material/core";
import {TranslateLoader, TranslateModule, TranslateService} from "@ngx-translate/core";
import {TranslateHttpLoader} from "@ngx-translate/http-loader";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";
import {ONEMRVA_MAT_NATIVE_DATE_FORMAT} from "@onemrvapublic/design-system/shared";
import {
    AggregatedChangePersonalDataValidateResponse,
    BASE_PATH,
    FieldSource,
    RequestBasicInfoResponse,
} from "@rest-client/cu-bff";
import {catchError, EMPTY, forkJoin, Observable, takeUntil, tap} from "rxjs";
import {CuC9AnnexesComponent} from "../../components/cu-c9-annexes/cu-c9-annexes.component";
import {
    CuTaskStatusMessageComponent,
} from "../../components/cu-task-status-message/cu-task-status-message.component";
import {DataConsistencyComponent} from "../../components/data-consistency/data-consistency.component";
import {DataLoaderWrapperComponent} from "../../components/data-loader-wrapper/data-loader-wrapper.component";
import {ConfigService} from "../../config/config.service";
import {environment} from "../../environments/environment";
import {DataValidationService} from "../../http/data-validation.service";
import {GeoLookupService} from "../../http/geo-lookup.service";
import {FormUtilsService} from "../../services/form-utils.service";
import {ToastService} from "../../services/toast.service";
import {WoUtils} from "../../wo-utils";
import {S24ActionButtonComponent} from "../common/s24-action-button/s24-action-button.component";
import {provideCuApi} from "../data-capture/data-capture.component";
import {BaseWebComponent} from "../common/base-web-component";
import {MatTooltipModule} from "@angular/material/tooltip";
import {RedirectHandlerService} from "../../http/redirect-handler.service";
import {MatDialog} from "@angular/material/dialog";
import {CuDialogComponent} from "../../components/cu-dialog/cu-dialog.component";

@Component({
    selector: "lib-data-validation",
    standalone: true,
    imports: [
        CuC9AnnexesComponent,
        CommonModule,
        OnemrvaThemeModule,
        ReactiveFormsModule,
        TranslateModule,
        DataConsistencyComponent,
        MatButton,
        CuTaskStatusMessageComponent,
        MatTooltipModule,
        S24ActionButtonComponent,
        DataLoaderWrapperComponent,
    ],
    providers: [
        {
            provide: DateAdapter,
            useClass: LuxonDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
        {
            provide: MAT_LUXON_DATE_ADAPTER_OPTIONS,
            useValue: {useUtc: true},
        },
        {
            provide: MAT_DATE_FORMATS,
            useValue: ONEMRVA_MAT_NATIVE_DATE_FORMAT,
        },
        {
            provide: BASE_PATH,
            useValue: environment.apiBasePath,
        },
        provideCuApi(),
        {
            provide: TranslateLoader,
            useFactory: (http: HttpClient, configService: ConfigService) => {
                const baseURL = configService.getEnvironmentVariable("baseUrl");
                return new TranslateHttpLoader(
                    http,
                    baseURL + `/assets/i18n/`,
                    ".json",
                );
            },
            deps: [HttpClient, ConfigService],
        },
        TranslateService,
    ],
    templateUrl: "./data-validation.component.html",
    styleUrl: "./data-validation.component.scss",
    encapsulation: ViewEncapsulation.None,
})

export class DataValidationComponent extends BaseWebComponent {
    citizenData = signal<RequestBasicInfoResponse | null>(null);
    dataConsistencyData!: AggregatedChangePersonalDataValidateResponse;
    selectedFieldSources: FieldSource[] = [];
    hasChangesToSave = false;
    areActionButtonsEnabled = false;
    isTableConsistent = false;

    constructor(
        translate: TranslateService,
        geoLookupService: GeoLookupService,
        configService: ConfigService,
        readonly redirectHandlerService: RedirectHandlerService,
        readonly matDialog: MatDialog,
        readonly dataValidationService: DataValidationService,
        readonly toastService: ToastService,
    ) {
        super(translate, geoLookupService, configService);
    }

    override ngOnInit() {
        super.ngOnInit();
        WoUtils.triggerWoWebComponentMaxMinSize();
    }

    protected isFormClosedOrWaiting(): boolean {
        return FormUtilsService.isClosedOrWaiting(this.status, this.task);
    }

    onTableConsistencyChanged(isConsistent: boolean): void {
        this.isTableConsistent = isConsistent;
    }

    protected override initializeComponentServices(token: string): void {
        this.dataValidationService.initializeServices(token);
        this.redirectHandlerService.initializeServices(token);
    }

    protected override async getWoTaskById(requestId: string): Promise<void> {
        this.dataValidationService.getAggregatedData(requestId).pipe(
            tap(response => {
                if (response) {
                    this.dataConsistencyData = response;
                    // this.dataConsistencyData!.onemCitizenInformation!.nationality = "200";
                    // this.dataConsistencyData!.onemCitizenInformation!.birthDate = "2000-02-01"
                    this.citizenData.set(response.basicInfo || null);
                }
            }),
            catchError(error => {
                this.handleLoadingError(error);
                console.error("Error fetching task:", error);
                return EMPTY;
            }),
            takeUntil(this.destroy$),
        ).subscribe();
    }

    save(noMessage= false): void {
        if (!this.hasChangesToSave || !this.selectedFieldSources.length) {
            return;
        }

        const citizenFields: FieldSource[] = [...(this.dataConsistencyData.citizenInformation?.fieldSources || [])];
        const paymentFields: FieldSource[] = [...(this.dataConsistencyData.modeOfPayment?.fieldSources || [])];

        this.selectedFieldSources.forEach(fieldSource => {
            let mappedFieldSource = {...fieldSource};

            if (fieldSource.fieldName === "bankAccount") {
                mappedFieldSource.fieldName = "account";
            } else if (fieldSource.fieldName === "otherPersonName") {
                mappedFieldSource.fieldName = "otherPersonName";
            }

            if (fieldSource.fieldName && ["birthDate", "nationality", "address"].includes(fieldSource.fieldName)) {
                const existingIndex = citizenFields.findIndex(fs => fs.fieldName === fieldSource.fieldName);
                if (existingIndex !== -1) {
                    citizenFields[existingIndex] = fieldSource;
                } else {
                    citizenFields.push(fieldSource);
                }
            } else if (mappedFieldSource.fieldName && ["iban", "otherPersonName", "account"].includes(mappedFieldSource.fieldName)) {
                const existingIndex = paymentFields.findIndex(fs => fs.fieldName === mappedFieldSource.fieldName);
                if (existingIndex !== -1) {
                    paymentFields[existingIndex] = mappedFieldSource;
                } else {
                    paymentFields.push(mappedFieldSource);
                }
            }
        });

        const calls: Observable<any>[] = [];

        if (citizenFields.length > 0) {
            calls.push(this.dataValidationService.selectCitizenInformationSources(this.requestId, citizenFields));
        }

        if (paymentFields.length > 0) {
            calls.push(this.dataValidationService.selectModeOfPaymentSources(this.requestId, paymentFields));
        }

        if (calls.length === 0) {
            return;
        }

        forkJoin(calls).pipe(
            tap(responses => {
                if(!noMessage) {
                    this.toastService.success("TOASTS.SEND_SUCCESS");
                }
                this.action.emit({ messageType: 'refreshTask' });
            }),
            catchError(error => {
                console.error("Error saving field sources:", error);
                this.toastService.error("TOASTS.SEND_ERROR");
                return EMPTY;
            }),
            takeUntil(this.destroy$),
        ).subscribe();
    }

    sendC51() {
        this.redirectHandlerService.getC51RedirectUrl(this.requestId).subscribe({
            next: (url: string) => {
                window.open(url, "_blank");
            },
            error: (error: Error) => {
                this.toastService.error("TOASTS.C51_ERROR");
                console.error("Error getting C51 redirect URL:", error);
            },
        });
    }

    onFieldSourcesChange(fieldSources: FieldSource[]): void {
        this.selectedFieldSources = fieldSources;
        this.hasChangesToSave = fieldSources.length > 0;
    }

    handleRegisVerificationChange(isVerified: boolean): void {
        this.areActionButtonsEnabled = isVerified;
    }

    validateAndContinue(): void {
        this.save(true);

        const dialogRef = this.matDialog.open(CuDialogComponent, {
            data: {
                title: this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.TITLE"),
                content: `${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.SUBTITLE")}
                    <div class="validate">${this.translate.instant(
                    "CU_DATA_CONSISTENCY.VALIDATION_DIALOG.BODY_1")}
                    <strong>${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.BODY_2")}</strong>
                    <br>
                    ${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.BODY_3")}
                    <strong>${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.BODY_4")}</strong>
                    ${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.BODY_5")}
                    <strong>${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.BODY_6")}</strong>
                    </div>
                    ${this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.FOOTER")}`,
                primaryActionText: this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.PRIMARY"),
                secondaryActionText: this.translate.instant("CU_DATA_CONSISTENCY.VALIDATION_DIALOG.SECONDARY"),
                dialogType: "warn",
                dialogSize: "medium",
                onPrimaryAction: () => {
                    this.dataValidationService.closeTask(this.requestId, "VALIDATION_DATA").pipe(
                        tap(responses => {
                            this.redirectHandlerService.openS24Session(this.requestId);
                            this.toastService.success("TOASTS.SEND_SUCCESS");
                            this.action.emit({ messageType: 'refreshTask' });
                            this.getWoTaskById(this.requestId);
                            dialogRef.close(true);
                        }),
                        catchError(error => {
                            console.error("Error closing task:", error);
                            dialogRef.close(false);
                            return EMPTY;
                        }),
                        takeUntil(this.destroy$),
                    ).subscribe();
                    dialogRef.close(true);
                },
                onSecondaryAction: () => {
                    dialogRef.close(false);
                },
            },
        });

    }

    protected readonly FormUtilsService = FormUtilsService;
}