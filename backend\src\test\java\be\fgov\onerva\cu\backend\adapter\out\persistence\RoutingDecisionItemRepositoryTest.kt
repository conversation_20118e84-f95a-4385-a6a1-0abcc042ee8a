package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RoutingDecisionItemEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RoutingDecisionItemRepository
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.ManualVerificationType

@DataJpaTest
@SpringJUnitConfig
class RoutingDecisionItemRepositoryTest {

    @Autowired
    lateinit var routingDecisionItemRepository: RoutingDecisionItemRepository

    @Autowired
    lateinit var entityManager: TestEntityManager

    @Test
    fun `should find routing decisions by request information id`() {
        // given
        val changePersonalDataRequest = ChangePersonalDataRequestEntity(
            c9Id = 123L,
            c9Type = "C9_TYPE",
            opKey = "OP123",
            sectOp = "SECT01",
            requestDate = LocalDate.now(),
            ssin = "***********",
            documentType = IdentityDocumentType.ELECTRONIC,
            unemploymentOffice = null,
            paymentInstitution = null,
            introductionDate = LocalDate.now(),
            dateValid = null,
            scanNumber = null,
            scanUrl = null,
            operatorCode = null,
            entityCode = null,
            introductionType = null,
            dueDate = null,
            numbox = null,
            ec1Id = null,
            ec1DisplayUrl = null,
        )
        entityManager.persistAndFlush(changePersonalDataRequest)

        val routingDecision1 = RoutingDecisionItemEntity(
            request = changePersonalDataRequest,
            type = ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD,
            value = true,
        )

        val routingDecision2 = RoutingDecisionItemEntity(
            request = changePersonalDataRequest,
            type = ManualVerificationType.NON_BELGIAN_RESIDENT,
            value = false,
        )

        entityManager.persistAndFlush(routingDecision1)
        entityManager.persistAndFlush(routingDecision2)

        // when
        val result = routingDecisionItemRepository.findByRequestId(changePersonalDataRequest.id)

        // then
        assertThat(result).hasSize(2)
        assertThat(result.map { it.type }).containsExactlyInAnyOrder(
            ManualVerificationType.CITIZEN_OVER_65_YEARS_OLD,
            ManualVerificationType.NON_BELGIAN_RESIDENT
        )
        assertThat(result.map { it.value }).containsExactlyInAnyOrder(true, false)
    }

    @Test
    fun `should return empty list when no routing decisions found for request information id`() {
        // given  
        val nonExistentId = UUID.randomUUID()

        // when
        val result = routingDecisionItemRepository.findByRequestId(nonExistentId)

        // then
        assertThat(result).isEmpty()
    }
}