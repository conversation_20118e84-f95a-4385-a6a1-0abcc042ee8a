import {CommonModule} from "@angular/common";
import {Component, Input, SimpleChanges} from "@angular/core";
import {MatButtonModule} from "@angular/material/button";
import {MatIconModule} from "@angular/material/icon";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaMatMessageBoxComponent} from "@onemrvapublic/design-system/mat-message-box";
import {
  OnemrvaMatPanelComponent,
  OnemrvaMatPanelContentComponent,
  OnemrvaMatPanelTitleComponent,
} from "@onemrvapublic/design-system/mat-panel";
import {OnemRvaCDNService} from "@onemrvapublic/design-system/shared";
import {FormUtilsService} from "../../../services/form-utils.service";

@Component({
  selector: 'lib-error-display',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule, TranslateModule, OnemrvaMatPanelComponent,
    OnemrvaMatPanelTitleComponent, OnemrvaMatPanelContentComponent, OnemrvaMatMessageBoxComponent],
  templateUrl: './error-display.component.html',
  styleUrl: './error-display.component.scss'
})
export class ErrorDisplayComponent {
  @Input() task!: any;

  public imgUrl: String = "";

  constructor(
              private cdn: OnemRvaCDNService) {
    this.imgUrl = this.cdn.getImg("ori/alone.svg");
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes["task"] && this.task) {
      this.processLink = FormUtilsService.getWaveProcessUrl(this.task);
    }
  }

  @Input() message!: string;
  processLink: string = "";
  protected readonly FormUtilsService = FormUtilsService;
}
