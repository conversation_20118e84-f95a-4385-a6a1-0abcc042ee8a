package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainModeOfPayment
import be.fgov.onerva.cu.backend.adapter.out.mapper.toModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ModeOfPaymentRepository
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

@Service
@Transactional
class ModeOfPaymentPersistenceAdapter(
    val changePersonalDataRepository: ChangePersonalDataRepository,
    val modeOfPaymentRepository: ModeOfPaymentRepository,
) : ModeOfPaymentPort {
    val log = logger;

    @LogMethodCall
    override fun getModeOfPayment(requestId: UUID): ModeOfPayment? {
        changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        return modeOfPaymentRepository.findByRequestId(requestId)?.toDomainModeOfPayment()
    }

    @LogMethodCall
    override fun persistModeOfPayment(requestId: UUID, @SensitiveParam modeOfPayment: ModeOfPayment) {
        val changePersonalData = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")

        var modeOfPaymentEntity = changePersonalData.modeOfPayment
        if (modeOfPaymentEntity == null) {
            modeOfPaymentEntity = modeOfPayment.toModeOfPaymentEntity(changePersonalData, UpdateStatus.EDITED)
            modeOfPaymentRepository.save(modeOfPaymentEntity)
        } else {
            modeOfPaymentEntity.apply {
                otherPersonName = modeOfPayment.otherPersonName
                iban = modeOfPayment.iban
                bic = modeOfPayment.bic
                validFrom = modeOfPayment.validFrom
                updateStatus = UpdateStatus.EDITED
            }
        }
    }

    override fun getLatestRevision(requestId: UUID): Int {
        val id = modeOfPaymentRepository.findByRequestId(requestId)?.id
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
        return modeOfPaymentRepository.findLastChangeRevision(id).getOrNull()?.revisionNumber?.getOrNull() ?: 0
    }

    override fun getModeOfPaymentForRevision(requestId: UUID, revision: Int): ModeOfPayment {
        val id = getEntityId(requestId)
        return modeOfPaymentRepository.findRevision(id, revision).orElseThrow {
            InvalidRequestIdException("Revision not found for request $requestId and revision $revision")
        }.entity.toDomainModeOfPayment()
    }

    override fun getEntityId(requestId: UUID): UUID {
        return modeOfPaymentRepository.findByRequestId(requestId)?.id
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")
    }

    override fun patchCurrentDataWithRevision(requestId: UUID, revision: Int) {
        log.info("Patching current data for request ID: $requestId with the latest revision data")

        val currentData = modeOfPaymentRepository.findByRequestId(requestId)
        if (currentData == null) {
            throw InvalidRequestIdException("Request ID not found: $requestId")
        }
        val latestRevision = modeOfPaymentRepository.findRevision(currentData.id, revision)

        currentData.apply {
            this.otherPersonName = latestRevision.get().entity.otherPersonName
            this.iban = latestRevision.get().entity.iban
            this.bic = latestRevision.get().entity.bic
            this.validFrom = latestRevision.get().entity.validFrom
            this.updateStatus = UpdateStatus.EDITED
        }

        log.info("Patching complete. Updated mode of payment data")

        modeOfPaymentRepository.save(currentData)
    }
}