package be.fgov.onerva.cu.bff.lookup

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.groups.Tuple.tuple
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.api.LookupApi
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.LookupSimpleItemizedDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class LookupServiceImplTest {

    @MockK
    private lateinit var lookupApi: LookupApi

    @InjectMockKs
    private lateinit var lookupService: LookupServiceImpl

    @Test
    fun `init should populate caches on startup`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgium", "België", "1"),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4"),
            createLookupDTO(3, "LU", "Lux", "3", "Lux", LocalDate.of(2002, 1, 1))
        )
        val nationalityDTOs = listOf(
            createLookupDTO(1, "BE", "Belgian", "Belgisch", "1"),
            createLookupDTO(2, "FR", "French", "Frans", "4"),
            createLookupDTO(3, "LU", "Luxembourgeois", "Lux", "3", LocalDate.of(2002, 1, 1))
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns nationalityDTOs

        // When
        lookupService.init()

        // Then
        verify(exactly = 1) { lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME) }
        verify(exactly = 1) { lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME) }
    }

    @Test
    fun `lookupCountry should return all countries from cache`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgium", "België", "1"),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCountry(null)

        // Then
        assertThat(result)
            .hasSize(2)
            .extracting("code", "descFr", "descNl")
            .containsExactlyInAnyOrder(
                tuple("BE", "Belgium", "België"),
                tuple("FR", "France", "Frankrijk")
            )
    }

    @Test
    fun `lookupNationality should return all nationalities from cache`() {
        // Given
        val nationalityDTOs = listOf(
            createLookupDTO(1, "BE", "Belgian", "Belgisch", "1"),
            createLookupDTO(2, "FR", "French", "Frans", "4")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns nationalityDTOs

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupNationality(null)

        // Then
        assertThat(result)
            .hasSize(2)
            .extracting("code", "descFr", "descNl")
            .containsExactlyInAnyOrder(
                tuple("BE", "Belgian", "Belgisch"),
                tuple("FR", "French", "Frans")
            )
    }

    @Test
    fun `updateFromLookupApi should filter out entries with endDate`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgium", "België", "1", null),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4", LocalDate.now()),
            createLookupDTO(3, "NL", "Netherlands", "Nederland", "2", null)
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCountry(null)

        // Then
        assertThat(result)
            .hasSize(2)
            .extracting("code", "descFr", "descNl")
            .containsExactlyInAnyOrder(
                tuple("BE", "Belgium", "België"),
                tuple("NL", "Netherlands", "Nederland")
            )
    }

    @Test
    fun `lookupCountry should populate cache if empty`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgium", "België", "1"),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()

        // When
        val result = lookupService.lookupCountry(null)

        // Then
        assertThat(result).hasSize(2)
        verify(exactly = 1) { lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME) }
    }

    private fun createLookupDTO(
        id: Int,
        code: String,
        descFr: String,
        descNl: String,
        onemCountryCode: String?,
        endDate: LocalDate? = null,
    ): LookupSimpleItemizedDTO {
        return LookupSimpleItemizedDTO().apply {
            setId(id)
            setCode(code)
            setDescFr(descFr)
            setDescNl(descNl)
            setEndDate(endDate)
            singlePropertiesMap(mapOf("countryCode" to onemCountryCode))
        }
    }

    @Test
    fun `updateCity should handle valid city lookups`() {
        // Given
        val lookup1 = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Bruxelles")
            setDescNl("Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "1000",
                    "code.code" to "BRU"
                )
            )
        }

        val lookup2 = LookupSimpleItemizedDTO().apply {
            setId(2)
            setDescFr("Anvers")
            setDescNl("Antwerpen")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "2000",
                    "code.code" to "ANT"
                )
            )
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(lookup1, lookup2)

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCity(null)

        // Then
        assertThat(result)
            .hasSize(2)
            .extracting("nisCode", "code", "descFr", "descNl")
            .containsExactlyInAnyOrder(
                tuple("1000", "BRU", "Bruxelles", "Brussel"),
                tuple("2000", "ANT", "Anvers", "Antwerpen")
            )
    }

    @Test
    fun `updateCity should keep most recent entry for same nisCode`() {
        // Given
        val olderLookup = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Old Bruxelles")
            setDescNl("Old Brussel")
            setBeginDate(LocalDate.of(2023, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "1000",
                    "code.code" to "BRU"
                )
            )
        }

        val newerLookup = LookupSimpleItemizedDTO().apply {
            setId(2)
            setDescFr("New Bruxelles")
            setDescNl("New Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "1000",
                    "code.code" to "BRU"
                )
            )
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(olderLookup, newerLookup)

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCity(null)

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("nisCode", "code", "descFr", "descNl", "beginDate")
            .containsExactly(
                tuple("1000", "BRU", "New Bruxelles", "New Brussel", LocalDate.of(2024, 1, 1))
            )
    }

    @Test
    fun `updateCity should filter out entries with endDate`() {
        // Given
        val activeCity = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Bruxelles")
            setDescNl("Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "1000",
                    "code.code" to "BRU"
                )
            )
        }

        val inactiveCity = LookupSimpleItemizedDTO().apply {
            setId(2)
            setDescFr("Anvers")
            setDescNl("Antwerpen")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(LocalDate.of(2024, 12, 31))
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "2000",
                    "code.code" to "ANT"
                )
            )
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(activeCity, inactiveCity)

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCity(null)

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("nisCode", "code", "descFr", "descNl")
            .containsExactly(
                tuple("1000", "BRU", "Bruxelles", "Brussel")
            )
    }

    @Test
    fun `updateCity should throw InvalidLookupException when codeMultiple is not a map`() {
        // Given
        val invalidLookup = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Bruxelles")
            setDescNl("Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple("invalid")
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(invalidLookup)

        // When/Then
        lookupService.updateFromLookupApi()
        // Error is logged
    }

    @Test
    fun `updateCity should throw InvalidLookupException when nisCode is missing`() {
        // Given
        val invalidLookup = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Bruxelles")
            setDescNl("Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.code" to "BRU"
                )
            )
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(invalidLookup)

        // When/Then
        lookupService.updateFromLookupApi()
        // Error is logged
    }

    @Test
    fun `lookupCountry should filter by French description`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgique", "België", "1"),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4"),
            createLookupDTO(3, "DE", "Allemagne", "Duitsland", "5")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCountry("fra")

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("code", "descFr", "descNl")
            .containsExactly(
                tuple("FR", "France", "Frankrijk")
            )
    }

    @Test
    fun `lookupCountry should filter by Dutch description`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgique", "België", "1"),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4"),
            createLookupDTO(3, "DE", "Allemagne", "Duitsland", "5")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCountry("duits")

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("code", "descFr", "descNl")
            .containsExactly(
                tuple("DE", "Allemagne", "Duitsland")
            )
    }

    @Test
    fun `lookupCountry should filter by code`() {
        // Given
        val countryDTOs = listOf(
            createLookupDTO(1, "BE", "Belgique", "België", "1"),
            createLookupDTO(2, "FR", "France", "Frankrijk", "4"),
            createLookupDTO(3, "DE", "Allemagne", "Duitsland", "5")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns countryDTOs

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCountry("BE")

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("code", "descFr", "descNl")
            .containsExactly(
                tuple("BE", "Belgique", "België")
            )
    }

    @Test
    fun `lookupNationality should filter by search query`() {
        // Given
        val nationalityDTOs = listOf(
            createLookupDTO(1, "BE", "Belge", "Belgisch", "1"),
            createLookupDTO(2, "FR", "Français", "Frans", "4"),
            createLookupDTO(3, "DE", "Allemand", "Duits", "5")
        )

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns nationalityDTOs

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupNationality("fra")

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("code", "descFr", "descNl")
            .containsExactly(
                tuple("FR", "Français", "Frans")
            )
    }

    @Test
    fun `lookupCity should filter by search query`() {
        // Given
        val city1 = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Bruxelles")
            setDescNl("Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "1000",
                    "code.code" to "BRU"
                )
            )
        }

        val city2 = LookupSimpleItemizedDTO().apply {
            setId(2)
            setDescFr("Bruges")
            setDescNl("Brugge")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "8000",
                    "code.code" to "BRG"
                )
            )
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(city1, city2)

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCity("ssel")

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("nisCode", "code", "descFr", "descNl")
            .containsExactly(
                tuple("1000", "BRU", "Bruxelles", "Brussel")
            )
    }

    @Test
    fun `lookupCity should handle case insensitive search`() {
        // Given
        val city1 = LookupSimpleItemizedDTO().apply {
            setId(1)
            setDescFr("Bruxelles")
            setDescNl("Brussel")
            setBeginDate(LocalDate.of(2024, 1, 1))
            setEndDate(null)
            setCodeMultiple(
                mapOf(
                    "code.nisCode" to "1000",
                    "code.code" to "BRU"
                )
            )
        }

        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_ONEM_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.COUNTRY_NATIONALITY_BCSS_CLASS_NAME)
        } returns emptyList()
        every {
            lookupApi.lookupGetAllLookupsGet(lookupService.INS_POST_CLASS_NAME)
        } returns listOf(city1)

        // When
        lookupService.updateFromLookupApi()
        val result = lookupService.lookupCity("BRUX")

        // Then
        assertThat(result)
            .hasSize(1)
            .extracting("nisCode", "code", "descFr", "descNl")
            .containsExactly(
                tuple("1000", "BRU", "Bruxelles", "Brussel")
            )
    }
}