Feature: Citizen Information

  Background:
    Given the database is clean
    And test data for change personal data is loaded
    And I am authenticated as "test_user" with role "cu_role_user"

  Scenario: Select source for citizen information
    Given a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    And a citizen with SSIN "18031307065" exists with personal information
    When I select the following sources for citizen information:
      | fieldName   | source |
      | birthDate   | C1     |
      | nationality | ONEM   |
      | address     | ONEM   |
    Then the citizen information response status should be 204
    And the citizen information should be updated with:
      | birth_date       | 1975-06-09  |
      | nationality_code | 151         |
      | street           | Main Street |
      | box_number       | Box A       |
      | house_number     | 123         |
      | zip_code         | 1000        |
      | city             | Brussels    |

  Scenario: Update citizen information with valid data
    Given nationality lookups are configured
    And a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    When I update the citizen information with:
      | birthDate        | 2006-10-12  |
      | nationalityCode  | 111         |
      | street           | Main Street |
      | houseNumber      | 123         |
      | boxNumber        | A           |
      | zipCode          | 1000        |
      | city             | Brussels    |
      | countryCode      | 150         |
      | addressValidFrom | 2025-09-01  |
    Then the response status should be 204
    And the database should be updated with the citizen information

  Scenario: Update citizen information with invalid nationality
    Given nationality lookups are configured
    And a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    When I update the citizen information with:
      | birthDate        | 2006-10-12  |
      | nationality_code | 999         |
      | street           | Main Street |
      | houseNumber      | 123         |
      | boxNumber        | A           |
      | zipCode          | 1000        |
      | city             | Brussels    |
      | country_code     | 150         |
      | addressValidFrom | 2025-09-01  |
    Then the response status should be 400

  Scenario: Update citizen information with blank street
    Given nationality lookups are configured
    And a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    When I update the citizen information with:
      | birthDate       | 2006-10-12 |
      | nationalityCode | 111        |
      | street          |            |
      | houseNumber     | 123        |
      | boxNumber       | A          |
      | zipCode         | 1000       |
      | city            | Brussels   |
      | countryCode     | 150        |
      | addressValidFrom | 2025-09-01  |
    Then the response status should be 400

  Scenario: Update citizen information with future birth date
    Given nationality lookups are configured
    And a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    When I update the citizen information with:
      | birthDate       | 2026-01-01  |
      | nationalityCode | 111         |
      | street          | Main Street |
      | houseNumber     | 123         |
      | boxNumber       | A           |
      | zipCode         | 1000        |
      | city            | Brussels    |
      | countryCode     | 150         |
      | addressValidFrom | 2025-09-01  |
    Then the response status should be 400