import {Component, EventEmitter, Input, Output} from "@angular/core";
import {<PERSON><PERSON>utton} from "@angular/material/button";
import {TranslatePipe} from "@ngx-translate/core";
import {RedirectHandlerService} from "../../../../http/redirect-handler.service";
import {FormUtilsService} from "../../../../services/form-utils.service";

@Component({
    selector: "lib-regis-check",
    imports: [
        TranslatePipe,
        MatButton,
    ],
    templateUrl: "./regis-check.component.html",
    styleUrl: "./regis-check.component.scss",
    standalone: true,
})
export class RegisCheckComponent {

    @Input() requestId: string | undefined;
    @Input() language!: string;
    @Input() taskStatus!: string;
    @Input() task!: any;
    @Output() verificationChange = new EventEmitter<boolean>();

    isVerified = false;

    constructor(readonly redirectHandlerService: RedirectHandlerService) {
    }

    openRegis() {
        if (!this.requestId) {
            console.error("Request ID is missing");
            return;
        }
        this.redirectHandlerService.getRegisRedirectUrl(this.requestId, this.language)
            .subscribe({
                next: (url) => {
                    if (url) {
                        window.open(url, "_blank");
                    }
                },
                error: (error) => {
                    console.error("Failed to get Regis URL", error);
                },
            });
    }

    protected isFormClosedOrWaiting(): boolean {
        return FormUtilsService.isClosedOrWaiting(this.taskStatus, this.task);
    }

    onVerificationChange(event: any): void {
        this.isVerified = event.target.checked;
        this.verificationChange.emit(this.isVerified);
    }

}
