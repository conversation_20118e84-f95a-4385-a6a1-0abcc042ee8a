package be.fgov.onerva.cu.backend.taskapi

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.rest.client.wo.smals.api.TaskApi
import be.fgov.onerva.cu.backend.rest.client.wo.smals.rest.model.TaskDTO
import be.fgov.onerva.cu.backend.rest.client.wo.smals.rest.model.TechnicalInformationDTO
import be.fgov.onerva.cu.common.utils.logger

@Service
class TaskApiServiceImpl(val taskApi:TaskApi): TaskApiService {
    private val log = logger

    override fun softDeleteTask(taskId: Long, reason: String?): TaskDTO? {
        return try {
            val technicalInformation = TaskDTO().technicalInformation(
                TechnicalInformationDTO().technicalStatus(TechnicalInformationDTO.TechnicalStatusEnum.DELETED)
            )

           taskApi.patchTask(taskId, null, technicalInformation)

        } catch (e: RuntimeException) {
            log.error("Could not soft delete the task with id {}.", taskId, e)
            null
        }
    }
}