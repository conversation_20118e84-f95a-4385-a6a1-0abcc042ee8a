package be.fgov.onerva.cu.backend.application.validation

import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import org.springframework.stereotype.Component
import be.fgov.onerva.cu.backend.application.port.out.LookupPort
import be.fgov.onerva.cu.backend.lookup.LookupService

/**
 * Validates that a string value represents a valid nationality or country code.
 * The validator uses [LookupService] to check if the provided code exists in the system.
 *
 * @property lookupService Service used to retrieve valid nationality/country codes
 */
@Component
class NationalityAndCountryCodeValidator(val lookupPort: LookupPort) :
    ConstraintValidator<ValidNationalityOrCountryCode, Int> {

    /**
     * Validates if the provided value is a valid nationality/country code.
     *
     * @param value The code to validate
     * @param context The constraint validator context (unused)
     * @return true if the code is valid, false otherwise
     */
    override fun isValid(value: Int?, context: ConstraintValidatorContext?): Bo<PERSON>an {
        if (value == null) {
            return false
        }
        return lookupPort.validateCountryOrNationality(value)
    }
}