import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { FormBuilder } from '@angular/forms';

import { CdfFormService } from './cdf-form.service';

describe('CdfFormService (Jest)', () => {
  let service: CdfFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      providers: [FormBuilder, CdfFormService],
    });
    service = TestBed.inject(CdfFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('createEmptyForm', () => {
    it('should create a FormGroup with the expected child FormGroups', () => {
      const form = service.createEmptyForm();
      expect(form).toBeTruthy();
      expect(form.get('requestPanel')).toBeTruthy();
      expect(form.get('employeePanel')).toBeTruthy();
      expect(form.get('bankPanel')).toBeTruthy();
      expect(form.get('syndicatePanel')).toBeTruthy();
    });

    it('should have the correct controls in the requestPanel', () => {
      const form = service.createEmptyForm();
      const requestPanel = form.get('requestPanel');
      expect(requestPanel?.get('startDate')).toBeTruthy();
    });

    it('should have the correct controls in the employeePanel', () => {
      const form = service.createEmptyForm();
      const employeePanel = form.get('employeePanel');
      expect(employeePanel?.get('niss')).toBeTruthy();
      expect(employeePanel?.get('birthDate')).toBeTruthy();
      expect(employeePanel?.get('lastName')).toBeTruthy();
      expect(employeePanel?.get('firstName')).toBeTruthy();
      expect(employeePanel?.get('nationalityCode')).toBeTruthy();
      expect(employeePanel?.get('searchedNationality')).toBeTruthy();
      expect(employeePanel?.get('countryCode')).toBeTruthy();
      expect(employeePanel?.get('searchedCountry')).toBeTruthy();
      expect(employeePanel?.get('street')).toBeTruthy();
      expect(employeePanel?.get('streetNbr')).toBeTruthy();
      expect(employeePanel?.get('streetBox')).toBeTruthy();
      expect(employeePanel?.get('postCode')).toBeTruthy();
      expect(employeePanel?.get('city')).toBeTruthy();
      expect(employeePanel?.get('addressFromDate')).toBeTruthy();
    });

    it('should have the correct controls in the bankPanel', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');
      expect(bankPanel?.get('isMyBankAccount')).toBeTruthy();
      expect(bankPanel?.get('otherPersonName')).toBeTruthy();
      expect(bankPanel?.get('iban')).toBeTruthy();
      expect(bankPanel?.get('bic')).toBeTruthy();
      expect(bankPanel?.get('bankFromDate')).toBeTruthy();
    });

    it('should have the correct controls in the syndicatePanel', () => {
      const form = service.createEmptyForm();
      const syndicatePanel = form.get('syndicatePanel');
      expect(syndicatePanel?.get('syndicateContribution')).toBeTruthy();
      expect(syndicatePanel?.get('contributionDeductionFromTheMonth')).toBeTruthy();
      expect(syndicatePanel?.get('stopContributionDeductionFromTheMonth')).toBeTruthy();
    });
  });

  describe('extractCitizenInformation', () => {
    it('should return null if employeePanel does not exist', () => {
      const form = service.createEmptyForm();
      form.removeControl('employeePanel');
      const result = service.extractCitizenInformation(form);
      expect(result).toBeNull();
    });

    it('should extract citizen information correctly', () => {
      const form = service.createEmptyForm();
      const employeePanel = form.get('employeePanel');

      employeePanel?.get('birthDate')?.setValue('1990-01-01');
      employeePanel?.get('nationalityCode')?.setValue({ code: 150, label: 'Belgium' });
      employeePanel?.get('countryCode')?.setValue({ code: 150, label: 'Belgium' });
      employeePanel?.get('street')?.setValue('Main Street');
      employeePanel?.get('streetNbr')?.setValue('123');
      employeePanel?.get('streetBox')?.setValue('A');
      employeePanel?.get('postCode')?.setValue('1000');
      employeePanel?.get('city')?.setValue('Brussels');
      employeePanel?.get('addressFromDate')?.setValue('2023-01-01');

      const result = service.extractCitizenInformation(form);
      expect(result).toEqual({
        birthDate: '1990-01-01',
        nationalityCode: 150,
        address: {
          countryCode: 150,
          street: 'Main Street',
          houseNumber: '123',
          boxNumber: 'A',
          zipCode: '1000',
          city: 'Brussels',
          validFrom: '2023-01-01',
        },
      });
    });

    it('should handle empty addressFromDate', () => {
      const form = service.createEmptyForm();
      const employeePanel = form.get('employeePanel');

      employeePanel?.get('birthDate')?.setValue('1990-01-01');
      employeePanel?.get('nationalityCode')?.setValue({ code: 150, label: 'Belgium' });
      employeePanel?.get('countryCode')?.setValue({ code: 150, label: 'Belgium' });
      employeePanel?.get('street')?.setValue('Main Street');
      employeePanel?.get('streetNbr')?.setValue('123');
      employeePanel?.get('streetBox')?.setValue('A');
      employeePanel?.get('postCode')?.setValue('1000');
      employeePanel?.get('city')?.setValue('Brussels');
      // Not setting addressFromDate

      const result = service.extractCitizenInformation(form);
      expect(result).toEqual({
        birthDate: '1990-01-01',
        nationalityCode: 150,
        address: {
          countryCode: 150,
          street: 'Main Street',
          houseNumber: '123',
          boxNumber: 'A',
          zipCode: '1000',
          city: 'Brussels',
          validFrom: '',
        },
      });
    });
  });

  describe('extractPaymentInformation', () => {
    it('should return undefined if bankPanel does not exist', () => {
      const form = service.createEmptyForm();
      form.removeControl('bankPanel');
      const result = service.extractPaymentInformation(form);
      expect(result).toBeUndefined();
    });

    it('should extract payment information with otherPersonName correctly', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');

      bankPanel?.get('isMyBankAccount')?.setValue('bank_account_for_other_person_name');
      bankPanel?.get('otherPersonName')?.setValue('John Doe');
      bankPanel?.get('iban')?.setValue('BE1234567890');
      bankPanel?.get('bic')?.setValue('');
      bankPanel?.get('bankFromDate')?.setValue('2023-01-01');

      const result = service.extractPaymentInformation(form);
      expect(result).toEqual({
        otherPersonName: 'John Doe',
        iban: 'BE1234567890',
        bic: undefined,
        validFrom: '2023-01-01',
      });
    });

    it('should extract payment information for Belgian IBAN correctly', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');

      bankPanel?.get('isMyBankAccount')?.setValue('is_my_bank_account');
      bankPanel?.get('iban')?.setValue('BE68 5390 0754 7034');
      bankPanel?.get('bic')?.setValue('IGNORED');  // BIC should be ignored for Belgian IBANs
      bankPanel?.get('bankFromDate')?.setValue('2023-01-01');

      const result = service.extractPaymentInformation(form);
      expect(result).toEqual({
        otherPersonName: undefined,
        iban: '****************',
        bic: undefined,  // BIC is undefined for Belgian IBANs
        validFrom: '2023-01-01',
      });
    });

    it('should extract payment information for foreign IBAN correctly', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');

      bankPanel?.get('isMyBankAccount')?.setValue('is_my_bank_account');
      bankPanel?.get('iban')?.setValue('FR76 3000 6000 0112 3456 7890 189');
      bankPanel?.get('bic')?.setValue('AGRIFRPP');
      bankPanel?.get('bankFromDate')?.setValue('2023-01-01');

      const result = service.extractPaymentInformation(form);
      expect(result).toEqual({
        otherPersonName: undefined,
        iban: '***************************',
        bic: 'AGRIFRPP',  // BIC is included for non-Belgian IBANs
        validFrom: '2023-01-01',
      });
    });

    it('should handle empty bankFromDate', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');

      bankPanel?.get('isMyBankAccount')?.setValue('is_my_bank_account');
      bankPanel?.get('iban')?.setValue('BE68 5390 0754 7034');
      // Not setting bankFromDate

      const result = service.extractPaymentInformation(form);
      expect(result).toEqual({
        otherPersonName: undefined,
        iban: '****************',
        bic: undefined,
        validFrom: '',
      });
    });

    it('should handle IBANs with spaces correctly', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');

      bankPanel?.get('isMyBankAccount')?.setValue('is_my_bank_account');
      bankPanel?.get('iban')?.setValue('BE68 5390 0754 7034');
      bankPanel?.get('bankFromDate')?.setValue('2023-01-01');

      const result = service.extractPaymentInformation(form);
      expect(result?.iban).toBe('****************');
    });

    it('should handle case-insensitive IBAN country codes', () => {
      const form = service.createEmptyForm();
      const bankPanel = form.get('bankPanel');

      bankPanel?.get('isMyBankAccount')?.setValue('is_my_bank_account');
      bankPanel?.get('iban')?.setValue('be68 5390 0754 7034');  // lowercase
      bankPanel?.get('bic')?.setValue('SHOULD_BE_IGNORED');
      bankPanel?.get('bankFromDate')?.setValue('2023-01-01');

      const result = service.extractPaymentInformation(form);
      expect(result?.bic).toBeUndefined();  // BIC should be undefined for Belgian IBAN
    });
  });

  describe('extractUnionContribution', () => {
    it('should return null if syndicatePanel does not exist', () => {
      const form = service.createEmptyForm();
      form.removeControl('syndicatePanel');
      const result = service.extractUnionContribution(form);
      expect(result).toBeNull();
    });

    it('should extract contribution data for "CONTRIBUTION"', () => {
      const form = service.createEmptyForm();
      const syndicatePanel = form.get('syndicatePanel');
      syndicatePanel?.get('syndicateContribution')?.setValue('CONTRIBUTION');
      syndicatePanel?.get('contributionDeductionFromTheMonth')?.setValue(new Date('2023-01-01'));

      const result = service.extractUnionContribution(form);
      expect(result).toEqual({
        authorized: true,
        effectiveDate: 'Sun Jan 01',
      });
    });

    it('should extract contribution data for "STOP_CONTRIBUTION"', () => {
      const form = service.createEmptyForm();
      const syndicatePanel = form.get('syndicatePanel');
      syndicatePanel?.get('syndicateContribution')?.setValue('STOP_CONTRIBUTION');
      syndicatePanel
          ?.get('stopContributionDeductionFromTheMonth')
          ?.setValue(new Date('2023-02-01'));

      const result = service.extractUnionContribution(form);
      expect(result).toEqual({
        authorized: false,
        effectiveDate: 'Wed Feb 01',
      });
    });

    it('should extract contribution data for "CONTRIBUTION_NOT_AUTHORIZED"', () => {
      const form = service.createEmptyForm();
      const syndicatePanel = form.get('syndicatePanel');
      syndicatePanel?.get('syndicateContribution')?.setValue('CONTRIBUTION_NOT_AUTHORIZED');

      const result = service.extractUnionContribution(form);
      expect(result).toEqual({
        authorized: null,
        effectiveDate: '',
      });
    });
  });

  describe('extractBasicInformation', () => {
    it('should return null if requestPanel does not exist', () => {
      const form = service.createEmptyForm();
      form.removeControl('requestPanel');
      const result = service.extractBasicInformation(form);
      expect(result).toBeNull();
    });

    it('should extract basic information correctly', () => {
      const form = service.createEmptyForm();
      const requestPanel = form.get('requestPanel');
      requestPanel?.get('startDate')?.setValue('2023-03-15');

      const result = service.extractBasicInformation(form);
      expect(result).toEqual({
        requestDate: '2023-03-15',
      });
    });
  });
});