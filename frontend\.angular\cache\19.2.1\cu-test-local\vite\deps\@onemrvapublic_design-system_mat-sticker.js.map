{"version": 3, "sources": ["../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-sticker.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { HostBinding, Input, Component, NgModule } from '@angular/core';\nimport { OnemrvaMatColor } from '@onemrvapublic/design-system/utils';\nconst _c0 = [\"*\"];\nclass OnemrvaMatStickerComponent {\n  constructor() {\n    this.color = OnemrvaMatColor.PRIMARY;\n  }\n  /** @hidden @internal */\n  get _isPrimary() {\n    return this.color === OnemrvaMatColor.PRIMARY;\n  }\n  /** @hidden @internal */\n  get _isAccent() {\n    return this.color === OnemrvaMatColor.ACCENT;\n  }\n  /** @hidden @internal */\n  get _isError() {\n    return this.color === OnemrvaMatColor.ERROR;\n  }\n  /** @hidden @internal */\n  get _isWarn() {\n    return this.color === OnemrvaMatColor.WARN;\n  }\n  /** @hidden @internal */\n  get _isSuccess() {\n    return this.color === OnemrvaMatColor.SUCCESS;\n  }\n  /** @hidden @internal */\n  get _isInfo() {\n    return this.color === OnemrvaMatColor.INFO;\n  }\n  /** @hidden @internal */\n  get _isGrayscale() {\n    return this.color === OnemrvaMatColor.GRAYSCALE;\n  }\n  static {\n    this.ɵfac = function OnemrvaMatStickerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatStickerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OnemrvaMatStickerComponent,\n      selectors: [[\"onemrva-mat-sticker\"]],\n      hostVars: 14,\n      hostBindings: function OnemrvaMatStickerComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-primary\", ctx._isPrimary)(\"mat-accent\", ctx._isAccent)(\"mat-error\", ctx._isError)(\"mat-warn\", ctx._isWarn)(\"mat-success\", ctx._isSuccess)(\"mat-info\", ctx._isInfo)(\"mat-grayscale\", ctx._isGrayscale);\n        }\n      },\n      inputs: {\n        color: \"color\"\n      },\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function OnemrvaMatStickerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatStickerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'onemrva-mat-sticker',\n      template: ` <ng-content></ng-content>`,\n      standalone: true\n    }]\n  }], null, {\n    color: [{\n      type: Input\n    }],\n    _isPrimary: [{\n      type: HostBinding,\n      args: ['class.mat-primary']\n    }],\n    _isAccent: [{\n      type: HostBinding,\n      args: ['class.mat-accent']\n    }],\n    _isError: [{\n      type: HostBinding,\n      args: ['class.mat-error']\n    }],\n    _isWarn: [{\n      type: HostBinding,\n      args: ['class.mat-warn']\n    }],\n    _isSuccess: [{\n      type: HostBinding,\n      args: ['class.mat-success']\n    }],\n    _isInfo: [{\n      type: HostBinding,\n      args: ['class.mat-info']\n    }],\n    _isGrayscale: [{\n      type: HostBinding,\n      args: ['class.mat-grayscale']\n    }]\n  });\n})();\nclass OnemrvaMatStickerModule {\n  static {\n    this.ɵfac = function OnemrvaMatStickerModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatStickerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaMatStickerModule,\n      imports: [OnemrvaMatStickerComponent],\n      exports: [OnemrvaMatStickerComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatStickerModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      imports: [OnemrvaMatStickerComponent],\n      exports: [OnemrvaMatStickerComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of mat-sticker\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaMatStickerComponent, OnemrvaMatStickerModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,QAAQ,gBAAgB;AAAA,EAC/B;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,UAAU;AAAA,MACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,eAAe,IAAI,UAAU,EAAE,cAAc,IAAI,SAAS,EAAE,aAAa,IAAI,QAAQ,EAAE,YAAY,IAAI,OAAO,EAAE,eAAe,IAAI,UAAU,EAAE,YAAY,IAAI,OAAO,EAAE,iBAAiB,IAAI,YAAY;AAAA,QAC1N;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,0BAA0B;AAAA,MACpC,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC;AAAA,MACf,SAAS,CAAC,0BAA0B;AAAA,MACpC,SAAS,CAAC,0BAA0B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}