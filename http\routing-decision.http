### Get basic request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/routing-decision
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### PUT Routing Decisions - All OK (all false)
PUT {{backend-url}}/api/requests/{{latestRequestId}}/routing-decision
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

[
  {
    "type": "CITIZEN_OVER_65_YEARS_OLD",
    "value": false
  },
  {
    "type": "RELEVANT_TO_PORT_WORKER",
    "value": false
  },
  {
    "type": "NON_BELGIAN_RESIDENT",
    "value": false
  },
  {
    "type": "TRANSFER_BETWEEN_OP_OR_OC",
    "value": false
  },
  {
    "type": "REQUEST_FOR_ECONOMIC_REASON",
    "value": false
  },
  {
    "type": "RELEVANT_TO_APPRENTICESHIP",
    "value": false
  },
  {
    "type": "CASE_OF_IMPULSION",
    "value": false
  }
]

### PUT Routing Decisions - NOT All OK (one is true)
PUT {{backend-url}}/api/requests/{{latestRequestId}}/routing-decision
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

[
  {
    "type": "CITIZEN_OVER_65_YEARS_OLD",
    "value": false
  },
  {
    "type": "RELEVANT_TO_PORT_WORKER",
    "value": false
  },
  {
    "type": "NON_BELGIAN_RESIDENT",
    "value": true
  },
  {
    "type": "TRANSFER_BETWEEN_OP_OR_OC",
    "value": false
  },
  {
    "type": "REQUEST_FOR_ECONOMIC_REASON",
    "value": false
  },
  {
    "type": "RELEVANT_TO_APPRENTICESHIP",
    "value": false
  },
  {
    "type": "CASE_OF_IMPULSION",
    "value": false
  }
]


