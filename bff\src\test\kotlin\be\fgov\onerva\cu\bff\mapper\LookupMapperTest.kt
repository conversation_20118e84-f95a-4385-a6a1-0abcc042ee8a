package be.fgov.onerva.cu.bff.mapper

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import be.fgov.onerva.cu.bff.lookup.CountryDTO
import be.fgov.onerva.cu.bff.lookup.NationalityDTO

class LookupMapperTest {

    @Test
    fun `toCountryResponse should map CountryDTO correctly`() {
        // Given
        val countryDTO = CountryDTO(
            lookupId = 1,
            code = "BE",
            descFr = "Belgium",
            descNl = "België"
        )

        // When
        val response = countryDTO.toCountryResponse()

        // Then
        assertThat(response)
            .isNotNull
            .extracting("code", "descFr", "descNl")
            .containsExactly("BE", "Belgium", "België")
    }

    @Test
    fun `toNationalityResponse should map NationalityDTO correctly`() {
        // Given
        val nationalityDTO = NationalityDTO(
            lookupId = 1,
            code = "BE",
            descFr = "Belgian", descNl = "Belgisch", "1"
        )

        // When
        val response = nationalityDTO.toNationalityResponse()

        // Then
        assertThat(response)
            .isNotNull
            .extracting("code", "descFr", "descNl")
            .containsExactly("BE", "Belgian", "Belgisch")
    }

    @Test
    fun `toCountryResponse should handle empty strings`() {
        // Given
        val countryDTO = CountryDTO(
            lookupId = 1,
            code = "",
            descFr = "",
            descNl = "",
        )

        // When
        val response = countryDTO.toCountryResponse()

        // Then
        assertThat(response)
            .isNotNull
            .extracting("code", "descFr", "descNl")
            .containsExactly("", "", "")
    }

    @Test
    fun `toNationalityResponse should handle empty strings`() {
        // Given
        val nationalityDTO = NationalityDTO(
            lookupId = 1,
            code = "",
            descFr = "",
            descNl = "", "1"
        )

        // When
        val response = nationalityDTO.toNationalityResponse()

        // Then
        assertThat(response)
            .isNotNull
            .extracting("code", "descFr", "descNl")
            .containsExactly("", "", "")
    }
}