package be.fgov.onerva.cu.bff.mapper

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import be.fgov.onerva.cu.bff.lookup.CityDTO
import be.fgov.onerva.cu.bff.lookup.CountryDTO
import be.fgov.onerva.cu.bff.lookup.NationalityDTO
import be.fgov.onerva.cu.bff.model.Address
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import io.mockk.junit5.MockKExtension

@ExtendWith(MockKExtension::class)
class ControllerMapperTest {

    @Nested
    inner class CountryDTOToCountryResponseTest {

        @Test
        fun `should correctly map CountryDTO to CountryResponse`() {
            // Given
            val countryDTO = CountryDTO(
                lookupId = 7654,
                code = "BE",
                descFr = "Belgique",
                descNl = "België",
            )

            // When
            val result = countryDTO.toCountryResponse()

            // Then
            assertThat(result.code).isEqualTo("BE")
            assertThat(result.descFr).isEqualTo("Belgique")
            assertThat(result.descNl).isEqualTo("België")
        }

        @ParameterizedTest
        @CsvSource(
            "BE, Belgique, België",
            "FR, France, Frankrijk",
            "DE, Allemagne, Duitsland",
            "US, États-Unis, Verenigde Staten"
        )
        fun `should correctly map various CountryDTO instances to CountryResponse`(
            code: String,
            descFr: String,
            descNl: String,
        ) {
            // Given
            val countryDTO = CountryDTO(
                lookupId = 7654,
                code = code,
                descFr = descFr,
                descNl = descNl
            )

            // When
            val result = countryDTO.toCountryResponse()

            // Then
            assertThat(result.code).isEqualTo(code)
            assertThat(result.descFr).isEqualTo(descFr)
            assertThat(result.descNl).isEqualTo(descNl)
        }
    }

    @Nested
    inner class NationalityDTOToNationalityResponseTest {

        @Test
        fun `should correctly map NationalityDTO to NationalityResponse`() {
            // Given
            val nationalityDTO = NationalityDTO(
                lookupId = 7654,
                code = "BE",
                descFr = "Belge",
                descNl = "Belgisch", "1"
            )

            // When
            val result = nationalityDTO.toNationalityResponse()

            // Then
            assertThat(result.code).isEqualTo("BE")
            assertThat(result.descFr).isEqualTo("Belge")
            assertThat(result.descNl).isEqualTo("Belgisch")
        }

        @ParameterizedTest
        @CsvSource(
            "BE, Belge, Belgisch",
            "FR, Français, Frans",
            "DE, Allemand, Duits",
            "US, Américain, Amerikaans"
        )
        fun `should correctly map various NationalityDTO instances to NationalityResponse`(
            code: String,
            descFr: String,
            descNl: String,
        ) {
            // Given
            val nationalityDTO = NationalityDTO(
                lookupId = 7654,
                code = code,
                descFr = descFr,
                descNl = descNl, "1"
            )

            // When
            val result = nationalityDTO.toNationalityResponse()

            // Then
            assertThat(result.code).isEqualTo(code)
            assertThat(result.descFr).isEqualTo(descFr)
            assertThat(result.descNl).isEqualTo(descNl)
        }
    }

    @Nested
    inner class CityDTOToCityResponseTest {

        @Test
        fun `should correctly map CityDTO to CityResponse`() {
            // Given
            val cityDTO = CityDTO(
                lookupId = 7654,
                code = "1000",
                descFr = "Bruxelles",
                descNl = "Brussel",
                nisCode = "21001",
                beginDate = LocalDate.now().minusDays(10),
            )

            // When
            val result = cityDTO.toCityResponse()

            // Then
            assertThat(result.code).isEqualTo("1000")
            assertThat(result.descFr).isEqualTo("Bruxelles")
            assertThat(result.descNl).isEqualTo("Brussel")
            assertThat(result.nisCode).isEqualTo("21001")
        }

        @ParameterizedTest
        @CsvSource(
            "1000, Bruxelles, Brussel, 21001",
            "2000, Anvers, Antwerpen, 11001",
            "3000, Louvain, Leuven, 24062",
            "9000, Gand, Gent, 44021"
        )
        fun `should correctly map various CityDTO instances to CityResponse`(
            code: String,
            descFr: String,
            descNl: String,
            nisCode: String,
        ) {
            // Given
            val cityDTO = CityDTO(
                lookupId = 7654,
                code = code,
                descFr = descFr,
                descNl = descNl,
                nisCode = nisCode,
                beginDate = LocalDate.now().minusDays(10),
            )

            // When
            val result = cityDTO.toCityResponse()

            // Then
            assertThat(result.code).isEqualTo(code)
            assertThat(result.descFr).isEqualTo(descFr)
            assertThat(result.descNl).isEqualTo(descNl)
            assertThat(result.nisCode).isEqualTo(nisCode)
        }
    }

    @Nested
    inner class AddressToAddressNullableTest {

        @Test
        fun `should correctly map Address to AddressNullable`() {
            // Given
            val address = Address(
                street = "Rue de la Loi",
                houseNumber = "16",
                boxNumber = "A",
                countryCode = 150,
                city = "Bruxelles",
                zipCode = "1000"
            )

            // When
            val result = address.toAddressNullable()

            // Then
            assertThat(result.street).isEqualTo("Rue de la Loi")
            assertThat(result.houseNumber).isEqualTo("16")
            assertThat(result.boxNumber).isEqualTo("A")
            assertThat(result.countryCode).isEqualTo(150)
            assertThat(result.city).isEqualTo("Bruxelles")
            assertThat(result.zipCode).isEqualTo("1000")
        }

        @Test
        fun `should correctly map Address with null values to AddressNullable`() {
            // Given
            val address = Address(
                street = null,
                houseNumber = null,
                boxNumber = null,
                countryCode = null,
                city = null,
                zipCode = null
            )

            // When
            val result = address.toAddressNullable()

            // Then
            assertThat(result.street).isNull()
            assertThat(result.houseNumber).isNull()
            assertThat(result.boxNumber).isNull()
            assertThat(result.countryCode).isNull()
            assertThat(result.city).isNull()
            assertThat(result.zipCode).isNull()
        }
    }

    @Nested
    inner class CitizenInfoWithAddressToCitizenInformationDetailNullableResponseTest {

        @Test
        fun `should correctly map CitizenInfoWithAddress to CitizenInformationDetailNullableResponse`() {
            // Given
            val address = Address(
                street = "Rue de la Loi",
                houseNumber = "16",
                boxNumber = "A",
                countryCode = 150,
                city = "Bruxelles",
                zipCode = "1000"
            )

            val citizenInfo = CitizenInfoWithAddress(
                firstName = "John",
                lastName = "Doe",
                numbox = 123456,
                nationalityCode = 111,
                address = address,
                iban = "****************",
                bic = null,
                otherPersonName = null,
                paymentMode = 1,
                unionDue = null
            )

            // When
            val result = citizenInfo.toCitizenInformationDetailNullableResponse()

            // Then
            assertThat(result.birthDate).isNull()
            assertThat(result.nationalityCode).isEqualTo(111)
            assertThat(result.address).isNotNull()
            assertThat(result.address?.street).isEqualTo("Rue de la Loi")
            assertThat(result.address?.houseNumber).isEqualTo("16")
            assertThat(result.address?.boxNumber).isEqualTo("A")
            assertThat(result.address?.countryCode).isEqualTo(150)
            assertThat(result.address?.city).isEqualTo("Bruxelles")
            assertThat(result.address?.zipCode).isEqualTo("1000")
        }

        @Test
        fun `should correctly map CitizenInfoWithAddress with null nationality to CitizenInformationDetailNullableResponse`() {
            // Given
            val address = Address(
                street = "Rue de la Loi",
                houseNumber = "16",
                boxNumber = "A",
                countryCode = 150,
                city = "Bruxelles",
                zipCode = "1000"
            )

            val citizenInfo = CitizenInfoWithAddress(
                firstName = "John",
                lastName = "Doe",
                numbox = 123456,
                nationalityCode = null,
                address = address,
                iban = "****************",
                bic = null,
                otherPersonName = null,
                paymentMode = 1,
                unionDue = null
            )

            // When
            val result = citizenInfo.toCitizenInformationDetailNullableResponse()

            // Then
            assertThat(result.birthDate).isNull()
            assertThat(result.nationalityCode).isNull()
            assertThat(result.address).isNotNull()
        }
    }
}
