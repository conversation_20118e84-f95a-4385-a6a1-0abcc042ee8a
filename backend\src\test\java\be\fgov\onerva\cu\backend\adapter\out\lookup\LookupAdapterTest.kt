package be.fgov.onerva.cu.backend.adapter.out.lookup

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.out.external.lookup.LookupAdapter
import be.fgov.onerva.cu.backend.lookup.LookupService
import be.fgov.onerva.cu.backend.lookup.NationalityDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class LookupAdapterTest {

    @MockK
    private lateinit var lookupService: LookupService

    @InjectMockKs
    private lateinit var lookupAdapter: LookupAdapter

    @Test
    fun `validateCountryOrNationality should return true when code exists in nationality codes`() {
        // Given
        val validCode = 150
        val nationalityDTOs = listOf(
            NationalityDTO(
                lookupId = 1,
                code = "150",
                descFr = "Belge",
                descNl = "Belgisch",
                onemCountryCode = "1",
            ),
            NationalityDTO(
                lookupId = 2,
                code = "111",
                descFr = "Français",
                descNl = "Frans",
                onemCountryCode = "1",
            )
        )

        every { lookupService.lookupNationality(validCode.toString()) } returns nationalityDTOs

        // When
        val result = lookupAdapter.validateCountryOrNationality(validCode)

        // Then
        assertThat(result).isTrue()
        verify(exactly = 1) { lookupService.lookupNationality(validCode.toString()) }
    }

    @Test
    fun `validateCountryOrNationality should return false when code does not exist in nationality codes`() {
        // Given
        val invalidCode = 9999
        val nationalityDTOs = listOf(
            NationalityDTO(
                lookupId = 1,
                code = "150",
                descFr = "Belge",
                descNl = "Belgisch",
                onemCountryCode = "1",
            )
        )

        every { lookupService.lookupNationality(invalidCode.toString()) } returns nationalityDTOs

        // When
        val result = lookupAdapter.validateCountryOrNationality(invalidCode)

        // Then
        assertThat(result).isFalse()
        verify(exactly = 1) { lookupService.lookupNationality(invalidCode.toString()) }
    }

    @Test
    fun `validateCountryOrNationality should return false when nationality list is empty`() {
        // Given
        val code = 150
        every { lookupService.lookupNationality(code.toString()) } returns emptyList()

        // When
        val result = lookupAdapter.validateCountryOrNationality(code)

        // Then
        assertThat(result).isFalse()
        verify(exactly = 1) { lookupService.lookupNationality(code.toString()) }
    }
}