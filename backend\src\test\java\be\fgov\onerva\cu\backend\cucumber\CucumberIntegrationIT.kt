package be.fgov.onerva.cu.backend.cucumber

import org.junit.platform.suite.api.ConfigurationParameter
import org.junit.platform.suite.api.IncludeEngines
import org.junit.platform.suite.api.SelectClasspathResource
import org.junit.platform.suite.api.Suite
import io.cucumber.junit.platform.engine.Constants

@Suite
@IncludeEngines("cucumber")
@SelectClasspathResource("be/fgov/onerva/cu/backend/features")
@ConfigurationParameter(key = Constants.PLUGIN_PROPERTY_NAME, value = "pretty")
@ConfigurationParameter(
    key = Constants.GLUE_PROPERTY_NAME,
    value = "be.fgov.onerva.cu.backend.cucumber"
)
public class CucumberIntegrationIT {
    // This class intentionally left empty
}