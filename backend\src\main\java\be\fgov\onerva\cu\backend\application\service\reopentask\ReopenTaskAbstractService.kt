package be.fgov.onerva.cu.backend.application.service.reopentask

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.exception.ReopenTaskException
import be.fgov.onerva.cu.backend.application.port.out.TaskApiPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.common.utils.logger

@Service
abstract class ReopenTaskAbstractService(
    val waveTaskPort: WaveTaskPort,
    val waveTaskPersistencePort: WaveTaskPersistencePort,
    val taskApiPort: TaskApiPort,
) {
    private val log = logger

    companion object {
        protected const val REASON = "Reopen task"
        private const val ANONYMOUS = "anonymous"
    }

    protected fun getWaveTaskEntity(
        requestId: UUID,
        taskType: String,
    ): WaveTask {
        return waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, taskType)
    }

    protected fun getOpenWaveTaskEntity(
        requestId: UUID,
        taskType: String,
    ): WaveTask {
        return waveTaskPersistencePort.getOpenWaveTaskEntityByRequestId(requestId, taskType)
    }

    protected fun getWaveTaskRevision(
        requestId: UUID,
        taskCode: String,
    ): WaveTaskRevisionNumbers {
        return waveTaskPersistencePort.getCitizenInformationRevision(requestId, taskCode)
    }

    protected fun removeWaveTaskEntity(
        requestId: UUID,
        taskType: String,
    ) {
        log.info("Removing task of type: ${taskType} from request: ${requestId}")
        waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, taskType)
    }

    protected fun checkTaskIsEmpty(task: Any?, errorMessage: String) {
        if (task == null) {
            throw ReopenTaskException(errorMessage)
        }
    }

    abstract fun reopenTask(requestId: UUID)

    protected fun openTask(waveTask: WaveTask) {
        log.info("Reopening data capture task")
        waveTaskPersistencePort.openTask(waveTask)
    }

    fun deleteTask(requestId: UUID) {
    }

    protected fun executeWoAwakeTask(taskId: String) {
        log.info("Executing wo awake task for task id: $taskId")
        waveTaskPort.awakeTask(taskId, REASON)
    }

    protected fun deleteNextTask(requestId: UUID) {
    }

    protected fun executeWaveTaskSoftDelete(taskId: String) {
        log.info("Executing soft delete task for task id: $taskId")
        waveTaskPersistencePort.softDeleteTaskById(taskId)
    }

    protected fun softDeleteTaskOnApi(taskId: String) {
        taskApiPort.softDeleteTask(taskId.toLong(), "Soft deletion")
    }
}