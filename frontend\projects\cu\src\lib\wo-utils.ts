import {Injectable} from "@angular/core";

@Injectable({
    providedIn: "root",
})
export class WoUtils {

    static triggerWoWebComponentMaxMinSize(): void {
        const panel = document.getElementById('task-interaction-panel');

        if (!panel) {
            console.warn("WO panel with ID \"task-interaction-panel\" was not found");
            return;
        }

        const buttons = panel.querySelectorAll<HTMLButtonElement>('.btn-unstyled.mb-0');
        const button = Array.from(buttons).find(btn => !btn.disabled);

        if (!button) {
            console.warn("WO button was not found or is not an HTML element");
            return;
        }

        button.click();
    }
}
