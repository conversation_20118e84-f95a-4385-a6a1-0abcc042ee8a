package be.fgov.onerva.cu.backend.cucumber.steps

import org.springframework.beans.factory.annotation.Autowired
import be.fgov.onerva.cu.backend.cucumber.steps.context.TestContext
import be.fgov.onerva.cu.backend.security.MockedJwtTokenSecurityContextFactory
import io.cucumber.java.en.Given
import io.cucumber.spring.ScenarioScope

@ScenarioScope
class AuthenticationSteps {

    @Autowired
    private lateinit var testContext: TestContext

    @Given("I am authenticated as {string} with role {string}")
    fun authenticateWithRole(username: String, role: String) {
        // Store in test context
        testContext.authenticatedUsername = username
        testContext.authenticatedRoles = arrayOf(role)

        // Also set in security context as before (though we won't rely on this)
        MockedJwtTokenSecurityContextFactory.populateSecurityContext(
            username,
            arrayOf(role)
        )
    }
}