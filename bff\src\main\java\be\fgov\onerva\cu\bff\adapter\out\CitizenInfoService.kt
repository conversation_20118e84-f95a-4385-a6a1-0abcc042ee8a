package be.fgov.onerva.cu.bff.adapter.out

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.bff.lookup.LookupService
import be.fgov.onerva.cu.bff.mapper.toCitizenInfoWithAddress
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import be.fgov.onerva.cu.common.utils.logger

@Service
class CitizenInfoService(
    private val citizenInfoApiFactory: CitizenInfoApiFactory,
    private val lookupService: LookupService,
    service: LookupService,
) {
    private val log = logger

    fun getCitizenInfo(ssin: String, authHeader: String): CitizenInfoWithAddress? {
        val citizenInfoApi = citizenInfoApiFactory(authHeader)
        val citizenInfoPageDTO = citizenInfoApi.searchCitizenInfo(listOf(ssin), null, "SUMMARY", 0, 10)

        val citizenInfoDTO = citizenInfoPageDTO.content.firstOrNull()?.also { citizenInfoDTO ->
            log.debug("getCitizenInfoWithAuth: Citizen found for ssin {}: {}", ssin, citizenInfoDTO)
        }
        val translatedCountryCode = citizenInfoDTO?.let {
            lookupService.getNationalityCodeFromOnemCountryCode(it.addressObj.countryCode.toString())
        }
        return citizenInfoDTO?.toCitizenInfoWithAddress(translatedCountryCode)
    }
}