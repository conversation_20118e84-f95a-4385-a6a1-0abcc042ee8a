package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import org.springframework.data.domain.Limit
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.toCitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainChangePersonalData
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainWaveTask
import be.fgov.onerva.cu.backend.adapter.out.mapper.toModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.mapper.toUnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.WaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ModeOfPaymentRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.UnionContributionRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.WaveTaskRepository
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataPersistCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataUpdateCommand
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.PersistChangePersonalDataPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

/**
 * Persistence adapter for storing change of address requests in the database.
 *
 * This adapter implements the [PersistChangePersonalDataPort] interface and handles
 * the persistence of change of address requests. Currently, returns a mock implementation
 * with a hardcoded ID for testing purposes.
 *
 * @see PersistChangePersonalDataPort
 * @see ChangePersonalDataPersistCommand
 * @see ChangePersonalDataRequest
 */
@Service("changePersonalDataPersistenceAdapter")
@Transactional
class ChangePersonalDataPersistenceAdapter(
    val changePersonalDataRepository: ChangePersonalDataRepository,
    val citizenInformationRepository: CitizenInformationRepository,
    val modeOfPaymentRepository: ModeOfPaymentRepository,
    val unionContributionRepository: UnionContributionRepository,
    val waveTaskRepository: WaveTaskRepository,
) : PersistChangePersonalDataPort {
    private val log = logger

    @LogMethodCall
    override fun getChangePersonalDataById(requestId: UUID): ChangePersonalDataRequest? {
        log.info("Retrieving change of address request with ID: $requestId")
        val changePersonalDataRequest =
            changePersonalDataRepository.findByIdOrNull(requestId)?.toDomainChangePersonalData()
                ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
        log.info("Retrieved wo tasks with ID: $requestId")
        val allByRequestId = waveTaskRepository.findAllByRequestId(requestId)

        changePersonalDataRequest.changePersonalDataCaptureWaveTask =
            allByRequestId.firstOrNull {
                it is ChangePersonalDataCaptureWaveTaskEntity
            }?.let(WaveTaskEntity::toDomainWaveTask)

        changePersonalDataRequest.changePersonalDataValidateWaveTask =
            allByRequestId.firstOrNull { it is ChangePersonalDataValidateWaveTaskEntity }
                ?.let(WaveTaskEntity::toDomainWaveTask)

        return changePersonalDataRequest
    }

    @LogMethodCall
    override fun persistChangePersonalData(@SensitiveParam changePersonalDataPersistCommand: ChangePersonalDataPersistCommand): ChangePersonalDataRequest {
        log.info("Persisting change of address with c9id: ${changePersonalDataPersistCommand.c9id}")

        return ChangePersonalDataRequestEntity(
            c9Id = changePersonalDataPersistCommand.c9id,
            c9Type = changePersonalDataPersistCommand.c9Type,
            opKey = changePersonalDataPersistCommand.opKey,
            sectOp = changePersonalDataPersistCommand.sectOp,
            requestDate = changePersonalDataPersistCommand.requestDate,
            ssin = changePersonalDataPersistCommand.ssin,
            documentType = changePersonalDataPersistCommand.documentType,
            paymentInstitution = changePersonalDataPersistCommand.paymentInstitution,
            unemploymentOffice = changePersonalDataPersistCommand.unemploymentOffice,
            introductionDate = changePersonalDataPersistCommand.introductionDate,
            dateValid = changePersonalDataPersistCommand.dateValid,
            scanNumber = changePersonalDataPersistCommand.scanNumber,
            scanUrl = changePersonalDataPersistCommand.scanUrl,
            operatorCode = changePersonalDataPersistCommand.operatorCode,
            entityCode = changePersonalDataPersistCommand.entityCode,
            introductionType = changePersonalDataPersistCommand.introductionType,
            dueDate = changePersonalDataPersistCommand.dueDate,
            numbox = changePersonalDataPersistCommand.numbox,
            ec1Id = changePersonalDataPersistCommand.ec1Id,
            ec1DisplayUrl = changePersonalDataPersistCommand.ec1DisplayUrl,
        ).let {
            changePersonalDataRepository.save(it)
            it.citizenInformation = persistCitizenInformation(
                changePersonalDataPersistCommand.citizenInformation, it
            )
            it.modeOfPayment = persistModeOfPayment(
                changePersonalDataPersistCommand.modeOfPayment, it
            )
            it.unionContribution = persistUnionContribution(
                changePersonalDataPersistCommand.unionContribution, it
            )
            it.toDomainChangePersonalData()
        }
    }

    override fun getChangePersonalDataByC9Id(c9Id: Long): ChangePersonalDataRequest? =
        changePersonalDataRepository.findByC9Id(c9Id, Sort.by(Sort.Direction.DESC, "createdDate"), Limit.of(1))
            .firstOrNull()?.let {
                this.getChangePersonalDataById(it.id)
            }

    override fun updateChangePersonalDataWithDecision(
        requestId: UUID,
        updateChangePersonalDataDecisionCommand: UpdateChangePersonalDataDecisionCommand,
    ) {
        val changePersonalDataRequest = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        changePersonalDataRequest.decisionFromMfx = true
        changePersonalDataRequest.decisionType = updateChangePersonalDataDecisionCommand.decisionType
        changePersonalDataRequest.decisionDate = updateChangePersonalDataDecisionCommand.decisionDate
        changePersonalDataRequest.decisionUser = updateChangePersonalDataDecisionCommand.user
        changePersonalDataRequest.decisionBarema = updateChangePersonalDataDecisionCommand.decisionBarema
    }

    @Transactional
    override fun updateChangePersonalDataWithC9EnvelopeData(requestId: UUID, command: ChangePersonalDataUpdateCommand) {
        val changePersonalDataRequest = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        changePersonalDataRequest.paymentInstitution = command.paymentInstitution
        changePersonalDataRequest.entityCode = command.entityCode
        changePersonalDataRequest.unemploymentOffice = command.unemploymentOffice
        changePersonalDataRequest.introductionDate = command.introductionDate
        changePersonalDataRequest.dateValid = command.dateValid
        changePersonalDataRequest.operatorCode = command.operatorCode
        changePersonalDataRequest.scanUrl = command.scanUrl
        changePersonalDataRequest.scanNumber = command.scanNumber
        changePersonalDataRequest.ec1Id = command.ec1Id
        changePersonalDataRequest.ec1DisplayUrl = command.ec1DisplayUrl
        changePersonalDataRequest.documentType = command.documentType
        changePersonalDataRequest.introductionType = command.introductionType

        changePersonalDataRepository.save(changePersonalDataRequest)
    }

    private fun persistCitizenInformation(
        citizenInformation: CitizenInformation?,
        request: ChangePersonalDataRequestEntity,
    ): CitizenInformationEntity? = citizenInformation?.toCitizenInformationEntity(request, UpdateStatus.FROM_C9)
        ?.apply { citizenInformationRepository.save(this) }

    private fun persistModeOfPayment(
        modeOfPayment: ModeOfPayment?,
        request: ChangePersonalDataRequestEntity,
    ): ModeOfPaymentEntity? = modeOfPayment?.toModeOfPaymentEntity(request, UpdateStatus.FROM_C9)
        ?.apply { modeOfPaymentRepository.save(this) }

    private fun persistUnionContribution(
        unionContribution: UnionContribution?,
        request: ChangePersonalDataRequestEntity,
    ): UnionContributionEntity? = unionContribution?.toUnionContributionEntity(request, UpdateStatus.FROM_C9)
        ?.apply { unionContributionRepository.save(this) }
}