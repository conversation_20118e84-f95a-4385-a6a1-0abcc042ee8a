{"version": 3, "sources": ["../../../../../../node_modules/ngx-mask/fesm2022/ngx-mask.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, inject, Injectable, ElementRef, Renderer2, makeEnvironmentProviders, input, output, signal, Directive, HostListener, Pipe } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nvar MaskExpression;\n(function (MaskExpression) {\n  MaskExpression[\"SEPARATOR\"] = \"separator\";\n  MaskExpression[\"PERCENT\"] = \"percent\";\n  MaskExpression[\"IP\"] = \"IP\";\n  MaskExpression[\"CPF_CNPJ\"] = \"CPF_CNPJ\";\n  MaskExpression[\"MONTH\"] = \"M\";\n  MaskExpression[\"MONTHS\"] = \"M0\";\n  MaskExpression[\"MINUTE\"] = \"m\";\n  MaskExpression[\"HOUR\"] = \"h\";\n  MaskExpression[\"HOURS\"] = \"H\";\n  MaskExpression[\"MINUTES\"] = \"m0\";\n  MaskExpression[\"HOURS_HOUR\"] = \"Hh\";\n  MaskExpression[\"SECONDS\"] = \"s0\";\n  MaskExpression[\"HOURS_MINUTES_SECONDS\"] = \"Hh:m0:s0\";\n  MaskExpression[\"EMAIL_MASK\"] = \"A*@A*.A*\";\n  MaskExpression[\"HOURS_MINUTES\"] = \"Hh:m0\";\n  MaskExpression[\"MINUTES_SECONDS\"] = \"m0:s0\";\n  MaskExpression[\"DAYS_MONTHS_YEARS\"] = \"d0/M0/0000\";\n  MaskExpression[\"DAYS_MONTHS\"] = \"d0/M0\";\n  MaskExpression[\"DAYS\"] = \"d0\";\n  MaskExpression[\"DAY\"] = \"d\";\n  MaskExpression[\"SECOND\"] = \"s\";\n  MaskExpression[\"LETTER_S\"] = \"S\";\n  MaskExpression[\"DOT\"] = \".\";\n  MaskExpression[\"COMMA\"] = \",\";\n  MaskExpression[\"CURLY_BRACKETS_LEFT\"] = \"{\";\n  MaskExpression[\"CURLY_BRACKETS_RIGHT\"] = \"}\";\n  MaskExpression[\"MINUS\"] = \"-\";\n  MaskExpression[\"OR\"] = \"||\";\n  MaskExpression[\"HASH\"] = \"#\";\n  MaskExpression[\"EMPTY_STRING\"] = \"\";\n  MaskExpression[\"SYMBOL_STAR\"] = \"*\";\n  MaskExpression[\"SYMBOL_QUESTION\"] = \"?\";\n  MaskExpression[\"SLASH\"] = \"/\";\n  MaskExpression[\"WHITE_SPACE\"] = \" \";\n  MaskExpression[\"NUMBER_ZERO\"] = \"0\";\n  MaskExpression[\"NUMBER_NINE\"] = \"9\";\n  MaskExpression[\"BACKSPACE\"] = \"Backspace\";\n  MaskExpression[\"DELETE\"] = \"Delete\";\n  MaskExpression[\"ARROW_LEFT\"] = \"ArrowLeft\";\n  MaskExpression[\"ARROW_UP\"] = \"ArrowUp\";\n  MaskExpression[\"DOUBLE_ZERO\"] = \"00\";\n})(MaskExpression || (MaskExpression = {}));\nconst NGX_MASK_CONFIG = new InjectionToken('ngx-mask config');\nconst NEW_CONFIG = new InjectionToken('new ngx-mask config');\nconst INITIAL_CONFIG = new InjectionToken('initial ngx-mask config');\nconst initialConfig = {\n  suffix: '',\n  prefix: '',\n  thousandSeparator: ' ',\n  decimalMarker: ['.', ','],\n  clearIfNotMatch: false,\n  showMaskTyped: false,\n  instantPrefix: false,\n  placeHolderCharacter: '_',\n  dropSpecialCharacters: true,\n  hiddenInput: false,\n  shownMaskExpression: '',\n  separatorLimit: '',\n  allowNegativeNumbers: false,\n  validation: true,\n  specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n  leadZeroDateTime: false,\n  apm: false,\n  leadZero: false,\n  keepCharacterPositions: false,\n  triggerOnMaskChange: false,\n  inputTransformFn: value => value,\n  outputTransformFn: value => value,\n  maskFilled: new EventEmitter(),\n  patterns: {\n    '0': {\n      pattern: new RegExp('\\\\d')\n    },\n    '9': {\n      pattern: new RegExp('\\\\d'),\n      optional: true\n    },\n    X: {\n      pattern: new RegExp('\\\\d'),\n      symbol: '*'\n    },\n    A: {\n      pattern: new RegExp('[a-zA-Z0-9]')\n    },\n    S: {\n      pattern: new RegExp('[a-zA-Z]')\n    },\n    U: {\n      pattern: new RegExp('[A-Z]')\n    },\n    L: {\n      pattern: new RegExp('[a-z]')\n    },\n    d: {\n      pattern: new RegExp('\\\\d')\n    },\n    m: {\n      pattern: new RegExp('\\\\d')\n    },\n    M: {\n      pattern: new RegExp('\\\\d')\n    },\n    H: {\n      pattern: new RegExp('\\\\d')\n    },\n    h: {\n      pattern: new RegExp('\\\\d')\n    },\n    s: {\n      pattern: new RegExp('\\\\d')\n    }\n  }\n};\nconst timeMasks = [MaskExpression.HOURS_MINUTES_SECONDS, MaskExpression.HOURS_MINUTES, MaskExpression.MINUTES_SECONDS];\nconst withoutValidation = [MaskExpression.PERCENT, MaskExpression.HOURS_HOUR, MaskExpression.SECONDS, MaskExpression.MINUTES, MaskExpression.SEPARATOR, MaskExpression.DAYS_MONTHS_YEARS, MaskExpression.DAYS_MONTHS, MaskExpression.DAYS, MaskExpression.MONTHS];\nclass NgxMaskApplierService {\n  _config = inject(NGX_MASK_CONFIG);\n  dropSpecialCharacters = this._config.dropSpecialCharacters;\n  hiddenInput = this._config.hiddenInput;\n  clearIfNotMatch = this._config.clearIfNotMatch;\n  specialCharacters = this._config.specialCharacters;\n  patterns = this._config.patterns;\n  prefix = this._config.prefix;\n  suffix = this._config.suffix;\n  thousandSeparator = this._config.thousandSeparator;\n  decimalMarker = this._config.decimalMarker;\n  customPattern;\n  showMaskTyped = this._config.showMaskTyped;\n  placeHolderCharacter = this._config.placeHolderCharacter;\n  validation = this._config.validation;\n  separatorLimit = this._config.separatorLimit;\n  allowNegativeNumbers = this._config.allowNegativeNumbers;\n  leadZeroDateTime = this._config.leadZeroDateTime;\n  leadZero = this._config.leadZero;\n  apm = this._config.apm;\n  inputTransformFn = this._config.inputTransformFn;\n  outputTransformFn = this._config.outputTransformFn;\n  keepCharacterPositions = this._config.keepCharacterPositions;\n  instantPrefix = this._config.instantPrefix;\n  _shift = new Set();\n  plusOnePosition = false;\n  maskExpression = '';\n  actualValue = '';\n  showKeepCharacterExp = '';\n  shownMaskExpression = '';\n  deletedSpecialCharacter = false;\n  ipError;\n  cpfCnpjError;\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, cb = () => {}) {\n    if (!maskExpression || typeof inputValue !== 'string') {\n      return MaskExpression.EMPTY_STRING;\n    }\n    let cursor = 0;\n    let result = '';\n    let multi = false;\n    let backspaceShift = false;\n    let shift = 1;\n    let stepBack = false;\n    let processedValue = inputValue;\n    let processedPosition = position;\n    if (processedValue.slice(0, this.prefix.length) === this.prefix) {\n      processedValue = processedValue.slice(this.prefix.length, processedValue.length);\n    }\n    if (!!this.suffix && processedValue.length > 0) {\n      processedValue = this.checkAndRemoveSuffix(processedValue);\n    }\n    if (processedValue === '(' && this.prefix) {\n      processedValue = '';\n    }\n    const inputArray = processedValue.toString().split(MaskExpression.EMPTY_STRING);\n    if (this.allowNegativeNumbers && processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS) {\n      result += processedValue.slice(cursor, cursor + 1);\n    }\n    if (maskExpression === MaskExpression.IP) {\n      const valuesIP = processedValue.split(MaskExpression.DOT);\n      this.ipError = this._validIP(valuesIP);\n      maskExpression = '***************';\n    }\n    const arr = [];\n    for (let i = 0; i < processedValue.length; i++) {\n      if (processedValue[i]?.match('\\\\d')) {\n        arr.push(processedValue[i] ?? MaskExpression.EMPTY_STRING);\n      }\n    }\n    if (maskExpression === MaskExpression.CPF_CNPJ) {\n      this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n      if (arr.length > 11) {\n        maskExpression = '00.000.000/0000-00';\n      } else {\n        maskExpression = '000.000.000-00';\n      }\n    }\n    if (maskExpression.startsWith(MaskExpression.PERCENT)) {\n      if (processedValue.match('[a-z]|[A-Z]') || processedValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced) {\n        processedValue = this._stripToDecimal(processedValue);\n        const precision = this.getPrecision(maskExpression);\n        processedValue = this.checkInputPrecision(processedValue, precision, this.decimalMarker);\n      }\n      const decimalMarker = typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n      if (processedValue.indexOf(decimalMarker) > 0 && !this.percentage(processedValue.substring(0, processedValue.indexOf(decimalMarker)))) {\n        let base = processedValue.substring(0, processedValue.indexOf(decimalMarker) - 1);\n        if (this.allowNegativeNumbers && processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS && !backspaced) {\n          base = processedValue.substring(0, processedValue.indexOf(decimalMarker));\n        }\n        processedValue = `${base}${processedValue.substring(processedValue.indexOf(decimalMarker), processedValue.length)}`;\n      }\n      let value = '';\n      this.allowNegativeNumbers && processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS ? value = `${MaskExpression.MINUS}${processedValue.slice(cursor + 1, cursor + processedValue.length)}` : value = processedValue;\n      if (this.percentage(value)) {\n        result = this._splitPercentZero(processedValue);\n      } else {\n        result = this._splitPercentZero(processedValue.substring(0, processedValue.length - 1));\n      }\n    } else if (maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n      if (processedValue.match('[wа-яА-Я]') || processedValue.match('[ЁёА-я]') || processedValue.match('[a-z]|[A-Z]') || processedValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) || processedValue.match('[^A-Za-z0-9,]')) {\n        processedValue = this._stripToDecimal(processedValue);\n      }\n      const precision = this.getPrecision(maskExpression);\n      let decimalMarker = this.decimalMarker;\n      if (Array.isArray(this.decimalMarker)) {\n        if (this.actualValue.includes(this.decimalMarker[0]) || this.actualValue.includes(this.decimalMarker[1])) {\n          decimalMarker = this.actualValue.includes(this.decimalMarker[0]) ? this.decimalMarker[0] : this.decimalMarker[1];\n        } else {\n          decimalMarker = this.decimalMarker.find(dm => dm !== this.thousandSeparator);\n        }\n      }\n      if (backspaced) {\n        const {\n          decimalMarkerIndex,\n          nonZeroIndex\n        } = this._findFirstNonZeroAndDecimalIndex(processedValue, decimalMarker);\n        const zeroIndexMinus = processedValue[0] === MaskExpression.MINUS;\n        const zeroIndexNumberZero = processedValue[0] === MaskExpression.NUMBER_ZERO;\n        const zeroIndexDecimalMarker = processedValue[0] === decimalMarker;\n        const firstIndexDecimalMarker = processedValue[1] === decimalMarker;\n        if (zeroIndexDecimalMarker && !nonZeroIndex || zeroIndexMinus && firstIndexDecimalMarker && !nonZeroIndex || zeroIndexNumberZero && !decimalMarkerIndex && !nonZeroIndex) {\n          processedValue = MaskExpression.NUMBER_ZERO;\n        }\n        if (decimalMarkerIndex && nonZeroIndex && zeroIndexMinus && processedPosition === 1) {\n          if (decimalMarkerIndex < nonZeroIndex || decimalMarkerIndex > nonZeroIndex) {\n            processedValue = MaskExpression.MINUS + processedValue.slice(nonZeroIndex);\n          }\n        }\n        if (!decimalMarkerIndex && nonZeroIndex && processedValue.length > nonZeroIndex) {\n          processedValue = zeroIndexMinus ? MaskExpression.MINUS + processedValue.slice(nonZeroIndex) : processedValue.slice(nonZeroIndex);\n        }\n        if (decimalMarkerIndex && nonZeroIndex && processedPosition === 0) {\n          if (decimalMarkerIndex < nonZeroIndex) {\n            processedValue = processedValue.slice(decimalMarkerIndex - 1);\n          }\n          if (decimalMarkerIndex > nonZeroIndex) {\n            processedValue = processedValue.slice(nonZeroIndex);\n          }\n        }\n      }\n      if (precision === 0) {\n        processedValue = this.allowNegativeNumbers ? processedValue.length > 2 && processedValue[0] === MaskExpression.MINUS && processedValue[1] === MaskExpression.NUMBER_ZERO && processedValue[2] !== this.thousandSeparator && processedValue[2] !== MaskExpression.COMMA && processedValue[2] !== MaskExpression.DOT ? '-' + processedValue.slice(2, processedValue.length) : processedValue[0] === MaskExpression.NUMBER_ZERO && processedValue.length > 1 && processedValue[1] !== this.thousandSeparator && processedValue[1] !== MaskExpression.COMMA && processedValue[1] !== MaskExpression.DOT ? processedValue.slice(1, processedValue.length) : processedValue : processedValue.length > 1 && processedValue[0] === MaskExpression.NUMBER_ZERO && processedValue[1] !== this.thousandSeparator && processedValue[1] !== MaskExpression.COMMA && processedValue[1] !== MaskExpression.DOT ? processedValue.slice(1, processedValue.length) : processedValue;\n      } else {\n        if (processedValue[0] === decimalMarker && processedValue.length > 1 && !backspaced) {\n          processedValue = MaskExpression.NUMBER_ZERO + processedValue.slice(0, processedValue.length + 1);\n          this.plusOnePosition = true;\n        }\n        if (processedValue[0] === MaskExpression.NUMBER_ZERO && processedValue[1] !== decimalMarker && processedValue[1] !== this.thousandSeparator && !backspaced) {\n          processedValue = processedValue.length > 1 ? processedValue.slice(0, 1) + decimalMarker + processedValue.slice(1, processedValue.length + 1) : processedValue;\n          this.plusOnePosition = true;\n        }\n        if (this.allowNegativeNumbers && !backspaced && processedValue[0] === MaskExpression.MINUS && (processedValue[1] === decimalMarker || processedValue[1] === MaskExpression.NUMBER_ZERO)) {\n          processedValue = processedValue[1] === decimalMarker && processedValue.length > 2 ? processedValue.slice(0, 1) + MaskExpression.NUMBER_ZERO + processedValue.slice(1, processedValue.length) : processedValue[1] === MaskExpression.NUMBER_ZERO && processedValue.length > 2 && processedValue[2] !== decimalMarker ? processedValue.slice(0, 2) + decimalMarker + processedValue.slice(2, processedValue.length) : processedValue;\n          this.plusOnePosition = true;\n        }\n      }\n      const thousandSeparatorCharEscaped = this._charToRegExpExpression(this.thousandSeparator);\n      let invalidChars = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(thousandSeparatorCharEscaped, '');\n      if (Array.isArray(this.decimalMarker)) {\n        for (const marker of this.decimalMarker) {\n          invalidChars = invalidChars.replace(this._charToRegExpExpression(marker), MaskExpression.EMPTY_STRING);\n        }\n      } else {\n        invalidChars = invalidChars.replace(this._charToRegExpExpression(this.decimalMarker), '');\n      }\n      const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n      if (processedValue.match(invalidCharRegexp)) {\n        processedValue = processedValue.substring(0, processedValue.length - 1);\n      }\n      processedValue = this.checkInputPrecision(processedValue, precision, this.decimalMarker);\n      const strForSep = processedValue.replace(new RegExp(thousandSeparatorCharEscaped, 'g'), '');\n      result = this._formatWithSeparators(strForSep, this.thousandSeparator, this.decimalMarker, precision);\n      const commaShift = result.indexOf(MaskExpression.COMMA) - processedValue.indexOf(MaskExpression.COMMA);\n      const shiftStep = result.length - processedValue.length;\n      const backspacedDecimalMarkerWithSeparatorLimit = backspaced && result.length < inputValue.length && this.separatorLimit;\n      if ((result[processedPosition - 1] === this.thousandSeparator || result[processedPosition - this.prefix.length]) && this.prefix && backspaced) {\n        processedPosition = processedPosition - 1;\n      } else if (shiftStep > 0 && result[processedPosition] !== this.thousandSeparator || backspacedDecimalMarkerWithSeparatorLimit) {\n        backspaceShift = true;\n        let _shift = 0;\n        do {\n          this._shift.add(processedPosition + _shift);\n          _shift++;\n        } while (_shift < shiftStep);\n      } else if (result[processedPosition - 1] === this.thousandSeparator || shiftStep === -4 || shiftStep === -3 || result[processedPosition] === this.thousandSeparator) {\n        this._shift.clear();\n        this._shift.add(processedPosition - 1);\n      } else if (commaShift !== 0 && processedPosition > 0 && !(result.indexOf(MaskExpression.COMMA) >= processedPosition && processedPosition > 3) || !(result.indexOf(MaskExpression.DOT) >= processedPosition && processedPosition > 3) && shiftStep <= 0) {\n        this._shift.clear();\n        backspaceShift = true;\n        shift = shiftStep;\n        processedPosition += shiftStep;\n        this._shift.add(processedPosition);\n      } else {\n        this._shift.clear();\n      }\n    } else {\n      for (let i = 0, inputSymbol = inputArray[0]; i < inputArray.length; i++, inputSymbol = inputArray[i] ?? MaskExpression.EMPTY_STRING) {\n        if (cursor === maskExpression.length) {\n          break;\n        }\n        const symbolStarInPattern = MaskExpression.SYMBOL_STAR in this.patterns;\n        if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) && maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION) {\n          result += inputSymbol;\n          cursor += 2;\n        } else if (maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR && multi && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING)) {\n          result += inputSymbol;\n          cursor += 3;\n          multi = false;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) && maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR && !symbolStarInPattern) {\n          result += inputSymbol;\n          multi = true;\n        } else if (maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING)) {\n          result += inputSymbol;\n          cursor += 3;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING)) {\n          if (maskExpression[cursor] === MaskExpression.HOURS) {\n            if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === MaskExpression.HOUR) {\n            if (this.apm ? result.length === 1 && Number(result) > 1 || result === '1' && Number(inputSymbol) > 2 || processedValue.slice(cursor - 1, cursor).length === 1 && Number(processedValue.slice(cursor - 1, cursor)) > 2 || processedValue.slice(cursor - 1, cursor) === '1' && Number(inputSymbol) > 2 : result === '2' && Number(inputSymbol) > 3 || (result.slice(cursor - 2, cursor) === '2' || result.slice(cursor - 3, cursor) === '2' || result.slice(cursor - 4, cursor) === '2' || result.slice(cursor - 1, cursor) === '2') && Number(inputSymbol) > 3 && cursor > 10) {\n              processedPosition = processedPosition + 1;\n              cursor += 1;\n              i--;\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === MaskExpression.MINUTE || maskExpression[cursor] === MaskExpression.SECOND) {\n            if (Number(inputSymbol) > 5) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          const daysCount = 31;\n          const inputValueCursor = processedValue[cursor];\n          const inputValueCursorPlusOne = processedValue[cursor + 1];\n          const inputValueCursorPlusTwo = processedValue[cursor + 2];\n          const inputValueCursorMinusOne = processedValue[cursor - 1];\n          const inputValueCursorMinusTwo = processedValue[cursor - 2];\n          const inputValueSliceMinusThreeMinusOne = processedValue.slice(cursor - 3, cursor - 1);\n          const inputValueSliceMinusOnePlusOne = processedValue.slice(cursor - 1, cursor + 1);\n          const inputValueSliceCursorPlusTwo = processedValue.slice(cursor, cursor + 2);\n          const inputValueSliceMinusTwoCursor = processedValue.slice(cursor - 2, cursor);\n          if (maskExpression[cursor] === MaskExpression.DAY) {\n            const maskStartWithMonth = maskExpression.slice(0, 2) === MaskExpression.MONTHS;\n            const startWithMonthInput = maskExpression.slice(0, 2) === MaskExpression.MONTHS && this.specialCharacters.includes(inputValueCursorMinusTwo);\n            if (Number(inputSymbol) > 3 && this.leadZeroDateTime || !maskStartWithMonth && (Number(inputValueSliceCursorPlusTwo) > daysCount || Number(inputValueSliceMinusOnePlusOne) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne)) || (startWithMonthInput ? Number(inputValueSliceMinusOnePlusOne) > daysCount || !this.specialCharacters.includes(inputValueCursor) && this.specialCharacters.includes(inputValueCursorPlusTwo) || this.specialCharacters.includes(inputValueCursor) : Number(inputValueSliceCursorPlusTwo) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne) && !backspaced)) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === MaskExpression.MONTH) {\n            const monthsCount = 12;\n            const withoutDays = cursor === 0 && (Number(inputSymbol) > 2 || Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne) && !backspaced);\n            const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n            const day1monthInput = inputValueSliceMinusThreeMinusOne.includes(specialChart) && maskExpression.includes('d0') && (this.specialCharacters.includes(inputValueCursorMinusTwo) && Number(inputValueSliceMinusOnePlusOne) > monthsCount && !this.specialCharacters.includes(inputValueCursor) || this.specialCharacters.includes(inputValueCursor));\n            const day2monthInput = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && this.specialCharacters.includes(inputValueCursorMinusOne) && (Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne));\n            const day2monthInputDot = Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5 || this.specialCharacters.includes(inputValueCursorPlusOne) && cursor === 5;\n            const day1monthPaste = Number(inputValueSliceMinusThreeMinusOne) > daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueSliceMinusTwoCursor) && Number(inputValueSliceMinusTwoCursor) > monthsCount && maskExpression.includes('d0');\n            const day2monthPaste = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueCursorMinusOne) && Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n            if (Number(inputSymbol) > 1 && this.leadZeroDateTime || withoutDays || day1monthInput || day2monthPaste || day1monthPaste || day2monthInput || day2monthInputDot && !this.leadZeroDateTime) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.includes(inputSymbol) && maskExpression[cursor] === inputSymbol) {\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.indexOf(maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) !== -1) {\n          result += maskExpression[cursor];\n          cursor++;\n          this._shiftStep(cursor);\n          i--;\n        } else if (maskExpression[cursor] === MaskExpression.NUMBER_NINE && this.showMaskTyped) {\n          this._shiftStep(cursor);\n        } else if (this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING] && this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]?.optional) {\n          if (!!inputArray[cursor] && maskExpression !== '***************' && maskExpression !== '000.000.000-00' && maskExpression !== '00.000.000/0000-00' && !maskExpression.match(/^9+\\.0+$/) && !this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]?.optional) {\n            result += inputArray[cursor];\n          }\n          if (maskExpression.includes(MaskExpression.NUMBER_NINE + MaskExpression.SYMBOL_STAR) && maskExpression.includes(MaskExpression.NUMBER_ZERO + MaskExpression.SYMBOL_STAR)) {\n            cursor++;\n          }\n          cursor++;\n          i--;\n        } else if (this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR && this._findSpecialChar(this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION && this._findSpecialChar(this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.showMaskTyped && this.specialCharacters.indexOf(inputSymbol) < 0 && inputSymbol !== this.placeHolderCharacter && this.placeHolderCharacter.length === 1) {\n          stepBack = true;\n        }\n      }\n    }\n    if (result[processedPosition - 1] && result.length + 1 === maskExpression.length && this.specialCharacters.indexOf(maskExpression[maskExpression.length - 1] ?? MaskExpression.EMPTY_STRING) !== -1) {\n      result += maskExpression[maskExpression.length - 1];\n    }\n    let newPosition = processedPosition + 1;\n    while (this._shift.has(newPosition)) {\n      shift++;\n      newPosition++;\n    }\n    let actualShift = justPasted && !maskExpression.startsWith(MaskExpression.SEPARATOR) ? cursor : this._shift.has(processedPosition) ? shift : 0;\n    if (stepBack) {\n      actualShift--;\n    }\n    cb(actualShift, backspaceShift);\n    if (shift < 0) {\n      this._shift.clear();\n    }\n    let onlySpecial = false;\n    if (backspaced) {\n      onlySpecial = inputArray.every(char => this.specialCharacters.includes(char));\n    }\n    let res = `${this.prefix}${onlySpecial ? MaskExpression.EMPTY_STRING : result}${this.showMaskTyped ? '' : this.suffix}`;\n    if (result.length === 0) {\n      res = this.instantPrefix ? `${this.prefix}${result}` : `${result}`;\n    }\n    const isSpecialCharacterMaskFirstSymbol = processedValue.length === 1 && this.specialCharacters.includes(maskExpression[0]) && processedValue !== maskExpression[0];\n    if (!this._checkSymbolMask(processedValue, maskExpression[1]) && isSpecialCharacterMaskFirstSymbol) {\n      return '';\n    }\n    if (result.includes(MaskExpression.MINUS) && this.prefix && this.allowNegativeNumbers) {\n      if (backspaced && result === MaskExpression.MINUS) {\n        return '';\n      }\n      res = `${MaskExpression.MINUS}${this.prefix}${result.split(MaskExpression.MINUS).join(MaskExpression.EMPTY_STRING)}${this.suffix}`;\n    }\n    return res;\n  }\n  _findDropSpecialChar(inputSymbol) {\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      return this.dropSpecialCharacters.find(val => val === inputSymbol);\n    }\n    return this._findSpecialChar(inputSymbol);\n  }\n  _findSpecialChar(inputSymbol) {\n    return this.specialCharacters.find(val => val === inputSymbol);\n  }\n  _checkSymbolMask(inputSymbol, maskSymbol) {\n    this.patterns = this.customPattern ? this.customPattern : this.patterns;\n    return (this.patterns[maskSymbol]?.pattern && this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ?? false;\n  }\n  _formatWithSeparators = (str, thousandSeparatorChar, decimalChars, precision) => {\n    let x = [];\n    let decimalChar = '';\n    if (Array.isArray(decimalChars)) {\n      const regExp = new RegExp(decimalChars.map(v => '[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v).join('|'));\n      x = str.split(regExp);\n      decimalChar = str.match(regExp)?.[0] ?? MaskExpression.EMPTY_STRING;\n    } else {\n      x = str.split(decimalChars);\n      decimalChar = decimalChars;\n    }\n    const decimals = x.length > 1 ? `${decimalChar}${x[1]}` : MaskExpression.EMPTY_STRING;\n    let res = x[0] ?? MaskExpression.EMPTY_STRING;\n    const separatorLimit = this.separatorLimit.replace(/\\s/g, MaskExpression.EMPTY_STRING);\n    if (separatorLimit && +separatorLimit) {\n      if (res[0] === MaskExpression.MINUS) {\n        res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n      } else {\n        res = res.slice(0, separatorLimit.length);\n      }\n    }\n    const rgx = /(\\d+)(\\d{3})/;\n    while (thousandSeparatorChar && rgx.test(res)) {\n      res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n    }\n    if (typeof precision === 'undefined') {\n      return res + decimals;\n    } else if (precision === 0) {\n      return res;\n    }\n    return res + decimals.substring(0, precision + 1);\n  };\n  percentage = str => {\n    const sanitizedStr = str.replace(',', '.');\n    const value = Number(this.allowNegativeNumbers && str.includes(MaskExpression.MINUS) ? sanitizedStr.slice(1, str.length) : sanitizedStr);\n    return !isNaN(value) && value >= 0 && value <= 100;\n  };\n  getPrecision = maskExpression => {\n    const x = maskExpression.split(MaskExpression.DOT);\n    if (x.length > 1) {\n      return Number(x[x.length - 1]);\n    }\n    return Infinity;\n  };\n  checkAndRemoveSuffix = inputValue => {\n    for (let i = this.suffix?.length - 1; i >= 0; i--) {\n      const substr = this.suffix.substring(i, this.suffix?.length);\n      if (inputValue.includes(substr) && i !== this.suffix?.length - 1 && (i - 1 < 0 || !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))) {\n        return inputValue.replace(substr, MaskExpression.EMPTY_STRING);\n      }\n    }\n    return inputValue;\n  };\n  checkInputPrecision = (inputValue, precision, decimalMarker) => {\n    let processedInputValue = inputValue;\n    let processedDecimalMarker = decimalMarker;\n    if (precision < Infinity) {\n      if (Array.isArray(processedDecimalMarker)) {\n        const marker = processedDecimalMarker.find(dm => dm !== this.thousandSeparator);\n        processedDecimalMarker = marker ? marker : processedDecimalMarker[0];\n      }\n      const precisionRegEx = new RegExp(this._charToRegExpExpression(processedDecimalMarker) + `\\\\d{${precision}}.*$`);\n      const precisionMatch = processedInputValue.match(precisionRegEx);\n      const precisionMatchLength = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n      if (precisionMatchLength - 1 > precision) {\n        const diff = precisionMatchLength - 1 - precision;\n        processedInputValue = processedInputValue.substring(0, processedInputValue.length - diff);\n      }\n      if (precision === 0 && this._compareOrIncludes(processedInputValue[processedInputValue.length - 1], processedDecimalMarker, this.thousandSeparator)) {\n        processedInputValue = processedInputValue.substring(0, processedInputValue.length - 1);\n      }\n    }\n    return processedInputValue;\n  };\n  _stripToDecimal(str) {\n    return str.split(MaskExpression.EMPTY_STRING).filter((i, idx) => {\n      const isDecimalMarker = typeof this.decimalMarker === 'string' ? i === this.decimalMarker : this.decimalMarker.includes(i);\n      return i.match('^-?\\\\d') || i === this.thousandSeparator || isDecimalMarker || i === MaskExpression.MINUS && idx === 0 && this.allowNegativeNumbers;\n    }).join(MaskExpression.EMPTY_STRING);\n  }\n  _charToRegExpExpression(char) {\n    if (char) {\n      const charsToEscape = '[\\\\^$.|?*+()';\n      return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n    }\n    return char;\n  }\n  _shiftStep(cursor) {\n    this._shift.add(cursor + this.prefix.length || 0);\n  }\n  _compareOrIncludes(value, comparedValue, excludedValue) {\n    return Array.isArray(comparedValue) ? comparedValue.filter(v => v !== excludedValue).includes(value) : value === comparedValue;\n  }\n  _validIP(valuesIP) {\n    return !(valuesIP.length === 4 && !valuesIP.some((value, index) => {\n      if (valuesIP.length !== index + 1) {\n        return value === MaskExpression.EMPTY_STRING || Number(value) > 255;\n      }\n      return value === MaskExpression.EMPTY_STRING || Number(value.substring(0, 3)) > 255;\n    }));\n  }\n  _splitPercentZero(value) {\n    if (value === MaskExpression.MINUS && this.allowNegativeNumbers) {\n      return value;\n    }\n    const decimalIndex = typeof this.decimalMarker === 'string' ? value.indexOf(this.decimalMarker) : value.indexOf(MaskExpression.DOT);\n    const emptyOrMinus = this.allowNegativeNumbers && value.includes(MaskExpression.MINUS) ? '-' : '';\n    if (decimalIndex === -1) {\n      const parsedValue = parseInt(emptyOrMinus ? value.slice(1, value.length) : value, 10);\n      return isNaN(parsedValue) ? MaskExpression.EMPTY_STRING : `${emptyOrMinus}${parsedValue}`;\n    } else {\n      const integerPart = parseInt(value.replace('-', '').substring(0, decimalIndex), 10);\n      const decimalPart = value.substring(decimalIndex + 1);\n      const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n      const decimal = typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n      return integerString === MaskExpression.EMPTY_STRING ? MaskExpression.EMPTY_STRING : `${emptyOrMinus}${integerString}${decimal}${decimalPart}`;\n    }\n  }\n  _findFirstNonZeroAndDecimalIndex(inputString, decimalMarker) {\n    let decimalMarkerIndex = null;\n    let nonZeroIndex = null;\n    for (let i = 0; i < inputString.length; i++) {\n      const char = inputString[i];\n      if (char === decimalMarker && decimalMarkerIndex === null) {\n        decimalMarkerIndex = i;\n      }\n      if (char && char >= '1' && char <= '9' && nonZeroIndex === null) {\n        nonZeroIndex = i;\n      }\n      if (decimalMarkerIndex !== null && nonZeroIndex !== null) {\n        break;\n      }\n    }\n    return {\n      decimalMarkerIndex,\n      nonZeroIndex\n    };\n  }\n  static ɵfac = function NgxMaskApplierService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgxMaskApplierService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgxMaskApplierService,\n    factory: NgxMaskApplierService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskApplierService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NgxMaskService extends NgxMaskApplierService {\n  isNumberValue = false;\n  maskIsShown = '';\n  selStart = null;\n  selEnd = null;\n  maskChanged = false;\n  maskExpressionArray = [];\n  triggerOnMaskChange = false;\n  previousValue = '';\n  currentValue = '';\n  writingValue = false;\n  _emitValue = false;\n  _start;\n  _end;\n  onChange = _ => {};\n  _elementRef = inject(ElementRef, {\n    optional: true\n  });\n  document = inject(DOCUMENT);\n  _config = inject(NGX_MASK_CONFIG);\n  _renderer = inject(Renderer2, {\n    optional: true\n  });\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, cb = () => {}) {\n    if (!maskExpression) {\n      return inputValue !== this.actualValue ? this.actualValue : inputValue;\n    }\n    this.maskIsShown = this.showMaskTyped ? this.showMaskInInput() : MaskExpression.EMPTY_STRING;\n    if (this.maskExpression === MaskExpression.IP && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n    }\n    if (this.maskExpression === MaskExpression.CPF_CNPJ && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n    }\n    if (!inputValue && this.showMaskTyped) {\n      this.formControlResult(this.prefix);\n      return `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    }\n    const getSymbol = !!inputValue && typeof this.selStart === 'number' ? inputValue[this.selStart] ?? MaskExpression.EMPTY_STRING : MaskExpression.EMPTY_STRING;\n    let newInputValue = '';\n    let newPosition = position;\n    if ((this.hiddenInput || inputValue && inputValue.indexOf(MaskExpression.SYMBOL_STAR) >= 0) && !this.writingValue) {\n      let actualResult = inputValue && inputValue.length === 1 ? inputValue.split(MaskExpression.EMPTY_STRING) : this.actualValue.split(MaskExpression.EMPTY_STRING);\n      if (backspaced) {\n        actualResult = actualResult.slice(0, position).concat(actualResult.slice(position + 1));\n      }\n      if (this.showMaskTyped) {\n        inputValue = this.removeMask(inputValue);\n        actualResult = this.removeMask(actualResult.join('')).split(MaskExpression.EMPTY_STRING);\n      }\n      if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n        this.selStart = Number(this.selStart);\n        this.selEnd = Number(this.selEnd);\n      } else {\n        if (inputValue !== MaskExpression.EMPTY_STRING && actualResult.length) {\n          if (typeof this.selStart === 'number' && typeof this.selEnd === 'number') {\n            if (inputValue.length > actualResult.length) {\n              actualResult.splice(this.selStart, 0, getSymbol);\n            } else if (inputValue.length < actualResult.length) {\n              if (actualResult.length - inputValue.length === 1) {\n                if (backspaced) {\n                  actualResult.splice(this.selStart - 1, 1);\n                } else {\n                  actualResult.splice(inputValue.length - 1, 1);\n                }\n              } else {\n                actualResult.splice(this.selStart, this.selEnd - this.selStart);\n              }\n            }\n          }\n        } else {\n          actualResult = [];\n        }\n      }\n      if (this.showMaskTyped && !this.hiddenInput) {\n        newInputValue = this.removeMask(inputValue);\n      }\n      if (this.actualValue.length) {\n        if (actualResult.length < inputValue.length) {\n          newInputValue = this.shiftTypedSymbols(actualResult.join(MaskExpression.EMPTY_STRING));\n        } else if (actualResult.length === inputValue.length) {\n          newInputValue = actualResult.join(MaskExpression.EMPTY_STRING);\n        } else {\n          newInputValue = inputValue;\n        }\n      } else {\n        newInputValue = inputValue;\n      }\n    }\n    if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n      newInputValue = inputValue;\n    }\n    if (backspaced && this.specialCharacters.indexOf(this.maskExpression[newPosition] ?? MaskExpression.EMPTY_STRING) !== -1 && this.showMaskTyped && !this.prefix) {\n      newInputValue = this.currentValue;\n    }\n    if (this.deletedSpecialCharacter && newPosition) {\n      if (this.specialCharacters.includes(this.actualValue.slice(newPosition, newPosition + 1))) {\n        newPosition = newPosition + 1;\n      } else if (maskExpression.slice(newPosition - 1, newPosition + 1) !== MaskExpression.MONTHS) {\n        newPosition = newPosition - 2;\n      }\n      this.deletedSpecialCharacter = false;\n    }\n    if (this.showMaskTyped && this.placeHolderCharacter.length === 1 && !this.leadZeroDateTime) {\n      newInputValue = this.removeMask(newInputValue);\n    }\n    if (this.maskChanged) {\n      newInputValue = inputValue;\n    } else {\n      newInputValue = Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n    }\n    if (this.showMaskTyped && this.keepCharacterPositions && this.actualValue && !justPasted && !this.writingValue) {\n      const value = this.dropSpecialCharacters ? this.removeMask(this.actualValue) : this.actualValue;\n      this.formControlResult(value);\n      return this.actualValue ? this.actualValue : `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    }\n    const result = super.applyMask(newInputValue, maskExpression, newPosition, justPasted, backspaced, cb);\n    this.actualValue = this.getActualValue(result);\n    if (this.thousandSeparator === MaskExpression.DOT && this.decimalMarker === MaskExpression.DOT) {\n      this.decimalMarker = MaskExpression.COMMA;\n    }\n    if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && this.dropSpecialCharacters === true) {\n      this.specialCharacters = this.specialCharacters.filter(item => !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator));\n    }\n    if (result || result === '') {\n      this.previousValue = this.currentValue;\n      this.currentValue = result;\n      this._emitValue = this.previousValue !== this.currentValue || this.maskChanged || this.writingValue || this.previousValue === this.currentValue && justPasted;\n    }\n    this._emitValue ? this.writingValue && this.triggerOnMaskChange ? requestAnimationFrame(() => this.formControlResult(result)) : this.formControlResult(result) : '';\n    if (!this.showMaskTyped || this.showMaskTyped && this.hiddenInput) {\n      if (this.hiddenInput) {\n        return `${this.hideInput(result, this.maskExpression)}${this.maskIsShown.slice(result.length)}`;\n      }\n      return result;\n    }\n    const resLen = result.length;\n    const prefNmask = `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    if (this.maskExpression.includes(MaskExpression.HOURS)) {\n      const countSkipedSymbol = this._numberSkipedSymbols(result);\n      return `${result}${prefNmask.slice(resLen + countSkipedSymbol)}`;\n    } else if (this.maskExpression === MaskExpression.IP || this.maskExpression === MaskExpression.CPF_CNPJ) {\n      return `${result}${prefNmask}`;\n    }\n    return `${result}${prefNmask.slice(resLen)}`;\n  }\n  _numberSkipedSymbols(value) {\n    const regex = /(^|\\D)(\\d\\D)/g;\n    let match = regex.exec(value);\n    let countSkipedSymbol = 0;\n    while (match != null) {\n      countSkipedSymbol += 1;\n      match = regex.exec(value);\n    }\n    return countSkipedSymbol;\n  }\n  applyValueChanges(position, justPasted, backspaced, cb = () => {}) {\n    const formElement = this._elementRef?.nativeElement;\n    if (!formElement) {\n      return;\n    }\n    formElement.value = this.applyMask(formElement.value, this.maskExpression, position, justPasted, backspaced, cb);\n    if (formElement === this._getActiveElement()) {\n      return;\n    }\n    this.clearIfNotMatchFn();\n  }\n  hideInput(inputValue, maskExpression) {\n    return inputValue.split(MaskExpression.EMPTY_STRING).map((curr, index) => {\n      if (this.patterns && this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING] && this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]?.symbol) {\n        return this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]?.symbol;\n      }\n      return curr;\n    }).join(MaskExpression.EMPTY_STRING);\n  }\n  getActualValue(res) {\n    const compare = res.split(MaskExpression.EMPTY_STRING).filter((symbol, i) => {\n      const maskChar = this.maskExpression[i] ?? MaskExpression.EMPTY_STRING;\n      return this._checkSymbolMask(symbol, maskChar) || this.specialCharacters.includes(maskChar) && symbol === maskChar;\n    });\n    if (compare.join(MaskExpression.EMPTY_STRING) === res) {\n      return compare.join(MaskExpression.EMPTY_STRING);\n    }\n    return res;\n  }\n  shiftTypedSymbols(inputValue) {\n    let symbolToReplace = '';\n    const newInputValue = inputValue && inputValue.split(MaskExpression.EMPTY_STRING).map((currSymbol, index) => {\n      if (this.specialCharacters.includes(inputValue[index + 1] ?? MaskExpression.EMPTY_STRING) && inputValue[index + 1] !== this.maskExpression[index + 1]) {\n        symbolToReplace = currSymbol;\n        return inputValue[index + 1];\n      }\n      if (symbolToReplace.length) {\n        const replaceSymbol = symbolToReplace;\n        symbolToReplace = MaskExpression.EMPTY_STRING;\n        return replaceSymbol;\n      }\n      return currSymbol;\n    }) || [];\n    return newInputValue.join(MaskExpression.EMPTY_STRING);\n  }\n  numberToString(value) {\n    if (!value && value !== 0 || this.maskExpression.startsWith(MaskExpression.SEPARATOR) && (this.leadZero || !this.dropSpecialCharacters) || this.maskExpression.startsWith(MaskExpression.SEPARATOR) && this.separatorLimit.length > 14 && String(value).length > 14) {\n      return String(value);\n    }\n    return Number(value).toLocaleString('fullwide', {\n      useGrouping: false,\n      maximumFractionDigits: 20\n    }).replace(`/${MaskExpression.MINUS}/`, MaskExpression.MINUS);\n  }\n  showMaskInInput(inputVal) {\n    if (this.showMaskTyped && !!this.shownMaskExpression) {\n      if (this.maskExpression.length !== this.shownMaskExpression.length) {\n        throw new Error('Mask expression must match mask placeholder length');\n      } else {\n        return this.shownMaskExpression;\n      }\n    } else if (this.showMaskTyped) {\n      if (inputVal) {\n        if (this.maskExpression === MaskExpression.IP) {\n          return this._checkForIp(inputVal);\n        }\n        if (this.maskExpression === MaskExpression.CPF_CNPJ) {\n          return this._checkForCpfCnpj(inputVal);\n        }\n      }\n      if (this.placeHolderCharacter.length === this.maskExpression.length) {\n        return this.placeHolderCharacter;\n      }\n      return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n    }\n    return '';\n  }\n  clearIfNotMatchFn() {\n    const formElement = this._elementRef?.nativeElement;\n    if (!formElement) {\n      return;\n    }\n    if (this.clearIfNotMatch && this.prefix.length + this.maskExpression.length + this.suffix.length !== formElement.value.replace(this.placeHolderCharacter, MaskExpression.EMPTY_STRING).length) {\n      this.formElementProperty = ['value', MaskExpression.EMPTY_STRING];\n      this.applyMask('', this.maskExpression);\n    }\n  }\n  set formElementProperty([name, value]) {\n    if (!this._renderer || !this._elementRef) {\n      return;\n    }\n    Promise.resolve().then(() => this._renderer?.setProperty(this._elementRef?.nativeElement, name, value));\n  }\n  checkDropSpecialCharAmount(mask) {\n    const chars = mask.split(MaskExpression.EMPTY_STRING).filter(item => this._findDropSpecialChar(item));\n    return chars.length;\n  }\n  removeMask(inputValue) {\n    return this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.specialCharacters.concat('_').concat(this.placeHolderCharacter));\n  }\n  _checkForIp(inputVal) {\n    if (inputVal === MaskExpression.HASH) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    const arr = [];\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n      if (!value) {\n        continue;\n      }\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n    if (arr.length <= 3) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    if (arr.length > 3 && arr.length <= 6) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    if (arr.length > 6 && arr.length <= 9) {\n      return this.placeHolderCharacter;\n    }\n    if (arr.length > 9 && arr.length <= 12) {\n      return '';\n    }\n    return '';\n  }\n  _checkForCpfCnpj(inputVal) {\n    const cpf = `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    const cnpj = `${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    if (inputVal === MaskExpression.HASH) {\n      return cpf;\n    }\n    const arr = [];\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n      if (!value) {\n        continue;\n      }\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n    if (arr.length <= 3) {\n      return cpf.slice(arr.length, cpf.length);\n    }\n    if (arr.length > 3 && arr.length <= 6) {\n      return cpf.slice(arr.length + 1, cpf.length);\n    }\n    if (arr.length > 6 && arr.length <= 9) {\n      return cpf.slice(arr.length + 2, cpf.length);\n    }\n    if (arr.length > 9 && arr.length < 11) {\n      return cpf.slice(arr.length + 3, cpf.length);\n    }\n    if (arr.length === 11) {\n      return '';\n    }\n    if (arr.length === 12) {\n      if (inputVal.length === 17) {\n        return cnpj.slice(16, cnpj.length);\n      }\n      return cnpj.slice(15, cnpj.length);\n    }\n    if (arr.length > 12 && arr.length <= 14) {\n      return cnpj.slice(arr.length + 4, cnpj.length);\n    }\n    return '';\n  }\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  formControlResult(inputValue) {\n    if (this.writingValue && !inputValue) {\n      this.onChange(this.outputTransformFn(null));\n      return;\n    }\n    if (this.writingValue || !this.triggerOnMaskChange && this.maskChanged) {\n      this.triggerOnMaskChange && this.maskChanged ? this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue)))))) : '';\n      this.writingValue = false;\n      this.maskChanged = false;\n      return;\n    }\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.dropSpecialCharacters)))));\n    } else if (this.dropSpecialCharacters || !this.dropSpecialCharacters && this.prefix === inputValue) {\n      this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))));\n    } else {\n      this.onChange(this.outputTransformFn(this._toNumber(inputValue)));\n    }\n  }\n  _toNumber(value) {\n    if (!this.isNumberValue || value === MaskExpression.EMPTY_STRING) {\n      return value;\n    }\n    if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && (this.leadZero || !this.dropSpecialCharacters)) {\n      return value;\n    }\n    if (String(value).length > 14 && this.maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n      return String(value);\n    }\n    const num = Number(value);\n    if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && Number.isNaN(num)) {\n      const val = String(value).replace(',', '.');\n      return Number(val);\n    }\n    return Number.isNaN(num) ? value : num;\n  }\n  _removeMask(value, specialCharactersForRemove) {\n    if (this.maskExpression.startsWith(MaskExpression.PERCENT) && value.includes(MaskExpression.DOT)) {\n      return value;\n    }\n    return value ? value.replace(this._regExpForRemove(specialCharactersForRemove), MaskExpression.EMPTY_STRING) : value;\n  }\n  _removePrefix(value) {\n    if (!this.prefix) {\n      return value;\n    }\n    return value ? value.replace(this.prefix, MaskExpression.EMPTY_STRING) : value;\n  }\n  _removeSuffix(value) {\n    if (!this.suffix) {\n      return value;\n    }\n    return value ? value.replace(this.suffix, MaskExpression.EMPTY_STRING) : value;\n  }\n  _retrieveSeparatorValue(result) {\n    let specialCharacters = Array.isArray(this.dropSpecialCharacters) ? this.specialCharacters.filter(v => {\n      return this.dropSpecialCharacters.includes(v);\n    }) : this.specialCharacters;\n    if (!this.deletedSpecialCharacter && this._checkPatternForSpace() && result.includes(MaskExpression.WHITE_SPACE) && this.maskExpression.includes(MaskExpression.SYMBOL_STAR)) {\n      specialCharacters = specialCharacters.filter(char => char !== MaskExpression.WHITE_SPACE);\n    }\n    return this._removeMask(result, specialCharacters);\n  }\n  _regExpForRemove(specialCharactersForRemove) {\n    return new RegExp(specialCharactersForRemove.map(item => `\\\\${item}`).join('|'), 'gi');\n  }\n  _replaceDecimalMarkerToDot(value) {\n    const markers = Array.isArray(this.decimalMarker) ? this.decimalMarker : [this.decimalMarker];\n    return value.replace(this._regExpForRemove(markers), MaskExpression.DOT);\n  }\n  _checkSymbols(result) {\n    let processedResult = result;\n    if (processedResult === MaskExpression.EMPTY_STRING) {\n      return processedResult;\n    }\n    if (this.maskExpression.startsWith(MaskExpression.PERCENT) && this.decimalMarker === MaskExpression.COMMA) {\n      processedResult = processedResult.replace(MaskExpression.COMMA, MaskExpression.DOT);\n    }\n    const separatorPrecision = this._retrieveSeparatorPrecision(this.maskExpression);\n    const separatorValue = this.specialCharacters.length === 0 ? this._retrieveSeparatorValue(processedResult) : this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(processedResult));\n    if (!this.isNumberValue) {\n      return separatorValue;\n    }\n    if (separatorPrecision) {\n      if (processedResult === this.decimalMarker) {\n        return null;\n      }\n      if (separatorValue.length > 14) {\n        return String(separatorValue);\n      }\n      return this._checkPrecision(this.maskExpression, separatorValue);\n    } else {\n      return separatorValue;\n    }\n  }\n  _checkPatternForSpace() {\n    for (const key in this.patterns) {\n      if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n        const patternString = this.patterns[key]?.pattern.toString();\n        const pattern = this.patterns[key]?.pattern;\n        if (patternString?.includes(MaskExpression.WHITE_SPACE) && pattern?.test(this.maskExpression)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  _retrieveSeparatorPrecision(maskExpretion) {\n    const matcher = maskExpretion.match(new RegExp(`^separator\\\\.([^d]*)`));\n    return matcher ? Number(matcher[1]) : null;\n  }\n  _checkPrecision(separatorExpression, separatorValue) {\n    const separatorPrecision = this.getPrecision(separatorExpression);\n    let value = separatorValue;\n    if (separatorExpression.indexOf('2') > 0 || this.leadZero && Number(separatorPrecision) > 0) {\n      if (this.decimalMarker === MaskExpression.COMMA && this.leadZero) {\n        value = value.replace(',', '.');\n      }\n      return this.leadZero ? Number(value).toFixed(Number(separatorPrecision)) : Number(value).toFixed(2);\n    }\n    return this.numberToString(value);\n  }\n  _repeatPatternSymbols(maskExp) {\n    return maskExp.match(/{[0-9]+}/) && maskExp.split(MaskExpression.EMPTY_STRING).reduce((accum, currVal, index) => {\n      this._start = currVal === MaskExpression.CURLY_BRACKETS_LEFT ? index : this._start;\n      if (currVal !== MaskExpression.CURLY_BRACKETS_RIGHT) {\n        return this._findSpecialChar(currVal) ? accum + currVal : accum;\n      }\n      this._end = index;\n      const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n      const replaceWith = new Array(repeatNumber + 1).join(maskExp[this._start - 1]);\n      if (maskExp.slice(0, this._start).length > 1 && maskExp.includes(MaskExpression.LETTER_S)) {\n        const symbols = maskExp.slice(0, this._start - 1);\n        return symbols.includes(MaskExpression.CURLY_BRACKETS_LEFT) ? accum + replaceWith : symbols + accum + replaceWith;\n      } else {\n        return accum + replaceWith;\n      }\n    }, '') || maskExp;\n  }\n  currentLocaleDecimalMarker() {\n    return 1.1.toLocaleString().substring(1, 2);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵNgxMaskService_BaseFactory;\n    return function NgxMaskService_Factory(__ngFactoryType__) {\n      return (ɵNgxMaskService_BaseFactory || (ɵNgxMaskService_BaseFactory = i0.ɵɵgetInheritedFactory(NgxMaskService)))(__ngFactoryType__ || NgxMaskService);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgxMaskService,\n    factory: NgxMaskService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskService, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction _configFactory() {\n  const initConfig = inject(INITIAL_CONFIG);\n  const configValue = inject(NEW_CONFIG);\n  return configValue instanceof Function ? {\n    ...initConfig,\n    ...configValue()\n  } : {\n    ...initConfig,\n    ...configValue\n  };\n}\nfunction provideNgxMask(configValue) {\n  return [{\n    provide: NEW_CONFIG,\n    useValue: configValue\n  }, {\n    provide: INITIAL_CONFIG,\n    useValue: initialConfig\n  }, {\n    provide: NGX_MASK_CONFIG,\n    useFactory: _configFactory\n  }, NgxMaskService];\n}\nfunction provideEnvironmentNgxMask(configValue) {\n  return makeEnvironmentProviders(provideNgxMask(configValue));\n}\nclass NgxMaskDirective {\n  mask = input('');\n  specialCharacters = input([]);\n  patterns = input({});\n  prefix = input('');\n  suffix = input('');\n  thousandSeparator = input(' ');\n  decimalMarker = input('.');\n  dropSpecialCharacters = input(null);\n  hiddenInput = input(null);\n  showMaskTyped = input(null);\n  placeHolderCharacter = input(null);\n  shownMaskExpression = input(null);\n  clearIfNotMatch = input(null);\n  validation = input(null);\n  separatorLimit = input('');\n  allowNegativeNumbers = input(null);\n  leadZeroDateTime = input(null);\n  leadZero = input(null);\n  triggerOnMaskChange = input(null);\n  apm = input(null);\n  inputTransformFn = input(null);\n  outputTransformFn = input(null);\n  keepCharacterPositions = input(null);\n  instantPrefix = input(null);\n  maskFilled = output();\n  _maskValue = signal('');\n  _inputValue = signal('');\n  _position = signal(null);\n  _code = signal('');\n  _maskExpressionArray = signal([]);\n  _justPasted = signal(false);\n  _isFocused = signal(false);\n  _isComposing = signal(false);\n  _maskService = inject(NgxMaskService, {\n    self: true\n  });\n  document = inject(DOCUMENT);\n  _config = inject(NGX_MASK_CONFIG);\n  onChange = _ => {};\n  onTouch = () => {};\n  ngOnChanges(changes) {\n    const {\n      mask,\n      specialCharacters,\n      patterns,\n      prefix,\n      suffix,\n      thousandSeparator,\n      decimalMarker,\n      dropSpecialCharacters,\n      hiddenInput,\n      showMaskTyped,\n      placeHolderCharacter,\n      shownMaskExpression,\n      clearIfNotMatch,\n      validation,\n      separatorLimit,\n      allowNegativeNumbers,\n      leadZeroDateTime,\n      leadZero,\n      triggerOnMaskChange,\n      apm,\n      inputTransformFn,\n      outputTransformFn,\n      keepCharacterPositions,\n      instantPrefix\n    } = changes;\n    if (mask) {\n      if (mask.currentValue !== mask.previousValue && !mask.firstChange) {\n        this._maskService.maskChanged = true;\n      }\n      if (mask.currentValue && mask.currentValue.split(MaskExpression.OR).length > 1) {\n        this._maskExpressionArray.set(mask.currentValue.split(MaskExpression.OR).sort((a, b) => {\n          return a.length - b.length;\n        }));\n        this._setMask();\n      } else {\n        this._maskExpressionArray.set([]);\n        this._maskValue.set(mask.currentValue || MaskExpression.EMPTY_STRING);\n        this._maskService.maskExpression = this._maskValue();\n      }\n    }\n    if (specialCharacters) {\n      if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n        return;\n      } else {\n        this._maskService.specialCharacters = specialCharacters.currentValue || [];\n      }\n    }\n    if (allowNegativeNumbers) {\n      this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n      if (this._maskService.allowNegativeNumbers) {\n        this._maskService.specialCharacters = this._maskService.specialCharacters.filter(c => c !== MaskExpression.MINUS);\n      }\n    }\n    if (patterns && patterns.currentValue) {\n      this._maskService.patterns = patterns.currentValue;\n    }\n    if (apm && apm.currentValue) {\n      this._maskService.apm = apm.currentValue;\n    }\n    if (instantPrefix) {\n      this._maskService.instantPrefix = instantPrefix.currentValue;\n    }\n    if (prefix) {\n      this._maskService.prefix = prefix.currentValue;\n    }\n    if (suffix) {\n      this._maskService.suffix = suffix.currentValue;\n    }\n    if (thousandSeparator) {\n      this._maskService.thousandSeparator = thousandSeparator.currentValue;\n      if (thousandSeparator.previousValue && thousandSeparator.currentValue) {\n        const previousDecimalMarker = this._maskService.decimalMarker;\n        if (thousandSeparator.currentValue === this._maskService.decimalMarker) {\n          this._maskService.decimalMarker = thousandSeparator.currentValue === MaskExpression.COMMA ? MaskExpression.DOT : MaskExpression.COMMA;\n        }\n        if (this._maskService.dropSpecialCharacters === true) {\n          this._maskService.specialCharacters = this._config.specialCharacters;\n        }\n        if (typeof previousDecimalMarker === 'string' && typeof this._maskService.decimalMarker === 'string') {\n          this._inputValue.set(this._inputValue().split(thousandSeparator.previousValue).join('').replace(previousDecimalMarker, this._maskService.decimalMarker));\n          this._maskService.actualValue = this._inputValue();\n        }\n        this._maskService.writingValue = true;\n      }\n    }\n    if (decimalMarker) {\n      this._maskService.decimalMarker = decimalMarker.currentValue;\n    }\n    if (dropSpecialCharacters) {\n      this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n    }\n    if (hiddenInput) {\n      this._maskService.hiddenInput = hiddenInput.currentValue;\n      if (hiddenInput.previousValue === true && hiddenInput.currentValue === false) {\n        this._inputValue.set(this._maskService.actualValue);\n      }\n    }\n    if (showMaskTyped) {\n      this._maskService.showMaskTyped = showMaskTyped.currentValue;\n      if (showMaskTyped.previousValue === false && showMaskTyped.currentValue === true && this._isFocused()) {\n        requestAnimationFrame(() => {\n          this._maskService._elementRef?.nativeElement.click();\n        });\n      }\n    }\n    if (placeHolderCharacter) {\n      this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n    }\n    if (shownMaskExpression) {\n      this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n    }\n    if (clearIfNotMatch) {\n      this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n    }\n    if (validation) {\n      this._maskService.validation = validation.currentValue;\n    }\n    if (separatorLimit) {\n      this._maskService.separatorLimit = separatorLimit.currentValue;\n    }\n    if (leadZeroDateTime) {\n      this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n    }\n    if (leadZero) {\n      this._maskService.leadZero = leadZero.currentValue;\n    }\n    if (triggerOnMaskChange) {\n      this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n    }\n    if (inputTransformFn) {\n      this._maskService.inputTransformFn = inputTransformFn.currentValue;\n    }\n    if (outputTransformFn) {\n      this._maskService.outputTransformFn = outputTransformFn.currentValue;\n    }\n    if (keepCharacterPositions) {\n      this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n    }\n    this._applyMask();\n  }\n  validate({\n    value\n  }) {\n    const processedValue = typeof value === 'number' ? String(value) : value;\n    const maskValue = this._maskValue();\n    if (!this._maskService.validation || !maskValue) {\n      return null;\n    }\n    if (this._maskService.ipError) {\n      return this._createValidationError(processedValue);\n    }\n    if (this._maskService.cpfCnpjError) {\n      return this._createValidationError(processedValue);\n    }\n    if (maskValue.startsWith(MaskExpression.SEPARATOR)) {\n      return null;\n    }\n    if (withoutValidation.includes(maskValue)) {\n      return null;\n    }\n    if (this._maskService.clearIfNotMatch) {\n      return null;\n    }\n    if (timeMasks.includes(maskValue)) {\n      return this._validateTime(processedValue);\n    }\n    if (maskValue === MaskExpression.EMAIL_MASK) {\n      const emailPattern = /^[^@]+@[^@]+\\.[^@]+$/;\n      if (!emailPattern.test(processedValue) && processedValue) {\n        return this._createValidationError(processedValue);\n      } else {\n        return null;\n      }\n    }\n    if (processedValue && processedValue.length >= 1) {\n      let counterOfOpt = 0;\n      if (maskValue.includes(MaskExpression.CURLY_BRACKETS_LEFT) && maskValue.includes(MaskExpression.CURLY_BRACKETS_RIGHT)) {\n        const lengthInsideCurlyBrackets = maskValue.slice(maskValue.indexOf(MaskExpression.CURLY_BRACKETS_LEFT) + 1, maskValue.indexOf(MaskExpression.CURLY_BRACKETS_RIGHT));\n        return lengthInsideCurlyBrackets === String(processedValue.length) ? null : this._createValidationError(processedValue);\n      }\n      if (maskValue.startsWith(MaskExpression.PERCENT)) {\n        return null;\n      }\n      for (const key in this._maskService.patterns) {\n        if (this._maskService.patterns[key]?.optional) {\n          if (maskValue.indexOf(key) !== maskValue.lastIndexOf(key)) {\n            const opt = maskValue.split(MaskExpression.EMPTY_STRING).filter(i => i === key).join(MaskExpression.EMPTY_STRING);\n            counterOfOpt += opt.length;\n          } else if (maskValue.indexOf(key) !== -1) {\n            counterOfOpt++;\n          }\n          if (maskValue.indexOf(key) !== -1 && processedValue.length >= maskValue.indexOf(key)) {\n            return null;\n          }\n          if (counterOfOpt === maskValue.length) {\n            return null;\n          }\n        }\n      }\n      if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) > 1 && processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_STAR) || maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) > 1 && processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_QUESTION)) {\n        return this._createValidationError(processedValue);\n      }\n      if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) === -1 || maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === -1) {\n        const array = maskValue.split('*');\n        const length = this._maskService.dropSpecialCharacters ? maskValue.length - this._maskService.checkDropSpecialCharAmount(maskValue) - counterOfOpt : this.prefix() ? maskValue.length + this.prefix().length - counterOfOpt : maskValue.length - counterOfOpt;\n        if (array.length === 1) {\n          if (processedValue.length < length) {\n            return this._createValidationError(processedValue);\n          }\n        }\n        if (array.length > 1) {\n          const lastIndexArray = array[array.length - 1];\n          if (lastIndexArray && this._maskService.specialCharacters.includes(lastIndexArray[0]) && String(processedValue).includes(lastIndexArray[0] ?? '') && !this.dropSpecialCharacters()) {\n            const special = value.split(lastIndexArray[0]);\n            return special[special.length - 1].length === lastIndexArray.length - 1 ? null : this._createValidationError(processedValue);\n          } else if ((lastIndexArray && !this._maskService.specialCharacters.includes(lastIndexArray[0]) || !lastIndexArray || this._maskService.dropSpecialCharacters) && processedValue.length >= length - 1) {\n            return null;\n          } else {\n            return this._createValidationError(processedValue);\n          }\n        }\n      }\n      if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) === 1 || maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === 1) {\n        return null;\n      }\n    }\n    if (value) {\n      this.maskFilled.emit();\n      return null;\n    }\n    return null;\n  }\n  onPaste() {\n    this._justPasted.set(true);\n  }\n  onFocus() {\n    this._isFocused.set(true);\n  }\n  onModelChange(value) {\n    if ((value === MaskExpression.EMPTY_STRING || value === null || typeof value === 'undefined') && this._maskService.actualValue) {\n      this._maskService.actualValue = this._maskService.getActualValue(MaskExpression.EMPTY_STRING);\n    }\n  }\n  onInput(e) {\n    if (this._isComposing()) {\n      return;\n    }\n    const el = e.target;\n    const transformedValue = this._maskService.inputTransformFn(el.value);\n    if (el.type !== 'number') {\n      if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n        el.value = transformedValue.toString();\n        this._inputValue.set(el.value);\n        this._setMask();\n        if (!this._maskValue()) {\n          this.onChange(el.value);\n          return;\n        }\n        let position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n        if (this.showMaskTyped() && this.keepCharacterPositions() && this._maskService.placeHolderCharacter.length === 1) {\n          const suffix = this.suffix();\n          const prefix = this.prefix();\n          const inputSymbol = el.value.slice(position - 1, position);\n          const prefixLength = prefix.length;\n          const checkSymbols = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position - 1 - prefixLength] ?? MaskExpression.EMPTY_STRING);\n          const checkSpecialCharacter = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position + 1 - prefixLength] ?? MaskExpression.EMPTY_STRING);\n          const selectRangeBackspace = this._maskService.selStart === this._maskService.selEnd;\n          const selStart = Number(this._maskService.selStart) - prefixLength;\n          const selEnd = Number(this._maskService.selEnd) - prefixLength;\n          const backspaceOrDelete = this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE;\n          if (backspaceOrDelete) {\n            if (!selectRangeBackspace) {\n              if (this._maskService.selStart === prefixLength) {\n                this._maskService.actualValue = `${prefix}${this._maskService.maskIsShown.slice(0, selEnd)}${this._inputValue().split(prefix).join('')}`;\n              } else if (this._maskService.selStart === this._maskService.maskIsShown.length + prefixLength) {\n                this._maskService.actualValue = `${this._inputValue()}${this._maskService.maskIsShown.slice(selStart, selEnd)}`;\n              } else {\n                this._maskService.actualValue = `${prefix}${this._inputValue().split(prefix).join('').slice(0, selStart)}${this._maskService.maskIsShown.slice(selStart, selEnd)}${this._maskService.actualValue.slice(selEnd + prefixLength, this._maskService.maskIsShown.length + prefixLength)}${suffix}`;\n              }\n            } else if (!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(position - prefixLength, position + 1 - prefixLength)) && selectRangeBackspace) {\n              if (selStart === 1 && prefix) {\n                this._maskService.actualValue = `${prefix}${this._maskService.placeHolderCharacter}${el.value.split(prefix).join('').split(suffix).join('')}${suffix}`;\n                position = position - 1;\n              } else {\n                const part1 = el.value.substring(0, position);\n                const part2 = el.value.substring(position);\n                this._maskService.actualValue = `${part1}${this._maskService.placeHolderCharacter}${part2}`;\n              }\n            }\n            position = this._code() === MaskExpression.DELETE ? position + 1 : position;\n          }\n          if (!backspaceOrDelete) {\n            if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n              position = Number(el.selectionStart) - 1;\n            } else if (this._maskService.specialCharacters.includes(el.value.slice(position, position + 1)) && checkSpecialCharacter && !this._maskService.specialCharacters.includes(el.value.slice(position + 1, position + 2))) {\n              this._maskService.actualValue = `${el.value.slice(0, position - 1)}${el.value.slice(position, position + 1)}${inputSymbol}${el.value.slice(position + 2)}`;\n              position = position + 1;\n            } else if (checkSymbols) {\n              if (el.value.length === 1 && position === 1) {\n                this._maskService.actualValue = `${prefix}${inputSymbol}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${suffix}`;\n              } else {\n                this._maskService.actualValue = `${el.value.slice(0, position - 1)}${inputSymbol}${el.value.slice(position + 1).split(suffix).join('')}${suffix}`;\n              }\n            } else if (prefix && el.value.length === 1 && position - prefixLength === 1 && this._maskService._checkSymbolMask(el.value, this._maskService.maskExpression[position - 1 - prefixLength] ?? MaskExpression.EMPTY_STRING)) {\n              this._maskService.actualValue = `${prefix}${el.value}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${suffix}`;\n            }\n          }\n        }\n        let caretShift = 0;\n        let backspaceShift = false;\n        if (this._code() === MaskExpression.DELETE && MaskExpression.SEPARATOR) {\n          this._maskService.deletedSpecialCharacter = true;\n        }\n        if (this._inputValue().length >= this._maskService.maskExpression.length - 1 && this._code() !== MaskExpression.BACKSPACE && this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS && position < 10) {\n          const inputSymbol = this._inputValue().slice(position - 1, position);\n          el.value = this._inputValue().slice(0, position - 1) + inputSymbol + this._inputValue().slice(position + 1);\n        }\n        if (this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS && this.leadZeroDateTime()) {\n          if (position < 3 && Number(el.value) > 31 && Number(el.value) < 40 || position === 5 && Number(el.value.slice(3, 5)) > 12) {\n            position = position + 2;\n          }\n        }\n        if (this._maskService.maskExpression === MaskExpression.HOURS_MINUTES_SECONDS && this.apm()) {\n          if (this._justPasted() && el.value.slice(0, 2) === MaskExpression.DOUBLE_ZERO) {\n            el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n          }\n          el.value = el.value === MaskExpression.DOUBLE_ZERO ? MaskExpression.NUMBER_ZERO : el.value;\n        }\n        this._maskService.applyValueChanges(position, this._justPasted(), this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE, (shift, _backspaceShift) => {\n          this._justPasted.set(false);\n          caretShift = shift;\n          backspaceShift = _backspaceShift;\n        });\n        if (this._getActiveElement() !== el) {\n          return;\n        }\n        if (this._maskService.plusOnePosition) {\n          position = position + 1;\n          this._maskService.plusOnePosition = false;\n        }\n        if (this._maskExpressionArray().length) {\n          if (this._code() === MaskExpression.BACKSPACE) {\n            const specialChartMinusOne = this.specialCharacters().includes(this._maskService.actualValue.slice(position - 1, position));\n            const allowFewMaskChangeMask = this._maskService.removeMask(this._inputValue())?.length === this._maskService.removeMask(this._maskService.maskExpression)?.length;\n            const specialChartPlusOne = this.specialCharacters().includes(this._maskService.actualValue.slice(position, position + 1));\n            if (allowFewMaskChangeMask && !specialChartPlusOne) {\n              position = el.selectionStart + 1;\n            } else {\n              position = specialChartMinusOne ? position - 1 : position;\n            }\n          } else {\n            position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n          }\n        }\n        this._position.set(this._position() === 1 && this._inputValue().length === 1 ? null : this._position());\n        let positionToApply = this._position() ? this._inputValue().length + position + caretShift : position + (this._code() === MaskExpression.BACKSPACE && !backspaceShift ? 0 : caretShift);\n        if (positionToApply > this._getActualInputLength()) {\n          positionToApply = el.value === this._maskService.decimalMarker && el.value.length === 1 ? this._getActualInputLength() + 1 : this._getActualInputLength();\n        }\n        if (positionToApply < 0) {\n          positionToApply = 0;\n        }\n        el.setSelectionRange(positionToApply, positionToApply);\n        this._position.set(null);\n      } else {\n        console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof transformedValue);\n      }\n    } else {\n      if (!this._maskValue()) {\n        this.onChange(el.value);\n        return;\n      }\n      this._maskService.applyValueChanges(el.value.length, this._justPasted(), this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE);\n    }\n  }\n  onCompositionStart() {\n    this._isComposing.set(true);\n  }\n  onCompositionEnd(e) {\n    this._isComposing.set(false);\n    this._justPasted.set(true);\n    this.onInput(e);\n  }\n  onBlur(e) {\n    if (this._maskValue()) {\n      const el = e.target;\n      if (this._maskService.leadZero && el.value.length > 0 && typeof this._maskService.decimalMarker === 'string') {\n        const maskExpression = this._maskService.maskExpression;\n        const decimalMarker = this._maskService.decimalMarker;\n        const suffix = this._maskService.suffix;\n        const precision = Number(this._maskService.maskExpression.slice(maskExpression.length - 1, maskExpression.length));\n        if (precision > 0) {\n          el.value = suffix ? el.value.split(suffix).join('') : el.value;\n          const decimalPart = el.value.split(decimalMarker)[1];\n          el.value = el.value.includes(decimalMarker) ? el.value + MaskExpression.NUMBER_ZERO.repeat(precision - decimalPart.length) + suffix : el.value + decimalMarker + MaskExpression.NUMBER_ZERO.repeat(precision) + suffix;\n          this._maskService.actualValue = el.value;\n        }\n      }\n      this._maskService.clearIfNotMatchFn();\n    }\n    this._isFocused.set(false);\n    this.onTouch();\n  }\n  onClick(e) {\n    if (!this._maskValue()) {\n      return;\n    }\n    const el = e.target;\n    const posStart = 0;\n    const posEnd = 0;\n    if (el !== null && el.selectionStart !== null && el.selectionStart === el.selectionEnd && el.selectionStart > this._maskService.prefix.length && e.keyCode !== 38) {\n      if (this._maskService.showMaskTyped && !this.keepCharacterPositions()) {\n        this._maskService.maskIsShown = this._maskService.showMaskInInput();\n        if (el.setSelectionRange && this._maskService.prefix + this._maskService.maskIsShown === el.value) {\n          el.focus();\n          el.setSelectionRange(posStart, posEnd);\n        } else {\n          if (el.selectionStart > this._maskService.actualValue.length) {\n            el.setSelectionRange(this._maskService.actualValue.length, this._maskService.actualValue.length);\n          }\n        }\n      }\n    }\n    const nextValue = el && (el.value === this._maskService.prefix ? this._maskService.prefix + this._maskService.maskIsShown : el.value);\n    if (el && el.value !== nextValue) {\n      el.value = nextValue;\n    }\n    if (el && el.type !== 'number' && (el.selectionStart || el.selectionEnd) <= this._maskService.prefix.length) {\n      const specialCharactersAtTheStart = this._maskService.maskExpression.match(new RegExp(`^[${this._maskService.specialCharacters.map(c => `\\\\${c}`).join('')}]+`))?.[0].length || 0;\n      el.selectionStart = this._maskService.prefix.length + specialCharactersAtTheStart;\n      return;\n    }\n    if (el && el.selectionEnd > this._getActualInputLength()) {\n      el.selectionEnd = this._getActualInputLength();\n    }\n  }\n  onKeyDown(e) {\n    if (!this._maskValue()) {\n      return;\n    }\n    if (this._isComposing()) {\n      if (e.key === 'Enter') {\n        this.onCompositionEnd(e);\n      }\n      return;\n    }\n    this._code.set(e.code ? e.code : e.key);\n    const el = e.target;\n    this._inputValue.set(el.value);\n    this._setMask();\n    if (el.type !== 'number') {\n      if (e.key === MaskExpression.ARROW_UP) {\n        e.preventDefault();\n      }\n      if (e.key === MaskExpression.ARROW_LEFT || e.key === MaskExpression.BACKSPACE || e.key === MaskExpression.DELETE) {\n        if (e.key === MaskExpression.BACKSPACE && el.value.length === 0) {\n          el.selectionStart = el.selectionEnd;\n        }\n        if (e.key === MaskExpression.BACKSPACE && el.selectionStart !== 0) {\n          const prefixLength = this.prefix().length;\n          const specialCharacters = this.specialCharacters().length ? this.specialCharacters() : this._config.specialCharacters;\n          if (prefixLength > 1 && el.selectionStart <= prefixLength) {\n            el.setSelectionRange(prefixLength, el.selectionEnd);\n          } else {\n            if (this._inputValue().length !== el.selectionStart && el.selectionStart !== 1) {\n              while (specialCharacters.includes((this._inputValue()[el.selectionStart - 1] ?? MaskExpression.EMPTY_STRING).toString()) && (prefixLength >= 1 && el.selectionStart > prefixLength || prefixLength === 0)) {\n                el.setSelectionRange(el.selectionStart - 1, el.selectionEnd);\n              }\n            }\n          }\n        }\n        this.checkSelectionOnDeletion(el);\n        if (this._maskService.prefix.length && el.selectionStart <= this._maskService.prefix.length && el.selectionEnd <= this._maskService.prefix.length) {\n          e.preventDefault();\n        }\n        const cursorStart = el.selectionStart;\n        if (e.key === MaskExpression.BACKSPACE && !el.readOnly && cursorStart === 0 && el.selectionEnd === el.value.length && el.value.length !== 0) {\n          this._position.set(this._maskService.prefix ? this._maskService.prefix.length : 0);\n          this._maskService.applyMask(this._maskService.prefix, this._maskService.maskExpression, this._position());\n        }\n      }\n      if (!!this.suffix() && this.suffix().length > 1 && this._inputValue().length - this.suffix().length < el.selectionStart) {\n        el.setSelectionRange(this._inputValue().length - this.suffix().length, this._inputValue().length);\n      } else if (e.code === 'KeyA' && e.ctrlKey || e.code === 'KeyA' && e.metaKey) {\n        el.setSelectionRange(0, this._getActualInputLength());\n        e.preventDefault();\n      }\n      this._maskService.selStart = el.selectionStart;\n      this._maskService.selEnd = el.selectionEnd;\n    }\n  }\n  async writeValue(controlValue) {\n    let value = controlValue;\n    const inputTransformFn = this.inputTransformFn();\n    if (typeof value === 'object' && value !== null && 'value' in value) {\n      if ('disable' in value) {\n        this.setDisabledState(Boolean(value.disable));\n      }\n      value = value.value;\n    }\n    if (value !== null) {\n      value = inputTransformFn ? inputTransformFn(value) : value;\n    }\n    if (typeof value === 'string' || typeof value === 'number' || value === null || typeof value === 'undefined') {\n      if (value === null || typeof value === 'undefined' || value === '') {\n        this._maskService.currentValue = '';\n        this._maskService.previousValue = '';\n      }\n      let inputValue = value;\n      if (typeof inputValue === 'number' || this._maskValue().startsWith(MaskExpression.SEPARATOR)) {\n        inputValue = String(inputValue);\n        const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n        if (!Array.isArray(this._maskService.decimalMarker)) {\n          inputValue = this._maskService.decimalMarker !== localeDecimalMarker ? inputValue.replace(localeDecimalMarker, this._maskService.decimalMarker) : inputValue;\n        }\n        if (this._maskService.leadZero && inputValue && this.mask() && this.dropSpecialCharacters() !== false) {\n          inputValue = this._maskService._checkPrecision(this._maskService.maskExpression, inputValue);\n        }\n        if (this._maskService.decimalMarker === MaskExpression.COMMA || Array.isArray(this._maskService.decimalMarker) && this._maskService.thousandSeparator === MaskExpression.DOT) {\n          inputValue = inputValue.toString().replace(MaskExpression.DOT, MaskExpression.COMMA);\n        }\n        if (this.mask()?.startsWith(MaskExpression.SEPARATOR) && this.leadZero()) {\n          requestAnimationFrame(() => {\n            this._maskService.applyMask(inputValue?.toString() ?? '', this._maskService.maskExpression);\n          });\n        }\n        this._maskService.isNumberValue = true;\n      }\n      if (typeof inputValue !== 'string' || value === null || typeof value === 'undefined') {\n        inputValue = '';\n      }\n      this._inputValue.set(inputValue);\n      this._setMask();\n      if (inputValue && this._maskService.maskExpression || this._maskService.maskExpression && (this._maskService.prefix || this._maskService.showMaskTyped)) {\n        if (typeof inputTransformFn !== 'function') {\n          this._maskService.writingValue = true;\n        }\n        this._maskService.formElementProperty = ['value', this._maskService.applyMask(inputValue, this._maskService.maskExpression)];\n        if (typeof inputTransformFn !== 'function') {\n          this._maskService.writingValue = false;\n        }\n      } else {\n        this._maskService.formElementProperty = ['value', inputValue];\n      }\n      this._inputValue.set(inputValue);\n    } else {\n      console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof value);\n    }\n  }\n  registerOnChange(fn) {\n    this._maskService.onChange = this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouch = fn;\n  }\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  checkSelectionOnDeletion(el) {\n    const prefixLength = this.prefix().length;\n    const suffixLength = this.suffix().length;\n    const inputValueLength = this._inputValue().length;\n    el.selectionStart = Math.min(Math.max(prefixLength, el.selectionStart), inputValueLength - suffixLength);\n    el.selectionEnd = Math.min(Math.max(prefixLength, el.selectionEnd), inputValueLength - suffixLength);\n  }\n  setDisabledState(isDisabled) {\n    this._maskService.formElementProperty = ['disabled', isDisabled];\n  }\n  _applyMask() {\n    this._maskService.maskExpression = this._maskService._repeatPatternSymbols(this._maskValue() || '');\n    this._maskService.formElementProperty = ['value', this._maskService.applyMask(this._inputValue(), this._maskService.maskExpression)];\n  }\n  _validateTime(value) {\n    const rowMaskLen = this._maskValue().split(MaskExpression.EMPTY_STRING).filter(s => s !== ':').length;\n    if (!value) {\n      return null;\n    }\n    if (+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen || value.length <= rowMaskLen - 2) {\n      return this._createValidationError(value);\n    }\n    return null;\n  }\n  _getActualInputLength() {\n    return this._maskService.actualValue.length || this._maskService.actualValue.length + this._maskService.prefix.length;\n  }\n  _createValidationError(actualValue) {\n    return {\n      mask: {\n        requiredMask: this._maskValue(),\n        actualValue\n      }\n    };\n  }\n  _setMask() {\n    this._maskExpressionArray().some(mask => {\n      const specialChart = mask.split(MaskExpression.EMPTY_STRING).some(char => this._maskService.specialCharacters.includes(char));\n      if (specialChart && this._inputValue() && this._areAllCharactersInEachStringSame(this._maskExpressionArray()) || mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n        const test = this._maskService.removeMask(this._inputValue())?.length <= this._maskService.removeMask(mask)?.length;\n        if (test) {\n          const maskValue = mask.includes(MaskExpression.CURLY_BRACKETS_LEFT) ? this._maskService._repeatPatternSymbols(mask) : mask;\n          this._maskValue.set(maskValue);\n          this._maskService.maskExpression = maskValue;\n          return test;\n        } else {\n          const expression = this._maskExpressionArray()[this._maskExpressionArray().length - 1] ?? MaskExpression.EMPTY_STRING;\n          const maskValue = expression.includes(MaskExpression.CURLY_BRACKETS_LEFT) ? this._maskService._repeatPatternSymbols(expression) : expression;\n          this._maskValue.set(maskValue);\n          this._maskService.maskExpression = maskValue;\n        }\n      } else {\n        const cleanMask = this._maskService.removeMask(mask);\n        const check = this._maskService.removeMask(this._inputValue())?.split(MaskExpression.EMPTY_STRING).every((character, index) => {\n          const indexMask = cleanMask.charAt(index);\n          return this._maskService._checkSymbolMask(character, indexMask);\n        });\n        if (check || this._justPasted()) {\n          this._maskValue.set(mask);\n          this._maskService.maskExpression = mask;\n          return check;\n        }\n      }\n    });\n  }\n  _areAllCharactersInEachStringSame(array) {\n    const specialCharacters = this._maskService.specialCharacters;\n    function removeSpecialCharacters(str) {\n      const regex = new RegExp(`[${specialCharacters.map(ch => `\\\\${ch}`).join('')}]`, 'g');\n      return str.replace(regex, '');\n    }\n    const processedArr = array.map(removeSpecialCharacters);\n    return processedArr.every(str => {\n      const uniqueCharacters = new Set(str);\n      return uniqueCharacters.size === 1;\n    });\n  }\n  static ɵfac = function NgxMaskDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgxMaskDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgxMaskDirective,\n    selectors: [[\"input\", \"mask\", \"\"], [\"textarea\", \"mask\", \"\"]],\n    hostBindings: function NgxMaskDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"paste\", function NgxMaskDirective_paste_HostBindingHandler() {\n          return ctx.onPaste();\n        })(\"focus\", function NgxMaskDirective_focus_HostBindingHandler($event) {\n          return ctx.onFocus($event);\n        })(\"ngModelChange\", function NgxMaskDirective_ngModelChange_HostBindingHandler($event) {\n          return ctx.onModelChange($event);\n        })(\"input\", function NgxMaskDirective_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        })(\"compositionstart\", function NgxMaskDirective_compositionstart_HostBindingHandler($event) {\n          return ctx.onCompositionStart($event);\n        })(\"compositionend\", function NgxMaskDirective_compositionend_HostBindingHandler($event) {\n          return ctx.onCompositionEnd($event);\n        })(\"blur\", function NgxMaskDirective_blur_HostBindingHandler($event) {\n          return ctx.onBlur($event);\n        })(\"click\", function NgxMaskDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        })(\"keydown\", function NgxMaskDirective_keydown_HostBindingHandler($event) {\n          return ctx.onKeyDown($event);\n        });\n      }\n    },\n    inputs: {\n      mask: [1, \"mask\"],\n      specialCharacters: [1, \"specialCharacters\"],\n      patterns: [1, \"patterns\"],\n      prefix: [1, \"prefix\"],\n      suffix: [1, \"suffix\"],\n      thousandSeparator: [1, \"thousandSeparator\"],\n      decimalMarker: [1, \"decimalMarker\"],\n      dropSpecialCharacters: [1, \"dropSpecialCharacters\"],\n      hiddenInput: [1, \"hiddenInput\"],\n      showMaskTyped: [1, \"showMaskTyped\"],\n      placeHolderCharacter: [1, \"placeHolderCharacter\"],\n      shownMaskExpression: [1, \"shownMaskExpression\"],\n      clearIfNotMatch: [1, \"clearIfNotMatch\"],\n      validation: [1, \"validation\"],\n      separatorLimit: [1, \"separatorLimit\"],\n      allowNegativeNumbers: [1, \"allowNegativeNumbers\"],\n      leadZeroDateTime: [1, \"leadZeroDateTime\"],\n      leadZero: [1, \"leadZero\"],\n      triggerOnMaskChange: [1, \"triggerOnMaskChange\"],\n      apm: [1, \"apm\"],\n      inputTransformFn: [1, \"inputTransformFn\"],\n      outputTransformFn: [1, \"outputTransformFn\"],\n      keepCharacterPositions: [1, \"keepCharacterPositions\"],\n      instantPrefix: [1, \"instantPrefix\"]\n    },\n    outputs: {\n      maskFilled: \"maskFilled\"\n    },\n    exportAs: [\"mask\", \"ngxMask\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: NgxMaskDirective,\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: NgxMaskDirective,\n      multi: true\n    }, NgxMaskService]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'input[mask], textarea[mask]',\n      standalone: true,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, NgxMaskService],\n      exportAs: 'mask,ngxMask'\n    }]\n  }], null, {\n    onPaste: [{\n      type: HostListener,\n      args: ['paste']\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus', ['$event']]\n    }],\n    onModelChange: [{\n      type: HostListener,\n      args: ['ngModelChange', ['$event']]\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }],\n    onCompositionStart: [{\n      type: HostListener,\n      args: ['compositionstart', ['$event']]\n    }],\n    onCompositionEnd: [{\n      type: HostListener,\n      args: ['compositionend', ['$event']]\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur', ['$event']]\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass NgxMaskPipe {\n  defaultOptions = inject(NGX_MASK_CONFIG);\n  _maskService = inject(NgxMaskService);\n  _maskExpressionArray = signal([]);\n  _mask = signal('');\n  transform(value, mask, {\n    patterns,\n    ...config\n  } = {}) {\n    let processedValue = value;\n    const currentConfig = {\n      maskExpression: mask,\n      ...this.defaultOptions,\n      ...config,\n      patterns: {\n        ...this._maskService.patterns,\n        ...patterns\n      }\n    };\n    Object.entries(currentConfig).forEach(([key, val]) => {\n      this._maskService[key] = val;\n    });\n    if (mask.includes('||')) {\n      const maskParts = mask.split('||');\n      if (maskParts.length > 1) {\n        this._maskExpressionArray.set(maskParts.sort((a, b) => a.length - b.length));\n        this._setMask(processedValue);\n        return this._maskService.applyMask(`${processedValue}`, this._mask());\n      } else {\n        this._maskExpressionArray.set([]);\n        return this._maskService.applyMask(`${processedValue}`, this._mask());\n      }\n    }\n    if (mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n      return this._maskService.applyMask(`${processedValue}`, this._maskService._repeatPatternSymbols(mask));\n    }\n    if (mask.startsWith(MaskExpression.SEPARATOR)) {\n      if (config.decimalMarker) {\n        this._maskService.decimalMarker = config.decimalMarker;\n      }\n      if (config.thousandSeparator) {\n        this._maskService.thousandSeparator = config.thousandSeparator;\n      }\n      if (config.leadZero) {\n        this._maskService.leadZero = config.leadZero;\n      }\n      processedValue = String(processedValue);\n      const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n      if (!Array.isArray(this._maskService.decimalMarker)) {\n        processedValue = this._maskService.decimalMarker !== localeDecimalMarker ? processedValue.replace(localeDecimalMarker, this._maskService.decimalMarker) : processedValue;\n      }\n      if (this._maskService.leadZero && processedValue && this._maskService.dropSpecialCharacters !== false) {\n        processedValue = this._maskService._checkPrecision(mask, processedValue);\n      }\n      if (this._maskService.decimalMarker === MaskExpression.COMMA) {\n        processedValue = processedValue.replace(MaskExpression.DOT, MaskExpression.COMMA);\n      }\n      this._maskService.isNumberValue = true;\n    }\n    if (processedValue === null || typeof processedValue === 'undefined') {\n      return this._maskService.applyMask('', mask);\n    }\n    return this._maskService.applyMask(`${processedValue}`, mask);\n  }\n  _setMask(value) {\n    if (this._maskExpressionArray().length > 0) {\n      this._maskExpressionArray().some(mask => {\n        const test = this._maskService.removeMask(value)?.length <= this._maskService.removeMask(mask)?.length;\n        if (value && test) {\n          this._mask.set(mask);\n          return test;\n        } else {\n          const expression = this._maskExpressionArray()[this._maskExpressionArray.length - 1] ?? MaskExpression.EMPTY_STRING;\n          this._mask.set(expression);\n        }\n      });\n    }\n  }\n  static ɵfac = function NgxMaskPipe_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgxMaskPipe)();\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"mask\",\n    type: NgxMaskPipe,\n    pure: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'mask',\n      pure: true,\n      standalone: true\n    }]\n  }], null, null);\n})();\nexport { INITIAL_CONFIG, NEW_CONFIG, NGX_MASK_CONFIG, NgxMaskDirective, NgxMaskPipe, NgxMaskService, initialConfig, provideEnvironmentNgxMask, provideNgxMask, timeMasks, withoutValidation };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI;AAAA,CACH,SAAUA,iBAAgB;AACzB,EAAAA,gBAAe,WAAW,IAAI;AAC9B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,IAAI,IAAI;AACvB,EAAAA,gBAAe,UAAU,IAAI;AAC7B,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,MAAM,IAAI;AACzB,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,YAAY,IAAI;AAC/B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,uBAAuB,IAAI;AAC1C,EAAAA,gBAAe,YAAY,IAAI;AAC/B,EAAAA,gBAAe,eAAe,IAAI;AAClC,EAAAA,gBAAe,iBAAiB,IAAI;AACpC,EAAAA,gBAAe,mBAAmB,IAAI;AACtC,EAAAA,gBAAe,aAAa,IAAI;AAChC,EAAAA,gBAAe,MAAM,IAAI;AACzB,EAAAA,gBAAe,KAAK,IAAI;AACxB,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,UAAU,IAAI;AAC7B,EAAAA,gBAAe,KAAK,IAAI;AACxB,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,qBAAqB,IAAI;AACxC,EAAAA,gBAAe,sBAAsB,IAAI;AACzC,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,IAAI,IAAI;AACvB,EAAAA,gBAAe,MAAM,IAAI;AACzB,EAAAA,gBAAe,cAAc,IAAI;AACjC,EAAAA,gBAAe,aAAa,IAAI;AAChC,EAAAA,gBAAe,iBAAiB,IAAI;AACpC,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,aAAa,IAAI;AAChC,EAAAA,gBAAe,aAAa,IAAI;AAChC,EAAAA,gBAAe,aAAa,IAAI;AAChC,EAAAA,gBAAe,WAAW,IAAI;AAC9B,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,YAAY,IAAI;AAC/B,EAAAA,gBAAe,UAAU,IAAI;AAC7B,EAAAA,gBAAe,aAAa,IAAI;AAClC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAC5D,IAAM,aAAa,IAAI,eAAe,qBAAqB;AAC3D,IAAM,iBAAiB,IAAI,eAAe,yBAAyB;AACnE,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,mBAAmB;AAAA,EACnB,eAAe,CAAC,KAAK,GAAG;AAAA,EACxB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,YAAY;AAAA,EACZ,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACxF,kBAAkB;AAAA,EAClB,KAAK;AAAA,EACL,UAAU;AAAA,EACV,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,kBAAkB,WAAS;AAAA,EAC3B,mBAAmB,WAAS;AAAA,EAC5B,YAAY,IAAI,aAAa;AAAA,EAC7B,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,KAAK;AAAA,MACH,SAAS,IAAI,OAAO,KAAK;AAAA,MACzB,UAAU;AAAA,IACZ;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,MACzB,QAAQ;AAAA,IACV;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,aAAa;AAAA,IACnC;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,UAAU;AAAA,IAChC;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,OAAO;AAAA,IAC7B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,OAAO;AAAA,IAC7B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,IACA,GAAG;AAAA,MACD,SAAS,IAAI,OAAO,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAM,YAAY,CAAC,eAAe,uBAAuB,eAAe,eAAe,eAAe,eAAe;AACrH,IAAM,oBAAoB,CAAC,eAAe,SAAS,eAAe,YAAY,eAAe,SAAS,eAAe,SAAS,eAAe,WAAW,eAAe,mBAAmB,eAAe,aAAa,eAAe,MAAM,eAAe,MAAM;AAChQ,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,UAAU,OAAO,eAAe;AAAA,EAChC,wBAAwB,KAAK,QAAQ;AAAA,EACrC,cAAc,KAAK,QAAQ;AAAA,EAC3B,kBAAkB,KAAK,QAAQ;AAAA,EAC/B,oBAAoB,KAAK,QAAQ;AAAA,EACjC,WAAW,KAAK,QAAQ;AAAA,EACxB,SAAS,KAAK,QAAQ;AAAA,EACtB,SAAS,KAAK,QAAQ;AAAA,EACtB,oBAAoB,KAAK,QAAQ;AAAA,EACjC,gBAAgB,KAAK,QAAQ;AAAA,EAC7B;AAAA,EACA,gBAAgB,KAAK,QAAQ;AAAA,EAC7B,uBAAuB,KAAK,QAAQ;AAAA,EACpC,aAAa,KAAK,QAAQ;AAAA,EAC1B,iBAAiB,KAAK,QAAQ;AAAA,EAC9B,uBAAuB,KAAK,QAAQ;AAAA,EACpC,mBAAmB,KAAK,QAAQ;AAAA,EAChC,WAAW,KAAK,QAAQ;AAAA,EACxB,MAAM,KAAK,QAAQ;AAAA,EACnB,mBAAmB,KAAK,QAAQ;AAAA,EAChC,oBAAoB,KAAK,QAAQ;AAAA,EACjC,yBAAyB,KAAK,QAAQ;AAAA,EACtC,gBAAgB,KAAK,QAAQ;AAAA,EAC7B,SAAS,oBAAI,IAAI;AAAA,EACjB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,UAAU,YAAY,gBAAgB,WAAW,GAAG,aAAa,OAAO,aAAa,OAAO,KAAK,MAAM;AAAA,EAAC,GAAG;AACzG,QAAI,CAAC,kBAAkB,OAAO,eAAe,UAAU;AACrD,aAAO,eAAe;AAAA,IACxB;AACA,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,WAAW;AACf,QAAI,iBAAiB;AACrB,QAAI,oBAAoB;AACxB,QAAI,eAAe,MAAM,GAAG,KAAK,OAAO,MAAM,MAAM,KAAK,QAAQ;AAC/D,uBAAiB,eAAe,MAAM,KAAK,OAAO,QAAQ,eAAe,MAAM;AAAA,IACjF;AACA,QAAI,CAAC,CAAC,KAAK,UAAU,eAAe,SAAS,GAAG;AAC9C,uBAAiB,KAAK,qBAAqB,cAAc;AAAA,IAC3D;AACA,QAAI,mBAAmB,OAAO,KAAK,QAAQ;AACzC,uBAAiB;AAAA,IACnB;AACA,UAAM,aAAa,eAAe,SAAS,EAAE,MAAM,eAAe,YAAY;AAC9E,QAAI,KAAK,wBAAwB,eAAe,MAAM,QAAQ,SAAS,CAAC,MAAM,eAAe,OAAO;AAClG,gBAAU,eAAe,MAAM,QAAQ,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,mBAAmB,eAAe,IAAI;AACxC,YAAM,WAAW,eAAe,MAAM,eAAe,GAAG;AACxD,WAAK,UAAU,KAAK,SAAS,QAAQ;AACrC,uBAAiB;AAAA,IACnB;AACA,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAI,eAAe,CAAC,GAAG,MAAM,KAAK,GAAG;AACnC,YAAI,KAAK,eAAe,CAAC,KAAK,eAAe,YAAY;AAAA,MAC3D;AAAA,IACF;AACA,QAAI,mBAAmB,eAAe,UAAU;AAC9C,WAAK,eAAe,IAAI,WAAW,MAAM,IAAI,WAAW;AACxD,UAAI,IAAI,SAAS,IAAI;AACnB,yBAAiB;AAAA,MACnB,OAAO;AACL,yBAAiB;AAAA,MACnB;AAAA,IACF;AACA,QAAI,eAAe,WAAW,eAAe,OAAO,GAAG;AACrD,UAAI,eAAe,MAAM,aAAa,KAAK,eAAe,MAAM,oCAAoC,KAAK,CAAC,YAAY;AACpH,yBAAiB,KAAK,gBAAgB,cAAc;AACpD,cAAM,YAAY,KAAK,aAAa,cAAc;AAClD,yBAAiB,KAAK,oBAAoB,gBAAgB,WAAW,KAAK,aAAa;AAAA,MACzF;AACA,YAAM,gBAAgB,OAAO,KAAK,kBAAkB,WAAW,KAAK,gBAAgB,eAAe;AACnG,UAAI,eAAe,QAAQ,aAAa,IAAI,KAAK,CAAC,KAAK,WAAW,eAAe,UAAU,GAAG,eAAe,QAAQ,aAAa,CAAC,CAAC,GAAG;AACrI,YAAI,OAAO,eAAe,UAAU,GAAG,eAAe,QAAQ,aAAa,IAAI,CAAC;AAChF,YAAI,KAAK,wBAAwB,eAAe,MAAM,QAAQ,SAAS,CAAC,MAAM,eAAe,SAAS,CAAC,YAAY;AACjH,iBAAO,eAAe,UAAU,GAAG,eAAe,QAAQ,aAAa,CAAC;AAAA,QAC1E;AACA,yBAAiB,GAAG,IAAI,GAAG,eAAe,UAAU,eAAe,QAAQ,aAAa,GAAG,eAAe,MAAM,CAAC;AAAA,MACnH;AACA,UAAI,QAAQ;AACZ,WAAK,wBAAwB,eAAe,MAAM,QAAQ,SAAS,CAAC,MAAM,eAAe,QAAQ,QAAQ,GAAG,eAAe,KAAK,GAAG,eAAe,MAAM,SAAS,GAAG,SAAS,eAAe,MAAM,CAAC,KAAK,QAAQ;AAChN,UAAI,KAAK,WAAW,KAAK,GAAG;AAC1B,iBAAS,KAAK,kBAAkB,cAAc;AAAA,MAChD,OAAO;AACL,iBAAS,KAAK,kBAAkB,eAAe,UAAU,GAAG,eAAe,SAAS,CAAC,CAAC;AAAA,MACxF;AAAA,IACF,WAAW,eAAe,WAAW,eAAe,SAAS,GAAG;AAC9D,UAAI,eAAe,MAAM,WAAW,KAAK,eAAe,MAAM,SAAS,KAAK,eAAe,MAAM,aAAa,KAAK,eAAe,MAAM,sCAAsC,KAAK,eAAe,MAAM,eAAe,GAAG;AACxN,yBAAiB,KAAK,gBAAgB,cAAc;AAAA,MACtD;AACA,YAAM,YAAY,KAAK,aAAa,cAAc;AAClD,UAAI,gBAAgB,KAAK;AACzB,UAAI,MAAM,QAAQ,KAAK,aAAa,GAAG;AACrC,YAAI,KAAK,YAAY,SAAS,KAAK,cAAc,CAAC,CAAC,KAAK,KAAK,YAAY,SAAS,KAAK,cAAc,CAAC,CAAC,GAAG;AACxG,0BAAgB,KAAK,YAAY,SAAS,KAAK,cAAc,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC;AAAA,QACjH,OAAO;AACL,0BAAgB,KAAK,cAAc,KAAK,QAAM,OAAO,KAAK,iBAAiB;AAAA,QAC7E;AAAA,MACF;AACA,UAAI,YAAY;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,KAAK,iCAAiC,gBAAgB,aAAa;AACvE,cAAM,iBAAiB,eAAe,CAAC,MAAM,eAAe;AAC5D,cAAM,sBAAsB,eAAe,CAAC,MAAM,eAAe;AACjE,cAAM,yBAAyB,eAAe,CAAC,MAAM;AACrD,cAAM,0BAA0B,eAAe,CAAC,MAAM;AACtD,YAAI,0BAA0B,CAAC,gBAAgB,kBAAkB,2BAA2B,CAAC,gBAAgB,uBAAuB,CAAC,sBAAsB,CAAC,cAAc;AACxK,2BAAiB,eAAe;AAAA,QAClC;AACA,YAAI,sBAAsB,gBAAgB,kBAAkB,sBAAsB,GAAG;AACnF,cAAI,qBAAqB,gBAAgB,qBAAqB,cAAc;AAC1E,6BAAiB,eAAe,QAAQ,eAAe,MAAM,YAAY;AAAA,UAC3E;AAAA,QACF;AACA,YAAI,CAAC,sBAAsB,gBAAgB,eAAe,SAAS,cAAc;AAC/E,2BAAiB,iBAAiB,eAAe,QAAQ,eAAe,MAAM,YAAY,IAAI,eAAe,MAAM,YAAY;AAAA,QACjI;AACA,YAAI,sBAAsB,gBAAgB,sBAAsB,GAAG;AACjE,cAAI,qBAAqB,cAAc;AACrC,6BAAiB,eAAe,MAAM,qBAAqB,CAAC;AAAA,UAC9D;AACA,cAAI,qBAAqB,cAAc;AACrC,6BAAiB,eAAe,MAAM,YAAY;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AACA,UAAI,cAAc,GAAG;AACnB,yBAAiB,KAAK,uBAAuB,eAAe,SAAS,KAAK,eAAe,CAAC,MAAM,eAAe,SAAS,eAAe,CAAC,MAAM,eAAe,eAAe,eAAe,CAAC,MAAM,KAAK,qBAAqB,eAAe,CAAC,MAAM,eAAe,SAAS,eAAe,CAAC,MAAM,eAAe,MAAM,MAAM,eAAe,MAAM,GAAG,eAAe,MAAM,IAAI,eAAe,CAAC,MAAM,eAAe,eAAe,eAAe,SAAS,KAAK,eAAe,CAAC,MAAM,KAAK,qBAAqB,eAAe,CAAC,MAAM,eAAe,SAAS,eAAe,CAAC,MAAM,eAAe,MAAM,eAAe,MAAM,GAAG,eAAe,MAAM,IAAI,iBAAiB,eAAe,SAAS,KAAK,eAAe,CAAC,MAAM,eAAe,eAAe,eAAe,CAAC,MAAM,KAAK,qBAAqB,eAAe,CAAC,MAAM,eAAe,SAAS,eAAe,CAAC,MAAM,eAAe,MAAM,eAAe,MAAM,GAAG,eAAe,MAAM,IAAI;AAAA,MACr5B,OAAO;AACL,YAAI,eAAe,CAAC,MAAM,iBAAiB,eAAe,SAAS,KAAK,CAAC,YAAY;AACnF,2BAAiB,eAAe,cAAc,eAAe,MAAM,GAAG,eAAe,SAAS,CAAC;AAC/F,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,eAAe,CAAC,MAAM,eAAe,eAAe,eAAe,CAAC,MAAM,iBAAiB,eAAe,CAAC,MAAM,KAAK,qBAAqB,CAAC,YAAY;AAC1J,2BAAiB,eAAe,SAAS,IAAI,eAAe,MAAM,GAAG,CAAC,IAAI,gBAAgB,eAAe,MAAM,GAAG,eAAe,SAAS,CAAC,IAAI;AAC/I,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,KAAK,wBAAwB,CAAC,cAAc,eAAe,CAAC,MAAM,eAAe,UAAU,eAAe,CAAC,MAAM,iBAAiB,eAAe,CAAC,MAAM,eAAe,cAAc;AACvL,2BAAiB,eAAe,CAAC,MAAM,iBAAiB,eAAe,SAAS,IAAI,eAAe,MAAM,GAAG,CAAC,IAAI,eAAe,cAAc,eAAe,MAAM,GAAG,eAAe,MAAM,IAAI,eAAe,CAAC,MAAM,eAAe,eAAe,eAAe,SAAS,KAAK,eAAe,CAAC,MAAM,gBAAgB,eAAe,MAAM,GAAG,CAAC,IAAI,gBAAgB,eAAe,MAAM,GAAG,eAAe,MAAM,IAAI;AACpZ,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF;AACA,YAAM,+BAA+B,KAAK,wBAAwB,KAAK,iBAAiB;AACxF,UAAI,eAAe,2CAA2C,QAAQ,8BAA8B,EAAE;AACtG,UAAI,MAAM,QAAQ,KAAK,aAAa,GAAG;AACrC,mBAAW,UAAU,KAAK,eAAe;AACvC,yBAAe,aAAa,QAAQ,KAAK,wBAAwB,MAAM,GAAG,eAAe,YAAY;AAAA,QACvG;AAAA,MACF,OAAO;AACL,uBAAe,aAAa,QAAQ,KAAK,wBAAwB,KAAK,aAAa,GAAG,EAAE;AAAA,MAC1F;AACA,YAAM,oBAAoB,IAAI,OAAO,MAAM,eAAe,GAAG;AAC7D,UAAI,eAAe,MAAM,iBAAiB,GAAG;AAC3C,yBAAiB,eAAe,UAAU,GAAG,eAAe,SAAS,CAAC;AAAA,MACxE;AACA,uBAAiB,KAAK,oBAAoB,gBAAgB,WAAW,KAAK,aAAa;AACvF,YAAM,YAAY,eAAe,QAAQ,IAAI,OAAO,8BAA8B,GAAG,GAAG,EAAE;AAC1F,eAAS,KAAK,sBAAsB,WAAW,KAAK,mBAAmB,KAAK,eAAe,SAAS;AACpG,YAAM,aAAa,OAAO,QAAQ,eAAe,KAAK,IAAI,eAAe,QAAQ,eAAe,KAAK;AACrG,YAAM,YAAY,OAAO,SAAS,eAAe;AACjD,YAAM,4CAA4C,cAAc,OAAO,SAAS,WAAW,UAAU,KAAK;AAC1G,WAAK,OAAO,oBAAoB,CAAC,MAAM,KAAK,qBAAqB,OAAO,oBAAoB,KAAK,OAAO,MAAM,MAAM,KAAK,UAAU,YAAY;AAC7I,4BAAoB,oBAAoB;AAAA,MAC1C,WAAW,YAAY,KAAK,OAAO,iBAAiB,MAAM,KAAK,qBAAqB,2CAA2C;AAC7H,yBAAiB;AACjB,YAAI,SAAS;AACb,WAAG;AACD,eAAK,OAAO,IAAI,oBAAoB,MAAM;AAC1C;AAAA,QACF,SAAS,SAAS;AAAA,MACpB,WAAW,OAAO,oBAAoB,CAAC,MAAM,KAAK,qBAAqB,cAAc,MAAM,cAAc,MAAM,OAAO,iBAAiB,MAAM,KAAK,mBAAmB;AACnK,aAAK,OAAO,MAAM;AAClB,aAAK,OAAO,IAAI,oBAAoB,CAAC;AAAA,MACvC,WAAW,eAAe,KAAK,oBAAoB,KAAK,EAAE,OAAO,QAAQ,eAAe,KAAK,KAAK,qBAAqB,oBAAoB,MAAM,EAAE,OAAO,QAAQ,eAAe,GAAG,KAAK,qBAAqB,oBAAoB,MAAM,aAAa,GAAG;AACtP,aAAK,OAAO,MAAM;AAClB,yBAAiB;AACjB,gBAAQ;AACR,6BAAqB;AACrB,aAAK,OAAO,IAAI,iBAAiB;AAAA,MACnC,OAAO;AACL,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,cAAc,WAAW,CAAC,GAAG,IAAI,WAAW,QAAQ,KAAK,cAAc,WAAW,CAAC,KAAK,eAAe,cAAc;AACnI,YAAI,WAAW,eAAe,QAAQ;AACpC;AAAA,QACF;AACA,cAAM,sBAAsB,eAAe,eAAe,KAAK;AAC/D,YAAI,KAAK,iBAAiB,aAAa,eAAe,MAAM,KAAK,eAAe,YAAY,KAAK,eAAe,SAAS,CAAC,MAAM,eAAe,iBAAiB;AAC9J,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,eAAe,SAAS,CAAC,MAAM,eAAe,eAAe,SAAS,KAAK,iBAAiB,aAAa,eAAe,SAAS,CAAC,KAAK,eAAe,YAAY,GAAG;AAC9K,oBAAU;AACV,oBAAU;AACV,kBAAQ;AAAA,QACV,WAAW,KAAK,iBAAiB,aAAa,eAAe,MAAM,KAAK,eAAe,YAAY,KAAK,eAAe,SAAS,CAAC,MAAM,eAAe,eAAe,CAAC,qBAAqB;AACzL,oBAAU;AACV,kBAAQ;AAAA,QACV,WAAW,eAAe,SAAS,CAAC,MAAM,eAAe,mBAAmB,KAAK,iBAAiB,aAAa,eAAe,SAAS,CAAC,KAAK,eAAe,YAAY,GAAG;AACzK,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,KAAK,iBAAiB,aAAa,eAAe,MAAM,KAAK,eAAe,YAAY,GAAG;AACpG,cAAI,eAAe,MAAM,MAAM,eAAe,OAAO;AACnD,gBAAI,KAAK,MAAM,OAAO,WAAW,IAAI,IAAI,OAAO,WAAW,IAAI,GAAG;AAChE,kCAAoB,CAAC,KAAK,mBAAmB,oBAAoB,IAAI;AACrE,wBAAU;AACV,mBAAK,WAAW,MAAM;AACtB;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,MAAM,MAAM,eAAe,MAAM;AAClD,gBAAI,KAAK,MAAM,OAAO,WAAW,KAAK,OAAO,MAAM,IAAI,KAAK,WAAW,OAAO,OAAO,WAAW,IAAI,KAAK,eAAe,MAAM,SAAS,GAAG,MAAM,EAAE,WAAW,KAAK,OAAO,eAAe,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,KAAK,eAAe,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI,WAAW,OAAO,OAAO,WAAW,IAAI,MAAM,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,OAAO,OAAO,MAAM,SAAS,GAAG,MAAM,MAAM,QAAQ,OAAO,WAAW,IAAI,KAAK,SAAS,IAAI;AAC7iB,kCAAoB,oBAAoB;AACxC,wBAAU;AACV;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,MAAM,MAAM,eAAe,UAAU,eAAe,MAAM,MAAM,eAAe,QAAQ;AACxG,gBAAI,OAAO,WAAW,IAAI,GAAG;AAC3B,kCAAoB,CAAC,KAAK,mBAAmB,oBAAoB,IAAI;AACrE,wBAAU;AACV,mBAAK,WAAW,MAAM;AACtB;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,gBAAM,mBAAmB,eAAe,MAAM;AAC9C,gBAAM,0BAA0B,eAAe,SAAS,CAAC;AACzD,gBAAM,0BAA0B,eAAe,SAAS,CAAC;AACzD,gBAAM,2BAA2B,eAAe,SAAS,CAAC;AAC1D,gBAAM,2BAA2B,eAAe,SAAS,CAAC;AAC1D,gBAAM,oCAAoC,eAAe,MAAM,SAAS,GAAG,SAAS,CAAC;AACrF,gBAAM,iCAAiC,eAAe,MAAM,SAAS,GAAG,SAAS,CAAC;AAClF,gBAAM,+BAA+B,eAAe,MAAM,QAAQ,SAAS,CAAC;AAC5E,gBAAM,gCAAgC,eAAe,MAAM,SAAS,GAAG,MAAM;AAC7E,cAAI,eAAe,MAAM,MAAM,eAAe,KAAK;AACjD,kBAAM,qBAAqB,eAAe,MAAM,GAAG,CAAC,MAAM,eAAe;AACzE,kBAAM,sBAAsB,eAAe,MAAM,GAAG,CAAC,MAAM,eAAe,UAAU,KAAK,kBAAkB,SAAS,wBAAwB;AAC5I,gBAAI,OAAO,WAAW,IAAI,KAAK,KAAK,oBAAoB,CAAC,uBAAuB,OAAO,4BAA4B,IAAI,aAAa,OAAO,8BAA8B,IAAI,aAAa,KAAK,kBAAkB,SAAS,uBAAuB,OAAO,sBAAsB,OAAO,8BAA8B,IAAI,aAAa,CAAC,KAAK,kBAAkB,SAAS,gBAAgB,KAAK,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,KAAK,kBAAkB,SAAS,gBAAgB,IAAI,OAAO,4BAA4B,IAAI,aAAa,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,CAAC,aAAa;AACtmB,kCAAoB,CAAC,KAAK,mBAAmB,oBAAoB,IAAI;AACrE,wBAAU;AACV,mBAAK,WAAW,MAAM;AACtB;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,MAAM,MAAM,eAAe,OAAO;AACnD,kBAAM,cAAc;AACpB,kBAAM,cAAc,WAAW,MAAM,OAAO,WAAW,IAAI,KAAK,OAAO,4BAA4B,IAAI,eAAe,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,CAAC;AACnL,kBAAM,eAAe,eAAe,MAAM,SAAS,GAAG,SAAS,CAAC;AAChE,kBAAM,iBAAiB,kCAAkC,SAAS,YAAY,KAAK,eAAe,SAAS,IAAI,MAAM,KAAK,kBAAkB,SAAS,wBAAwB,KAAK,OAAO,8BAA8B,IAAI,eAAe,CAAC,KAAK,kBAAkB,SAAS,gBAAgB,KAAK,KAAK,kBAAkB,SAAS,gBAAgB;AAChV,kBAAM,iBAAiB,OAAO,iCAAiC,KAAK,aAAa,CAAC,KAAK,kBAAkB,SAAS,iCAAiC,KAAK,KAAK,kBAAkB,SAAS,wBAAwB,MAAM,OAAO,4BAA4B,IAAI,eAAe,KAAK,kBAAkB,SAAS,uBAAuB;AACnU,kBAAM,oBAAoB,OAAO,4BAA4B,IAAI,eAAe,WAAW,KAAK,KAAK,kBAAkB,SAAS,uBAAuB,KAAK,WAAW;AACvK,kBAAM,iBAAiB,OAAO,iCAAiC,IAAI,aAAa,CAAC,KAAK,kBAAkB,SAAS,iCAAiC,KAAK,CAAC,KAAK,kBAAkB,SAAS,6BAA6B,KAAK,OAAO,6BAA6B,IAAI,eAAe,eAAe,SAAS,IAAI;AAC7S,kBAAM,iBAAiB,OAAO,iCAAiC,KAAK,aAAa,CAAC,KAAK,kBAAkB,SAAS,iCAAiC,KAAK,CAAC,KAAK,kBAAkB,SAAS,wBAAwB,KAAK,OAAO,8BAA8B,IAAI;AAC/P,gBAAI,OAAO,WAAW,IAAI,KAAK,KAAK,oBAAoB,eAAe,kBAAkB,kBAAkB,kBAAkB,kBAAkB,qBAAqB,CAAC,KAAK,kBAAkB;AAC1L,kCAAoB,CAAC,KAAK,mBAAmB,oBAAoB,IAAI;AACrE,wBAAU;AACV,mBAAK,WAAW,MAAM;AACtB;AACA,kBAAI,KAAK,kBAAkB;AACzB,0BAAU;AAAA,cACZ;AACA;AAAA,YACF;AAAA,UACF;AACA,oBAAU;AACV;AAAA,QACF,WAAW,KAAK,kBAAkB,SAAS,WAAW,KAAK,eAAe,MAAM,MAAM,aAAa;AACjG,oBAAU;AACV;AAAA,QACF,WAAW,KAAK,kBAAkB,QAAQ,eAAe,MAAM,KAAK,eAAe,YAAY,MAAM,IAAI;AACvG,oBAAU,eAAe,MAAM;AAC/B;AACA,eAAK,WAAW,MAAM;AACtB;AAAA,QACF,WAAW,eAAe,MAAM,MAAM,eAAe,eAAe,KAAK,eAAe;AACtF,eAAK,WAAW,MAAM;AAAA,QACxB,WAAW,KAAK,SAAS,eAAe,MAAM,KAAK,eAAe,YAAY,KAAK,KAAK,SAAS,eAAe,MAAM,KAAK,eAAe,YAAY,GAAG,UAAU;AACjK,cAAI,CAAC,CAAC,WAAW,MAAM,KAAK,mBAAmB,qBAAqB,mBAAmB,oBAAoB,mBAAmB,wBAAwB,CAAC,eAAe,MAAM,UAAU,KAAK,CAAC,KAAK,SAAS,eAAe,MAAM,KAAK,eAAe,YAAY,GAAG,UAAU;AAC1Q,sBAAU,WAAW,MAAM;AAAA,UAC7B;AACA,cAAI,eAAe,SAAS,eAAe,cAAc,eAAe,WAAW,KAAK,eAAe,SAAS,eAAe,cAAc,eAAe,WAAW,GAAG;AACxK;AAAA,UACF;AACA;AACA;AAAA,QACF,WAAW,KAAK,eAAe,SAAS,CAAC,MAAM,eAAe,eAAe,KAAK,iBAAiB,KAAK,eAAe,SAAS,CAAC,KAAK,eAAe,YAAY,KAAK,KAAK,iBAAiB,WAAW,MAAM,KAAK,eAAe,SAAS,CAAC,KAAK,OAAO;AACrP,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,KAAK,eAAe,SAAS,CAAC,MAAM,eAAe,mBAAmB,KAAK,iBAAiB,KAAK,eAAe,SAAS,CAAC,KAAK,eAAe,YAAY,KAAK,KAAK,iBAAiB,WAAW,MAAM,KAAK,eAAe,SAAS,CAAC,KAAK,OAAO;AACzP,oBAAU;AACV,oBAAU;AAAA,QACZ,WAAW,KAAK,iBAAiB,KAAK,kBAAkB,QAAQ,WAAW,IAAI,KAAK,gBAAgB,KAAK,wBAAwB,KAAK,qBAAqB,WAAW,GAAG;AACvK,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,oBAAoB,CAAC,KAAK,OAAO,SAAS,MAAM,eAAe,UAAU,KAAK,kBAAkB,QAAQ,eAAe,eAAe,SAAS,CAAC,KAAK,eAAe,YAAY,MAAM,IAAI;AACnM,gBAAU,eAAe,eAAe,SAAS,CAAC;AAAA,IACpD;AACA,QAAI,cAAc,oBAAoB;AACtC,WAAO,KAAK,OAAO,IAAI,WAAW,GAAG;AACnC;AACA;AAAA,IACF;AACA,QAAI,cAAc,cAAc,CAAC,eAAe,WAAW,eAAe,SAAS,IAAI,SAAS,KAAK,OAAO,IAAI,iBAAiB,IAAI,QAAQ;AAC7I,QAAI,UAAU;AACZ;AAAA,IACF;AACA,OAAG,aAAa,cAAc;AAC9B,QAAI,QAAQ,GAAG;AACb,WAAK,OAAO,MAAM;AAAA,IACpB;AACA,QAAI,cAAc;AAClB,QAAI,YAAY;AACd,oBAAc,WAAW,MAAM,UAAQ,KAAK,kBAAkB,SAAS,IAAI,CAAC;AAAA,IAC9E;AACA,QAAI,MAAM,GAAG,KAAK,MAAM,GAAG,cAAc,eAAe,eAAe,MAAM,GAAG,KAAK,gBAAgB,KAAK,KAAK,MAAM;AACrH,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,KAAK,gBAAgB,GAAG,KAAK,MAAM,GAAG,MAAM,KAAK,GAAG,MAAM;AAAA,IAClE;AACA,UAAM,oCAAoC,eAAe,WAAW,KAAK,KAAK,kBAAkB,SAAS,eAAe,CAAC,CAAC,KAAK,mBAAmB,eAAe,CAAC;AAClK,QAAI,CAAC,KAAK,iBAAiB,gBAAgB,eAAe,CAAC,CAAC,KAAK,mCAAmC;AAClG,aAAO;AAAA,IACT;AACA,QAAI,OAAO,SAAS,eAAe,KAAK,KAAK,KAAK,UAAU,KAAK,sBAAsB;AACrF,UAAI,cAAc,WAAW,eAAe,OAAO;AACjD,eAAO;AAAA,MACT;AACA,YAAM,GAAG,eAAe,KAAK,GAAG,KAAK,MAAM,GAAG,OAAO,MAAM,eAAe,KAAK,EAAE,KAAK,eAAe,YAAY,CAAC,GAAG,KAAK,MAAM;AAAA,IAClI;AACA,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,aAAa;AAChC,QAAI,MAAM,QAAQ,KAAK,qBAAqB,GAAG;AAC7C,aAAO,KAAK,sBAAsB,KAAK,SAAO,QAAQ,WAAW;AAAA,IACnE;AACA,WAAO,KAAK,iBAAiB,WAAW;AAAA,EAC1C;AAAA,EACA,iBAAiB,aAAa;AAC5B,WAAO,KAAK,kBAAkB,KAAK,SAAO,QAAQ,WAAW;AAAA,EAC/D;AAAA,EACA,iBAAiB,aAAa,YAAY;AACxC,SAAK,WAAW,KAAK,gBAAgB,KAAK,gBAAgB,KAAK;AAC/D,YAAQ,KAAK,SAAS,UAAU,GAAG,WAAW,KAAK,SAAS,UAAU,GAAG,QAAQ,KAAK,WAAW,MAAM;AAAA,EACzG;AAAA,EACA,wBAAwB,CAAC,KAAK,uBAAuB,cAAc,cAAc;AAC/E,QAAI,IAAI,CAAC;AACT,QAAI,cAAc;AAClB,QAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,YAAM,SAAS,IAAI,OAAO,aAAa,IAAI,OAAK,eAAe,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC;AACxG,UAAI,IAAI,MAAM,MAAM;AACpB,oBAAc,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,eAAe;AAAA,IACzD,OAAO;AACL,UAAI,IAAI,MAAM,YAAY;AAC1B,oBAAc;AAAA,IAChB;AACA,UAAM,WAAW,EAAE,SAAS,IAAI,GAAG,WAAW,GAAG,EAAE,CAAC,CAAC,KAAK,eAAe;AACzE,QAAI,MAAM,EAAE,CAAC,KAAK,eAAe;AACjC,UAAM,iBAAiB,KAAK,eAAe,QAAQ,OAAO,eAAe,YAAY;AACrF,QAAI,kBAAkB,CAAC,gBAAgB;AACrC,UAAI,IAAI,CAAC,MAAM,eAAe,OAAO;AACnC,cAAM,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,EAAE,MAAM,GAAG,eAAe,MAAM,CAAC;AAAA,MACpE,OAAO;AACL,cAAM,IAAI,MAAM,GAAG,eAAe,MAAM;AAAA,MAC1C;AAAA,IACF;AACA,UAAM,MAAM;AACZ,WAAO,yBAAyB,IAAI,KAAK,GAAG,GAAG;AAC7C,YAAM,IAAI,QAAQ,KAAK,OAAO,wBAAwB,IAAI;AAAA,IAC5D;AACA,QAAI,OAAO,cAAc,aAAa;AACpC,aAAO,MAAM;AAAA,IACf,WAAW,cAAc,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,MAAM,SAAS,UAAU,GAAG,YAAY,CAAC;AAAA,EAClD;AAAA,EACA,aAAa,SAAO;AAClB,UAAM,eAAe,IAAI,QAAQ,KAAK,GAAG;AACzC,UAAM,QAAQ,OAAO,KAAK,wBAAwB,IAAI,SAAS,eAAe,KAAK,IAAI,aAAa,MAAM,GAAG,IAAI,MAAM,IAAI,YAAY;AACvI,WAAO,CAAC,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS;AAAA,EACjD;AAAA,EACA,eAAe,oBAAkB;AAC/B,UAAM,IAAI,eAAe,MAAM,eAAe,GAAG;AACjD,QAAI,EAAE,SAAS,GAAG;AAChB,aAAO,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,gBAAc;AACnC,aAAS,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,YAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,QAAQ,MAAM;AAC3D,UAAI,WAAW,SAAS,MAAM,KAAK,MAAM,KAAK,QAAQ,SAAS,MAAM,IAAI,IAAI,KAAK,CAAC,WAAW,SAAS,KAAK,OAAO,UAAU,IAAI,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI;AAC1J,eAAO,WAAW,QAAQ,QAAQ,eAAe,YAAY;AAAA,MAC/D;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,CAAC,YAAY,WAAW,kBAAkB;AAC9D,QAAI,sBAAsB;AAC1B,QAAI,yBAAyB;AAC7B,QAAI,YAAY,UAAU;AACxB,UAAI,MAAM,QAAQ,sBAAsB,GAAG;AACzC,cAAM,SAAS,uBAAuB,KAAK,QAAM,OAAO,KAAK,iBAAiB;AAC9E,iCAAyB,SAAS,SAAS,uBAAuB,CAAC;AAAA,MACrE;AACA,YAAM,iBAAiB,IAAI,OAAO,KAAK,wBAAwB,sBAAsB,IAAI,OAAO,SAAS,MAAM;AAC/G,YAAM,iBAAiB,oBAAoB,MAAM,cAAc;AAC/D,YAAM,wBAAwB,kBAAkB,eAAe,CAAC,GAAG,WAAW;AAC9E,UAAI,uBAAuB,IAAI,WAAW;AACxC,cAAM,OAAO,uBAAuB,IAAI;AACxC,8BAAsB,oBAAoB,UAAU,GAAG,oBAAoB,SAAS,IAAI;AAAA,MAC1F;AACA,UAAI,cAAc,KAAK,KAAK,mBAAmB,oBAAoB,oBAAoB,SAAS,CAAC,GAAG,wBAAwB,KAAK,iBAAiB,GAAG;AACnJ,8BAAsB,oBAAoB,UAAU,GAAG,oBAAoB,SAAS,CAAC;AAAA,MACvF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,KAAK;AACnB,WAAO,IAAI,MAAM,eAAe,YAAY,EAAE,OAAO,CAAC,GAAG,QAAQ;AAC/D,YAAM,kBAAkB,OAAO,KAAK,kBAAkB,WAAW,MAAM,KAAK,gBAAgB,KAAK,cAAc,SAAS,CAAC;AACzH,aAAO,EAAE,MAAM,QAAQ,KAAK,MAAM,KAAK,qBAAqB,mBAAmB,MAAM,eAAe,SAAS,QAAQ,KAAK,KAAK;AAAA,IACjI,CAAC,EAAE,KAAK,eAAe,YAAY;AAAA,EACrC;AAAA,EACA,wBAAwB,MAAM;AAC5B,QAAI,MAAM;AACR,YAAM,gBAAgB;AACtB,aAAO,SAAS,MAAM,QAAQ,cAAc,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAAA,IACjF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,QAAQ;AACjB,SAAK,OAAO,IAAI,SAAS,KAAK,OAAO,UAAU,CAAC;AAAA,EAClD;AAAA,EACA,mBAAmB,OAAO,eAAe,eAAe;AACtD,WAAO,MAAM,QAAQ,aAAa,IAAI,cAAc,OAAO,OAAK,MAAM,aAAa,EAAE,SAAS,KAAK,IAAI,UAAU;AAAA,EACnH;AAAA,EACA,SAAS,UAAU;AACjB,WAAO,EAAE,SAAS,WAAW,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,UAAU;AACjE,UAAI,SAAS,WAAW,QAAQ,GAAG;AACjC,eAAO,UAAU,eAAe,gBAAgB,OAAO,KAAK,IAAI;AAAA,MAClE;AACA,aAAO,UAAU,eAAe,gBAAgB,OAAO,MAAM,UAAU,GAAG,CAAC,CAAC,IAAI;AAAA,IAClF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,OAAO;AACvB,QAAI,UAAU,eAAe,SAAS,KAAK,sBAAsB;AAC/D,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,kBAAkB,WAAW,MAAM,QAAQ,KAAK,aAAa,IAAI,MAAM,QAAQ,eAAe,GAAG;AAClI,UAAM,eAAe,KAAK,wBAAwB,MAAM,SAAS,eAAe,KAAK,IAAI,MAAM;AAC/F,QAAI,iBAAiB,IAAI;AACvB,YAAM,cAAc,SAAS,eAAe,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,OAAO,EAAE;AACpF,aAAO,MAAM,WAAW,IAAI,eAAe,eAAe,GAAG,YAAY,GAAG,WAAW;AAAA,IACzF,OAAO;AACL,YAAM,cAAc,SAAS,MAAM,QAAQ,KAAK,EAAE,EAAE,UAAU,GAAG,YAAY,GAAG,EAAE;AAClF,YAAM,cAAc,MAAM,UAAU,eAAe,CAAC;AACpD,YAAM,gBAAgB,MAAM,WAAW,IAAI,KAAK,YAAY,SAAS;AACrE,YAAM,UAAU,OAAO,KAAK,kBAAkB,WAAW,KAAK,gBAAgB,eAAe;AAC7F,aAAO,kBAAkB,eAAe,eAAe,eAAe,eAAe,GAAG,YAAY,GAAG,aAAa,GAAG,OAAO,GAAG,WAAW;AAAA,IAC9I;AAAA,EACF;AAAA,EACA,iCAAiC,aAAa,eAAe;AAC3D,QAAI,qBAAqB;AACzB,QAAI,eAAe;AACnB,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAM,OAAO,YAAY,CAAC;AAC1B,UAAI,SAAS,iBAAiB,uBAAuB,MAAM;AACzD,6BAAqB;AAAA,MACvB;AACA,UAAI,QAAQ,QAAQ,OAAO,QAAQ,OAAO,iBAAiB,MAAM;AAC/D,uBAAe;AAAA,MACjB;AACA,UAAI,uBAAuB,QAAQ,iBAAiB,MAAM;AACxD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAN,MAAM,wBAAuB,sBAAsB;AAAA,EACjD,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,sBAAsB,CAAC;AAAA,EACvB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA,WAAW,OAAK;AAAA,EAAC;AAAA,EACjB,cAAc,OAAO,YAAY;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,QAAQ;AAAA,EAC1B,UAAU,OAAO,eAAe;AAAA,EAChC,YAAY,OAAO,WAAW;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,YAAY,gBAAgB,WAAW,GAAG,aAAa,OAAO,aAAa,OAAO,KAAK,MAAM;AAAA,EAAC,GAAG;AACzG,QAAI,CAAC,gBAAgB;AACnB,aAAO,eAAe,KAAK,cAAc,KAAK,cAAc;AAAA,IAC9D;AACA,SAAK,cAAc,KAAK,gBAAgB,KAAK,gBAAgB,IAAI,eAAe;AAChF,QAAI,KAAK,mBAAmB,eAAe,MAAM,KAAK,eAAe;AACnE,WAAK,cAAc,KAAK,gBAAgB,cAAc,eAAe,IAAI;AAAA,IAC3E;AACA,QAAI,KAAK,mBAAmB,eAAe,YAAY,KAAK,eAAe;AACzE,WAAK,cAAc,KAAK,gBAAgB,cAAc,eAAe,IAAI;AAAA,IAC3E;AACA,QAAI,CAAC,cAAc,KAAK,eAAe;AACrC,WAAK,kBAAkB,KAAK,MAAM;AAClC,aAAO,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM;AAAA,IACxD;AACA,UAAM,YAAY,CAAC,CAAC,cAAc,OAAO,KAAK,aAAa,WAAW,WAAW,KAAK,QAAQ,KAAK,eAAe,eAAe,eAAe;AAChJ,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAClB,SAAK,KAAK,eAAe,cAAc,WAAW,QAAQ,eAAe,WAAW,KAAK,MAAM,CAAC,KAAK,cAAc;AACjH,UAAI,eAAe,cAAc,WAAW,WAAW,IAAI,WAAW,MAAM,eAAe,YAAY,IAAI,KAAK,YAAY,MAAM,eAAe,YAAY;AAC7J,UAAI,YAAY;AACd,uBAAe,aAAa,MAAM,GAAG,QAAQ,EAAE,OAAO,aAAa,MAAM,WAAW,CAAC,CAAC;AAAA,MACxF;AACA,UAAI,KAAK,eAAe;AACtB,qBAAa,KAAK,WAAW,UAAU;AACvC,uBAAe,KAAK,WAAW,aAAa,KAAK,EAAE,CAAC,EAAE,MAAM,eAAe,YAAY;AAAA,MACzF;AACA,UAAI,OAAO,KAAK,aAAa,YAAY,OAAO,KAAK,WAAW,UAAU;AACxE,aAAK,WAAW,OAAO,KAAK,QAAQ;AACpC,aAAK,SAAS,OAAO,KAAK,MAAM;AAAA,MAClC,OAAO;AACL,YAAI,eAAe,eAAe,gBAAgB,aAAa,QAAQ;AACrE,cAAI,OAAO,KAAK,aAAa,YAAY,OAAO,KAAK,WAAW,UAAU;AACxE,gBAAI,WAAW,SAAS,aAAa,QAAQ;AAC3C,2BAAa,OAAO,KAAK,UAAU,GAAG,SAAS;AAAA,YACjD,WAAW,WAAW,SAAS,aAAa,QAAQ;AAClD,kBAAI,aAAa,SAAS,WAAW,WAAW,GAAG;AACjD,oBAAI,YAAY;AACd,+BAAa,OAAO,KAAK,WAAW,GAAG,CAAC;AAAA,gBAC1C,OAAO;AACL,+BAAa,OAAO,WAAW,SAAS,GAAG,CAAC;AAAA,gBAC9C;AAAA,cACF,OAAO;AACL,6BAAa,OAAO,KAAK,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,cAChE;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,yBAAe,CAAC;AAAA,QAClB;AAAA,MACF;AACA,UAAI,KAAK,iBAAiB,CAAC,KAAK,aAAa;AAC3C,wBAAgB,KAAK,WAAW,UAAU;AAAA,MAC5C;AACA,UAAI,KAAK,YAAY,QAAQ;AAC3B,YAAI,aAAa,SAAS,WAAW,QAAQ;AAC3C,0BAAgB,KAAK,kBAAkB,aAAa,KAAK,eAAe,YAAY,CAAC;AAAA,QACvF,WAAW,aAAa,WAAW,WAAW,QAAQ;AACpD,0BAAgB,aAAa,KAAK,eAAe,YAAY;AAAA,QAC/D,OAAO;AACL,0BAAgB;AAAA,QAClB;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AACA,QAAI,eAAe,KAAK,eAAe,CAAC,KAAK,cAAc;AACzD,sBAAgB;AAAA,IAClB;AACA,QAAI,cAAc,KAAK,kBAAkB,QAAQ,KAAK,eAAe,WAAW,KAAK,eAAe,YAAY,MAAM,MAAM,KAAK,iBAAiB,CAAC,KAAK,QAAQ;AAC9J,sBAAgB,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,2BAA2B,aAAa;AAC/C,UAAI,KAAK,kBAAkB,SAAS,KAAK,YAAY,MAAM,aAAa,cAAc,CAAC,CAAC,GAAG;AACzF,sBAAc,cAAc;AAAA,MAC9B,WAAW,eAAe,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,eAAe,QAAQ;AAC3F,sBAAc,cAAc;AAAA,MAC9B;AACA,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,KAAK,iBAAiB,KAAK,qBAAqB,WAAW,KAAK,CAAC,KAAK,kBAAkB;AAC1F,sBAAgB,KAAK,WAAW,aAAa;AAAA,IAC/C;AACA,QAAI,KAAK,aAAa;AACpB,sBAAgB;AAAA,IAClB,OAAO;AACL,sBAAgB,QAAQ,aAAa,KAAK,cAAc,SAAS,gBAAgB;AAAA,IACnF;AACA,QAAI,KAAK,iBAAiB,KAAK,0BAA0B,KAAK,eAAe,CAAC,cAAc,CAAC,KAAK,cAAc;AAC9G,YAAM,QAAQ,KAAK,wBAAwB,KAAK,WAAW,KAAK,WAAW,IAAI,KAAK;AACpF,WAAK,kBAAkB,KAAK;AAC5B,aAAO,KAAK,cAAc,KAAK,cAAc,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM;AAAA,IAC9F;AACA,UAAM,SAAS,MAAM,UAAU,eAAe,gBAAgB,aAAa,YAAY,YAAY,EAAE;AACrG,SAAK,cAAc,KAAK,eAAe,MAAM;AAC7C,QAAI,KAAK,sBAAsB,eAAe,OAAO,KAAK,kBAAkB,eAAe,KAAK;AAC9F,WAAK,gBAAgB,eAAe;AAAA,IACtC;AACA,QAAI,KAAK,eAAe,WAAW,eAAe,SAAS,KAAK,KAAK,0BAA0B,MAAM;AACnG,WAAK,oBAAoB,KAAK,kBAAkB,OAAO,UAAQ,CAAC,KAAK,mBAAmB,MAAM,KAAK,eAAe,KAAK,iBAAiB,CAAC;AAAA,IAC3I;AACA,QAAI,UAAU,WAAW,IAAI;AAC3B,WAAK,gBAAgB,KAAK;AAC1B,WAAK,eAAe;AACpB,WAAK,aAAa,KAAK,kBAAkB,KAAK,gBAAgB,KAAK,eAAe,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,gBAAgB;AAAA,IACrJ;AACA,SAAK,aAAa,KAAK,gBAAgB,KAAK,sBAAsB,sBAAsB,MAAM,KAAK,kBAAkB,MAAM,CAAC,IAAI,KAAK,kBAAkB,MAAM,IAAI;AACjK,QAAI,CAAC,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,aAAa;AACjE,UAAI,KAAK,aAAa;AACpB,eAAO,GAAG,KAAK,UAAU,QAAQ,KAAK,cAAc,CAAC,GAAG,KAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AAAA,MAC/F;AACA,aAAO;AAAA,IACT;AACA,UAAM,SAAS,OAAO;AACtB,UAAM,YAAY,GAAG,KAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,MAAM;AACjE,QAAI,KAAK,eAAe,SAAS,eAAe,KAAK,GAAG;AACtD,YAAM,oBAAoB,KAAK,qBAAqB,MAAM;AAC1D,aAAO,GAAG,MAAM,GAAG,UAAU,MAAM,SAAS,iBAAiB,CAAC;AAAA,IAChE,WAAW,KAAK,mBAAmB,eAAe,MAAM,KAAK,mBAAmB,eAAe,UAAU;AACvG,aAAO,GAAG,MAAM,GAAG,SAAS;AAAA,IAC9B;AACA,WAAO,GAAG,MAAM,GAAG,UAAU,MAAM,MAAM,CAAC;AAAA,EAC5C;AAAA,EACA,qBAAqB,OAAO;AAC1B,UAAM,QAAQ;AACd,QAAI,QAAQ,MAAM,KAAK,KAAK;AAC5B,QAAI,oBAAoB;AACxB,WAAO,SAAS,MAAM;AACpB,2BAAqB;AACrB,cAAQ,MAAM,KAAK,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,UAAU,YAAY,YAAY,KAAK,MAAM;AAAA,EAAC,GAAG;AACjE,UAAM,cAAc,KAAK,aAAa;AACtC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,gBAAY,QAAQ,KAAK,UAAU,YAAY,OAAO,KAAK,gBAAgB,UAAU,YAAY,YAAY,EAAE;AAC/G,QAAI,gBAAgB,KAAK,kBAAkB,GAAG;AAC5C;AAAA,IACF;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,UAAU,YAAY,gBAAgB;AACpC,WAAO,WAAW,MAAM,eAAe,YAAY,EAAE,IAAI,CAAC,MAAM,UAAU;AACxE,UAAI,KAAK,YAAY,KAAK,SAAS,eAAe,KAAK,KAAK,eAAe,YAAY,KAAK,KAAK,SAAS,eAAe,KAAK,KAAK,eAAe,YAAY,GAAG,QAAQ;AACvK,eAAO,KAAK,SAAS,eAAe,KAAK,KAAK,eAAe,YAAY,GAAG;AAAA,MAC9E;AACA,aAAO;AAAA,IACT,CAAC,EAAE,KAAK,eAAe,YAAY;AAAA,EACrC;AAAA,EACA,eAAe,KAAK;AAClB,UAAM,UAAU,IAAI,MAAM,eAAe,YAAY,EAAE,OAAO,CAAC,QAAQ,MAAM;AAC3E,YAAM,WAAW,KAAK,eAAe,CAAC,KAAK,eAAe;AAC1D,aAAO,KAAK,iBAAiB,QAAQ,QAAQ,KAAK,KAAK,kBAAkB,SAAS,QAAQ,KAAK,WAAW;AAAA,IAC5G,CAAC;AACD,QAAI,QAAQ,KAAK,eAAe,YAAY,MAAM,KAAK;AACrD,aAAO,QAAQ,KAAK,eAAe,YAAY;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,YAAY;AAC5B,QAAI,kBAAkB;AACtB,UAAM,gBAAgB,cAAc,WAAW,MAAM,eAAe,YAAY,EAAE,IAAI,CAAC,YAAY,UAAU;AAC3G,UAAI,KAAK,kBAAkB,SAAS,WAAW,QAAQ,CAAC,KAAK,eAAe,YAAY,KAAK,WAAW,QAAQ,CAAC,MAAM,KAAK,eAAe,QAAQ,CAAC,GAAG;AACrJ,0BAAkB;AAClB,eAAO,WAAW,QAAQ,CAAC;AAAA,MAC7B;AACA,UAAI,gBAAgB,QAAQ;AAC1B,cAAM,gBAAgB;AACtB,0BAAkB,eAAe;AACjC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,KAAK,CAAC;AACP,WAAO,cAAc,KAAK,eAAe,YAAY;AAAA,EACvD;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,CAAC,SAAS,UAAU,KAAK,KAAK,eAAe,WAAW,eAAe,SAAS,MAAM,KAAK,YAAY,CAAC,KAAK,0BAA0B,KAAK,eAAe,WAAW,eAAe,SAAS,KAAK,KAAK,eAAe,SAAS,MAAM,OAAO,KAAK,EAAE,SAAS,IAAI;AACnQ,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,WAAO,OAAO,KAAK,EAAE,eAAe,YAAY;AAAA,MAC9C,aAAa;AAAA,MACb,uBAAuB;AAAA,IACzB,CAAC,EAAE,QAAQ,IAAI,eAAe,KAAK,KAAK,eAAe,KAAK;AAAA,EAC9D;AAAA,EACA,gBAAgB,UAAU;AACxB,QAAI,KAAK,iBAAiB,CAAC,CAAC,KAAK,qBAAqB;AACpD,UAAI,KAAK,eAAe,WAAW,KAAK,oBAAoB,QAAQ;AAClE,cAAM,IAAI,MAAM,oDAAoD;AAAA,MACtE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF,WAAW,KAAK,eAAe;AAC7B,UAAI,UAAU;AACZ,YAAI,KAAK,mBAAmB,eAAe,IAAI;AAC7C,iBAAO,KAAK,YAAY,QAAQ;AAAA,QAClC;AACA,YAAI,KAAK,mBAAmB,eAAe,UAAU;AACnD,iBAAO,KAAK,iBAAiB,QAAQ;AAAA,QACvC;AAAA,MACF;AACA,UAAI,KAAK,qBAAqB,WAAW,KAAK,eAAe,QAAQ;AACnE,eAAO,KAAK;AAAA,MACd;AACA,aAAO,KAAK,eAAe,QAAQ,OAAO,KAAK,oBAAoB;AAAA,IACrE;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,UAAM,cAAc,KAAK,aAAa;AACtC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,KAAK,OAAO,SAAS,KAAK,eAAe,SAAS,KAAK,OAAO,WAAW,YAAY,MAAM,QAAQ,KAAK,sBAAsB,eAAe,YAAY,EAAE,QAAQ;AAC7L,WAAK,sBAAsB,CAAC,SAAS,eAAe,YAAY;AAChE,WAAK,UAAU,IAAI,KAAK,cAAc;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,oBAAoB,CAAC,MAAM,KAAK,GAAG;AACrC,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,aAAa;AACxC;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,WAAW,YAAY,KAAK,aAAa,eAAe,MAAM,KAAK,CAAC;AAAA,EACxG;AAAA,EACA,2BAA2B,MAAM;AAC/B,UAAM,QAAQ,KAAK,MAAM,eAAe,YAAY,EAAE,OAAO,UAAQ,KAAK,qBAAqB,IAAI,CAAC;AACpG,WAAO,MAAM;AAAA,EACf;AAAA,EACA,WAAW,YAAY;AACrB,WAAO,KAAK,YAAY,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,GAAG,KAAK,kBAAkB,OAAO,GAAG,EAAE,OAAO,KAAK,oBAAoB,CAAC;AAAA,EAClJ;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,aAAa,eAAe,MAAM;AACpC,aAAO,GAAG,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB;AAAA,IAC5H;AACA,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,QAAQ,SAAS,CAAC,KAAK,eAAe;AAC5C,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,MAAM,KAAK,GAAG;AACtB,YAAI,KAAK,KAAK;AAAA,MAChB;AAAA,IACF;AACA,QAAI,IAAI,UAAU,GAAG;AACnB,aAAO,GAAG,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,KAAK,oBAAoB;AAAA,IAC/F;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,GAAG,KAAK,oBAAoB,IAAI,KAAK,oBAAoB;AAAA,IAClE;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,KAAK;AAAA,IACd;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,IAAI;AACtC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,MAAM,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB;AAClV,UAAM,OAAO,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,GAAG,KAAK,oBAAoB,IAAS,KAAK,oBAAoB,GAAG,KAAK,oBAAoB;AAC7a,QAAI,aAAa,eAAe,MAAM;AACpC,aAAO;AAAA,IACT;AACA,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,QAAQ,SAAS,CAAC,KAAK,eAAe;AAC5C,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,MAAM,KAAK,GAAG;AACtB,YAAI,KAAK,KAAK;AAAA,MAChB;AAAA,IACF;AACA,QAAI,IAAI,UAAU,GAAG;AACnB,aAAO,IAAI,MAAM,IAAI,QAAQ,IAAI,MAAM;AAAA,IACzC;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,IAAI,MAAM,IAAI,SAAS,GAAG,IAAI,MAAM;AAAA,IAC7C;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,UAAU,GAAG;AACrC,aAAO,IAAI,MAAM,IAAI,SAAS,GAAG,IAAI,MAAM;AAAA,IAC7C;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,SAAS,IAAI;AACrC,aAAO,IAAI,MAAM,IAAI,SAAS,GAAG,IAAI,MAAM;AAAA,IAC7C;AACA,QAAI,IAAI,WAAW,IAAI;AACrB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,WAAW,IAAI;AACrB,UAAI,SAAS,WAAW,IAAI;AAC1B,eAAO,KAAK,MAAM,IAAI,KAAK,MAAM;AAAA,MACnC;AACA,aAAO,KAAK,MAAM,IAAI,KAAK,MAAM;AAAA,IACnC;AACA,QAAI,IAAI,SAAS,MAAM,IAAI,UAAU,IAAI;AACvC,aAAO,KAAK,MAAM,IAAI,SAAS,GAAG,KAAK,MAAM;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,WAAW,KAAK,UAAU;AAC1C,UAAM,eAAe,UAAU,eAAe;AAC9C,QAAI,CAAC,cAAc,eAAe;AAChC,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,KAAK,kBAAkB,YAAY;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB,YAAY;AAC5B,QAAI,KAAK,gBAAgB,CAAC,YAAY;AACpC,WAAK,SAAS,KAAK,kBAAkB,IAAI,CAAC;AAC1C;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB,CAAC,KAAK,uBAAuB,KAAK,aAAa;AACtE,WAAK,uBAAuB,KAAK,cAAc,KAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;AAC/K,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,KAAK,qBAAqB,GAAG;AAC7C,WAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,KAAK,cAAc,KAAK,YAAY,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,GAAG,KAAK,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAAA,IAC5K,WAAW,KAAK,yBAAyB,CAAC,KAAK,yBAAyB,KAAK,WAAW,YAAY;AAClG,WAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IAC9H,OAAO;AACL,WAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,UAAU,CAAC,CAAC;AAAA,IAClE;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,KAAK,iBAAiB,UAAU,eAAe,cAAc;AAChE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe,WAAW,eAAe,SAAS,MAAM,KAAK,YAAY,CAAC,KAAK,wBAAwB;AAC9G,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,EAAE,SAAS,MAAM,KAAK,eAAe,WAAW,eAAe,SAAS,GAAG;AACzF,aAAO,OAAO,KAAK;AAAA,IACrB;AACA,UAAM,MAAM,OAAO,KAAK;AACxB,QAAI,KAAK,eAAe,WAAW,eAAe,SAAS,KAAK,OAAO,MAAM,GAAG,GAAG;AACjF,YAAM,MAAM,OAAO,KAAK,EAAE,QAAQ,KAAK,GAAG;AAC1C,aAAO,OAAO,GAAG;AAAA,IACnB;AACA,WAAO,OAAO,MAAM,GAAG,IAAI,QAAQ;AAAA,EACrC;AAAA,EACA,YAAY,OAAO,4BAA4B;AAC7C,QAAI,KAAK,eAAe,WAAW,eAAe,OAAO,KAAK,MAAM,SAAS,eAAe,GAAG,GAAG;AAChG,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,MAAM,QAAQ,KAAK,iBAAiB,0BAA0B,GAAG,eAAe,YAAY,IAAI;AAAA,EACjH;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,MAAM,QAAQ,KAAK,QAAQ,eAAe,YAAY,IAAI;AAAA,EAC3E;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,MAAM,QAAQ,KAAK,QAAQ,eAAe,YAAY,IAAI;AAAA,EAC3E;AAAA,EACA,wBAAwB,QAAQ;AAC9B,QAAI,oBAAoB,MAAM,QAAQ,KAAK,qBAAqB,IAAI,KAAK,kBAAkB,OAAO,OAAK;AACrG,aAAO,KAAK,sBAAsB,SAAS,CAAC;AAAA,IAC9C,CAAC,IAAI,KAAK;AACV,QAAI,CAAC,KAAK,2BAA2B,KAAK,sBAAsB,KAAK,OAAO,SAAS,eAAe,WAAW,KAAK,KAAK,eAAe,SAAS,eAAe,WAAW,GAAG;AAC5K,0BAAoB,kBAAkB,OAAO,UAAQ,SAAS,eAAe,WAAW;AAAA,IAC1F;AACA,WAAO,KAAK,YAAY,QAAQ,iBAAiB;AAAA,EACnD;AAAA,EACA,iBAAiB,4BAA4B;AAC3C,WAAO,IAAI,OAAO,2BAA2B,IAAI,UAAQ,KAAK,IAAI,EAAE,EAAE,KAAK,GAAG,GAAG,IAAI;AAAA,EACvF;AAAA,EACA,2BAA2B,OAAO;AAChC,UAAM,UAAU,MAAM,QAAQ,KAAK,aAAa,IAAI,KAAK,gBAAgB,CAAC,KAAK,aAAa;AAC5F,WAAO,MAAM,QAAQ,KAAK,iBAAiB,OAAO,GAAG,eAAe,GAAG;AAAA,EACzE;AAAA,EACA,cAAc,QAAQ;AACpB,QAAI,kBAAkB;AACtB,QAAI,oBAAoB,eAAe,cAAc;AACnD,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe,WAAW,eAAe,OAAO,KAAK,KAAK,kBAAkB,eAAe,OAAO;AACzG,wBAAkB,gBAAgB,QAAQ,eAAe,OAAO,eAAe,GAAG;AAAA,IACpF;AACA,UAAM,qBAAqB,KAAK,4BAA4B,KAAK,cAAc;AAC/E,UAAM,iBAAiB,KAAK,kBAAkB,WAAW,IAAI,KAAK,wBAAwB,eAAe,IAAI,KAAK,2BAA2B,KAAK,wBAAwB,eAAe,CAAC;AAC1L,QAAI,CAAC,KAAK,eAAe;AACvB,aAAO;AAAA,IACT;AACA,QAAI,oBAAoB;AACtB,UAAI,oBAAoB,KAAK,eAAe;AAC1C,eAAO;AAAA,MACT;AACA,UAAI,eAAe,SAAS,IAAI;AAC9B,eAAO,OAAO,cAAc;AAAA,MAC9B;AACA,aAAO,KAAK,gBAAgB,KAAK,gBAAgB,cAAc;AAAA,IACjE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,eAAW,OAAO,KAAK,UAAU;AAC/B,UAAI,KAAK,SAAS,GAAG,KAAK,KAAK,SAAS,GAAG,GAAG,eAAe,SAAS,GAAG;AACvE,cAAM,gBAAgB,KAAK,SAAS,GAAG,GAAG,QAAQ,SAAS;AAC3D,cAAM,UAAU,KAAK,SAAS,GAAG,GAAG;AACpC,YAAI,eAAe,SAAS,eAAe,WAAW,KAAK,SAAS,KAAK,KAAK,cAAc,GAAG;AAC7F,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,4BAA4B,eAAe;AACzC,UAAM,UAAU,cAAc,MAAM,IAAI,OAAO,sBAAsB,CAAC;AACtE,WAAO,UAAU,OAAO,QAAQ,CAAC,CAAC,IAAI;AAAA,EACxC;AAAA,EACA,gBAAgB,qBAAqB,gBAAgB;AACnD,UAAM,qBAAqB,KAAK,aAAa,mBAAmB;AAChE,QAAI,QAAQ;AACZ,QAAI,oBAAoB,QAAQ,GAAG,IAAI,KAAK,KAAK,YAAY,OAAO,kBAAkB,IAAI,GAAG;AAC3F,UAAI,KAAK,kBAAkB,eAAe,SAAS,KAAK,UAAU;AAChE,gBAAQ,MAAM,QAAQ,KAAK,GAAG;AAAA,MAChC;AACA,aAAO,KAAK,WAAW,OAAO,KAAK,EAAE,QAAQ,OAAO,kBAAkB,CAAC,IAAI,OAAO,KAAK,EAAE,QAAQ,CAAC;AAAA,IACpG;AACA,WAAO,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,sBAAsB,SAAS;AAC7B,WAAO,QAAQ,MAAM,UAAU,KAAK,QAAQ,MAAM,eAAe,YAAY,EAAE,OAAO,CAAC,OAAO,SAAS,UAAU;AAC/G,WAAK,SAAS,YAAY,eAAe,sBAAsB,QAAQ,KAAK;AAC5E,UAAI,YAAY,eAAe,sBAAsB;AACnD,eAAO,KAAK,iBAAiB,OAAO,IAAI,QAAQ,UAAU;AAAA,MAC5D;AACA,WAAK,OAAO;AACZ,YAAM,eAAe,OAAO,QAAQ,MAAM,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AACrE,YAAM,cAAc,IAAI,MAAM,eAAe,CAAC,EAAE,KAAK,QAAQ,KAAK,SAAS,CAAC,CAAC;AAC7E,UAAI,QAAQ,MAAM,GAAG,KAAK,MAAM,EAAE,SAAS,KAAK,QAAQ,SAAS,eAAe,QAAQ,GAAG;AACzF,cAAM,UAAU,QAAQ,MAAM,GAAG,KAAK,SAAS,CAAC;AAChD,eAAO,QAAQ,SAAS,eAAe,mBAAmB,IAAI,QAAQ,cAAc,UAAU,QAAQ;AAAA,MACxG,OAAO;AACL,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,GAAG,EAAE,KAAK;AAAA,EACZ;AAAA,EACA,6BAA6B;AAC3B,WAAO,IAAI,eAAe,EAAE,UAAU,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,iBAAiB;AACxB,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,cAAc,OAAO,UAAU;AACrC,SAAO,uBAAuB,WAAW,kCACpC,aACA,YAAY,KACb,kCACC,aACA;AAEP;AACA,SAAS,eAAe,aAAa;AACnC,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG,cAAc;AACnB;AACA,SAAS,0BAA0B,aAAa;AAC9C,SAAO,yBAAyB,eAAe,WAAW,CAAC;AAC7D;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,MAAM,EAAE;AAAA,EACf,oBAAoB,MAAM,CAAC,CAAC;AAAA,EAC5B,WAAW,MAAM,CAAC,CAAC;AAAA,EACnB,SAAS,MAAM,EAAE;AAAA,EACjB,SAAS,MAAM,EAAE;AAAA,EACjB,oBAAoB,MAAM,GAAG;AAAA,EAC7B,gBAAgB,MAAM,GAAG;AAAA,EACzB,wBAAwB,MAAM,IAAI;AAAA,EAClC,cAAc,MAAM,IAAI;AAAA,EACxB,gBAAgB,MAAM,IAAI;AAAA,EAC1B,uBAAuB,MAAM,IAAI;AAAA,EACjC,sBAAsB,MAAM,IAAI;AAAA,EAChC,kBAAkB,MAAM,IAAI;AAAA,EAC5B,aAAa,MAAM,IAAI;AAAA,EACvB,iBAAiB,MAAM,EAAE;AAAA,EACzB,uBAAuB,MAAM,IAAI;AAAA,EACjC,mBAAmB,MAAM,IAAI;AAAA,EAC7B,WAAW,MAAM,IAAI;AAAA,EACrB,sBAAsB,MAAM,IAAI;AAAA,EAChC,MAAM,MAAM,IAAI;AAAA,EAChB,mBAAmB,MAAM,IAAI;AAAA,EAC7B,oBAAoB,MAAM,IAAI;AAAA,EAC9B,yBAAyB,MAAM,IAAI;AAAA,EACnC,gBAAgB,MAAM,IAAI;AAAA,EAC1B,aAAa,OAAO;AAAA,EACpB,aAAa,OAAO,EAAE;AAAA,EACtB,cAAc,OAAO,EAAE;AAAA,EACvB,YAAY,OAAO,IAAI;AAAA,EACvB,QAAQ,OAAO,EAAE;AAAA,EACjB,uBAAuB,OAAO,CAAC,CAAC;AAAA,EAChC,cAAc,OAAO,KAAK;AAAA,EAC1B,aAAa,OAAO,KAAK;AAAA,EACzB,eAAe,OAAO,KAAK;AAAA,EAC3B,eAAe,OAAO,gBAAgB;AAAA,IACpC,MAAM;AAAA,EACR,CAAC;AAAA,EACD,WAAW,OAAO,QAAQ;AAAA,EAC1B,UAAU,OAAO,eAAe;AAAA,EAChC,WAAW,OAAK;AAAA,EAAC;AAAA,EACjB,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,MAAM;AACR,UAAI,KAAK,iBAAiB,KAAK,iBAAiB,CAAC,KAAK,aAAa;AACjE,aAAK,aAAa,cAAc;AAAA,MAClC;AACA,UAAI,KAAK,gBAAgB,KAAK,aAAa,MAAM,eAAe,EAAE,EAAE,SAAS,GAAG;AAC9E,aAAK,qBAAqB,IAAI,KAAK,aAAa,MAAM,eAAe,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM;AACtF,iBAAO,EAAE,SAAS,EAAE;AAAA,QACtB,CAAC,CAAC;AACF,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,qBAAqB,IAAI,CAAC,CAAC;AAChC,aAAK,WAAW,IAAI,KAAK,gBAAgB,eAAe,YAAY;AACpE,aAAK,aAAa,iBAAiB,KAAK,WAAW;AAAA,MACrD;AAAA,IACF;AACA,QAAI,mBAAmB;AACrB,UAAI,CAAC,kBAAkB,gBAAgB,CAAC,MAAM,QAAQ,kBAAkB,YAAY,GAAG;AACrF;AAAA,MACF,OAAO;AACL,aAAK,aAAa,oBAAoB,kBAAkB,gBAAgB,CAAC;AAAA,MAC3E;AAAA,IACF;AACA,QAAI,sBAAsB;AACxB,WAAK,aAAa,uBAAuB,qBAAqB;AAC9D,UAAI,KAAK,aAAa,sBAAsB;AAC1C,aAAK,aAAa,oBAAoB,KAAK,aAAa,kBAAkB,OAAO,OAAK,MAAM,eAAe,KAAK;AAAA,MAClH;AAAA,IACF;AACA,QAAI,YAAY,SAAS,cAAc;AACrC,WAAK,aAAa,WAAW,SAAS;AAAA,IACxC;AACA,QAAI,OAAO,IAAI,cAAc;AAC3B,WAAK,aAAa,MAAM,IAAI;AAAA,IAC9B;AACA,QAAI,eAAe;AACjB,WAAK,aAAa,gBAAgB,cAAc;AAAA,IAClD;AACA,QAAI,QAAQ;AACV,WAAK,aAAa,SAAS,OAAO;AAAA,IACpC;AACA,QAAI,QAAQ;AACV,WAAK,aAAa,SAAS,OAAO;AAAA,IACpC;AACA,QAAI,mBAAmB;AACrB,WAAK,aAAa,oBAAoB,kBAAkB;AACxD,UAAI,kBAAkB,iBAAiB,kBAAkB,cAAc;AACrE,cAAM,wBAAwB,KAAK,aAAa;AAChD,YAAI,kBAAkB,iBAAiB,KAAK,aAAa,eAAe;AACtE,eAAK,aAAa,gBAAgB,kBAAkB,iBAAiB,eAAe,QAAQ,eAAe,MAAM,eAAe;AAAA,QAClI;AACA,YAAI,KAAK,aAAa,0BAA0B,MAAM;AACpD,eAAK,aAAa,oBAAoB,KAAK,QAAQ;AAAA,QACrD;AACA,YAAI,OAAO,0BAA0B,YAAY,OAAO,KAAK,aAAa,kBAAkB,UAAU;AACpG,eAAK,YAAY,IAAI,KAAK,YAAY,EAAE,MAAM,kBAAkB,aAAa,EAAE,KAAK,EAAE,EAAE,QAAQ,uBAAuB,KAAK,aAAa,aAAa,CAAC;AACvJ,eAAK,aAAa,cAAc,KAAK,YAAY;AAAA,QACnD;AACA,aAAK,aAAa,eAAe;AAAA,MACnC;AAAA,IACF;AACA,QAAI,eAAe;AACjB,WAAK,aAAa,gBAAgB,cAAc;AAAA,IAClD;AACA,QAAI,uBAAuB;AACzB,WAAK,aAAa,wBAAwB,sBAAsB;AAAA,IAClE;AACA,QAAI,aAAa;AACf,WAAK,aAAa,cAAc,YAAY;AAC5C,UAAI,YAAY,kBAAkB,QAAQ,YAAY,iBAAiB,OAAO;AAC5E,aAAK,YAAY,IAAI,KAAK,aAAa,WAAW;AAAA,MACpD;AAAA,IACF;AACA,QAAI,eAAe;AACjB,WAAK,aAAa,gBAAgB,cAAc;AAChD,UAAI,cAAc,kBAAkB,SAAS,cAAc,iBAAiB,QAAQ,KAAK,WAAW,GAAG;AACrG,8BAAsB,MAAM;AAC1B,eAAK,aAAa,aAAa,cAAc,MAAM;AAAA,QACrD,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,sBAAsB;AACxB,WAAK,aAAa,uBAAuB,qBAAqB;AAAA,IAChE;AACA,QAAI,qBAAqB;AACvB,WAAK,aAAa,sBAAsB,oBAAoB;AAAA,IAC9D;AACA,QAAI,iBAAiB;AACnB,WAAK,aAAa,kBAAkB,gBAAgB;AAAA,IACtD;AACA,QAAI,YAAY;AACd,WAAK,aAAa,aAAa,WAAW;AAAA,IAC5C;AACA,QAAI,gBAAgB;AAClB,WAAK,aAAa,iBAAiB,eAAe;AAAA,IACpD;AACA,QAAI,kBAAkB;AACpB,WAAK,aAAa,mBAAmB,iBAAiB;AAAA,IACxD;AACA,QAAI,UAAU;AACZ,WAAK,aAAa,WAAW,SAAS;AAAA,IACxC;AACA,QAAI,qBAAqB;AACvB,WAAK,aAAa,sBAAsB,oBAAoB;AAAA,IAC9D;AACA,QAAI,kBAAkB;AACpB,WAAK,aAAa,mBAAmB,iBAAiB;AAAA,IACxD;AACA,QAAI,mBAAmB;AACrB,WAAK,aAAa,oBAAoB,kBAAkB;AAAA,IAC1D;AACA,QAAI,wBAAwB;AAC1B,WAAK,aAAa,yBAAyB,uBAAuB;AAAA,IACpE;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,IACP;AAAA,EACF,GAAG;AACD,UAAM,iBAAiB,OAAO,UAAU,WAAW,OAAO,KAAK,IAAI;AACnE,UAAM,YAAY,KAAK,WAAW;AAClC,QAAI,CAAC,KAAK,aAAa,cAAc,CAAC,WAAW;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,SAAS;AAC7B,aAAO,KAAK,uBAAuB,cAAc;AAAA,IACnD;AACA,QAAI,KAAK,aAAa,cAAc;AAClC,aAAO,KAAK,uBAAuB,cAAc;AAAA,IACnD;AACA,QAAI,UAAU,WAAW,eAAe,SAAS,GAAG;AAClD,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,SAAS,SAAS,GAAG;AACzC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,iBAAiB;AACrC,aAAO;AAAA,IACT;AACA,QAAI,UAAU,SAAS,SAAS,GAAG;AACjC,aAAO,KAAK,cAAc,cAAc;AAAA,IAC1C;AACA,QAAI,cAAc,eAAe,YAAY;AAC3C,YAAM,eAAe;AACrB,UAAI,CAAC,aAAa,KAAK,cAAc,KAAK,gBAAgB;AACxD,eAAO,KAAK,uBAAuB,cAAc;AAAA,MACnD,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,kBAAkB,eAAe,UAAU,GAAG;AAChD,UAAI,eAAe;AACnB,UAAI,UAAU,SAAS,eAAe,mBAAmB,KAAK,UAAU,SAAS,eAAe,oBAAoB,GAAG;AACrH,cAAM,4BAA4B,UAAU,MAAM,UAAU,QAAQ,eAAe,mBAAmB,IAAI,GAAG,UAAU,QAAQ,eAAe,oBAAoB,CAAC;AACnK,eAAO,8BAA8B,OAAO,eAAe,MAAM,IAAI,OAAO,KAAK,uBAAuB,cAAc;AAAA,MACxH;AACA,UAAI,UAAU,WAAW,eAAe,OAAO,GAAG;AAChD,eAAO;AAAA,MACT;AACA,iBAAW,OAAO,KAAK,aAAa,UAAU;AAC5C,YAAI,KAAK,aAAa,SAAS,GAAG,GAAG,UAAU;AAC7C,cAAI,UAAU,QAAQ,GAAG,MAAM,UAAU,YAAY,GAAG,GAAG;AACzD,kBAAM,MAAM,UAAU,MAAM,eAAe,YAAY,EAAE,OAAO,OAAK,MAAM,GAAG,EAAE,KAAK,eAAe,YAAY;AAChH,4BAAgB,IAAI;AAAA,UACtB,WAAW,UAAU,QAAQ,GAAG,MAAM,IAAI;AACxC;AAAA,UACF;AACA,cAAI,UAAU,QAAQ,GAAG,MAAM,MAAM,eAAe,UAAU,UAAU,QAAQ,GAAG,GAAG;AACpF,mBAAO;AAAA,UACT;AACA,cAAI,iBAAiB,UAAU,QAAQ;AACrC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,UAAU,QAAQ,eAAe,WAAW,IAAI,KAAK,eAAe,SAAS,UAAU,QAAQ,eAAe,WAAW,KAAK,UAAU,QAAQ,eAAe,eAAe,IAAI,KAAK,eAAe,SAAS,UAAU,QAAQ,eAAe,eAAe,GAAG;AACpQ,eAAO,KAAK,uBAAuB,cAAc;AAAA,MACnD;AACA,UAAI,UAAU,QAAQ,eAAe,WAAW,MAAM,MAAM,UAAU,QAAQ,eAAe,eAAe,MAAM,IAAI;AACpH,cAAM,QAAQ,UAAU,MAAM,GAAG;AACjC,cAAM,SAAS,KAAK,aAAa,wBAAwB,UAAU,SAAS,KAAK,aAAa,2BAA2B,SAAS,IAAI,eAAe,KAAK,OAAO,IAAI,UAAU,SAAS,KAAK,OAAO,EAAE,SAAS,eAAe,UAAU,SAAS;AACjP,YAAI,MAAM,WAAW,GAAG;AACtB,cAAI,eAAe,SAAS,QAAQ;AAClC,mBAAO,KAAK,uBAAuB,cAAc;AAAA,UACnD;AAAA,QACF;AACA,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,iBAAiB,MAAM,MAAM,SAAS,CAAC;AAC7C,cAAI,kBAAkB,KAAK,aAAa,kBAAkB,SAAS,eAAe,CAAC,CAAC,KAAK,OAAO,cAAc,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,sBAAsB,GAAG;AAClL,kBAAM,UAAU,MAAM,MAAM,eAAe,CAAC,CAAC;AAC7C,mBAAO,QAAQ,QAAQ,SAAS,CAAC,EAAE,WAAW,eAAe,SAAS,IAAI,OAAO,KAAK,uBAAuB,cAAc;AAAA,UAC7H,YAAY,kBAAkB,CAAC,KAAK,aAAa,kBAAkB,SAAS,eAAe,CAAC,CAAC,KAAK,CAAC,kBAAkB,KAAK,aAAa,0BAA0B,eAAe,UAAU,SAAS,GAAG;AACpM,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,KAAK,uBAAuB,cAAc;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AACA,UAAI,UAAU,QAAQ,eAAe,WAAW,MAAM,KAAK,UAAU,QAAQ,eAAe,eAAe,MAAM,GAAG;AAClH,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,OAAO;AACT,WAAK,WAAW,KAAK;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,SAAK,YAAY,IAAI,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AACR,SAAK,WAAW,IAAI,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc,OAAO;AACnB,SAAK,UAAU,eAAe,gBAAgB,UAAU,QAAQ,OAAO,UAAU,gBAAgB,KAAK,aAAa,aAAa;AAC9H,WAAK,aAAa,cAAc,KAAK,aAAa,eAAe,eAAe,YAAY;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,QAAQ,GAAG;AACT,QAAI,KAAK,aAAa,GAAG;AACvB;AAAA,IACF;AACA,UAAM,KAAK,EAAE;AACb,UAAM,mBAAmB,KAAK,aAAa,iBAAiB,GAAG,KAAK;AACpE,QAAI,GAAG,SAAS,UAAU;AACxB,UAAI,OAAO,qBAAqB,YAAY,OAAO,qBAAqB,UAAU;AAChF,WAAG,QAAQ,iBAAiB,SAAS;AACrC,aAAK,YAAY,IAAI,GAAG,KAAK;AAC7B,aAAK,SAAS;AACd,YAAI,CAAC,KAAK,WAAW,GAAG;AACtB,eAAK,SAAS,GAAG,KAAK;AACtB;AAAA,QACF;AACA,YAAI,WAAW,GAAG,mBAAmB,IAAI,GAAG,iBAAiB,KAAK,aAAa,OAAO,SAAS,GAAG;AAClG,YAAI,KAAK,cAAc,KAAK,KAAK,uBAAuB,KAAK,KAAK,aAAa,qBAAqB,WAAW,GAAG;AAChH,gBAAM,SAAS,KAAK,OAAO;AAC3B,gBAAM,SAAS,KAAK,OAAO;AAC3B,gBAAM,cAAc,GAAG,MAAM,MAAM,WAAW,GAAG,QAAQ;AACzD,gBAAM,eAAe,OAAO;AAC5B,gBAAM,eAAe,KAAK,aAAa,iBAAiB,aAAa,KAAK,aAAa,eAAe,WAAW,IAAI,YAAY,KAAK,eAAe,YAAY;AACjK,gBAAM,wBAAwB,KAAK,aAAa,iBAAiB,aAAa,KAAK,aAAa,eAAe,WAAW,IAAI,YAAY,KAAK,eAAe,YAAY;AAC1K,gBAAM,uBAAuB,KAAK,aAAa,aAAa,KAAK,aAAa;AAC9E,gBAAM,WAAW,OAAO,KAAK,aAAa,QAAQ,IAAI;AACtD,gBAAM,SAAS,OAAO,KAAK,aAAa,MAAM,IAAI;AAClD,gBAAM,oBAAoB,KAAK,MAAM,MAAM,eAAe,aAAa,KAAK,MAAM,MAAM,eAAe;AACvG,cAAI,mBAAmB;AACrB,gBAAI,CAAC,sBAAsB;AACzB,kBAAI,KAAK,aAAa,aAAa,cAAc;AAC/C,qBAAK,aAAa,cAAc,GAAG,MAAM,GAAG,KAAK,aAAa,YAAY,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,YAAY,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,cACxI,WAAW,KAAK,aAAa,aAAa,KAAK,aAAa,YAAY,SAAS,cAAc;AAC7F,qBAAK,aAAa,cAAc,GAAG,KAAK,YAAY,CAAC,GAAG,KAAK,aAAa,YAAY,MAAM,UAAU,MAAM,CAAC;AAAA,cAC/G,OAAO;AACL,qBAAK,aAAa,cAAc,GAAG,MAAM,GAAG,KAAK,YAAY,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG,QAAQ,CAAC,GAAG,KAAK,aAAa,YAAY,MAAM,UAAU,MAAM,CAAC,GAAG,KAAK,aAAa,YAAY,MAAM,SAAS,cAAc,KAAK,aAAa,YAAY,SAAS,YAAY,CAAC,GAAG,MAAM;AAAA,cAC7R;AAAA,YACF,WAAW,CAAC,KAAK,aAAa,kBAAkB,SAAS,KAAK,aAAa,eAAe,MAAM,WAAW,cAAc,WAAW,IAAI,YAAY,CAAC,KAAK,sBAAsB;AAC9K,kBAAI,aAAa,KAAK,QAAQ;AAC5B,qBAAK,aAAa,cAAc,GAAG,MAAM,GAAG,KAAK,aAAa,oBAAoB,GAAG,GAAG,MAAM,MAAM,MAAM,EAAE,KAAK,EAAE,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM;AACpJ,2BAAW,WAAW;AAAA,cACxB,OAAO;AACL,sBAAM,QAAQ,GAAG,MAAM,UAAU,GAAG,QAAQ;AAC5C,sBAAM,QAAQ,GAAG,MAAM,UAAU,QAAQ;AACzC,qBAAK,aAAa,cAAc,GAAG,KAAK,GAAG,KAAK,aAAa,oBAAoB,GAAG,KAAK;AAAA,cAC3F;AAAA,YACF;AACA,uBAAW,KAAK,MAAM,MAAM,eAAe,SAAS,WAAW,IAAI;AAAA,UACrE;AACA,cAAI,CAAC,mBAAmB;AACtB,gBAAI,CAAC,gBAAgB,CAAC,yBAAyB,sBAAsB;AACnE,yBAAW,OAAO,GAAG,cAAc,IAAI;AAAA,YACzC,WAAW,KAAK,aAAa,kBAAkB,SAAS,GAAG,MAAM,MAAM,UAAU,WAAW,CAAC,CAAC,KAAK,yBAAyB,CAAC,KAAK,aAAa,kBAAkB,SAAS,GAAG,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,GAAG;AACrN,mBAAK,aAAa,cAAc,GAAG,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,GAAG,GAAG,MAAM,MAAM,UAAU,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,GAAG,MAAM,MAAM,WAAW,CAAC,CAAC;AACxJ,yBAAW,WAAW;AAAA,YACxB,WAAW,cAAc;AACvB,kBAAI,GAAG,MAAM,WAAW,KAAK,aAAa,GAAG;AAC3C,qBAAK,aAAa,cAAc,GAAG,MAAM,GAAG,WAAW,GAAG,KAAK,aAAa,YAAY,MAAM,GAAG,KAAK,aAAa,YAAY,MAAM,CAAC,GAAG,MAAM;AAAA,cACjJ,OAAO;AACL,qBAAK,aAAa,cAAc,GAAG,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,GAAG,GAAG,MAAM,MAAM,WAAW,CAAC,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM;AAAA,cACjJ;AAAA,YACF,WAAW,UAAU,GAAG,MAAM,WAAW,KAAK,WAAW,iBAAiB,KAAK,KAAK,aAAa,iBAAiB,GAAG,OAAO,KAAK,aAAa,eAAe,WAAW,IAAI,YAAY,KAAK,eAAe,YAAY,GAAG;AACzN,mBAAK,aAAa,cAAc,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,aAAa,YAAY,MAAM,GAAG,KAAK,aAAa,YAAY,MAAM,CAAC,GAAG,MAAM;AAAA,YAC9I;AAAA,UACF;AAAA,QACF;AACA,YAAI,aAAa;AACjB,YAAI,iBAAiB;AACrB,YAAI,KAAK,MAAM,MAAM,eAAe,UAAU,eAAe,WAAW;AACtE,eAAK,aAAa,0BAA0B;AAAA,QAC9C;AACA,YAAI,KAAK,YAAY,EAAE,UAAU,KAAK,aAAa,eAAe,SAAS,KAAK,KAAK,MAAM,MAAM,eAAe,aAAa,KAAK,aAAa,mBAAmB,eAAe,qBAAqB,WAAW,IAAI;AACnN,gBAAM,cAAc,KAAK,YAAY,EAAE,MAAM,WAAW,GAAG,QAAQ;AACnE,aAAG,QAAQ,KAAK,YAAY,EAAE,MAAM,GAAG,WAAW,CAAC,IAAI,cAAc,KAAK,YAAY,EAAE,MAAM,WAAW,CAAC;AAAA,QAC5G;AACA,YAAI,KAAK,aAAa,mBAAmB,eAAe,qBAAqB,KAAK,iBAAiB,GAAG;AACpG,cAAI,WAAW,KAAK,OAAO,GAAG,KAAK,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI,MAAM,aAAa,KAAK,OAAO,GAAG,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;AACzH,uBAAW,WAAW;AAAA,UACxB;AAAA,QACF;AACA,YAAI,KAAK,aAAa,mBAAmB,eAAe,yBAAyB,KAAK,IAAI,GAAG;AAC3F,cAAI,KAAK,YAAY,KAAK,GAAG,MAAM,MAAM,GAAG,CAAC,MAAM,eAAe,aAAa;AAC7E,eAAG,QAAQ,GAAG,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,MAAM,MAAM,GAAG,GAAG,MAAM,MAAM;AAAA,UACrE;AACA,aAAG,QAAQ,GAAG,UAAU,eAAe,cAAc,eAAe,cAAc,GAAG;AAAA,QACvF;AACA,aAAK,aAAa,kBAAkB,UAAU,KAAK,YAAY,GAAG,KAAK,MAAM,MAAM,eAAe,aAAa,KAAK,MAAM,MAAM,eAAe,QAAQ,CAAC,OAAO,oBAAoB;AACjL,eAAK,YAAY,IAAI,KAAK;AAC1B,uBAAa;AACb,2BAAiB;AAAA,QACnB,CAAC;AACD,YAAI,KAAK,kBAAkB,MAAM,IAAI;AACnC;AAAA,QACF;AACA,YAAI,KAAK,aAAa,iBAAiB;AACrC,qBAAW,WAAW;AACtB,eAAK,aAAa,kBAAkB;AAAA,QACtC;AACA,YAAI,KAAK,qBAAqB,EAAE,QAAQ;AACtC,cAAI,KAAK,MAAM,MAAM,eAAe,WAAW;AAC7C,kBAAM,uBAAuB,KAAK,kBAAkB,EAAE,SAAS,KAAK,aAAa,YAAY,MAAM,WAAW,GAAG,QAAQ,CAAC;AAC1H,kBAAM,yBAAyB,KAAK,aAAa,WAAW,KAAK,YAAY,CAAC,GAAG,WAAW,KAAK,aAAa,WAAW,KAAK,aAAa,cAAc,GAAG;AAC5J,kBAAM,sBAAsB,KAAK,kBAAkB,EAAE,SAAS,KAAK,aAAa,YAAY,MAAM,UAAU,WAAW,CAAC,CAAC;AACzH,gBAAI,0BAA0B,CAAC,qBAAqB;AAClD,yBAAW,GAAG,iBAAiB;AAAA,YACjC,OAAO;AACL,yBAAW,uBAAuB,WAAW,IAAI;AAAA,YACnD;AAAA,UACF,OAAO;AACL,uBAAW,GAAG,mBAAmB,IAAI,GAAG,iBAAiB,KAAK,aAAa,OAAO,SAAS,GAAG;AAAA,UAChG;AAAA,QACF;AACA,aAAK,UAAU,IAAI,KAAK,UAAU,MAAM,KAAK,KAAK,YAAY,EAAE,WAAW,IAAI,OAAO,KAAK,UAAU,CAAC;AACtG,YAAI,kBAAkB,KAAK,UAAU,IAAI,KAAK,YAAY,EAAE,SAAS,WAAW,aAAa,YAAY,KAAK,MAAM,MAAM,eAAe,aAAa,CAAC,iBAAiB,IAAI;AAC5K,YAAI,kBAAkB,KAAK,sBAAsB,GAAG;AAClD,4BAAkB,GAAG,UAAU,KAAK,aAAa,iBAAiB,GAAG,MAAM,WAAW,IAAI,KAAK,sBAAsB,IAAI,IAAI,KAAK,sBAAsB;AAAA,QAC1J;AACA,YAAI,kBAAkB,GAAG;AACvB,4BAAkB;AAAA,QACpB;AACA,WAAG,kBAAkB,iBAAiB,eAAe;AACrD,aAAK,UAAU,IAAI,IAAI;AAAA,MACzB,OAAO;AACL,gBAAQ,KAAK,sEAAsE,OAAO,gBAAgB;AAAA,MAC5G;AAAA,IACF,OAAO;AACL,UAAI,CAAC,KAAK,WAAW,GAAG;AACtB,aAAK,SAAS,GAAG,KAAK;AACtB;AAAA,MACF;AACA,WAAK,aAAa,kBAAkB,GAAG,MAAM,QAAQ,KAAK,YAAY,GAAG,KAAK,MAAM,MAAM,eAAe,aAAa,KAAK,MAAM,MAAM,eAAe,MAAM;AAAA,IAC9J;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa,IAAI,IAAI;AAAA,EAC5B;AAAA,EACA,iBAAiB,GAAG;AAClB,SAAK,aAAa,IAAI,KAAK;AAC3B,SAAK,YAAY,IAAI,IAAI;AACzB,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,WAAW,GAAG;AACrB,YAAM,KAAK,EAAE;AACb,UAAI,KAAK,aAAa,YAAY,GAAG,MAAM,SAAS,KAAK,OAAO,KAAK,aAAa,kBAAkB,UAAU;AAC5G,cAAM,iBAAiB,KAAK,aAAa;AACzC,cAAM,gBAAgB,KAAK,aAAa;AACxC,cAAM,SAAS,KAAK,aAAa;AACjC,cAAM,YAAY,OAAO,KAAK,aAAa,eAAe,MAAM,eAAe,SAAS,GAAG,eAAe,MAAM,CAAC;AACjH,YAAI,YAAY,GAAG;AACjB,aAAG,QAAQ,SAAS,GAAG,MAAM,MAAM,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG;AACzD,gBAAM,cAAc,GAAG,MAAM,MAAM,aAAa,EAAE,CAAC;AACnD,aAAG,QAAQ,GAAG,MAAM,SAAS,aAAa,IAAI,GAAG,QAAQ,eAAe,YAAY,OAAO,YAAY,YAAY,MAAM,IAAI,SAAS,GAAG,QAAQ,gBAAgB,eAAe,YAAY,OAAO,SAAS,IAAI;AAChN,eAAK,aAAa,cAAc,GAAG;AAAA,QACrC;AAAA,MACF;AACA,WAAK,aAAa,kBAAkB;AAAA,IACtC;AACA,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,QAAQ,GAAG;AACT,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB;AAAA,IACF;AACA,UAAM,KAAK,EAAE;AACb,UAAM,WAAW;AACjB,UAAM,SAAS;AACf,QAAI,OAAO,QAAQ,GAAG,mBAAmB,QAAQ,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,iBAAiB,KAAK,aAAa,OAAO,UAAU,EAAE,YAAY,IAAI;AACjK,UAAI,KAAK,aAAa,iBAAiB,CAAC,KAAK,uBAAuB,GAAG;AACrE,aAAK,aAAa,cAAc,KAAK,aAAa,gBAAgB;AAClE,YAAI,GAAG,qBAAqB,KAAK,aAAa,SAAS,KAAK,aAAa,gBAAgB,GAAG,OAAO;AACjG,aAAG,MAAM;AACT,aAAG,kBAAkB,UAAU,MAAM;AAAA,QACvC,OAAO;AACL,cAAI,GAAG,iBAAiB,KAAK,aAAa,YAAY,QAAQ;AAC5D,eAAG,kBAAkB,KAAK,aAAa,YAAY,QAAQ,KAAK,aAAa,YAAY,MAAM;AAAA,UACjG;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,OAAO,GAAG,UAAU,KAAK,aAAa,SAAS,KAAK,aAAa,SAAS,KAAK,aAAa,cAAc,GAAG;AAC/H,QAAI,MAAM,GAAG,UAAU,WAAW;AAChC,SAAG,QAAQ;AAAA,IACb;AACA,QAAI,MAAM,GAAG,SAAS,aAAa,GAAG,kBAAkB,GAAG,iBAAiB,KAAK,aAAa,OAAO,QAAQ;AAC3G,YAAM,8BAA8B,KAAK,aAAa,eAAe,MAAM,IAAI,OAAO,KAAK,KAAK,aAAa,kBAAkB,IAAI,OAAK,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU;AAChL,SAAG,iBAAiB,KAAK,aAAa,OAAO,SAAS;AACtD;AAAA,IACF;AACA,QAAI,MAAM,GAAG,eAAe,KAAK,sBAAsB,GAAG;AACxD,SAAG,eAAe,KAAK,sBAAsB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,UAAU,GAAG;AACX,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB;AAAA,IACF;AACA,QAAI,KAAK,aAAa,GAAG;AACvB,UAAI,EAAE,QAAQ,SAAS;AACrB,aAAK,iBAAiB,CAAC;AAAA,MACzB;AACA;AAAA,IACF;AACA,SAAK,MAAM,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;AACtC,UAAM,KAAK,EAAE;AACb,SAAK,YAAY,IAAI,GAAG,KAAK;AAC7B,SAAK,SAAS;AACd,QAAI,GAAG,SAAS,UAAU;AACxB,UAAI,EAAE,QAAQ,eAAe,UAAU;AACrC,UAAE,eAAe;AAAA,MACnB;AACA,UAAI,EAAE,QAAQ,eAAe,cAAc,EAAE,QAAQ,eAAe,aAAa,EAAE,QAAQ,eAAe,QAAQ;AAChH,YAAI,EAAE,QAAQ,eAAe,aAAa,GAAG,MAAM,WAAW,GAAG;AAC/D,aAAG,iBAAiB,GAAG;AAAA,QACzB;AACA,YAAI,EAAE,QAAQ,eAAe,aAAa,GAAG,mBAAmB,GAAG;AACjE,gBAAM,eAAe,KAAK,OAAO,EAAE;AACnC,gBAAM,oBAAoB,KAAK,kBAAkB,EAAE,SAAS,KAAK,kBAAkB,IAAI,KAAK,QAAQ;AACpG,cAAI,eAAe,KAAK,GAAG,kBAAkB,cAAc;AACzD,eAAG,kBAAkB,cAAc,GAAG,YAAY;AAAA,UACpD,OAAO;AACL,gBAAI,KAAK,YAAY,EAAE,WAAW,GAAG,kBAAkB,GAAG,mBAAmB,GAAG;AAC9E,qBAAO,kBAAkB,UAAU,KAAK,YAAY,EAAE,GAAG,iBAAiB,CAAC,KAAK,eAAe,cAAc,SAAS,CAAC,MAAM,gBAAgB,KAAK,GAAG,iBAAiB,gBAAgB,iBAAiB,IAAI;AACzM,mBAAG,kBAAkB,GAAG,iBAAiB,GAAG,GAAG,YAAY;AAAA,cAC7D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,aAAK,yBAAyB,EAAE;AAChC,YAAI,KAAK,aAAa,OAAO,UAAU,GAAG,kBAAkB,KAAK,aAAa,OAAO,UAAU,GAAG,gBAAgB,KAAK,aAAa,OAAO,QAAQ;AACjJ,YAAE,eAAe;AAAA,QACnB;AACA,cAAM,cAAc,GAAG;AACvB,YAAI,EAAE,QAAQ,eAAe,aAAa,CAAC,GAAG,YAAY,gBAAgB,KAAK,GAAG,iBAAiB,GAAG,MAAM,UAAU,GAAG,MAAM,WAAW,GAAG;AAC3I,eAAK,UAAU,IAAI,KAAK,aAAa,SAAS,KAAK,aAAa,OAAO,SAAS,CAAC;AACjF,eAAK,aAAa,UAAU,KAAK,aAAa,QAAQ,KAAK,aAAa,gBAAgB,KAAK,UAAU,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,CAAC,CAAC,KAAK,OAAO,KAAK,KAAK,OAAO,EAAE,SAAS,KAAK,KAAK,YAAY,EAAE,SAAS,KAAK,OAAO,EAAE,SAAS,GAAG,gBAAgB;AACvH,WAAG,kBAAkB,KAAK,YAAY,EAAE,SAAS,KAAK,OAAO,EAAE,QAAQ,KAAK,YAAY,EAAE,MAAM;AAAA,MAClG,WAAW,EAAE,SAAS,UAAU,EAAE,WAAW,EAAE,SAAS,UAAU,EAAE,SAAS;AAC3E,WAAG,kBAAkB,GAAG,KAAK,sBAAsB,CAAC;AACpD,UAAE,eAAe;AAAA,MACnB;AACA,WAAK,aAAa,WAAW,GAAG;AAChC,WAAK,aAAa,SAAS,GAAG;AAAA,IAChC;AAAA,EACF;AAAA,EACM,WAAW,cAAc;AAAA;AAC7B,UAAI,QAAQ;AACZ,YAAM,mBAAmB,KAAK,iBAAiB;AAC/C,UAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,WAAW,OAAO;AACnE,YAAI,aAAa,OAAO;AACtB,eAAK,iBAAiB,QAAQ,MAAM,OAAO,CAAC;AAAA,QAC9C;AACA,gBAAQ,MAAM;AAAA,MAChB;AACA,UAAI,UAAU,MAAM;AAClB,gBAAQ,mBAAmB,iBAAiB,KAAK,IAAI;AAAA,MACvD;AACA,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,UAAU,QAAQ,OAAO,UAAU,aAAa;AAC5G,YAAI,UAAU,QAAQ,OAAO,UAAU,eAAe,UAAU,IAAI;AAClE,eAAK,aAAa,eAAe;AACjC,eAAK,aAAa,gBAAgB;AAAA,QACpC;AACA,YAAI,aAAa;AACjB,YAAI,OAAO,eAAe,YAAY,KAAK,WAAW,EAAE,WAAW,eAAe,SAAS,GAAG;AAC5F,uBAAa,OAAO,UAAU;AAC9B,gBAAM,sBAAsB,KAAK,aAAa,2BAA2B;AACzE,cAAI,CAAC,MAAM,QAAQ,KAAK,aAAa,aAAa,GAAG;AACnD,yBAAa,KAAK,aAAa,kBAAkB,sBAAsB,WAAW,QAAQ,qBAAqB,KAAK,aAAa,aAAa,IAAI;AAAA,UACpJ;AACA,cAAI,KAAK,aAAa,YAAY,cAAc,KAAK,KAAK,KAAK,KAAK,sBAAsB,MAAM,OAAO;AACrG,yBAAa,KAAK,aAAa,gBAAgB,KAAK,aAAa,gBAAgB,UAAU;AAAA,UAC7F;AACA,cAAI,KAAK,aAAa,kBAAkB,eAAe,SAAS,MAAM,QAAQ,KAAK,aAAa,aAAa,KAAK,KAAK,aAAa,sBAAsB,eAAe,KAAK;AAC5K,yBAAa,WAAW,SAAS,EAAE,QAAQ,eAAe,KAAK,eAAe,KAAK;AAAA,UACrF;AACA,cAAI,KAAK,KAAK,GAAG,WAAW,eAAe,SAAS,KAAK,KAAK,SAAS,GAAG;AACxE,kCAAsB,MAAM;AAC1B,mBAAK,aAAa,UAAU,YAAY,SAAS,KAAK,IAAI,KAAK,aAAa,cAAc;AAAA,YAC5F,CAAC;AAAA,UACH;AACA,eAAK,aAAa,gBAAgB;AAAA,QACpC;AACA,YAAI,OAAO,eAAe,YAAY,UAAU,QAAQ,OAAO,UAAU,aAAa;AACpF,uBAAa;AAAA,QACf;AACA,aAAK,YAAY,IAAI,UAAU;AAC/B,aAAK,SAAS;AACd,YAAI,cAAc,KAAK,aAAa,kBAAkB,KAAK,aAAa,mBAAmB,KAAK,aAAa,UAAU,KAAK,aAAa,gBAAgB;AACvJ,cAAI,OAAO,qBAAqB,YAAY;AAC1C,iBAAK,aAAa,eAAe;AAAA,UACnC;AACA,eAAK,aAAa,sBAAsB,CAAC,SAAS,KAAK,aAAa,UAAU,YAAY,KAAK,aAAa,cAAc,CAAC;AAC3H,cAAI,OAAO,qBAAqB,YAAY;AAC1C,iBAAK,aAAa,eAAe;AAAA,UACnC;AAAA,QACF,OAAO;AACL,eAAK,aAAa,sBAAsB,CAAC,SAAS,UAAU;AAAA,QAC9D;AACA,aAAK,YAAY,IAAI,UAAU;AAAA,MACjC,OAAO;AACL,gBAAQ,KAAK,sEAAsE,OAAO,KAAK;AAAA,MACjG;AAAA,IACF;AAAA;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,aAAa,WAAW,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB,WAAW,KAAK,UAAU;AAC1C,UAAM,eAAe,UAAU,eAAe;AAC9C,QAAI,CAAC,cAAc,eAAe;AAChC,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,KAAK,kBAAkB,YAAY;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,yBAAyB,IAAI;AAC3B,UAAM,eAAe,KAAK,OAAO,EAAE;AACnC,UAAM,eAAe,KAAK,OAAO,EAAE;AACnC,UAAM,mBAAmB,KAAK,YAAY,EAAE;AAC5C,OAAG,iBAAiB,KAAK,IAAI,KAAK,IAAI,cAAc,GAAG,cAAc,GAAG,mBAAmB,YAAY;AACvG,OAAG,eAAe,KAAK,IAAI,KAAK,IAAI,cAAc,GAAG,YAAY,GAAG,mBAAmB,YAAY;AAAA,EACrG;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,aAAa,sBAAsB,CAAC,YAAY,UAAU;AAAA,EACjE;AAAA,EACA,aAAa;AACX,SAAK,aAAa,iBAAiB,KAAK,aAAa,sBAAsB,KAAK,WAAW,KAAK,EAAE;AAClG,SAAK,aAAa,sBAAsB,CAAC,SAAS,KAAK,aAAa,UAAU,KAAK,YAAY,GAAG,KAAK,aAAa,cAAc,CAAC;AAAA,EACrI;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,aAAa,KAAK,WAAW,EAAE,MAAM,eAAe,YAAY,EAAE,OAAO,OAAK,MAAM,GAAG,EAAE;AAC/F,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,EAAE,MAAM,MAAM,SAAS,CAAC,KAAK,QAAQ,KAAK,MAAM,SAAS,cAAc,MAAM,UAAU,aAAa,GAAG;AACzG,aAAO,KAAK,uBAAuB,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,YAAY,UAAU,KAAK,aAAa,YAAY,SAAS,KAAK,aAAa,OAAO;AAAA,EACjH;AAAA,EACA,uBAAuB,aAAa;AAClC,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,cAAc,KAAK,WAAW;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,EAAE,KAAK,UAAQ;AACvC,YAAM,eAAe,KAAK,MAAM,eAAe,YAAY,EAAE,KAAK,UAAQ,KAAK,aAAa,kBAAkB,SAAS,IAAI,CAAC;AAC5H,UAAI,gBAAgB,KAAK,YAAY,KAAK,KAAK,kCAAkC,KAAK,qBAAqB,CAAC,KAAK,KAAK,SAAS,eAAe,mBAAmB,GAAG;AAClK,cAAM,OAAO,KAAK,aAAa,WAAW,KAAK,YAAY,CAAC,GAAG,UAAU,KAAK,aAAa,WAAW,IAAI,GAAG;AAC7G,YAAI,MAAM;AACR,gBAAM,YAAY,KAAK,SAAS,eAAe,mBAAmB,IAAI,KAAK,aAAa,sBAAsB,IAAI,IAAI;AACtH,eAAK,WAAW,IAAI,SAAS;AAC7B,eAAK,aAAa,iBAAiB;AACnC,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,aAAa,KAAK,qBAAqB,EAAE,KAAK,qBAAqB,EAAE,SAAS,CAAC,KAAK,eAAe;AACzG,gBAAM,YAAY,WAAW,SAAS,eAAe,mBAAmB,IAAI,KAAK,aAAa,sBAAsB,UAAU,IAAI;AAClI,eAAK,WAAW,IAAI,SAAS;AAC7B,eAAK,aAAa,iBAAiB;AAAA,QACrC;AAAA,MACF,OAAO;AACL,cAAM,YAAY,KAAK,aAAa,WAAW,IAAI;AACnD,cAAM,QAAQ,KAAK,aAAa,WAAW,KAAK,YAAY,CAAC,GAAG,MAAM,eAAe,YAAY,EAAE,MAAM,CAAC,WAAW,UAAU;AAC7H,gBAAM,YAAY,UAAU,OAAO,KAAK;AACxC,iBAAO,KAAK,aAAa,iBAAiB,WAAW,SAAS;AAAA,QAChE,CAAC;AACD,YAAI,SAAS,KAAK,YAAY,GAAG;AAC/B,eAAK,WAAW,IAAI,IAAI;AACxB,eAAK,aAAa,iBAAiB;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kCAAkC,OAAO;AACvC,UAAM,oBAAoB,KAAK,aAAa;AAC5C,aAAS,wBAAwB,KAAK;AACpC,YAAM,QAAQ,IAAI,OAAO,IAAI,kBAAkB,IAAI,QAAM,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG;AACpF,aAAO,IAAI,QAAQ,OAAO,EAAE;AAAA,IAC9B;AACA,UAAM,eAAe,MAAM,IAAI,uBAAuB;AACtD,WAAO,aAAa,MAAM,SAAO;AAC/B,YAAM,mBAAmB,IAAI,IAAI,GAAG;AACpC,aAAO,iBAAiB,SAAS;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,EAAE,GAAG,CAAC,YAAY,QAAQ,EAAE,CAAC;AAAA,IAC3D,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,4CAA4C;AAC1E,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,iBAAO,IAAI,QAAQ,MAAM;AAAA,QAC3B,CAAC,EAAE,iBAAiB,SAAS,kDAAkD,QAAQ;AACrF,iBAAO,IAAI,cAAc,MAAM;AAAA,QACjC,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,iBAAO,IAAI,QAAQ,MAAM;AAAA,QAC3B,CAAC,EAAE,oBAAoB,SAAS,qDAAqD,QAAQ;AAC3F,iBAAO,IAAI,mBAAmB,MAAM;AAAA,QACtC,CAAC,EAAE,kBAAkB,SAAS,mDAAmD,QAAQ;AACvF,iBAAO,IAAI,iBAAiB,MAAM;AAAA,QACpC,CAAC,EAAE,QAAQ,SAAS,yCAAyC,QAAQ;AACnE,iBAAO,IAAI,OAAO,MAAM;AAAA,QAC1B,CAAC,EAAE,SAAS,SAAS,0CAA0C,QAAQ;AACrE,iBAAO,IAAI,QAAQ,MAAM;AAAA,QAC3B,CAAC,EAAE,WAAW,SAAS,4CAA4C,QAAQ;AACzE,iBAAO,IAAI,UAAU,MAAM;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,MAAM;AAAA,MAChB,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC1C,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,QAAQ,CAAC,GAAG,QAAQ;AAAA,MACpB,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC1C,eAAe,CAAC,GAAG,eAAe;AAAA,MAClC,uBAAuB,CAAC,GAAG,uBAAuB;AAAA,MAClD,aAAa,CAAC,GAAG,aAAa;AAAA,MAC9B,eAAe,CAAC,GAAG,eAAe;AAAA,MAClC,sBAAsB,CAAC,GAAG,sBAAsB;AAAA,MAChD,qBAAqB,CAAC,GAAG,qBAAqB;AAAA,MAC9C,iBAAiB,CAAC,GAAG,iBAAiB;AAAA,MACtC,YAAY,CAAC,GAAG,YAAY;AAAA,MAC5B,gBAAgB,CAAC,GAAG,gBAAgB;AAAA,MACpC,sBAAsB,CAAC,GAAG,sBAAsB;AAAA,MAChD,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,UAAU,CAAC,GAAG,UAAU;AAAA,MACxB,qBAAqB,CAAC,GAAG,qBAAqB;AAAA,MAC9C,KAAK,CAAC,GAAG,KAAK;AAAA,MACd,kBAAkB,CAAC,GAAG,kBAAkB;AAAA,MACxC,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC1C,wBAAwB,CAAC,GAAG,wBAAwB;AAAA,MACpD,eAAe,CAAC,GAAG,eAAe;AAAA,IACpC;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAC,QAAQ,SAAS;AAAA,IAC5B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,GAAG,cAAc,CAAC,GAAM,oBAAoB;AAAA,EAC9C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,GAAG,cAAc;AAAA,MACjB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AAAA,IACpC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;AAAA,IACvC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;AAAA,IACrC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,iBAAiB,OAAO,eAAe;AAAA,EACvC,eAAe,OAAO,cAAc;AAAA,EACpC,uBAAuB,OAAO,CAAC,CAAC;AAAA,EAChC,QAAQ,OAAO,EAAE;AAAA,EACjB,UAAU,OAAO,MAAM,KAGnB,CAAC,GAAG;AAHe,iBACrB;AAAA;AAAA,IA17DJ,IAy7DyB,IAElB,mBAFkB,IAElB;AAAA,MADH;AAAA;AAGA,QAAI,iBAAiB;AACrB,UAAM,gBAAgB;AAAA,MACpB,gBAAgB;AAAA,OACb,KAAK,iBACL,SAHiB;AAAA,MAIpB,UAAU,kCACL,KAAK,aAAa,WAClB;AAAA,IAEP;AACA,WAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,GAAG,MAAM;AACpD,WAAK,aAAa,GAAG,IAAI;AAAA,IAC3B,CAAC;AACD,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,YAAM,YAAY,KAAK,MAAM,IAAI;AACjC,UAAI,UAAU,SAAS,GAAG;AACxB,aAAK,qBAAqB,IAAI,UAAU,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;AAC3E,aAAK,SAAS,cAAc;AAC5B,eAAO,KAAK,aAAa,UAAU,GAAG,cAAc,IAAI,KAAK,MAAM,CAAC;AAAA,MACtE,OAAO;AACL,aAAK,qBAAqB,IAAI,CAAC,CAAC;AAChC,eAAO,KAAK,aAAa,UAAU,GAAG,cAAc,IAAI,KAAK,MAAM,CAAC;AAAA,MACtE;AAAA,IACF;AACA,QAAI,KAAK,SAAS,eAAe,mBAAmB,GAAG;AACrD,aAAO,KAAK,aAAa,UAAU,GAAG,cAAc,IAAI,KAAK,aAAa,sBAAsB,IAAI,CAAC;AAAA,IACvG;AACA,QAAI,KAAK,WAAW,eAAe,SAAS,GAAG;AAC7C,UAAI,OAAO,eAAe;AACxB,aAAK,aAAa,gBAAgB,OAAO;AAAA,MAC3C;AACA,UAAI,OAAO,mBAAmB;AAC5B,aAAK,aAAa,oBAAoB,OAAO;AAAA,MAC/C;AACA,UAAI,OAAO,UAAU;AACnB,aAAK,aAAa,WAAW,OAAO;AAAA,MACtC;AACA,uBAAiB,OAAO,cAAc;AACtC,YAAM,sBAAsB,KAAK,aAAa,2BAA2B;AACzE,UAAI,CAAC,MAAM,QAAQ,KAAK,aAAa,aAAa,GAAG;AACnD,yBAAiB,KAAK,aAAa,kBAAkB,sBAAsB,eAAe,QAAQ,qBAAqB,KAAK,aAAa,aAAa,IAAI;AAAA,MAC5J;AACA,UAAI,KAAK,aAAa,YAAY,kBAAkB,KAAK,aAAa,0BAA0B,OAAO;AACrG,yBAAiB,KAAK,aAAa,gBAAgB,MAAM,cAAc;AAAA,MACzE;AACA,UAAI,KAAK,aAAa,kBAAkB,eAAe,OAAO;AAC5D,yBAAiB,eAAe,QAAQ,eAAe,KAAK,eAAe,KAAK;AAAA,MAClF;AACA,WAAK,aAAa,gBAAgB;AAAA,IACpC;AACA,QAAI,mBAAmB,QAAQ,OAAO,mBAAmB,aAAa;AACpE,aAAO,KAAK,aAAa,UAAU,IAAI,IAAI;AAAA,IAC7C;AACA,WAAO,KAAK,aAAa,UAAU,GAAG,cAAc,IAAI,IAAI;AAAA,EAC9D;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAC1C,WAAK,qBAAqB,EAAE,KAAK,UAAQ;AACvC,cAAM,OAAO,KAAK,aAAa,WAAW,KAAK,GAAG,UAAU,KAAK,aAAa,WAAW,IAAI,GAAG;AAChG,YAAI,SAAS,MAAM;AACjB,eAAK,MAAM,IAAI,IAAI;AACnB,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,aAAa,KAAK,qBAAqB,EAAE,KAAK,qBAAqB,SAAS,CAAC,KAAK,eAAe;AACvG,eAAK,MAAM,IAAI,UAAU;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,QAA0B,aAAa;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["MaskExpression"]}