package be.fgov.onerva.cu.backend.application.validation

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.port.out.LookupPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class NationalityAndCountryCodeValidatorTest {

    @MockK
    private lateinit var lookupPort: LookupPort

    @InjectMockKs
    private lateinit var validator: NationalityAndCountryCodeValidator

    @Test
    fun `isValid should return false when value is null`() {
        // When
        val result = validator.isValid(null, null)

        // Then
        assertThat(result).isFalse()
        verify(exactly = 0) { lookupPort.validateCountryOrNationality(any()) }
    }

    @Test
    fun `isValid should return true when code exists in lookup service`() {
        // Given
        val validCode = 150
        every { lookupPort.validateCountryOrNationality(validCode) } returns true

        // When
        val result = validator.isValid(validCode, null)

        // Then
        assertThat(result).isTrue()
        verify(exactly = 1) { lookupPort.validateCountryOrNationality(validCode) }
    }

    @Test
    fun `isValid should return false when code does not exist in lookup service`() {
        // Given
        val invalidCode = 999
        every { lookupPort.validateCountryOrNationality(invalidCode) } returns false

        // When
        val result = validator.isValid(invalidCode, null)

        // Then
        assertThat(result).isFalse()
        verify(exactly = 1) { lookupPort.validateCountryOrNationality(invalidCode) }
    }
}