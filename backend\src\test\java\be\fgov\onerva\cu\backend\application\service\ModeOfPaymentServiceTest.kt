package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UpdateModeOfPayment
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.FieldSourcePort
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class ModeOfPaymentServiceTest {

    @MockK
    private lateinit var modeOfPaymentPort: ModeOfPaymentPort

    @MockK
    private lateinit var fieldSourcePort: FieldSourcePort

    @MockK
    private lateinit var historicalInformationService: HistoricalInformationService

    @InjectMockKs
    private lateinit var modeOfPaymentService: ModeOfPaymentService

    @Test
    fun `getModeOfPayment delegates to port`() {
        // Given
        val requestId = UUID.randomUUID()
        val modeOfPayment = ModeOfPayment(
            otherPersonName = null, iban = "****************", bic = null, LocalDate.of(2025, 1, 1)
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns modeOfPayment

        // When
        val result = modeOfPaymentService.getModeOfPayment(requestId)

        // Then
        assertThat(result).isEqualTo(modeOfPayment)
        verify(exactly = 1) { modeOfPaymentPort.getModeOfPayment(requestId) }
    }

    @Test
    fun `updateModeOfPayment delegates to port with correct data`() {
        // Given
        val requestId = UUID.randomUUID()
        val updateModeOfPayment = UpdateModeOfPayment(
            otherPersonName = null, iban = "****************", bic = null,
            validFrom = LocalDate.of(2025, 9, 1),
        )

        every {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == null && it.iban == "****************" && it.bic == null
                })
        } returns Unit

        // When
        modeOfPaymentService.updateModeOfPayment(requestId, updateModeOfPayment)

        // Then
        verify(exactly = 1) {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == null && it.iban == "****************" && it.bic == null
                })
        }
    }

    @Test
    fun `getModeOfPaymentFieldSources delegates to field source port`() {
        // Given
        val requestId = UUID.randomUUID()
        val entityId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("belgianSEPABankAccount", ExternalSource.C1), FieldSource("otherPersonName", ExternalSource.C1)
        )

        every { modeOfPaymentPort.getEntityId(requestId) } returns entityId
        every { fieldSourcePort.getAllFieldSources("mode_of_payment", entityId) } returns fieldSources

        // When
        val result = modeOfPaymentService.getModeOfPaymentFieldSources(requestId)

        // Then
        assertThat(result).isEqualTo(fieldSources)
        verify(exactly = 1) { modeOfPaymentPort.getEntityId(requestId) }
        verify(exactly = 1) { fieldSourcePort.getAllFieldSources("mode_of_payment", entityId) }
    }

    @Test
    fun `selectModeOfPaymentFieldSources throws exception when mode of payment not found`() {
        // Given
        val requestId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("belgianSEPABankAccount", ExternalSource.C1)
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns null

        // When/Then
        assertThatThrownBy {
            modeOfPaymentService.selectModeOfPaymentFieldSources(requestId, fieldSources)
        }.isInstanceOf(RequestIdNotFoundException::class.java)
            .hasMessage("Mode of payment not found for request ID: $requestId")

        verify(exactly = 1) { modeOfPaymentPort.getModeOfPayment(requestId) }
        verify(exactly = 0) { historicalInformationService.getModeOfPayment(any(), any()) }
    }

    @Test
    fun `selectModeOfPaymentFieldSources processes field sources from C1 correctly`() {
        // Given
        val requestId = UUID.randomUUID()
        val entityId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("account", ExternalSource.C1), FieldSource("otherPersonName", ExternalSource.C1)
        )

        val existingModeOfPayment = ModeOfPayment(
            otherPersonName = null, iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val c1ModeOfPayment = ModeOfPayment(
            otherPersonName = "John Doe", iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val onemCitizenInformation = HistoricalCitizenOnem(
            firstName = "John",
            lastName = "Doe",
            numbox = 42,
            nationalityCode = 111,
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = null,
                countryCode = 150,
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1),
            ),
            iban = "****************",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1)
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns existingModeOfPayment
        every { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) } returns c1ModeOfPayment
        every {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        } returns onemCitizenInformation
        every { modeOfPaymentPort.getEntityId(requestId) } returns entityId
        every {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == "John Doe" && it.iban == "****************" && it.bic == null && it.validFrom == LocalDate.of(
                        2025, 1, 1
                    )
                })
        } returns Unit
        every { fieldSourcePort.setMultipleFieldSources("mode_of_payment", entityId, fieldSources) } returns Unit

        // When
        modeOfPaymentService.selectModeOfPaymentFieldSources(requestId, fieldSources)

        // Then
        verify(exactly = 1) { modeOfPaymentPort.getModeOfPayment(requestId) }
        verify(exactly = 1) { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) }
        verify(exactly = 1) {
            historicalInformationService.getHistoricalCitizenOnem(requestId)
        }
        verify(exactly = 1) {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == "John Doe" && it.iban == "****************" && it.bic == null && it.validFrom == LocalDate.of(
                        2025, 1, 1
                    )
                })
        }
        verify(exactly = 1) { modeOfPaymentPort.getEntityId(requestId) }
        verify(exactly = 1) { fieldSourcePort.setMultipleFieldSources("mode_of_payment", entityId, fieldSources) }
    }

    @Test
    fun `selectModeOfPaymentFieldSources processes field sources from ONEM correctly`() {
        // Given
        val requestId = UUID.randomUUID()
        val entityId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("account", ExternalSource.ONEM)
        )

        val existingModeOfPayment = ModeOfPayment(
            otherPersonName = null, iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val c1ModeOfPayment = ModeOfPayment(
            otherPersonName = "John Doe", iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val onemCitizenInformation = HistoricalCitizenOnem(
            firstName = "John",
            lastName = "Doe",
            numbox = 42,
            nationalityCode = 111,
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = null,
                countryCode = 150,
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1)
            ),
            iban = "****************",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns existingModeOfPayment
        every { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) } returns c1ModeOfPayment
        every {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        } returns onemCitizenInformation
        every { modeOfPaymentPort.getEntityId(requestId) } returns entityId
        every {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == null && it.iban == "****************" && it.bic == null && it.validFrom == LocalDate.of(
                        2022, 1, 1
                    )
                })
        } returns Unit
        every { fieldSourcePort.setMultipleFieldSources("mode_of_payment", entityId, fieldSources) } returns Unit

        // When
        modeOfPaymentService.selectModeOfPaymentFieldSources(requestId, fieldSources)

        // Then
        verify(exactly = 1) { modeOfPaymentPort.getModeOfPayment(requestId) }
        verify(exactly = 1) { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) }
        verify(exactly = 1) {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId
            )
        }
        verify(exactly = 1) {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == null && it.iban == "****************" && it.bic == null && it.validFrom == LocalDate.of(
                        2022, 1, 1
                    )
                })
        }
        verify(exactly = 1) { modeOfPaymentPort.getEntityId(requestId) }
        verify(exactly = 1) { fieldSourcePort.setMultipleFieldSources("mode_of_payment", entityId, fieldSources) }
    }

    @Test
    fun `selectModeOfPaymentFieldSources keeps existing values when field not in sources`() {
        // Given
        val requestId = UUID.randomUUID()
        val entityId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("account", ExternalSource.C1)
            // otherPersonName is not included
        )

        val existingModeOfPayment = ModeOfPayment(
            otherPersonName = "Original Name",
            iban = "****************",
            bic = null,
            validFrom = LocalDate.of(2025, 1, 1)
        )

        val c1ModeOfPayment = ModeOfPayment(
            otherPersonName = "John Doe", iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val onemCitizenInformation = HistoricalCitizenOnem(
            firstName = "John",
            lastName = "Doe",
            numbox = 42,
            nationalityCode = 111,
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = null,
                countryCode = 150,
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1),
            ),
            iban = "****************",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1)
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns existingModeOfPayment
        every { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) } returns c1ModeOfPayment
        every {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        } returns onemCitizenInformation
        every { modeOfPaymentPort.getEntityId(requestId) } returns entityId
        every {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, any()
            )
        } returns Unit
        every { fieldSourcePort.setMultipleFieldSources("mode_of_payment", entityId, fieldSources) } returns Unit

        // When
        modeOfPaymentService.selectModeOfPaymentFieldSources(requestId, fieldSources)

        // Then
        verify(exactly = 1) {
            modeOfPaymentPort.persistModeOfPayment(
                requestId, match {
                    it.otherPersonName == "Original Name" && // original value kept
                            it.iban == "****************" && it.bic == null && it.validFrom == LocalDate.of(2025, 1, 1)
                })
        }
    }

    @Test
    fun `selectModeOfPaymentFieldSources throws exception for AUTHENTIC_SOURCES with belgianSEPABankAccount`() {
        // Given
        val requestId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("account", ExternalSource.AUTHENTIC_SOURCES)
        )

        val existingModeOfPayment = ModeOfPayment(
            otherPersonName = null, iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val c1ModeOfPayment = ModeOfPayment(
            otherPersonName = "John Doe", iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val onemCitizenInformation = HistoricalCitizenOnem(
            firstName = "John",
            lastName = "Doe",
            numbox = 42,
            nationalityCode = 111,
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = null,
                countryCode = 150,
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1)
            ),
            iban = "****************",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1)
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns existingModeOfPayment
        every { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) } returns c1ModeOfPayment
        every {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        } returns onemCitizenInformation

        // When/Then
        assertThatThrownBy {
            modeOfPaymentService.selectModeOfPaymentFieldSources(requestId, fieldSources)
        }.isInstanceOf(InvalidExternalDataException::class.java)
            .hasMessage("Authentic sources does not provide bank account")

        verify(exactly = 1) { modeOfPaymentPort.getModeOfPayment(requestId) }
        verify(exactly = 1) { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) }
        verify(exactly = 1) {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        }
        verify(exactly = 0) { modeOfPaymentPort.persistModeOfPayment(any(), any()) }
    }

    @Test
    fun `selectModeOfPaymentFieldSources throws exception for AUTHENTIC_SOURCES with otherPersonName`() {
        // Given
        val requestId = UUID.randomUUID()
        val fieldSources = listOf(
            FieldSource("otherPersonName", ExternalSource.AUTHENTIC_SOURCES)
        )

        val existingModeOfPayment = ModeOfPayment(
            otherPersonName = null, iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val c1ModeOfPayment = ModeOfPayment(
            otherPersonName = "John Doe", iban = "****************", bic = null, validFrom = LocalDate.of(2025, 1, 1)
        )

        val onemCitizenInformation = HistoricalCitizenOnem(
            firstName = "John",
            lastName = "Doe",
            numbox = 42,
            nationalityCode = 111,
            birthDate = null,
            address = AddressNullable(
                street = "Main Street",
                houseNumber = "42",
                boxNumber = null,
                countryCode = 150,
                city = "Brussels",
                zipCode = "1000",
                valueDate = LocalDate.of(2022, 1, 1),
            ),
            iban = "****************",
            bic = null,
            otherPersonName = null,
            bankAccountValueDate = LocalDate.of(2022, 1, 1),
            paymentMode = 1,
            authorized = true,
            effectiveDate = LocalDate.of(2022, 1, 1)
        )

        every { modeOfPaymentPort.getModeOfPayment(requestId) } returns existingModeOfPayment
        every { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) } returns c1ModeOfPayment
        every {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        } returns onemCitizenInformation

        // When/Then
        assertThatThrownBy {
            modeOfPaymentService.selectModeOfPaymentFieldSources(requestId, fieldSources)
        }.isInstanceOf(InvalidExternalDataException::class.java)
            .hasMessage("Authentic sources does not provide other person name")

        verify(exactly = 1) { modeOfPaymentPort.getModeOfPayment(requestId) }
        verify(exactly = 1) { historicalInformationService.getModeOfPayment(requestId, ExternalSource.C1) }
        verify(exactly = 1) {
            historicalInformationService.getHistoricalCitizenOnem(
                requestId,
            )
        }
        verify(exactly = 0) { modeOfPaymentPort.persistModeOfPayment(any(), any()) }
    }
}