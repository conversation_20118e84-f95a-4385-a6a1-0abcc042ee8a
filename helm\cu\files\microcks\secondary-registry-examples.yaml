apiVersion: mocks.microcks.io/v1alpha1
kind: APIExamples
metadata:
  name: Register Proxy Service public API
  version: '1.0.0'
operations:
  'GET /getCitizenInfosBySsin':
    Citizen-18031307065:
      request:
        parameters:
          ssin: '18031307065'
          adresses: true
          contactaddress: false
          decease: false
          names: true
          nationalities: true
          replaced_niss: false
      response:
        status: '200'
        mediaType: application/json
        body:
          addresses:
            - "@type": 'ResidentialAddress'
              validityPeriod:
                beginDate: 1490565600000
                endDate: null
              radiated: false
              cityCode: 21001
              streetCode: 329
              postalCode: '1070'
              houseNumber: '37'
              boxNumber: 'ET01'
              countryCode: 150
              regionCode: 9
          birthdate: '2000-01-01'
          deceaseDate: null
          gender: null
          identification: 0
          lastName: 'Doe'
          names:
            - firstName: 'John'
              seq: 1
            - firstName: 'Deer<PERSON>'
              seq: 2
          nationalities:
            - nationalityCode: 150
              validityBeginDate: 946684800
          replacedSsin: null
          ssin: '18031307065'
          status: null
