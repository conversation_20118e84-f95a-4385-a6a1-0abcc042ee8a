alter table citizen_information
    add address_valid_from date;
alter table citizen_information_aud
    add address_valid_from date;
GO

-- Update valid_from with request_date from related request
update citizen_information
set address_valid_from = r.request_date
from citizen_information ci
         inner join request r on ci.request_id = r.id;
update citizen_information_aud
set address_valid_from = r.request_date
from citizen_information_aud ci_aud
         inner join citizen_information ci on ci.id = ci_aud.id
         inner join request r on ci.request_id = r.id;

-- Make the column not null
alter table citizen_information
    alter column address_valid_from date not null;
alter table citizen_information_aud
    alter column address_valid_from date not null;