{"version": 3, "sources": ["../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-message-box.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { HostBinding, Input, ViewChild, Component, NgModule } from '@angular/core';\nimport { OnemrvaMatColor } from '@onemrvapublic/design-system/utils';\nconst _c0 = [\"mbcontent\"];\nconst _c1 = [\"*\"];\nlet NEXT_ID = 0;\nclass OnemrvaMatMessageBoxComponent {\n  constructor() {\n    this.label = false;\n    this.messageCode = '';\n    /**\n     * Returns the `aria-label` attribute of the element.\n     *\n     * @example\n     * ```typescript\n     * let elementLabel = this.<element>.ariaLabel;\n     * ```\n     *\n     */\n    this.ariaLabel = 'message-box';\n    /**\n     * Returns the `role` attribute of the element.\n     *\n     * @example\n     * ```typescript\n     * let elementRole = this.<element>.role;\n     * ```\n     */\n    this.role = 'alert';\n    /**\n     * Host `class.onemrva-mat-message-box` binding.\n     *\n     * @hidden\n     * @internal\n     */\n    this.cssClass = 'onemrva-mat-message-box';\n    /**\n     * Sets the `id` of the element. If not set, the first component instance will have `id` = `\"onemrva-mat-*-0\"`.\n     *\n     * @example\n     * ```html\n     * <onemrva-mat-* id=\"my-first-*\"></onemrva-mat-*>\n     * ```\n     */\n    this.id = `onemrva-mat-message-box-${NEXT_ID++}`;\n    /**\n     * Sets the `data-cy` of the element. If not set, the first component instance will have `data-cy` = `\"onemrva-mat-*-0\"`.\n     *\n     * @example\n     * ```html\n     * <onemrva-mat-* id=\"my-first-*\"></onemrva-mat-*>\n     * ```\n     */\n    this.dataCy = `onemrva-mat-message-box-${NEXT_ID++}`;\n    /**\n     * @hidden\n     * @internal\n     */\n    this._color = OnemrvaMatColor.NONE;\n  }\n  /**\n   * Returns the color of the element.\n   *\n   * @example\n   * ```typescript\n   * let color = this.<element>.color;\n   * ```\n   */\n  get color() {\n    return this._color;\n  }\n  /**\n   * Sets the size  of the element.\n   * By default, the size is `\"small\"`. It can be set to `\"medium\"` or `\"large\"`.\n   *\n   * @example\n   * ```html\n   * <onemrva-mat-* size=\"large\"></onemrva-mat-*>\n   * ```\n   */\n  set color(value) {\n    switch (value) {\n      case OnemrvaMatColor.SUCCESS:\n      case OnemrvaMatColor.WARN:\n      case OnemrvaMatColor.NONE:\n      case OnemrvaMatColor.INFO:\n      case OnemrvaMatColor.GRAYSCALE:\n      case OnemrvaMatColor.ERROR:\n        this._color = value;\n        break;\n      default:\n        this._color = OnemrvaMatColor.INFO;\n    }\n  }\n  /** @hidden @internal */\n  get _isError() {\n    return this.color === OnemrvaMatColor.ERROR;\n  }\n  /** @hidden @internal */\n  get _isWarn() {\n    return this.color === OnemrvaMatColor.WARN;\n  }\n  /** @hidden @internal */\n  get _isSuccess() {\n    return this.color === OnemrvaMatColor.SUCCESS;\n  }\n  /** @hidden @internal */\n  get isGrayscale() {\n    return this.color === OnemrvaMatColor.GRAYSCALE || this.color === OnemrvaMatColor.NONE;\n  }\n  /** @hidden @internal */\n  get _isInfo() {\n    return this.color !== OnemrvaMatColor.ERROR && this.color !== OnemrvaMatColor.WARN && this.color !== OnemrvaMatColor.NONE && this.color !== OnemrvaMatColor.ACCENT && this.color !== OnemrvaMatColor.GRAYSCALE && this.color !== OnemrvaMatColor.SUCCESS;\n  }\n  ngAfterViewInit() {\n    if (this.messageCode !== '') {\n      console.warn('messageCode input has been deprecated in message-box component, please review documentation to fix this!');\n      this.mbcontent.nativeElement.insertAdjacentHTML('beforeend', this.messageCode);\n    }\n  }\n  static {\n    this.ɵfac = function OnemrvaMatMessageBoxComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatMessageBoxComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OnemrvaMatMessageBoxComponent,\n      selectors: [[\"onemrva-mat-message-box\"]],\n      viewQuery: function OnemrvaMatMessageBoxComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mbcontent = _t.first);\n        }\n      },\n      hostVars: 16,\n      hostBindings: function OnemrvaMatMessageBoxComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"role\", ctx.role)(\"id\", ctx.id)(\"data-cy\", ctx.dataCy);\n          i0.ɵɵclassProp(\"onemrva-mat-message-box\", ctx.cssClass)(\"mat-error\", ctx._isError)(\"mat-warn\", ctx._isWarn)(\"mat-success\", ctx._isSuccess)(\"mat-grayscale\", ctx.isGrayscale)(\"mat-info\", ctx._isInfo);\n        }\n      },\n      inputs: {\n        label: \"label\",\n        messageCode: \"messageCode\",\n        id: \"id\",\n        dataCy: \"dataCy\",\n        color: \"color\"\n      },\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 0,\n      consts: [[\"mbcontent\", \"\"]],\n      template: function OnemrvaMatMessageBoxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", null, 0);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatMessageBoxComponent, [{\n    type: Component,\n    args: [{\n      selector: 'onemrva-mat-message-box',\n      standalone: true,\n      template: \"<div #mbcontent>\\n  <ng-content></ng-content>\\n</div>\\n\"\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    messageCode: [{\n      type: Input\n    }],\n    mbcontent: [{\n      type: ViewChild,\n      args: ['mbcontent']\n    }],\n    ariaLabel: [{\n      type: HostBinding,\n      args: ['attr.aria-label']\n    }],\n    role: [{\n      type: HostBinding,\n      args: ['attr.role']\n    }],\n    cssClass: [{\n      type: HostBinding,\n      args: ['class.onemrva-mat-message-box']\n    }],\n    id: [{\n      type: HostBinding,\n      args: ['attr.id']\n    }, {\n      type: Input\n    }],\n    dataCy: [{\n      type: HostBinding,\n      args: ['attr.data-cy']\n    }, {\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    _isError: [{\n      type: HostBinding,\n      args: ['class.mat-error']\n    }],\n    _isWarn: [{\n      type: HostBinding,\n      args: ['class.mat-warn']\n    }],\n    _isSuccess: [{\n      type: HostBinding,\n      args: ['class.mat-success']\n    }],\n    isGrayscale: [{\n      type: HostBinding,\n      args: ['class.mat-grayscale']\n    }],\n    _isInfo: [{\n      type: HostBinding,\n      args: ['class.mat-info']\n    }]\n  });\n})();\nclass OnemrvaMatMessageBoxModule {\n  static {\n    this.ɵfac = function OnemrvaMatMessageBoxModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatMessageBoxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaMatMessageBoxModule,\n      imports: [OnemrvaMatMessageBoxComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatMessageBoxModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      imports: [OnemrvaMatMessageBoxComponent],\n      exports: []\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaMatMessageBoxComponent, OnemrvaMatMessageBoxModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAI,UAAU;AACd,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,cAAc;AAUnB,SAAK,YAAY;AASjB,SAAK,OAAO;AAOZ,SAAK,WAAW;AAShB,SAAK,KAAK,2BAA2B,SAAS;AAS9C,SAAK,SAAS,2BAA2B,SAAS;AAKlD,SAAK,SAAS,gBAAgB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,MAAM,OAAO;AACf,YAAQ,OAAO;AAAA,MACb,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AAAA,MACrB,KAAK,gBAAgB;AACnB,aAAK,SAAS;AACd;AAAA,MACF;AACE,aAAK,SAAS,gBAAgB;AAAA,IAClC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,UAAU,gBAAgB,aAAa,KAAK,UAAU,gBAAgB;AAAA,EACpF;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB,SAAS,KAAK,UAAU,gBAAgB,QAAQ,KAAK,UAAU,gBAAgB,QAAQ,KAAK,UAAU,gBAAgB,UAAU,KAAK,UAAU,gBAAgB,aAAa,KAAK,UAAU,gBAAgB;AAAA,EACnP;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB,IAAI;AAC3B,cAAQ,KAAK,0GAA0G;AACvH,WAAK,UAAU,cAAc,mBAAmB,aAAa,KAAK,WAAW;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,MACvC,WAAW,SAAS,oCAAoC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,QAClE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,2CAA2C,IAAI,KAAK;AACzE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,cAAc,IAAI,SAAS,EAAE,QAAQ,IAAI,IAAI,EAAE,MAAM,IAAI,EAAE,EAAE,WAAW,IAAI,MAAM;AACjG,UAAG,YAAY,2BAA2B,IAAI,QAAQ,EAAE,aAAa,IAAI,QAAQ,EAAE,YAAY,IAAI,OAAO,EAAE,eAAe,IAAI,UAAU,EAAE,iBAAiB,IAAI,WAAW,EAAE,YAAY,IAAI,OAAO;AAAA,QACtM;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,QACb,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;AAAA,MAC1B,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,6BAA6B;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC;AAAA,MACf,SAAS,CAAC,6BAA6B;AAAA,MACvC,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}