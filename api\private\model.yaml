components:
  schemas:
    RequestBasicInfoResponse:
      type: object
      required:
        - requestDate
        - firstName
        - lastName
        - ssin
        - c9Id
        - documentType
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted
        firstName:
          type: string
          description: The first name of the person
        lastName:
          type: string
          description: The last name of the person
        ssin:
          type: string
          description: The Social Security Identification Number
        introductionDate:
          type: string
          format: date
          description: The introduction date of the request
        dateValid:
          type: string
          format: date
          description: The valid date of the request
        annexes:
          type: array
          items:
            $ref: '#/components/schemas/Annex'
        decisionType:
          type: string
          description: The decision type of the request
          enum:
            - C2Y
            - C2N
            - C2F
            - C2P
            - C51
            - C9B
            - C2
            - C9NA
            - C9Bis
            - C2Prolongation
        decisionBarema:
          type: string
        c9Id:
          type: string
          description: The ID of the related C9 request
        documentType:
          type: string
          description: The type of identity document used for the request
          enum:
            - ELECTRONIC
            - PAPER
        pushbackStatus:
          type: string
          description: The pushback status of the request
          enum:
            - PENDING
            - OK
            - <PERSON>K

    Address:
      type: object
      required:
        - countryCode
        - street
        - houseNumber
        - zipCode
        - city
        - validFrom
      properties:
        countryCode:
          type: integer
          format: int32
          description: The country code of residence
        street:
          type: string
          description: The street name
        houseNumber:
          type: string
          description: The house number
        boxNumber:
          type: string
          description: The box number (optional)
        zipCode:
          type: string
          description: The postal/zip code
        city:
          type: string
          description: The city name
        validFrom:
          type: string
          format: date
          description: The date from which the address is valid (format YYYY-MM-DD)

    # Field source object to track source for individual fields
    FieldSource:
      type: object
      properties:
        fieldName:
          type: string
          description: The name of the field this source applies to
        source:
          $ref: '#/components/schemas/ExternalSource'

    # Updated schema with field sources
    UpdateCitizenInformationRequest:
      type: object
      required:
        - birthDate
        - nationalityCode
        - address
        - fieldSources
      properties:
        birthDate:
          type: string
          format: date
          description: The birth date of the employee (format YYYY-MM-DD)
        nationalityCode:
          type: integer
          format: int32
          description: The nationality (code) of the employee
        address:
          $ref: '#/components/schemas/Address'

    CitizenInformationDetailResponse:
      type: object
      properties:
        birthDate:
          type: string
          format: date
          description: The birth date of the employee (format YYYY-MM-DD)
        nationalityCode:
          type: integer
          format: int32
          description: The nationality of the employee
        address:
          $ref: '#/components/schemas/Address'
        fieldSources:
          type: array
          description: Sources for individual fields
          items:
            $ref: '#/components/schemas/FieldSource'

    ModeOfPaymentFields:
      type: object
      properties:
        otherPersonName:
          type: string
        iban:
          type: string
        bic:
          type: string
        validFrom:
          type: string
          format: date
          description: The date from which the mode of payment is valid (format YYYY-MM-DD)

    UpdateModeOfPaymentRequest:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'

    ModeOfPaymentDetailResponse:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'
        - type: object
          properties:
            fieldSources:
              type: array
              description: Sources for individual fields
              items:
                $ref: '#/components/schemas/FieldSource'

    UnionContributionFields:
      type: object
      properties:
        authorized:
          type: boolean
        effectiveDate:
          type: string
          format: date
          description: The effective date of the union contribution (format YYYY-MM-DD)

    UpdateUnionContributionRequest:
      allOf:
        - $ref: '#/components/schemas/UnionContributionFields'

    UnionContributionDetailResponse:
      allOf:
        - $ref: '#/components/schemas/UnionContributionFields'
        - type: object
          properties:
            fieldSources:
              type: array
              description: Sources for individual fields
              items:
                $ref: '#/components/schemas/FieldSource'

    AnnexType:
      type: string
      enum:
        - SCANNED_DOCUMENTS
        - EC1

    ExternalSource:
      type: string
      enum:
        - C1
        - ONEM
        - AUTHENTIC_SOURCES

    Annex:
      type: object
      description: Represents an attachment of the C9 and its display url
      properties:
        type:
          $ref: '#/components/schemas/AnnexType'
        url:
          type: string

    WaveTaskResponse:
      type: object
      required:
        - processId
        - taskId
        - status
        - waveUrl
      properties:
        processId:
          type: string
        taskId:
          type: string
        status:
          $ref: '#/components/schemas/WaveTaskStatus'
        waveUrl:
          type: string
          readOnly: true

    WaveTaskStatus:
      type: string
      enum:
        - OPEN
        - CLOSED
        - WAITING
        - DELETED

    KeycloakConfigResponse:
      type: object
      title: KeycloakConfigResponse
      required:
        - config
      properties:
        config:
          required:
            - clientId
            - url
            - realm
          type: object
          properties:
            url:
              type: string
              description: Base uri of the realm
              example: 'http://host.docker.internal:8082/auth/'
            clientId:
              type: string
              description: The id of the frontend client
            realm:
              type: string
              description: The keycloak realm
        initOptions:
          required:
            - redirectUri
            - checkLoginIframe
            - onLoad
          type: object
          properties:
            redirectUri:
              type: string
              description: The uri where keycloak must return after login
              example: "http://localhost:4300/"
            checkLoginIframe:
              type: boolean
            onLoad:
              type: string
              example: check-sso

    FlagsmithConfigResponse:
      type: object
      title: FlagsmithConfigResponse
      required:
        - environmentID
        - identity
        - api
      properties:
        environmentID:
          type: string
        identity:
          type: string
        api:
          type: string

    Environment:
      type: string
      enum:
        - LOCAL
        - TEST
        - VAL
        - PROD

    RequestInformationResponse:
      type: object
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted
        processedInWave:
          type: boolean
          description: Whether the request can be processed in Wave
        routingDecisions:
          type: array
          items:
            $ref: '#/components/schemas/RoutingDecision'
          description: List of routing decisions for manual verification

    UpdateRequestInformationRequest:
      type: object
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted
        processedInWave:
          type: boolean
          description: Whether the request can be processed in Wave
        routingDecisions:
          type: array
          items:
            $ref: '#/components/schemas/RoutingDecision'
          description: List of routing decisions for manual verification

    RoutingDecision:
      type: object
      required:
        - type
        - value
        - canBeProcessedInWave
      properties:
        type:
          $ref: '#/components/schemas/ManualVerificationType'
        value:
          type: boolean
          description: The answer to the verification question (true for yes, false for no)
        canBeProcessedInWave:
          type: boolean
          description: Whether this decision allows processing in Wave

    ManualVerificationType:
      type: string
      enum:
        - CITIZEN_OVER_65_YEARS_OLD
        - RELEVANT_TO_PORT_WORKER
        - NON_BELGIAN_RESIDENT
        - TRANSFER_BETWEEN_OP_OR_OC
        - REQUEST_FOR_ECONOMIC_REASON
        - RELEVANT_TO_APPRENTICESHIP
        - CASE_OF_IMPULSION
      description: |
        Types of manual verification questions:
        - CITIZEN_OVER_65_YEARS_OLD: Is the citizen over 65 years old?
        - RELEVANT_TO_PORT_WORKER: Is this relevant to a port worker?
        - NON_BELGIAN_RESIDENT: Is the citizen a non-Belgian resident?
        - TRANSFER_BETWEEN_OP_OR_OC: Is this a transfer between OP or OC?
        - REQUEST_FOR_ECONOMIC_REASON: Is this request for economic reasons?
        - RELEVANT_TO_APPRENTICESHIP: Is this relevant to an apprenticeship?
        - CASE_OF_IMPULSION: Is this a case of impulsion?

    CitizenInformationDetailNullableResponse:
      type: object
      properties:
        birthDate:
          type: string
          format: date
          description: The birth date of the employee (format YYYY-MM-DD)
        nationalityCode:
          type: integer
          format: int32
          description: The nationality of the employee
        address:
          $ref: '#/components/schemas/AddressNullable'
        fieldSources:
          type: array
          description: Sources for individual fields
          items:
            $ref: '#/components/schemas/FieldSource'

    HistoricalCitizenAuthenticSourcesResponse:
      allOf:
        - type: object
          properties:
            firstName:
              type: string
              description: The first name of the person
            lastName:
              type: string
              description: The last name of the person
            birthDate:
              type: string
              format: date
              description: The birth date of the employee (format YYYY-MM-DD)
            nationalityCode:
              type: integer
              format: int32
              description: The nationality of the employee
            address:
              $ref: '#/components/schemas/AddressNullable'
            valueDate:
              type: string
              format: date
              description: The value date for the authentic source

    HistoricalCitizenOnemResponse:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'
        - type: object
          properties:
            firstName:
              type: string
              description: The first name of the person
            lastName:
              type: string
              description: The last name of the person
            birthDate:
              type: string
              format: date
              description: The birth date of the employee (format YYYY-MM-DD)
            nationalityCode:
              type: integer
              format: int32
              description: The nationality of the employee
            address:
              $ref: '#/components/schemas/AddressNullable'
            addressValueDate:
              type: string
              format: date
              description: The value date for the bank account
            bankAccountValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionContributionValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionDue:
              $ref: '#/components/schemas/UnionContributionFields'

    HistoricalCitizenC1Response:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'
        - type: object
          properties:
            firstName:
              type: string
              description: The first name of the person
            lastName:
              type: string
              description: The last name of the person
            birthDate:
              type: string
              format: date
              description: The birth date of the employee (format YYYY-MM-DD)
            nationalityCode:
              type: integer
              format: int32
              description: The nationality of the employee
            address:
              $ref: '#/components/schemas/AddressNullable'
            addressValueDate:
              type: string
              format: date
              description: The value date for the bank account
            bankAccountValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionContributionValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionDue:
              $ref: '#/components/schemas/UnionContributionFields'

    HistoricalBaremaResponse:
      type: object
      properties:
        barema:
          type: string
        article:
          type: string

    AddressNullable:
      type: object
      properties:
        countryCode:
          type: integer
          format: int32
          description: The country of residence
        street:
          type: string
          description: The street name
        houseNumber:
          type: string
          description: The house number
        boxNumber:
          type: string
          description: The box number (optional)
        zipCode:
          type: string
          description: The postal/zip code
        city:
          type: string
          description: The city name
        validFrom:
          type: string
          format: date
          description: The date from which the address is valid (format YYYY-MM-DD)
    SelectFieldSourcesRequest:
      type: object
      required:
        - fieldSources
      properties:
        fieldSources:
          type: array
          description: Sources for individual fields
          items:
            $ref: '#/components/schemas/FieldSource'
          example:
            - fieldName: "birthDate"
              source: "AUTHENTIC_SOURCES"
            - fieldName: "nationality"
              source: "C1"
            - fieldName: "address.street"
              source: "ONEM"

    RoutingDecisionUpdateRequest:
      type: object
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/ManualVerificationType'
        value:
          type: boolean
          nullable: true
          description: The user's answer to the verification question. null indicates the question has not been answered yet.

    RoutingDecisionItemResponse:
      type: object
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/ManualVerificationType'
        value:
          type: boolean
          nullable: true
          description: The current answer to the verification question. null indicates the question has not been answered yet.

    RoutingDecisionResponse:
      type: object
      properties:
        processInWave:
          type: boolean
          nullable: true
          description: Whether the request can be processed in Wave. null indicates this has not been determined yet.
        routingDecisions:
          type: array
          items:
            $ref: '#/components/schemas/RoutingDecisionItemResponse'
          description: List of routing decisions for manual verification
