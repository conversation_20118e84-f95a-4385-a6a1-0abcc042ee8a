import {CommonModule} from "@angular/common";
import {Component, Input, OnChanges, SimpleChanges, ViewEncapsulation} from "@angular/core";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";
import {OnemrvaMatMessageBoxComponent} from "@onemrvapublic/design-system/mat-message-box";
import {RequestBasicInfoResponse} from "@rest-client/cu-bff";
import {FormUtilsService} from "../../services/form-utils.service";

@Component({
    selector: "lib-cu-task-status-message",
    imports: [
        CommonModule,
        OnemrvaThemeModule,
        TranslateModule,
        OnemrvaMatMessageBoxComponent,
    ],
    templateUrl: "./cu-task-status-message.component.html",
    standalone: true,
    styleUrl: "./cu-task-status-message.component.scss",
    encapsulation: ViewEncapsulation.None,
})
export class CuTaskStatusMessageComponent implements OnChanges {

    @Input() status?: string;
    @Input() taskNr?: number;
    @Input() task!: any;
    @Input() decisionType?: string;
    @Input() decisionBarema?: string;
    @Input() nextTaskDescription!: string;
    @Input() nextTaskAction!: string;
    @Input() pushbackStatus!: RequestBasicInfoResponse.PushbackStatusEnum | undefined;

    processLink: string = "";

    ngOnChanges(changes: SimpleChanges) {
        if (changes["task"] && this.task) {
            this.processLink = FormUtilsService.getWaveProcessUrl(this.task);
        }
    }

    isTreatedOnMainFrame(): boolean {
        return (this.status == "CLOSED") && !!this.decisionType && !FormUtilsService.isLogiclyDeleted(this.task);
    }

    isTreatedOnMainFrameForTask(): boolean {
        return this.isTreatedOnMainFrame() && this.taskNr == 2;
    }

    isClosedAndNotTreated(): boolean {
        return FormUtilsService.isClosedOrWaiting(this.status, this.task) &&
            !this.isTreatedOnMainFrame() &&
            !!this.nextTaskDescription &&
            !!this.nextTaskAction &&
            this.pushbackStatus != "NOK" &&
            this.pushbackStatus != "PENDING" &&
            !FormUtilsService.isLogiclyDeleted(this.task);
    }

    isNotClosed() {
        return this.status != "CLOSED";
    }

    showMainFrameMessage() {
        return this.isNotClosed() && !FormUtilsService.isLogiclyDeleted(this.task) && this.taskNr == 2;
    }

}
