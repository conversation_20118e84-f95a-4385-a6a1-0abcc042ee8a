package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainSnapshot
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.BaremaSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotAddress
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotModeOfPayment
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SnapshotUnionContribution
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.BaremaSnapshotRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationSnapshotRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.domain.Snapshot
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.SnapshotPort

@Service
@Transactional
class SnapshotAdapter(
    private val citizenInformationSnapshotRepository: CitizenInformationSnapshotRepository,
    private val requestRepository: RequestRepository,
    private val baremaSnapshotRepository: BaremaSnapshotRepository,
) : SnapshotPort {
    override fun getCitizenInformationSnapshot(
        requestId: UUID,
        source: ExternalSource,
    ): Snapshot<HistoricalCitizenSnapshot> {
        val citizenInformationSnapshotEntity =
            citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
        return citizenInformationSnapshotEntity?.toDomainSnapshot() ?: Snapshot.NotFound
    }

    override fun saveCitizenInformationSnapshot(
        requestId: UUID,
        source: ExternalSource,
        historicalCitizenSnapshot: HistoricalCitizenSnapshot,
    ) {
        val request = requestRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        val citizenInformationSnapshotEntity =
            citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
        if (citizenInformationSnapshotEntity == null) {

            // save
            val newCitizenInformationSnapshotEntity = CitizenInformationSnapshotEntity(
                request = request,
                lastName = historicalCitizenSnapshot.lastName,
                firstName = historicalCitizenSnapshot.firstName,
                nationalityCode = historicalCitizenSnapshot.nationalityCode,
                numbox = historicalCitizenSnapshot.numbox,
                birthDate = historicalCitizenSnapshot.birthDate,
                externalSource = source,
                readonly = false,
                modeOfPayment = SnapshotModeOfPayment(
                    iban = historicalCitizenSnapshot.iban,
                    bic = historicalCitizenSnapshot.bic,
                    foreignAccount = historicalCitizenSnapshot.iban?.startsWith("BE") ?: false,
                    ownBankAccount = historicalCitizenSnapshot.otherPersonName == null,
                    valueDate = historicalCitizenSnapshot.bankAccountValueDate,
                    otherPersonName = historicalCitizenSnapshot.otherPersonName,
                    paymentMode = historicalCitizenSnapshot.paymentMode ?: 1,
                ),
                address = SnapshotAddress(
                    street = historicalCitizenSnapshot.address.street,
                    houseNumber = historicalCitizenSnapshot.address.houseNumber,
                    boxNumber = historicalCitizenSnapshot.address.boxNumber,
                    zipCode = historicalCitizenSnapshot.address.zipCode,
                    city = historicalCitizenSnapshot.address.city,
                    countryCode = historicalCitizenSnapshot.address.countryCode,
                    valueDate = historicalCitizenSnapshot.address.valueDate,
                ),
                unionContribution = SnapshotUnionContribution(
                    historicalCitizenSnapshot.authorized,
                    historicalCitizenSnapshot.effectiveDate
                ),
            )
            citizenInformationSnapshotRepository.save(newCitizenInformationSnapshotEntity)
        } else {
            // update
            citizenInformationSnapshotEntity.apply {
                lastName = historicalCitizenSnapshot.lastName
                firstName = historicalCitizenSnapshot.firstName
                nationalityCode = historicalCitizenSnapshot.nationalityCode
                numbox = historicalCitizenSnapshot.numbox
                modeOfPayment = SnapshotModeOfPayment(
                    iban = historicalCitizenSnapshot.iban,
                    bic = historicalCitizenSnapshot.bic,
                    foreignAccount = historicalCitizenSnapshot.iban?.startsWith("BE") ?: false,
                    ownBankAccount = historicalCitizenSnapshot.otherPersonName == null,
                    valueDate = historicalCitizenSnapshot.bankAccountValueDate,
                    otherPersonName = historicalCitizenSnapshot.otherPersonName,
                    paymentMode = historicalCitizenSnapshot.paymentMode ?: 1,
                )
                address = SnapshotAddress(
                    street = historicalCitizenSnapshot.address.street,
                    houseNumber = historicalCitizenSnapshot.address.houseNumber,
                    boxNumber = historicalCitizenSnapshot.address.boxNumber,
                    zipCode = historicalCitizenSnapshot.address.zipCode,
                    city = historicalCitizenSnapshot.address.city,
                    countryCode = historicalCitizenSnapshot.address.countryCode,
                    valueDate = historicalCitizenSnapshot.address.valueDate,
                )
            }
        }
    }

    override fun makeCitizenInformationSnapshotReadonly(requestId: UUID, source: ExternalSource) {
        val citizenInformationSnapshotEntity =
            citizenInformationSnapshotRepository.findByRequestIdAndExternalSource(requestId, source)
        citizenInformationSnapshotEntity?.readonly = true
    }

    override fun getBaremaSnapshot(requestId: UUID): Snapshot<Barema> {
        val baremaSnapshotEntity =
            baremaSnapshotRepository.findByRequestId(requestId)
        return baremaSnapshotEntity?.toDomainSnapshot() ?: Snapshot.NotFound
    }

    override fun saveBaremaSnapshot(requestId: UUID, barema: Barema?) {
        val request = requestRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        val baremaSnapshotEntity =
            baremaSnapshotRepository.findByRequestId(requestId)
        if (baremaSnapshotEntity == null) {
            // save
            val newBaremaSnapshotEntity = BaremaSnapshotEntity(
                request = request,
                found = barema != null,
                barema = barema?.barema,
                article = barema?.article,
                readonly = false,
            )
            baremaSnapshotRepository.save(newBaremaSnapshotEntity)
        } else {
            // update
            baremaSnapshotEntity.apply {
                this.found = barema != null
                this.barema = barema?.barema
                this.article = barema?.article
            }
        }
    }

    override fun makeBaremaSnapshotReadonly(requestId: UUID) {
        val baremaSnapshotEntity =
            baremaSnapshotRepository.findByRequestId(requestId)
        baremaSnapshotEntity?.readonly = true
    }
}