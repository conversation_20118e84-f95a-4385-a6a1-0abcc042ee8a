package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.RoutingDecision
import be.fgov.onerva.cu.backend.application.domain.RoutingDecisionItem

/**
 * Use case interface for managing routing decisions.
 * Defines operations for retrieving and updating routing decisions for requests.
 */
interface RoutingDecisionUseCase {
    
    /**
     * Retrieves all routing decisions for a specific request.
     * Returns saved routing decisions if they exist, or routing decisions with null values 
     * for all ManualVerificationType entries if no routing decisions have been saved yet.
     * Also returns the processInWave status based on the current request state.
     * 
     * @param requestId The unique identifier of the request
     * @return RoutingDecision containing processInWave status and set of routing decision items
     */
    fun getRoutingDecisions(requestId: UUID): RoutingDecision
    
    /**
     * Updates routing decisions for a specific request.
     * Validates that the request is in OPEN status before allowing updates.
     * 
     * @param requestId The unique identifier of the request
     * @param routingDecisions The routing decision items to save
     * @throws IllegalStateException if request is in CLOSED status
     * @throws IncompleteRoutingDecisionsException if not all ManualVerificationType values are provided
     */
    fun updateRoutingDecisions(requestId: UUID, routingDecisions: Set<RoutingDecisionItem>)
}