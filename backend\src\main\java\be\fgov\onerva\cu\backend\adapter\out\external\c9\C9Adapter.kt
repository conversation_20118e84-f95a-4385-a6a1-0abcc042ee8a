package be.fgov.onerva.cu.backend.adapter.out.external.c9

import java.time.LocalDate
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.adapter.mapper.getEC1
import be.fgov.onerva.cu.backend.adapter.mapper.toDomainCitizenInformation
import be.fgov.onerva.cu.backend.adapter.mapper.toDomainModeOfPayment
import be.fgov.onerva.cu.backend.adapter.mapper.toDomainUnionContribution
import be.fgov.onerva.cu.backend.application.domain.C9Info
import be.fgov.onerva.cu.backend.application.domain.EC1Info
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.port.out.C9Port
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.unemployment.c9.api.C9Api
import be.fgov.onerva.unemployment.c9.api.Ec1Api

@Service
class C9Adapter(val ec1Api: Ec1Api, val c9Api: C9Api) : C9Port {

    @LogMethodCall
    override fun loadEC1(ec1Id: Int, defaultValidFrom: LocalDate): EC1Info {
        val ec1 = ec1Api.getC1s(ec1Id)

        return EC1Info(
            citizenInformation = ec1.identity?.toDomainCitizenInformation(
                ec1.reasonIntroduction.changeMyAddressFromDate ?: defaultValidFrom
            ),
            modeOfPayment = ec1.modeOfPayment?.toDomainModeOfPayment(
                ec1.reasonIntroduction.changeModeOfPaymentOrAccountNrFromDate ?: defaultValidFrom
            ),
            unionContribution = ec1.tradeUnionContribution?.toDomainUnionContribution(),
        )
    }

    @LogMethodCall
    override fun loadC9(c9Id: Long): C9Info {
        val c9 = c9Api.getC9s(c9Id.toInt())
        val eC1 = c9.getEC1()

        return C9Info(
            c9Id = c9.id,
            type = c9.type,
            ssin = c9.ssin,
            requestDate = c9.requestDate,
            opKey = c9.opKey,
            sectOp = c9.sectOp,
//            receptionDate = c9.introductionDate,
            introductionDate = c9.introductionDate,
            dateValid = c9.dateValid,
            paymentInstitution = c9.paymentInstitution,
            entityCode = c9.entityCode,
            unemploymentOffice = c9.unemploymentOffice,
            scanUrl = c9.scanUrl,
            scanNumber = c9.scanNumber,
            ec1Id = eC1?.id,
            ec1DisplayUrl = eC1?.displayUrl,
            documentType = if (eC1 != null) IdentityDocumentType.ELECTRONIC else IdentityDocumentType.PAPER,
            introductionType = c9.introductionType?.let { IntroductionType.valueOf(it) },
            operatorCode = c9.operatorCode,
            dueDate = c9.dueDate,
        )
    }
}