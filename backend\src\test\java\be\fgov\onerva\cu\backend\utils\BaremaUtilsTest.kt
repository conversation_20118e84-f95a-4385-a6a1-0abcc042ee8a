package be.fgov.onerva.cu.backend.utils

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class BaremaUtilsTest {

    @Test
    fun `should format barema correctly when starts with two digits`() {
        assertEquals("12/34", BaremaUtils.formatBarema("1234"))
        assertEquals("56/789", BaremaUtils.formatBarema("56789"))
        assertEquals("12/", BaremaUtils.formatBarema("12"))
    }

    @Test
    fun `should not format barema when not starting with two digits`() {
        assertEquals("AB34", BaremaUtils.formatBarema("AB34"))
        assertEquals("A1234", BaremaUtils.formatBarema("A1234"))
        assertEquals("1A234", BaremaUtils.formatBarema("1A234"))
    }

    @Test
    fun `should handle null and empty with default value`() {
        assertEquals("N/A", BaremaUtils.formatBarema(null))
        assertEquals("N/A", BaremaUtils.formatBarema(""))
    }

    @Test
    fun `should handle null and empty with custom default value`() {
        assertEquals("UNKNOWN", BaremaUtils.formatBarema(null, "UNKNOWN"))
        assertEquals("UNKNOWN", BaremaUtils.formatBarema("", "UNKNOWN"))
        assertEquals("", BaremaUtils.formatBarema(null, ""))
    }

    @Test
    fun `should handle single character input`() {
        assertEquals("1", BaremaUtils.formatBarema("1"))
        assertEquals("A", BaremaUtils.formatBarema("A"))
    }

    @Test
    fun `should handle mixed characters after digits`() {
        assertEquals("12/ABC", BaremaUtils.formatBarema("12ABC"))
        assertEquals("34/565D", BaremaUtils.formatBarema("34565D"))
    }

    @Test
    fun `should not reformat already formatted barema`() {
        assertEquals("12/34", BaremaUtils.formatBarema("12/34"))
        assertEquals("AB/CD", BaremaUtils.formatBarema("AB/CD"))
    }

    @Test
    fun `should handle edge case with slash in wrong position`() {
        assertEquals("12/345/", BaremaUtils.formatBarema("12345/"))
        assertEquals("12/3/45", BaremaUtils.formatBarema("123/45"))
    }
}