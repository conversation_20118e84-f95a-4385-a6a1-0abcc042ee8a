package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.history.Revision
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ModeOfPaymentRepository
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class ModeOfPaymentPersistenceAdapterTest {

    @MockK
    lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @MockK
    lateinit var modeOfPaymentRepository: ModeOfPaymentRepository

    @InjectMockKs
    lateinit var adapter: ModeOfPaymentPersistenceAdapter

    @Nested
    inner class GetModeOfPayment {

        @Test
        fun `should return mode of payment when request exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val changePersonalData = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.PAPER,
                ssin = "12345678901",
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                entityCode = "123456",
                scanUrl = "http://example.com/scan1",
                scanNumber = 12345L,
                operatorCode = 123,
                dateValid = LocalDate.now().plusDays(20),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            val modeOfPaymentEntity = ModeOfPaymentEntity(
                request = changePersonalData,
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.now(),
                updateStatus = UpdateStatus.FROM_C9,
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { modeOfPaymentRepository.findByRequestId(requestId) } returns modeOfPaymentEntity

            // When
            val result = adapter.getModeOfPayment(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result!!.otherPersonName).isEqualTo("John Doe")
            assertThat(result.iban).isEqualTo("****************")
            assertThat(result.bic).isNull()
        }

        @Test
        fun `should throw RequestIdNotFoundException when request does not exist`() {
            // Given
            val requestId = UUID.randomUUID()
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getModeOfPayment(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")
        }

        @Test
        fun `should return null when request exists but no mode of payment found`() {
            // Given
            val requestId = UUID.randomUUID()
            val changePersonalData = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.PAPER,
                ssin = "12345678901",
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                entityCode = "123456",
                scanUrl = "http://example.com/scan1",
                scanNumber = 12345L,
                operatorCode = 123,
                dateValid = LocalDate.now().plusDays(20),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { modeOfPaymentRepository.findByRequestId(requestId) } returns null

            // When
            val result = adapter.getModeOfPayment(requestId)

            // Then
            assertThat(result).isNull()
        }
    }

    @Nested
    inner class PersistModeOfPayment {

        @Test
        fun `should create new mode of payment when none exists`() {
            // Given
            val requestId = UUID.randomUUID()
            val changePersonalData = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.PAPER,
                ssin = "12345678901",
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                entityCode = "123456",
                scanUrl = "http://example.com/scan1",
                scanNumber = 12345L,
                operatorCode = 123,
                dateValid = LocalDate.now().plusDays(20),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            val modeOfPayment = ModeOfPayment(
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.of(2025, 1, 1),
            )

            val entitySlot = slot<ModeOfPaymentEntity>()

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { modeOfPaymentRepository.save(capture(entitySlot)) } answers { entitySlot.captured }

            // When
            adapter.persistModeOfPayment(requestId, modeOfPayment)

            // Then
            verify(exactly = 1) { modeOfPaymentRepository.save(any()) }
            with(entitySlot.captured) {
                assertThat(otherPersonName).isEqualTo("John Doe")
                assertThat(iban).isEqualTo("****************")
                assertThat(bic).isNull()
                assertThat(updateStatus).isEqualTo(UpdateStatus.EDITED)
                assertThat(validFrom).isEqualTo(LocalDate.of(2025, 1, 1))
            }
        }

        @Test
        fun `should update existing mode of payment`() {
            // Given
            val requestId = UUID.randomUUID()
            val changePersonalData = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                opKey = "OP123",
                sectOp = "SO123",
                requestDate = LocalDate.now(),
                documentType = IdentityDocumentType.PAPER,
                ssin = "12345678901",
                introductionDate = LocalDate.now(),
                unemploymentOffice = 123,
                paymentInstitution = 1,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
                entityCode = "123456",
                scanUrl = "http://example.com/scan1",
                scanNumber = 12345L,
                operatorCode = 123,
                dateValid = LocalDate.now().plusDays(20),
                numbox = 12,
                ec1Id = 1234,
                ec1DisplayUrl = "http://example.com/ec1",
            )
            val existingModeOfPayment = ModeOfPaymentEntity(
                request = changePersonalData,
                otherPersonName = "Old Name",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.now(),
                updateStatus = UpdateStatus.FROM_C9,
            )
            changePersonalData.modeOfPayment = existingModeOfPayment

            val newModeOfPayment = ModeOfPayment(
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.of(2025, 1, 1),
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData

            // When
            adapter.persistModeOfPayment(requestId, newModeOfPayment)

            // Then
            assertThat(existingModeOfPayment.otherPersonName).isEqualTo("John Doe")
            assertThat(existingModeOfPayment.iban).isEqualTo("****************")
            assertThat(existingModeOfPayment.bic).isNull()
            assertThat(existingModeOfPayment.updateStatus).isEqualTo(UpdateStatus.EDITED)
            assertThat(existingModeOfPayment.validFrom).isEqualTo(LocalDate.of(2025, 1, 1))
        }

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val modeOfPayment = ModeOfPayment(
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.of(2025, 1, 1),
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.persistModeOfPayment(requestId, modeOfPayment) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")
        }
    }

    @Nested
    inner class GetLatestRevision {

        @Test
        fun `should return correct revision number when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val expectedRevision = 5

            val modeOfPaymentEntity = ModeOfPaymentEntity(
                request = mockk(),
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.now(),
                updateStatus = UpdateStatus.EDITED,
            )

            val revision = mockk<Revision<Int, ModeOfPaymentEntity>>()
            every { revision.revisionNumber } returns Optional.of(expectedRevision)

            every { modeOfPaymentRepository.findByRequestId(requestId) } returns modeOfPaymentEntity
            every { modeOfPaymentRepository.findLastChangeRevision(any()) } returns Optional.of(revision)

            // When
            val result = adapter.getLatestRevision(requestId)

            // Then
            assertThat(result).isEqualTo(expectedRevision)
            verify(exactly = 1) {
                modeOfPaymentRepository.findByRequestId(requestId)
                modeOfPaymentRepository.findLastChangeRevision(any())
            }
        }

        @Test
        fun `should return 0 when no revision found`() {
            // Given
            val requestId = UUID.randomUUID()

            val modeOfPaymentEntity = ModeOfPaymentEntity(
                request = mockk(),
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.now(),
                updateStatus = UpdateStatus.EDITED,
            )

            every { modeOfPaymentRepository.findByRequestId(requestId) } returns modeOfPaymentEntity
            every { modeOfPaymentRepository.findLastChangeRevision(any()) } returns Optional.empty()

            // When
            val result = adapter.getLatestRevision(requestId)

            // Then
            assertThat(result).isZero()
            verify(exactly = 1) {
                modeOfPaymentRepository.findByRequestId(requestId)
                modeOfPaymentRepository.findLastChangeRevision(any())
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when modeOfPayment not found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { modeOfPaymentRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getLatestRevision(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { modeOfPaymentRepository.findByRequestId(requestId) }
            verify(exactly = 0) { modeOfPaymentRepository.findLastChangeRevision(any()) }
        }
    }

    @Nested
    inner class GetModeOfPaymentForRevision {

        @Test
        fun `getModeOfPaymentForRevision should return mode of payment when revision exists`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val entityId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val expectedRevision = 5

            val modeOfPaymentEntity = ModeOfPaymentEntity(
                request = mockk(),
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
                validFrom = LocalDate.now(),
                updateStatus = UpdateStatus.EDITED,
            ).apply { id = entityId }

            every {
                modeOfPaymentRepository.findByRequestId(requestId)
            } returns modeOfPaymentEntity
            val revisionEntity = mockk<Revision<Int, ModeOfPaymentEntity>>()
            every { revisionEntity.entity } returns modeOfPaymentEntity
            every { revisionEntity.revisionNumber } returns Optional.of(expectedRevision)
            every { modeOfPaymentRepository.findRevision(entityId, expectedRevision) } returns Optional.of(
                revisionEntity
            )

            // When
            val result = adapter.getModeOfPaymentForRevision(requestId, expectedRevision)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.iban).isEqualTo("****************")
            assertThat(result?.bic).isNull()
            assertThat(result?.otherPersonName).isEqualTo("John Doe")

            verify(exactly = 1) { modeOfPaymentRepository.findByRequestId(requestId) }
            verify(exactly = 1) { modeOfPaymentRepository.findRevision(entityId, expectedRevision) }
        }

        @Test
        fun `getModeOfPaymentForRevision should throw InvalidRequestIdException when request id not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2

            every { modeOfPaymentRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getModeOfPaymentForRevision(requestId, revision) }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { modeOfPaymentRepository.findByRequestId(requestId) }
            verify(exactly = 0) { modeOfPaymentRepository.findRevision(any(), any()) }
        }

        @Test
        fun `getModeOfPaymentForRevision should throw InvalidRequestIdException when revision not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2
            val modeOfPaymentId = UUID.randomUUID()
            val modeOfPaymentEntity = mockk<ModeOfPaymentEntity>()

            every { modeOfPaymentRepository.findByRequestId(requestId) } returns modeOfPaymentEntity
            every { modeOfPaymentEntity.id } returns modeOfPaymentId
            every { modeOfPaymentRepository.findRevision(modeOfPaymentId, revision) } returns Optional.empty()

            // When/Then
            assertThatThrownBy { adapter.getModeOfPaymentForRevision(requestId, revision) }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Revision not found for request $requestId and revision $revision")

            verify(exactly = 1) { modeOfPaymentRepository.findByRequestId(requestId) }
            verify(exactly = 1) { modeOfPaymentRepository.findRevision(modeOfPaymentId, revision) }
        }
    }

    @Nested
    @DisplayName("Patch Current Data with Revision Tests")
    inner class PatchCurrentDataWithRevision {
        @Test
        fun `patchCurrentDataWithRevision should successfully patch current mode of payment data with revision data`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2
            val entityId = UUID.randomUUID()

            val requestEntity = mockk<ChangePersonalDataRequestEntity>()

            val currentData = ModeOfPaymentEntity(
                otherPersonName = "Old Person Name",
                iban = "BE68 **************",
                bic = "GKCCBEBB",
                validFrom = LocalDate.of(2023, 1, 1),
                request = requestEntity,
                updateStatus = UpdateStatus.EDITED
            ).apply { id = entityId }

            val revisionData = ModeOfPaymentEntity(
                otherPersonName = "New Person Name",
                iban = "BE02 0012 3456 7890",
                bic = "GEBABEBB",
                validFrom = LocalDate.of(2024, 6, 15),
                request = requestEntity,
                updateStatus = UpdateStatus.EDITED
            )

            val revisionEntity = mockk<Revision<Int, ModeOfPaymentEntity>>()
            every { revisionEntity.entity } returns revisionData

            every { modeOfPaymentRepository.findByRequestId(requestId) } returns currentData
            every { modeOfPaymentRepository.findRevision(entityId, revision) } returns Optional.of(revisionEntity)
            every { modeOfPaymentRepository.save(any()) } returns currentData

            // When
            adapter.patchCurrentDataWithRevision(requestId, revision)

            // Then
            verify(exactly = 1) { modeOfPaymentRepository.findByRequestId(requestId) }
            verify(exactly = 1) { modeOfPaymentRepository.findRevision(entityId, revision) }
            verify(exactly = 1) { modeOfPaymentRepository.save(currentData) }

            // Vérifier que les données ont été mises à jour
            assertThat(currentData.otherPersonName).isEqualTo("New Person Name")
            assertThat(currentData.iban).isEqualTo("BE02 0012 3456 7890")
            assertThat(currentData.bic).isEqualTo("GEBABEBB")
            assertThat(currentData.validFrom).isEqualTo(LocalDate.of(2024, 6, 15))
            assertThat(currentData.updateStatus).isEqualTo(UpdateStatus.EDITED)
        }
    }
}
