---
global:
  coverage:
    enabled: true
  routes:
    host: "cu-ci.test.paas.onemrva.priv"
  logging:
    enabled: false
e2e:
  enabled: true

backend:
  image:
    registry: "docker-alpha.onemrva.priv"
  secrets:
    spring_datasource_password: "D3vD3vD3v$"
    spring_security_oauth2_client_registration_keycloak_clientsecret: cu-secret
  springConfiguration:
    client:
      security:
        enabled: false
    spring:
      profiles:
        active: ci
        autoconfigure:
          exclude:
            - org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
      security:
        oauth2:
          resourceserver:
            jwt:
              issuer-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents
          client:
            provider:
              keycloak:
                issuer-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents
      datasource:
        url: ***************************************
        username: sa
      rabbitmq:
        virtual-host: onemrva
        host: rabbitmq
      liquibase:
        contexts: ddl,dml,ddl-ci,dml-ci

    keycloak:
      auth-server-url: https://cu-ci-kc.test.paas.onemrva.priv
      checktoken: true
      redirect: https://cu-ci.test.paas.onemrva.priv/*
      realm: onemrva-agents

    app:
      allowMultipleC9s: true

    woThirdPartyApi:
      url: http://cu-fake-wo-api.cu-ci.svc.cluster.local:8080/thirdParties/v1
    woOrganizationalChartApi:
      url: http://cu-fake-wo-api.cu-ci.svc.cluster.local:8080/rest/organizationalChart/nsso/v2/services
    werkomgeving:
      enabled: true
      mock: true
      woFacadeApi:
        url: http://woconfigurator.cu-ci.svc.cluster.local:80/api
      gateway:
        url: http://cu-fake-wo-api.cu-ci.svc.cluster.local:8080/REST/nssoWorkEnvironmentGateway/v1

    rabbitmq:
      enabled: false
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: http://cu-microcks.cu-ci.svc.cluster.local/rest/Person+API/1.0.0
    barema:
      url: http://cu-wiremock.cu-ci.svc.cluster.local/rest/Bareme+REST+API/1.0.0
    c9Api:
      url: "http://cu-microcks.cu-ci.svc.cluster.local/rest/C9+REST+API/1.0.0"
    registry:
      url: http://cu-microcks.cu-ci.svc.cluster.local/rest/Register+Proxy+Service+public+API/1.0.0
    flagsmith:
      api:
        key: Ltu7TyR4Gf8LrGpvDig4sy
        url: https://flagsmith.prod.paas.onemrva.priv/api/v1/

  extraEnv:
    - name: "SPRING_DATASOURCE_PASSWORD"
      valueFrom:
        secretKeyRef:
          key: "spring_datasource_password"
          name: "cu-backend"
bff:
  image:
    registry: "docker-alpha.onemrva.priv"
  springConfiguration:
    client:
      security:
        enabled: false
    spring:
      profiles:
        active: ci
      security:
        oauth2:
          resourceserver:
            jwt:
              issuer-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents
          client:
            provider:
              keycloak:
                issuer-uri: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents
      rabbitmq:
        host: rabbitmq
    rabbitmq:
      oauth:
        enabled: false
    keycloak:
      auth-server-url: https://cu-ci-kc.test.paas.onemrva.priv
      checktoken: true
      redirect: https://cu-ci.test.paas.onemrva.priv/*
      realm: onemrva-agents
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: http://cu-microcks.cu-ci.svc.cluster.local/rest/Person+API/1.0.0
    backend:
      base-url: https://cu-ci.test.paas.onemrva.priv
    woUserFacadeApi:
      url: http://cu-microcks.cu-ci.svc.cluster.local/rest/WO+facade+API/v1
    c51:
      url: https://proc51.test.paas.onemrva.priv/proc51/home.jsf
    c9Api:
      url: "http://cu-microcks.cu-ci.svc.cluster.local/rest/C9+REST+API/1.0.0"
    regis:
      url: https://regis.test.paas.onemrva.priv/regis/regis/rew.seam
#frontend:
#  image:
#    registry: "docker-alpha.onemrva.priv"
cu:
  image:
    registry: "docker-alpha.onemrva.priv"
  route:
    path: "/elements"
  readinessProbe:
    httpGet:
      path: "/elements/elements.js"
  livenessProbe:
    httpGet:
      path: "/elements/elements.js"
infra:
  enabled: true
  rabbitmq:
    enabled: true
  keycloak:
    route:
      enabled: true
      path: /
      host: "cu-ci-kc.test.paas.onemrva.priv"
      tls:
        enabled: true
        insecureEdgeTerminationPolicy: "Allow"
    enabled: true
  mssql:
    enabled: true
kcconfig:
  enabled: true
  keycloak:
    url: "https://cu-ci-kc.test.paas.onemrva.priv"
  realm:
    name: "onemrva-agents"
    clients:
      cu-frontend:
        public: true
        redirectUris:
          - https://cu-ci.test.paas.onemrva.priv/
          - https://cu-ci.test.paas.onemrva.priv/*
          - http://localhost:4300/*
          - http://localhost:4300
        directAccessGrantsEnabled: true
    users:
      cu_user:
        enabled: true
      cu_admin:
        enabled: true
karate:
  enabled: false

woconfig:
  wo_backend:
    enabled: true
    fakeWoUrl: "http://cu-fake-wo-api.cu-ci.svc.cluster.local:8080"
  configClient:
    baseUrl: "http://woconfigurator"
    oauth:
      enabled: false
  wo-fake:
    enabled: true
    wo_api:
      enabled: true
security:
  redirect: https://cu-ci.test.paas.onemrva.priv/
  authserverurl: https://cu-ci-kc.test.paas.onemrva.priv
  backendBasePath: https://cu-ci.test.paas.onemrva.priv/api
  jwt:
    issuer: https://cu-ci-kc.test.paas.onemrva.priv/realms/onemrva-agents
    check: no-check

microcks:
  enabled: true
  global:
    microcksUrl: http://cu-microcks.cu-ci.svc.cluster.local

wiremock:
  enabled: true

