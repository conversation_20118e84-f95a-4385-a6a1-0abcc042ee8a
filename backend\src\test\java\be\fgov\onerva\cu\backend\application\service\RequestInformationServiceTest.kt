package be.fgov.onerva.cu.backend.application.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RequestInformationServiceTest {

    @MockK
    lateinit var requestInformationPort: RequestInformationPort

    @InjectMockKs
    lateinit var service: RequestInformationService

    @Test
    fun `Given getRequestInformation should return RequestInformation`() {
        // Given
        val requestId = UUID.randomUUID()
        val expectedRequestInformation = RequestInformation(
            requestDate = LocalDate.now()
        )

        every { requestInformationPort.getRequestInformation(requestId) } returns expectedRequestInformation

        // When
        val result = service.getRequestInformation(requestId)

        // Then
        assertThat(result).isNotNull
        assertThat(result).isEqualTo(expectedRequestInformation)
        verify(exactly = 1) { requestInformationPort.getRequestInformation(requestId) }
    }

    @Nested
    inner class UpdateRequestInformationTests {
        @Test
        fun `Given existing request information should update it`() {
            // Given
            val requestId = UUID.randomUUID()
            val existingRequestInformation = RequestInformation(
                requestDate = LocalDate.now().minusDays(1)
            )
            val newRequestInformation = RequestInformation(
                requestDate = LocalDate.now()
            )

            every { requestInformationPort.getRequestInformation(requestId) } returns existingRequestInformation
            every { requestInformationPort.updateRequestInformation(requestId, newRequestInformation) } returns Unit

            // When
            service.updateRequestInformation(requestId, newRequestInformation)

            // Then
            verify(exactly = 1) { requestInformationPort.getRequestInformation(requestId) }
            verify(exactly = 1) { requestInformationPort.updateRequestInformation(requestId, newRequestInformation) }
            verify(exactly = 0) { requestInformationPort.persistRequestInformation(any(), any()) }
        }

        @Test
        fun `Given no existing request information should create new one`() {
            // Given
            val requestId = UUID.randomUUID()
            val newRequestInformation = RequestInformation(
                requestDate = LocalDate.now()
            )

            every { requestInformationPort.getRequestInformation(requestId) } throws RuntimeException("Not found")
            every { requestInformationPort.persistRequestInformation(requestId, newRequestInformation) } returns Unit

            // When
            service.updateRequestInformation(requestId, newRequestInformation)

            // Then
            verify(exactly = 1) { requestInformationPort.getRequestInformation(requestId) }
            verify(exactly = 0) { requestInformationPort.updateRequestInformation(any(), any()) }
            verify(exactly = 1) { requestInformationPort.persistRequestInformation(requestId, newRequestInformation) }
        }
    }
}