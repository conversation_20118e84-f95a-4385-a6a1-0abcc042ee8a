package be.fgov.onerva.cu.bff.service

import java.util.UUID
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.context.request.RequestAttributes
import org.springframework.web.context.request.RequestContextHolder
import be.fgov.onerva.cu.bff.adapter.out.CitizenInfoService
import be.fgov.onerva.cu.bff.adapter.out.CuBackendService
import be.fgov.onerva.cu.bff.adapter.out.RestTemplateUtil
import be.fgov.onerva.cu.bff.exceptions.CitizenNotFoundException
import be.fgov.onerva.cu.bff.exceptions.MissingAuthException
import be.fgov.onerva.cu.bff.mapper.toCitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.mapper.toServerRoutingDecisionItemResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.AggregatedChangePersonalDataCaptureResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.AggregatedChangePersonalDataValidateResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateAggregatedChangePersonalDataRequest
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.NissUtils.getBirthDateFromSsin
import be.fgov.onerva.cu.common.utils.logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext

private fun ModeOfPaymentDetailResponse.isPopulated() = iban != null

private fun CitizenInformationDetailNullableResponse.isPopulated() = address != null || nationalityCode != null

private fun UnionContributionDetailResponse.isPopulated() = authorized != null || effectiveDate != null

@Service
class ChangePersonalDataAggregationService(
    private val citizenInfoService: CitizenInfoService,
    private val cuBackendService: CuBackendService,
    private val restTemplateUtil: RestTemplateUtil,
    @Value("\${backend.base-url:http://localhost:9091}") private val backendBaseUrl: String,
) {
    private val log = logger

    @LogMethodCall
    suspend fun getAggregatedRequestDataCapture(requestId: UUID): AggregatedChangePersonalDataCaptureResponse {
        // Capture the authorization header before switching context
        val authHeader = restTemplateUtil.captureAuthorizationHeader()
            ?: throw MissingAuthException(MISSING_AUTH_HEADER)

        return withContext(Dispatchers.IO) {
            coroutineScope {
                val requestAttributes = RequestContextHolder.getRequestAttributes()
                if (requestAttributes != null) {
                    requestAttributes.setAttribute("Authorization", authHeader, RequestAttributes.SCOPE_REQUEST)
                    RequestContextHolder.setRequestAttributes(requestAttributes, true)
                }

                log.info("DataCapture - Getting aggregated request data for request {}", requestId)
                val basicInfoDeferred = async { cuBackendService.getBasicInfo(requestId, authHeader) }
                val citizenInfoDeferred = async { cuBackendService.getCitizenInfo(requestId, authHeader) }
                val paymentDeferred = async { cuBackendService.getPaymentInfo(requestId, authHeader) }
                val unionDeferred = async { cuBackendService.getUnionInfo(requestId, authHeader) }
                val routingDecisionDeferred = async { cuBackendService.getRoutingDecision(requestId, authHeader) }

                val basicInfo = basicInfoDeferred.await()
                var modeOfPayment = paymentDeferred.await()
                var unionContribution = unionDeferred.await()
                val informationDetailResponse = citizenInfoDeferred.await()
                val routingDecision = routingDecisionDeferred.await()

                var citizenInformation: CitizenInformationDetailNullableResponse =
                    informationDetailResponse.toCitizenInformationDetailNullableResponse()

                // If the request is a paper request, and any of the citizen information, payment, or union contribution is not populated,
                // fetch the data from the citizen service for the prefill
                if (basicInfo.documentType == RequestBasicInfoResponse.DocumentType.PAPER &&
                    (!citizenInformation.isPopulated() || !modeOfPayment.isPopulated() || !unionContribution.isPopulated())
                ) {
                    val citizenInfoFromCitizenService =
                        citizenInfoService.getCitizenInfo(basicInfo.ssin, authHeader)
                            ?: throw CitizenNotFoundException("Citizen info not found for request $requestId")
                    if (!citizenInformation.isPopulated()) {
                        citizenInformation = citizenInfoFromCitizenService.toCitizenInformationDetailNullableResponse()
                    }
                    if (!modeOfPayment.isPopulated()) {
                        val paymentMode = citizenInfoFromCitizenService.paymentMode ?: 1
                        modeOfPayment = ModeOfPaymentDetailResponse(
                            iban = citizenInfoFromCitizenService.iban,
                            bic = citizenInfoFromCitizenService.bic,
                            otherPersonName = when (paymentMode) {
                                1 -> null
                                2 -> citizenInfoFromCitizenService.otherPersonName
                                else -> null
                            },
                        )
                    }
                    if (!unionContribution.isPopulated()) {
                        unionContribution = UnionContributionDetailResponse(
                            authorized = if (citizenInfoFromCitizenService.unionDue?.validFrom != null) citizenInfoFromCitizenService.unionDue.mandateActive else null,
                            effectiveDate = citizenInfoFromCitizenService.unionDue?.validFrom
                        )
                    }
                }

                // If the birthdate is not set in the citizen information, use the ssin to calculate it
                if (citizenInformation.birthDate == null && basicInfo.ssin != null) {
                    citizenInformation = citizenInformation.copy(
                        birthDate = getBirthDateFromSsin(basicInfo.ssin)
                    )
                }

                AggregatedChangePersonalDataCaptureResponse(
                    requestId = requestId,
                    basicInfo = basicInfo,
                    citizenInformation = citizenInformation,
                    modeOfPayment = modeOfPayment,
                    unionContribution = unionContribution,
                    processInWave = routingDecision.processInWave,
                    routingDecisions = routingDecision.routingDecisions?.mapNotNull { it.toServerRoutingDecisionItemResponse() },
                )
            }
        }
    }

    @LogMethodCall
    suspend fun getAggregatedRequestDataValidate(requestId: UUID): AggregatedChangePersonalDataValidateResponse {
        // Capture the authorization header before switching context
        val authHeader = restTemplateUtil.captureAuthorizationHeader()
            ?: throw MissingAuthException(MISSING_AUTH_HEADER)

        return withContext(Dispatchers.IO) {
            coroutineScope {
                log.info("DataValidate - Getting aggregated request data for request {}", requestId)

                val basicInfoDeferred = async { cuBackendService.getBasicInfo(requestId, authHeader) }
                val citizenInfoDeferred = async { cuBackendService.getCitizenInfo(requestId, authHeader) }
                val paymentDeferred = async { cuBackendService.getPaymentInfo(requestId, authHeader) }
                val unionDeferred = async { cuBackendService.getUnionInfo(requestId, authHeader) }
                val onemCitizenInformationDeferred =
                    async { cuBackendService.getCitizenInfoFromOnem(requestId, authHeader) }
                val c1CitizenInformationDeferred =
                    async { cuBackendService.getCitizenInfoFromC1(requestId, authHeader) }
                val authenticCitizenInformationDeferred =
                    async { cuBackendService.getCitizenInfoAuthentic(requestId, authHeader) }
                val baremaDeferred = async { cuBackendService.getHistoricalBarema(requestId, authHeader) }

                val basicInfo = basicInfoDeferred.await()
                val modeOfPayment = paymentDeferred.await()
                val informationDetailResponse = citizenInfoDeferred.await()
                val unionContribution = unionDeferred.await()
                val onemCitizenInformation = onemCitizenInformationDeferred.await()
                val c1CitizenInformation = c1CitizenInformationDeferred.await()
                val authenticCitizenInformation = authenticCitizenInformationDeferred.await()
                val barema = baremaDeferred.await()

                val citizenInformation: CitizenInformationDetailNullableResponse =
                    informationDetailResponse.toCitizenInformationDetailNullableResponse()

                AggregatedChangePersonalDataValidateResponse(
                    requestId = requestId,
                    basicInfo = basicInfo,
                    citizenInformation = citizenInformation,
                    modeOfPayment = modeOfPayment,
                    unionContribution = unionContribution,
                    onemCitizenInformation = onemCitizenInformation,
                    c1CitizenInformation = c1CitizenInformation,
                    authenticCitizenInformation = authenticCitizenInformation,
                    barema = barema
                )
            }
        }
    }

    @LogMethodCall
    suspend fun updateAggregateRequest(
        requestId: UUID,
        @SensitiveParam updateAggregatedChangePersonalDataRequest: UpdateAggregatedChangePersonalDataRequest,
    ) {
        // Capture the authorization header before switching context
        val authHeader = restTemplateUtil.captureAuthorizationHeader()
            ?: throw MissingAuthException(MISSING_AUTH_HEADER)

        withContext(Dispatchers.IO) {
            coroutineScope {
                // Handle each update concurrently using async
                val updateBasicInfo = async {
                    cuBackendService.updateBasicInfo(
                        requestId,
                        updateAggregatedChangePersonalDataRequest.basicInfo,
                        authHeader
                    )
                }

                val citizenInfoUpdate = async {
                    cuBackendService.updateCitizenInformation(
                        requestId,
                        updateAggregatedChangePersonalDataRequest.citizenInformation,
                        authHeader
                    )
                }

                val paymentUpdate = async {
                    cuBackendService.updateModeOfPayment(
                        requestId,
                        updateAggregatedChangePersonalDataRequest.modeOfPayment,
                        authHeader
                    )
                }

                val unionUpdate = async {
                    cuBackendService.updateUnionContribution(
                        requestId,
                        updateAggregatedChangePersonalDataRequest.unionContribution,
                        authHeader
                    )
                }

                val assignTask = async {
                    cuBackendService.assignTaskToUser(requestId, authHeader)
                }

                // Wait for all updates to complete
                updateBasicInfo.await()
                citizenInfoUpdate.await()
                paymentUpdate.await()
                unionUpdate.await()
                assignTask.await()
            }
        }
    }

    companion object {
        private const val MISSING_AUTH_HEADER = "Authorization header is missing"
    }
}
