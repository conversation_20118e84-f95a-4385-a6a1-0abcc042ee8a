import {
  MAT_FORM_FIELD_DEFAULT_OPTIONS
} from "./chunk-E7DHZY64.js";
import "./chunk-M5EMTDXH.js";
import "./chunk-KW5IDIQI.js";
import "./chunk-VK67YYVV.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-IY4PCAU4.js";
import "./chunk-6ONIGQ3T.js";
import "./chunk-VOL5AYIH.js";
import "./chunk-NFXQRNMD.js";
import "./chunk-3F3XMFWP.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system-theme/fesm2022/onemrvapublic-design-system-theme.mjs
var OnemrvaThemeModule = class _OnemrvaThemeModule {
  static {
    this.ɵfac = function OnemrvaThemeModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaThemeModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaThemeModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [{
        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
        useValue: {
          appearance: "outline",
          floatLabel: "always"
        }
      }]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaThemeModule, [{
    type: NgModule,
    args: [{
      providers: [{
        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
        useValue: {
          appearance: "outline",
          floatLabel: "always"
        }
      }],
      imports: []
    }]
  }], null, null);
})();
export {
  OnemrvaThemeModule
};
//# sourceMappingURL=@onemrvapublic_design-system-theme.js.map
