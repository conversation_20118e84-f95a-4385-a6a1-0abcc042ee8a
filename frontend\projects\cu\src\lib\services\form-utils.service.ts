import {Injectable} from "@angular/core";
import {FormControl} from "@angular/forms";
import {WaveTask} from "../model/Task.model";

@Injectable({
    providedIn: "root",
})
export class FormUtilsService {

    constructor() {
    }

    static checkLengthOfInput(ctrl: FormControl) {
        const maxLengthError = ctrl.errors?.["maxlength"];
        if (!maxLengthError) {
            return null;
        }
        return {
            requiredLength: maxLengthError.requiredLength,
            actualLength: maxLengthError.actualLength,
        };
    }

    static isClosedOrWaiting(status: string | undefined, task: WaveTask | undefined): boolean {
        return status === "CLOSED" || task?.state?.code === "Wait";
    }

    static isLogiclyDeleted(task: WaveTask | undefined): boolean { // todo check if it isn't in another place once the logic delete works. Could be by example under parentProcess
        return task?.technicalInformation?.technicalStatus === "DELETED";
    }

    static getWaveProcessUrl(task: WaveTask | undefined): string {
        const processId = task?.parentProcess?.processId;
        if (!processId) {
            return "";
        }
        return `/processes-page/process/(process-detail/${processId}!!sidemenu:process-detail/${processId})`;
    }

}
