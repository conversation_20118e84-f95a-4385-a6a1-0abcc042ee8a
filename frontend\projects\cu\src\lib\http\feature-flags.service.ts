import {Injectable} from "@angular/core";
import flagsmith from "flagsmith";
import {EMPTY, switchMap, tap} from "rxjs";
import {fromPromise} from "rxjs/internal/observable/innerFrom";
import {catchError} from "rxjs/operators";
import {ConfigService as CuConfigService, Configuration} from "@rest-client/cu-config";
import {ConfigService} from "../config/config.service";
import {HttpClient, HttpHeaders} from "@angular/common/http";

@Injectable({
    providedIn: "root",
})
export class FeatureFlagsService {

    public static readonly REOPEN_TASK = "reopen-task-enabled";

    private cuConfigService!: CuConfigService

    constructor(
        readonly http: HttpClient,
        readonly configService: ConfigService,
    ) {
        this.initializeServices();
        if (flagsmith && !flagsmith.initialised) {
            console.log("FeatureFlagsService - Initializing Flagsmith.");
            this.cuConfigService.getFlagsmithConfig().pipe(
                tap((config) => console.log("FeatureFlagsService - Received Flagsmith config: ", config)),
                switchMap((config) => {
                    return fromPromise(flagsmith.init({
                        environmentID: config.environmentID,
                        identity: config.identity,
                        api: config.api,
                        cacheFlags: false,

                        onChange: (_oldFlags, _params) => {
                            console.log("FeatureFlagsService - New flags for flagsmith: ", _oldFlags, _params);
                            // Occurs whenever flags are changed
                        },
                    }));
                }),
                tap(() => console.log("Flagsmith initialized: ")),
                catchError((error) => {
                    console.log("Error while initializing Flagsmith: ", error);
                    return EMPTY;
                }),
            )
                .subscribe();
        }
    }

    initializeServices(token?: string) {
        const configBff = new Configuration({
            basePath: this.configService.getEnvironmentVariable("apiBaseUrl", true)+"/api",
            credentials: token ? {"Bearer": token} : undefined,
        });

        const defaultHeaders = token ?
            new HttpHeaders()
                .set("Authorization", `Bearer ${token}`)
                .set("Content-Type", "application/json") :
            new HttpHeaders().set("Content-Type", "application/json");

        this.cuConfigService = new CuConfigService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );

        if (token) {
            this.cuConfigService.defaultHeaders = defaultHeaders;
        }
    }

    isFeatureEnabled(featureName: string): boolean {
        let isEnabled = false;
        if (flagsmith?.initialised) {
            isEnabled = flagsmith.hasFeature(featureName);
            console.log(`Feature flag ${featureName} is enabled: ${isEnabled}`);
        } else {
            console.log(`Flagsmith is not initialized, and the feature flag ${featureName} will be considered not enabled.`);
        }
        return isEnabled;
    }

}