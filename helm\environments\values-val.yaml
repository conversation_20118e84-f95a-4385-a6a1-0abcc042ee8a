---
global:
  routes:
    host: "cu.val.paas.onemrva.priv"
backend:
  replicaCount: 2
  extraEnv:
    - name: LOGGING_LEVEL_BE_FGOV_ONERVA_CU
      value: DEBUG
  secrets:
    spring_security_oauth2_client_registration_keycloak_clientsecret: "==INJECTED VIA BAMBOOSPEC SECRETS=="
  image:
    registry: "docker-beta.onemrva.priv"
  springConfiguration:
    client:
      security:
        enabled: true
    spring:
      rabbitmq:
        host: rabbitmq.rabbitmq.svc.cluster.local
      profiles:
        active: val
      security:
        oauth2:
          client:
            registration:
              keycloak:
                client-secret: "==INJECTED VIA BAMBOOSPEC SECRETS=="
          resourceserver:
            jwt:
              issuer-uri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents
      datasource:
        url: ********************************************************************************************************************************************
        username: cu_user
        password: "==INJECTED VIA BAMBOOSPEC SECRETS=="
      liquibase:
        contexts: ddl,dml,ddl-val,dml-val
    keycloak:
      auth-server-url: https://keycloak.val.paas.onemrva.priv
      checktoken: true
      redirect: https://cu.val.paas.onemrva.priv/*
      realm: onemrva-agents
    rabbitmq:
      oauth:
        enabled: true

    app:
      allowMultipleC9s: false

    woThirdPartyApi:
      url: https://wo-thirdparty-api.val.paas.onemrva.priv/thirdParties/v1
    woOrganizationalChartApi:
      url: https://wo-organizational-chart-api.val.paas.onemrva.priv/rest/organizationalChart/nsso/v2/services
    werkomgeving:
      enabled: true
      mock: false
      woFacadeApi:
        url: https://wo-configurator.val.paas.onemrva.priv/api
      gateway:
        url: https://wo-gateway.acc.neocloud.be/REST/nssoWorkEnvironmentGateway/v1
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: https://person.val.paas.onemrva.priv/api
    barema:
      url: https://bareme.val.paas.onemrva.priv/api
    c9Api:
      url: "https://c9.val.paas.onemrva.priv/api"
    registry:
      url: https://registerproxyservice.val.paas.onemrva.priv/registerproxyservice/rest
    flagsmith:
      api:
        key: HdB6swDsSJvEHJGG5jSf6V
        url: https://flagsmith.prod.paas.onemrva.priv/api/v1/
  proxy:
    host: "proxy-intra-val.onemrva.priv"
    exclusions: [ "*.neocloud.be","neocloud.be" ]
bff:
  replicaCount: 2
  extraEnv:
    - name: LOGGING_LEVEL_BE_FGOV_ONERVA_CU
      value: DEBUG
  secrets:
    spring_security_oauth2_client_registration_keycloak_clientsecret: "==INJECTED VIA BAMBOOSPEC SECRETS=="
  image:
    registry: "docker-beta.onemrva.priv"
  springConfiguration:
    client:
      security:
        enabled: true
    spring:
      security:
        oauth2:
          client:
            registration:
              keycloak:
                client-secret: "==INJECTED VIA BAMBOOSPEC SECRETS=="
            provider:
              keycloak:
                issuer-uri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents
                token-uri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token
          resourceserver:
            jwt:
              issuer-uri: https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents
    keycloak:
      auth-server-url: https://keycloak.val.paas.onemrva.priv
      checktoken: true
      redirect: https://cu.val.paas.onemrva.priv/*
      realm: onemrva-agents
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: https://person.val.paas.onemrva.priv/api
    backend:
      base-url: https://cu.val.paas.onemrva.priv
    woUserFacadeApi:
      url: https://wo-configurator.val.paas.onemrva.priv/api
    c51:
      url: https://proc51.val.paas.onemrva.priv/proc51/home.jsf
    regis:
      url: https://regis.val.paas.onemrva.priv/regis/regis/rew.seam
    c9Api:
      url: "https://c9.val.paas.onemrva.priv/api"
cu:
  image:
    registry: "docker-beta.onemrva.priv"
  route:
    path: "/elements"
  readinessProbe:
    httpGet:
      path: "/elements/elements.js"
  livenessProbe:
    httpGet:
      path: "/elements/elements.js"
kcconfig:
  keycloak:
    url: "https://keycloak.val.paas.onemrva.priv"
  realm:
    clients:
      cu-frontend:
        redirectUris:
          - "https://cu.val.paas.onemrva.priv/*"
    users:
      cu_admin:
        enabled: true
      cu_user:
        enabled: true

woconfig:
  configClient:
    baseUrl: https://wo-configurator.val.paas.onemrva.priv
    oauth:
      issuerUrl: "https://keycloak.val.paas.onemrva.priv/realms/onemrva-agents"
