package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.ReopenTaskException
import be.fgov.onerva.cu.backend.application.port.out.TaskApiPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.application.service.helpers.WaveTaskHelper
import be.fgov.onerva.cu.backend.application.service.reopentask.ReopenTaskAbstractService
import be.fgov.onerva.cu.backend.application.service.reopentask.ReopenTaskDataCaptureService
import be.fgov.onerva.cu.backend.application.service.reopentask.ReopenTaskDataValidationService
import be.fgov.onerva.cu.backend.application.service.reopentask.ReopenTaskProcessorService
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify

class ReopenTaskServiceTest {

    private val waveTaskPort: WaveTaskPort = mockk()
    private val waveTaskPersistencePort: WaveTaskPersistencePort = mockk()
    private val taskApiPort: TaskApiPort = mockk()
    private val waveTaskHelper: WaveTaskHelper = mockk()
    private val reopenTaskDataValidationService: ReopenTaskDataValidationService = mockk()

    private lateinit var dataCaptureService: ReopenTaskDataCaptureService
    private lateinit var dataValidationService: ReopenTaskDataValidationService
    private lateinit var processorService: ReopenTaskProcessorService

    private val testRequestId = UUID.randomUUID()
    private val testTaskId = "00000001"
    private val testProcessId = "test-process-id"
    private val testWaveTask = WaveTask(
        processId = testProcessId,
        taskId = testTaskId,
        status = WaveTaskStatus.OPEN
    )
    private val testRevisionNumbers = WaveTaskRevisionNumbers(
        citizenInformationRevisionNumber = 1,
        modeOfPaymentRevisionNumber = 2,
        unionContributionRevisionNumber = 3,
        requestInformationRevisionNumber = 4
    )

    @BeforeEach
    fun setUp() {
        clearAllMocks()

        dataCaptureService = ReopenTaskDataCaptureService(
            waveTaskPort = waveTaskPort,
            waveTaskPersistencePort = waveTaskPersistencePort,
            taskApiPort = taskApiPort,
            reopenTaskDataValidationService = reopenTaskDataValidationService,
            waveTaskHelper = waveTaskHelper
        )

        dataValidationService = ReopenTaskDataValidationService(
            waveTaskPort = waveTaskPort,
            waveTaskPersistencePort = waveTaskPersistencePort,
            taskApiPort = taskApiPort
        )

        processorService = ReopenTaskProcessorService(
            reopenTaskDataCaptureService = dataCaptureService,
            reopenTaskDataValidationService = dataValidationService
        )
    }

    @Nested
    @DisplayName("ReopenTaskDataCaptureService Tests")
    inner class DataCaptureServiceTests {

        @Test
        @DisplayName("Should successfully reopen data capture task")
        fun `reopenTask should execute all steps successfully`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } returns testWaveTask
            every { waveTaskPersistencePort.getCitizenInformationRevision(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } returns testRevisionNumbers
            every { waveTaskHelper.patchCurrentDataWithRevision(testRequestId, testRevisionNumbers) } just Runs
            every { reopenTaskDataValidationService.deleteTask(testRequestId) } just Runs
            every { waveTaskPersistencePort.openTask(testWaveTask) } just Runs
            every { waveTaskPort.awakeTask(testTaskId, "Reopen task") } just Runs

            // When
            dataCaptureService.reopenTask(testRequestId)

            // Then
            verify { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
            verify { waveTaskPersistencePort.getCitizenInformationRevision(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
            verify { waveTaskHelper.patchCurrentDataWithRevision(testRequestId, testRevisionNumbers) }
            verify { reopenTaskDataValidationService.deleteTask(testRequestId) }
            verify { waveTaskPersistencePort.openTask(testWaveTask) }
            verify { waveTaskPort.awakeTask(testTaskId, "Reopen task") }
        }

        @Test
        @DisplayName("Should successfully delete data capture task")
        fun `deleteTask should execute all steps successfully`() {
            // Given
            every { waveTaskPersistencePort.getOpenWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } returns testWaveTask
            every { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } just Runs
            every { waveTaskPersistencePort.softDeleteTaskById(testTaskId) } just Runs
            every { taskApiPort.softDeleteTask(testTaskId.toLong(), any()) } returns null

            // When
            dataCaptureService.deleteTask(testRequestId)

            // Then
            verify { waveTaskPersistencePort.getOpenWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
            verify { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
            verify { waveTaskPersistencePort.softDeleteTaskById(testTaskId) }
        }

        @Test
        @DisplayName("Should call validation service when deleting next task")
        fun `deleteNextTask should call validation service deleteTask`() {
            // This method is protected and tested via reopenTask()
            // The behavior is verified in the reopenTask test
        }

        @Test
        @DisplayName("Should throw exception when task not found during reopen")
        fun `reopenTask should throw exception when task not found`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } throws RuntimeException("Task not found")

            // When & Then
            val exception = assertThrows<RuntimeException> {
                dataCaptureService.reopenTask(testRequestId)
            }
        }

        @Test
        @DisplayName("Should throw exception when task not found during delete")
        fun `deleteTask should throw exception when task not found`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } throws RuntimeException("Task not found")

            // When & Then
            val exception = assertThrows<RuntimeException> {
                dataCaptureService.deleteTask(testRequestId)
            }
        }
    }

    @Nested
    @DisplayName("ReopenTaskDataValidationService Tests")
    inner class DataValidationServiceTests {

        @Test
        @DisplayName("Should successfully reopen data validation task")
        fun `reopenTask should execute all steps successfully`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) } returns testWaveTask
            every { waveTaskPersistencePort.openTask(testWaveTask) } just Runs
            every { waveTaskPort.awakeTask(testTaskId, "Reopen task") } just Runs

            // When
            dataValidationService.reopenTask(testRequestId)

            // Then
            verify { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) }
            verify { waveTaskPersistencePort.openTask(testWaveTask) }
            verify { waveTaskPort.awakeTask(testTaskId, "Reopen task") }
        }

        @Test
        @DisplayName("Should successfully delete data validation task")
        fun `deleteTask should execute all steps successfully`() {
            // Given
            every { waveTaskPersistencePort.getOpenWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) } returns testWaveTask
            every { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) } just Runs
            every { waveTaskPersistencePort.softDeleteTaskById(testTaskId) } just Runs
            every { taskApiPort.softDeleteTask(testTaskId.toLong(), any()) } returns null

            // When
            dataValidationService.deleteTask(testRequestId)

            // Then
            verify { waveTaskPersistencePort.getOpenWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) }
            verify { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) }
            verify { waveTaskPersistencePort.softDeleteTaskById(testTaskId) }
        }

        @Test
        @DisplayName("Should throw exception when task not found during reopen")
        fun `reopenTask should throw exception when task not found`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) } throws RuntimeException("Task not found")

            // When & Then
            val exception = assertThrows<RuntimeException> {
                dataValidationService.reopenTask(testRequestId)
            }
        }

        @Test
        @DisplayName("Should throw exception when task not found during delete")
        fun `deleteTask should throw exception when task not found`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) } throws RuntimeException("Task not found")

            // When & Then
            val exception = assertThrows<RuntimeException> {
                dataValidationService.deleteTask(testRequestId)
            }
        }
    }

    @Nested
    @DisplayName("ReopenTaskProcessorService Tests")
    inner class ProcessorServiceTests {

        @Test
        @DisplayName("Should successfully reopen data capture task via processor")
        fun `reopenTask should call data capture service for CHANGE_PERSONAL_DATA_CAPTURE task type`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } returns testWaveTask
            every { waveTaskPersistencePort.getCitizenInformationRevision(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } returns testRevisionNumbers
            every { waveTaskHelper.patchCurrentDataWithRevision(testRequestId, testRevisionNumbers) } just Runs
            every { reopenTaskDataValidationService.deleteTask(testRequestId) } just Runs
            every { waveTaskPersistencePort.openTask(testWaveTask) } just Runs
            every { waveTaskPort.awakeTask(testTaskId, "Reopen task") } just Runs

            // When
            processorService.reopenTask(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE)

            // Then
            verify { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
            verify { waveTaskPersistencePort.getCitizenInformationRevision(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) }
            verify { waveTaskHelper.patchCurrentDataWithRevision(testRequestId, testRevisionNumbers) }
            verify { reopenTaskDataValidationService.deleteTask(testRequestId) }
            verify { waveTaskPersistencePort.openTask(testWaveTask) }
            verify { waveTaskPort.awakeTask(testTaskId, "Reopen task") }
        }

        @Test
        @DisplayName("Should successfully reopen data validation task via processor")
        fun `reopenTask should call data validation service for VALIDATION_DATA task type`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) } returns testWaveTask
            every { waveTaskPersistencePort.openTask(testWaveTask) } just Runs
            every { waveTaskPort.awakeTask(testTaskId, "Reopen task") } just Runs

            // When
            processorService.reopenTask(testRequestId, WaveTaskPort.VALIDATION_DATA)

            // Then
            verify { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.VALIDATION_DATA) }
            verify { waveTaskPersistencePort.openTask(testWaveTask) }
            verify { waveTaskPort.awakeTask(testTaskId, "Reopen task") }
        }

        @Test
        @DisplayName("Should throw IllegalArgumentException for invalid task type")
        fun `reopenTask should throw IllegalArgumentException for unknown task type`() {
            // Given
            val invalidTaskType = "INVALID_TASK_TYPE"

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                processorService.reopenTask(testRequestId, invalidTaskType)
            }
            assertEquals("Invalid task type", exception.message)
        }

        @Test
        @DisplayName("Should throw IllegalArgumentException for null task type")
        fun `reopenTask should throw IllegalArgumentException for null task type`() {
            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                processorService.reopenTask(testRequestId, "NON_EXISTENT_TYPE")
            }
            assertEquals("Invalid task type", exception.message)
        }

        @Test
        @DisplayName("Should propagate exception from underlying service")
        fun `reopenTask should propagate exception from data capture service`() {
            // Given
            every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE) } throws RuntimeException("Database error")

            // When & Then
            val exception = assertThrows<RuntimeException> {
                processorService.reopenTask(testRequestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE)
            }
            assertEquals("Database error", exception.message)
        }
    }

    @Nested
    @DisplayName("ReopenTaskAbstractService Common Methods Tests")
    inner class AbstractServiceTests {

        @Test
        @DisplayName("Should throw ReopenTaskException when checkTaskIsEmpty receives null")
        fun `checkTaskIsEmpty should throw exception when task is null`() {
            // Given
            val errorMessage = "Task cannot be null"

            // When & Then
            val exception = assertThrows<ReopenTaskException> {
                TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testCheckTaskIsEmpty(null, errorMessage)
            }
        }

        @Test
        @DisplayName("Should not throw exception when checkTaskIsEmpty receives non-null task")
        fun `checkTaskIsEmpty should not throw exception when task is not null`() {
            // Given
            val task = testWaveTask
            val errorMessage = "Task cannot be null"

            // When & Then - should not throw
            TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testCheckTaskIsEmpty(task, errorMessage)
        }

        @Test
        @DisplayName("Should get wave task revision successfully")
        fun `getWaveTaskRevision should return revision numbers`() {
            // Given
            val taskCode = "TEST_TASK"
            every { waveTaskPersistencePort.getCitizenInformationRevision(testRequestId, taskCode) } returns testRevisionNumbers

            // When
            val result = TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testGetWaveTaskRevision(testRequestId, taskCode)

            // Then
            verify { waveTaskPersistencePort.getCitizenInformationRevision(testRequestId, taskCode) }
            assert(result == testRevisionNumbers)
        }

        @Test
        @DisplayName("Should execute wo awake task successfully")
        fun `executeWoAwakeTask should call awakeTask with correct parameters`() {
            // Given
            every { waveTaskPort.awakeTask(testTaskId, "Reopen task") } just Runs

            // When
            TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testExecuteWoAwakeTask(testTaskId)

            // Then
            verify { waveTaskPort.awakeTask(testTaskId, "Reopen task") }
        }

        @Test
        @DisplayName("Should execute wave task soft delete successfully")
        fun `executeWaveTaskSoftDelete should call softDeleteTaskById`() {
            // Given
            every { waveTaskPersistencePort.softDeleteTaskById(testTaskId) } just Runs

            // When
            TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testExecuteWaveTaskSoftDelete(testTaskId)

            // Then
            verify { waveTaskPersistencePort.softDeleteTaskById(testTaskId) }
        }

        @Test
        @DisplayName("Should open task successfully")
        fun `openTask should call persistence port openTask`() {
            // Given
            every { waveTaskPersistencePort.openTask(testWaveTask) } just Runs

            // When
            TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testOpenTask(testWaveTask)

            // Then
            verify { waveTaskPersistencePort.openTask(testWaveTask) }
        }

        @Test
        @DisplayName("Should remove wave task entity successfully")
        fun `removeWaveTaskEntity should call persistence port removeWaveTaskEntityByRequestId`() {
            // Given
            val taskType = "TEST_TASK"
            every { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(testRequestId, taskType) } just Runs

            // When
            TestReopenTaskService(waveTaskPort, waveTaskPersistencePort, taskApiPort).testRemoveWaveTaskEntity(testRequestId, taskType)

            // Then
            verify { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(testRequestId, taskType) }
        }
    }

    // Test implementation class to access protected methods
    private class TestReopenTaskService(
        waveTaskPort: WaveTaskPort,
        waveTaskPersistencePort: WaveTaskPersistencePort,
        taskApiPort: TaskApiPort
    ) : ReopenTaskAbstractService(waveTaskPort, waveTaskPersistencePort, taskApiPort) {

        override fun reopenTask(requestId: UUID) {
            // Implementation not needed for testing
        }

        fun testCheckTaskIsEmpty(task: Any?, errorMessage: String) {
            checkTaskIsEmpty(task, errorMessage)
        }

        fun testGetWaveTaskRevision(requestId: UUID, taskCode: String): WaveTaskRevisionNumbers {
            return getWaveTaskRevision(requestId, taskCode)
        }

        fun testExecuteWoAwakeTask(taskId: String) {
            executeWoAwakeTask(taskId)
        }

        fun testExecuteWaveTaskSoftDelete(taskId: String) {
            executeWaveTaskSoftDelete(taskId)
        }

        fun testOpenTask(waveTask: WaveTask) {
            openTask(waveTask)
        }

        fun testRemoveWaveTaskEntity(requestId: UUID, taskType: String) {
            removeWaveTaskEntity(requestId, taskType)
        }
    }
}

//@ExtendWith(MockKExtension::class)
//class ReopenTaskServiceTest {
//
//    @MockK
//    private lateinit var waveTaskPort: WaveTaskPort
//
//    @MockK
//    private lateinit var waveTaskPersistencePort: WaveTaskPersistencePort
//
//    @MockK
//    private lateinit var reopenTaskDataCaptureService: ReopenTaskDataCaptureService
//
//    @MockK
//    private lateinit var reopenTaskDataValidationService: ReopenTaskDataValidationService
//
//    @MockK
//    lateinit var waveTaskHelper: WaveTaskHelper
//
//    @InjectMockKs
//    private lateinit var reopenTaskService: ReopenTaskProcessorService
//
//    @InjectMockKs
//    private lateinit var reopenTaskProcessorService: ReopenTaskProcessorService
//
//    @InjectMockKs
//    lateinit var service: TestableReopenTaskService
//
//    class TestableReopenTaskService(
//        waveTaskPort: WaveTaskPort,
//        waveTaskPersistencePort: WaveTaskPersistencePort
//    ) : ReopenTaskAbstractService(waveTaskPort, waveTaskPersistencePort) {
//        override fun reopenTask(requestId: UUID) {
//
//        }
//
//        // expose protected methods as public for testing
//        fun callGetWaveTaskEntity(requestId: UUID, taskType: String) =
//            getWaveTaskEntity(requestId, taskType)
//
//        fun callRemoveWaveTaskEntity(requestId: UUID, taskType: String) =
//            removeWaveTaskEntity(requestId, taskType)
//
//        fun callCheckTaskIsEmpty(task: Any?, message: String) =
//            checkTaskIsEmpty(task, message)
//
//        fun callExecuteWoAwakeTask(taskId: String) =
//            executeWoAwakeTask(taskId)
//
//        fun callExecuteWaveTaskSoftDelete(taskId: String) =
//            executeWaveTaskSoftDelete(taskId)
//
//        fun callGetWaveTaskRevision(requestId: UUID, taskCode: String) =
//            getWaveTaskRevision(requestId, taskCode)
//    }
//
//
//    @Test
//    fun `should delegate to correct service based on task type`() {
//        // Given
//        val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
//        val taskType = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
//
//        every { reopenTaskDataCaptureService.reopenTask(requestId) } just Runs
//
//        // When
//        reopenTaskProcessorService.reopenTask(requestId, taskType)
//
//        // Then
//        verify(exactly = 1) { reopenTaskDataCaptureService.reopenTask(requestId) }
//        verify(exactly = 0) { reopenTaskDataValidationService.reopenTask(any()) }
//    }
//
//    @Test
//    fun `should throw exception when task type is not recognized`() {
//        // Given
//        val requestId = UUID.randomUUID()
//        val invalidTaskType = "UNKNOWN_TASK_TYPE"
//
//        assertThatThrownBy {
//            reopenTaskService.reopenTask(requestId, invalidTaskType)
//        }.isInstanceOf(IllegalArgumentException::class.java)
//            .hasMessage("Invalid task type")
//    }
//
//    @Test
//    fun `should call only the relevant reopen service`() {
//        val requestId = UUID.randomUUID()
//        val taskType = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
//
//        every { reopenTaskDataCaptureService.reopenTask(any()) } just Runs
//
//        // When
//        reopenTaskService.reopenTask(requestId, taskType)
//
//        // Then
//        verify(exactly = 1) { reopenTaskDataCaptureService.reopenTask(requestId) }
//        verify(exactly = 0) { reopenTaskDataValidationService.reopenTask(any()) }
//    }
//
//
//    // Abstract Service Tests
//
//    @Test
//    fun `should get wave task entity`() {
//        val requestId = UUID.randomUUID()
//        val taskType = "TEST_TASK"
//        val waveTask = WaveTask("taskId", "ref", WaveTaskStatus.WAITING)
//
//        every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, taskType) } returns waveTask
//
//        val result = service.callGetWaveTaskEntity(requestId, taskType)
//
//        assertEquals(waveTask, result)
//    }
//
//    @Test
//    fun `should throw ReopenTaskException when task is null`() {
//        assertThrows(ReopenTaskException::class.java) {
//            service.callCheckTaskIsEmpty(null, "empty task")
//        }
//    }
//
//    @Test
//    fun `should call waveTaskPort to awake task`() {
//        val taskId = "task123"
//        every { waveTaskPort.awakeTask(taskId, any()) } just Runs
//
//        service.callExecuteWoAwakeTask(taskId)
//
//        verify { waveTaskPort.awakeTask(taskId, "Reopen task") }
//    }
//
//    @Test
//    fun `should call persistencePort to delete task`() {
//        val taskId = "task123"
//        every { waveTaskPersistencePort.softDeleteTaskById(taskId) } just Runs
//
//        service.callExecuteWaveTaskSoftDelete(taskId)
//
//        verify { waveTaskPersistencePort.softDeleteTaskById(taskId) }
//    }
//
//    @Test
//    fun `should remove wave task entity`() {
//        val requestId = UUID.randomUUID()
//        val taskType = WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
//
//        every { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, taskType) } just Runs
//
//        service.callRemoveWaveTaskEntity(requestId, taskType)
//
//        verify { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, taskType) }
//    }
//
//    @Test
//    fun `should not throw when task is not null`() {
//        service.callCheckTaskIsEmpty("not null", "should not throw")
//    }
//
//    @Test
//    fun `should get wave task revision`() {
//        val requestId = UUID.randomUUID()
//        val taskCode = "REV_CODE"
//        val expected = WaveTaskRevisionNumbers(1, 2, 3, 4)
//
//        every { waveTaskPersistencePort.getCitizenInformationRevision(requestId, taskCode) } returns expected
//
//        val result = service.callGetWaveTaskRevision(requestId, taskCode)
//
//        assertEquals(expected, result)
//    }
//
//    @Test
//    fun `should delete data capture task`() {
//        val requestId = UUID.randomUUID()
//        val waveTask = WaveTask("task123", "ref", WaveTaskStatus.WAITING)
//
//        every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any()) } returns waveTask
//        every { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, any()) } just Runs
//        every { waveTaskPersistencePort.softDeleteTaskById(waveTask.taskId) } just Runs
//
//        service.deleteTask(requestId)
//
//        verify {
//            waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any())
//            waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, any())
//            waveTaskPersistencePort.softDeleteTaskById(waveTask.taskId)
//        }
//    }
//
//    @Test
//    fun `should reopen data capture task`() {
//        val requestId = UUID.randomUUID()
//        val waveTask = WaveTask("task123", "ref", WaveTaskStatus.WAITING)
//        val revisionNumbers = WaveTaskRevisionNumbers(1, 1, 1, 1)
//
//        every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any()) } returns waveTask
//        every { waveTaskPersistencePort.getCitizenInformationRevision(requestId, any()) } returns revisionNumbers
//        every { reopenTaskDataValidationService.deleteTask(requestId) } just Runs
//        every { waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers) } just Runs
//        every { waveTaskPersistencePort.openTask(waveTask) } just Runs
//        every { waveTaskPort.awakeTask(waveTask.taskId, any()) } just Runs
//
//        service.reopenTask(requestId)
//
//        verify {
//            waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any())
//            waveTaskPersistencePort.getCitizenInformationRevision(requestId, any())
//            waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
//            reopenTaskDataValidationService.deleteTask(requestId)
//            waveTaskPersistencePort.openTask(waveTask)
//            waveTaskPort.awakeTask(waveTask.taskId, "Reopen task")
//        }
//    }
//
//    @Test
//    fun `should delete validation task`() {
//        val requestId = UUID.randomUUID()
//        val waveTask = WaveTask("val123", "ref", WaveTaskStatus.WAITING)
//
//        every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any()) } returns waveTask
//        every { waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, any()) } just Runs
//        every { waveTaskPersistencePort.softDeleteTaskById(waveTask.taskId) } just Runs
//
//        service.deleteTask(requestId)
//
//        verify {
//            waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any())
//            waveTaskPersistencePort.removeWaveTaskEntityByRequestId(requestId, any())
//            waveTaskPersistencePort.softDeleteTaskById(waveTask.taskId)
//        }
//    }
//
//    @Test
//    fun `should reopen validation task`() {
//        val requestId = UUID.randomUUID()
//        val waveTask = WaveTask("val456", "ref", WaveTaskStatus.WAITING)
//
//        every { waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any()) } returns waveTask
//        every { waveTaskPersistencePort.openTask(waveTask) } just Runs
//        every { waveTaskPort.awakeTask(waveTask.taskId, any()) } just Runs
//
//        service.reopenTask(requestId)
//
//        verify {
//            waveTaskPersistencePort.getWaveTaskEntityByRequestId(requestId, any())
//            waveTaskPersistencePort.openTask(waveTask)
//            waveTaskPort.awakeTask(waveTask.taskId, "Reopen task")
//        }
//    }
//
//
//}