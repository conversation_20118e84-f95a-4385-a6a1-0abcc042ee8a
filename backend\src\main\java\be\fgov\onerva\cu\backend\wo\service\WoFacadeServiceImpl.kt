package be.fgov.onerva.cu.backend.wo.service

import java.time.LocalDate
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.util.Assert
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.backend.wo.dto.WoMetadataDTO
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO
import be.fgov.onerva.cu.backend.wo.exception.WoProblemException
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.wo.facade.api.FacadeControllerApi
import be.fgov.onerva.wo.facade.rest.model.AwakenRequestDTO
import be.fgov.onerva.wo.facade.rest.model.CreatableTaskDTO
import be.fgov.onerva.wo.facade.rest.model.InputMetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.InputThirdPartyDTO
import be.fgov.onerva.wo.facade.rest.model.SleepRequestDTO
import be.fgov.onerva.wo.facade.rest.model.StateDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyTypeDTO
import be.fgov.onerva.wo.organizational.chart.api.NodeApi
import be.fgov.onerva.wo.organizational.chart.rest.model.Node
import be.fgov.onerva.wo_thirdparty.api.DefaultApi
import be.fgov.onerva.wo_thirdparty.rest.model.Party
import lombok.RequiredArgsConstructor
import lombok.extern.slf4j.Slf4j

@Service
@RequiredArgsConstructor
@Slf4j
class WoFacadeServiceImpl(
    val facadeControllerApi: FacadeControllerApi,
    val partyApi: DefaultApi,
    val nodeApi: NodeApi,
) : WoFacadeService {
    private val log = logger

    @Value("\${werkomgeving.mock}")
    private val mockEnabled = false

    override fun createTask(
        processId: Long?,
        typeTaskCode: String,
        numbox: Int?,
        assignee: String,
        dueDate: LocalDate?,
        woProcessMetadataDTOList: List<WoMetadataDTO>,
        woTaskMetadataDTOList: List<WoMetadataDTO>,
    ): WoTaskDTO {
        log.info("Creating task with processId: {}, typeTaskCode: {}", processId, typeTaskCode)

        var citizenThirdParty: InputThirdPartyDTO? = null
        if (numbox != null) {
            citizenThirdParty = InputThirdPartyDTO(
                id = numbox.toString(),
                type = ThirdPartyTypeDTO.CITIZEN,
                quality = null
            )
        }
        val processMetadata = woProcessMetadataDTOList.map { woMetadataDTO: WoMetadataDTO ->
            InputMetaDataDTO(
                code = woMetadataDTO.code,
                value = woMetadataDTO.value
            )
        }
        val taskMetadata = woTaskMetadataDTOList.map { woMetadataDTO: WoMetadataDTO ->
            InputMetaDataDTO(
                code = woMetadataDTO.code,
                value = woMetadataDTO.value
            )
        }
        val creatableTaskDTO = CreatableTaskDTO(
            concernedEntities = listOfNotNull(citizenThirdParty),
            assignee = assignee,
            taskTypeCode = typeTaskCode,
            metadata = taskMetadata,
            dueDate = dueDate,
            correlation = null,
            processMetadata = processMetadata,
            processId = processId,
            permissionGroupId = null,
            businessId = null
        )

        if (mockEnabled) {
            if (numbox != null) {
                createParty(numbox.toString())
            }
            createUserInWoOrgChart(assignee)
        }

        val taskDTO = facadeControllerApi.createTask(creatableTaskDTO)

        Assert.notNull(taskDTO.processId, "ProcessId is null")
        Assert.notNull(taskDTO.taskId, "TaskId is null")
        Assert.notNull(taskDTO.status, "Status should not be null")

        return WoTaskDTO(
            taskDTO.processId.toString(),
            taskDTO.taskId.toString(),
            taskDTO.status?.task?.value ?: "",
            taskDTO.status?.taskStep,
        )
    }

    override fun awakeTask(taskId: Long, reason: String) {
        try {
            facadeControllerApi.awaken(taskId, AwakenRequestDTO("Reopen task"));
            log.info("Awakening task with id {}", taskId);
        } catch (e: RuntimeException) {
            log.error("Error when awakening task with id {}", taskId, e);
            throw e;
        }

    }

    override fun checkTaskCanBeUpdated(taskId: Long): Boolean {
        log.info("Checking if task with id {} can be closed", taskId)
        val task = facadeControllerApi.getTask(taskId)

        if (task == null || task.status == null) {
            log.warn("Task with id {} not found or status is null", taskId)
            return false
        }

        return task.status?.process != StateDTO.CLOSED && task.status?.task != StateDTO.CLOSED
    }

    override fun assignTaskToUser(taskId: Long, user: String) {
        log.info("Assigning task with id {} to user {}", taskId, user)
        if (mockEnabled) {
            createUserInWoOrgChart(user)
        }
        facadeControllerApi.assignTask(taskId, user, null)
    }

    override fun updateProcessData(processId: Long, woMetadataDTOList: List<WoMetadataDTO>) {
        log.info("Updating process with id {}", processId)
        val listMetadataDTO = woMetadataDTOList.map { woMetadataDTO: WoMetadataDTO ->
            InputMetaDataDTO(
                code = woMetadataDTO.code,
                value = woMetadataDTO.value
            )
        }
        facadeControllerApi.updateProcess(processId, listMetadataDTO)
    }

    override fun patchProcessData(processId: Long, taskId: Long, woMetadataDTOList: List<WoMetadataDTO>) {
        log.info("Patching process with id {}", processId)
        val metadataForUpdate: List<WoMetadataDTO> =
            facadeControllerApi.getTask(taskId)?.processMetadata?.map {
                val newMetadata = woMetadataDTOList.firstOrNull { inputWoMetadata -> inputWoMetadata.code == it.code }
                if (newMetadata != null) {
                    WoMetadataDTO(it.code, newMetadata.value)
                } else {
                    WoMetadataDTO(it.code, it.value)
                }
            } ?: throw WaveTaskNotFoundException("Wave task not found for task $taskId")
        this.updateProcessData(processId, metadataForUpdate)
    }

    override fun closeTaskAndProcess(taskId: Long) {
        log.info("Closing And Process task with id {}", taskId)
        facadeControllerApi.closeTask(taskId)
    }

    override fun closeTask(taskId: Long) {
        log.info("Closing task with id {}", taskId)
        facadeControllerApi.closeTask(taskId)
    }

    override fun sleepTask(taskId: Long, reason: String) {
        log.info("Sleeping task with id {} with reason {}", taskId, reason)
        facadeControllerApi.sleep(taskId, SleepRequestDTO(reason = reason, wakingUpDate = null))
    }

    override fun closeProcess(processId: Long) {
        log.info("Closing process with id {}", processId)
        facadeControllerApi.closeProcess(processId)
    }

    private fun createParty(id: String) {
        // Assuming Party is still Java-generated, keep the original fluent API style
        val party = Party().partyType("PERSON").id(id).ssin(id).active(true)

        partyApi.createParty(party, null)
        log.info("Successfully Created Party for niss")
    }

    /**
     * Creates a user in the Work Environment's organizational chart.
     * It sends a request to the NodeApi to create a new node of type 'EMPLOYEE'.
     */
    private fun createUserInWoOrgChart(username: String) {
        val node = Node(username, Node.TypeEnum.EMPLOYEE)
        try {
            nodeApi.createNode(node)
            log.info("Created user in organizational chart")
        } catch (e: RuntimeException) {
            log.error("Error when creating user in organizational chart", e)
            throw WoProblemException("Error when creating user in organizational chart")
        }
    }
}
