Feature: History

  Background:
    Given the database is clean
    And test data for change personal data is loaded
    And I am authenticated as "test_user" with role "cu_role_user"

  Scenario: Get ONEM historical information for citizen with SSIN "18031307065"
    Given a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    And a citizen with SSIN "18031307065" exists in the system
    When I get the ONEM historical information for the citizen
    Then the response status should be 200
    And the response should contain the following information:
      | firstName       | John             |
      | lastName        | Doe              |
      | birthDate       | null             |
      | nationalityCode | 111              |
      | iban            | **************** |
      | bic             | CMRPL            |
      | otherPersonName | null             |
    And the address in the response should contain the following values:
      | street      | Main Street |
      | boxNumber   | Box A       |
      | houseNumber | 123         |
      | zipCode     | 1000        |
      | city        | Brussels    |
      | countryCode | 150         |

