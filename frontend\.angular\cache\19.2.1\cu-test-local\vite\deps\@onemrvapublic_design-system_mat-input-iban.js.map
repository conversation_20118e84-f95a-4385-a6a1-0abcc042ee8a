{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/checkbox.mjs", "../../../../../../node_modules/@angular/material/fesm2022/progress-spinner.mjs", "../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-select-search.mjs", "../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-input-iban.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, inject, ElementRef, ChangeDetectorRef, NgZone, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { _StructuralStylesLoader, MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n    disabledInteractive: false\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n  /** The source checkbox of the event. */\n  source;\n  /** The new `checked` value of the checkbox. */\n  checked;\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _options = inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  /** Creates the change event that will be emitted by the checkbox. */\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  /** Gets the element on which to add the animation CSS classes. */\n  _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n  /** CSS classes to add when transitioning between the different checkbox states. */\n  _animationClasses = {\n    uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n    checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n    indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n  };\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  ariaLabel = '';\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  ariaLabelledby = null;\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  ariaDescribedby;\n  /**\n   * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n   */\n  ariaExpanded;\n  /**\n   * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n   */\n  ariaControls;\n  /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n  ariaOwns;\n  _uniqueId;\n  /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n  id;\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  /** Whether the checkbox is required. */\n  required;\n  /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n  labelPosition = 'after';\n  /** Name value will be applied to the input element if present */\n  name = null;\n  /** Event emitted when the checkbox's `checked` value changes. */\n  change = new EventEmitter();\n  /** Event emitted when the checkbox's `indeterminate` value changes. */\n  indeterminateChange = new EventEmitter();\n  /** The value attribute of the native input element */\n  value;\n  /** Whether the checkbox has a ripple. */\n  disableRipple;\n  /** The native `<input type=\"checkbox\">` element */\n  _inputElement;\n  /** The native `<label>` element */\n  _labelElement;\n  /** Tabindex for the checkbox. */\n  tabIndex;\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /**\n   * Theme color of the checkbox. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.io/components/checkbox/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.io/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the checkbox should remain interactive when it is disabled. */\n  disabledInteractive;\n  /**\n   * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  _currentAnimationClass = '';\n  _currentCheckState = TransitionCheckState.Init;\n  _controlValueAccessorChangeFn = () => {};\n  _validatorChangeFn = () => {};\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n    this.disabledInteractive = this._options?.disabledInteractive ?? false;\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _checked = false;\n  /** Whether the checkbox is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _disabled = false;\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate;\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = value;\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      }\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n    this._syncIndeterminate(this._indeterminate);\n  }\n  _indeterminate = false;\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  // Implemented as a part of Validator.\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn) {\n    this._validatorChangeFn = fn;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (this.disabled && this.disabledInteractive || !this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  _onInputClick() {\n    this._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    this._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n  static ɵfac = function MatCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCheckbox,\n    selectors: [[\"mat-checkbox\"]],\n    viewQuery: function MatCheckbox_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-checkbox\"],\n    hostVars: 16,\n    hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked)(\"mat-mdc-checkbox-disabled-interactive\", ctx.disabledInteractive);\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n      ariaExpanded: [2, \"aria-expanded\", \"ariaExpanded\", booleanAttribute],\n      ariaControls: [0, \"aria-controls\", \"ariaControls\"],\n      ariaOwns: [0, \"aria-owns\", \"ariaOwns\"],\n      id: \"id\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      labelPosition: \"labelPosition\",\n      name: \"name\",\n      value: \"value\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n      color: \"color\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n      checked: [2, \"checked\", \"checked\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      indeterminate: [2, \"indeterminate\", \"indeterminate\", booleanAttribute]\n    },\n    outputs: {\n      change: \"change\",\n      indeterminateChange: \"indeterminateChange\"\n    },\n    exportAs: [\"matCheckbox\"],\n    features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n      provide: NG_VALIDATORS,\n      useExisting: MatCheckbox,\n      multi: true\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 15,\n    vars: 23,\n    consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n    template: function MatCheckbox_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n        });\n        i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onTouchTargetClick());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"input\", 6, 1);\n        i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onBlur());\n        })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInputClick());\n        })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(6, \"div\", 7);\n        i0.ɵɵelementStart(7, \"div\", 8);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 9);\n        i0.ɵɵelement(9, \"path\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelement(10, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(11, \"div\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"label\", 13, 2);\n        i0.ɵɵprojection(14);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const checkbox_r2 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n        i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"aria-controls\", ctx.ariaControls)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? true : null)(\"aria-expanded\", ctx.ariaExpanded)(\"aria-owns\", ctx.ariaOwns)(\"name\", ctx.name)(\"value\", ctx.value);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"for\", ctx.inputId);\n      }\n    },\n    dependencies: [MatRipple, _MatInternalFormField],\n    styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked',\n        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"'\n      },\n      providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mdc-checkbox-state-layer-size, 40px);height:var(--mdc-checkbox-state-layer-size, 40px);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mdc-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mdc-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mdc-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mdc-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    ariaExpanded: [{\n      type: Input,\n      args: [{\n        alias: 'aria-expanded',\n        transform: booleanAttribute\n      }]\n    }],\n    ariaControls: [{\n      type: Input,\n      args: ['aria-controls']\n    }],\n    ariaOwns: [{\n      type: Input,\n      args: ['aria-owns']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? undefined : numberAttribute(value)\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatCheckboxRequiredValidator_BaseFactory;\n    return function MatCheckboxRequiredValidator_Factory(__ngFactoryType__) {\n      return (ɵMatCheckboxRequiredValidator_BaseFactory || (ɵMatCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatCheckboxRequiredValidator)))(__ngFactoryType__ || MatCheckboxRequiredValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCheckboxRequiredValidator,\n    selectors: [[\"mat-checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n      providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR]\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {\n  static ɵfac = function _MatCheckboxRequiredValidatorModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatCheckboxRequiredValidatorModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: _MatCheckboxRequiredValidatorModule,\n    imports: [MatCheckboxRequiredValidator],\n    exports: [MatCheckboxRequiredValidator]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatCheckboxRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckboxRequiredValidator],\n      exports: [MatCheckboxRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatCheckboxModule {\n  static ɵfac = function MatCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCheckboxModule,\n    imports: [MatCheckbox, MatCommonModule],\n    exports: [MatCheckbox, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckbox, MatCommonModule],\n      exports: [MatCheckbox, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst _c0 = [\"determinateSpinner\"];\nfunction MatProgressSpinner_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"circle\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"viewBox\", ctx_r0._viewBox());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r0._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx_r0._strokeCircumference() / 2, \"px\")(\"stroke-width\", ctx_r0._circleStrokeWidth(), \"%\");\n    i0.ɵɵattribute(\"r\", ctx_r0._circleRadius());\n  }\n}\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n  _elementRef = inject(ElementRef);\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.io/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.io/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  _defaultColor = 'primary';\n  /** The element of the determinate spinner. */\n  _determinateCircle;\n  constructor() {\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n    this._noopAnimations = animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner' ? 'indeterminate' : 'determinate';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  mode;\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  _value = 0;\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  get diameter() {\n    return this._diameter;\n  }\n  set diameter(size) {\n    this._diameter = size || 0;\n  }\n  _diameter = BASE_SIZE;\n  /** Stroke width of the progress spinner. */\n  get strokeWidth() {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value) {\n    this._strokeWidth = value || 0;\n  }\n  _strokeWidth;\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference() {\n    return 2 * Math.PI * this._circleRadius();\n  }\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._strokeCircumference() * (100 - this._value) / 100;\n    }\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n  static ɵfac = function MatProgressSpinner_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatProgressSpinner,\n    selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n    viewQuery: function MatProgressSpinner_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._determinateCircle = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-spinner\", \"mdc-circular-progress\"],\n    hostVars: 18,\n    hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuemax\", 100)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n        i0.ɵɵclassMap(\"mat-\" + ctx.color);\n        i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\")(\"--mdc-circular-progress-size\", ctx.diameter + \"px\")(\"--mdc-circular-progress-active-indicator-width\", ctx.diameter + \"px\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations)(\"mdc-circular-progress--indeterminate\", ctx.mode === \"indeterminate\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      value: [2, \"value\", \"value\", numberAttribute],\n      diameter: [2, \"diameter\", \"diameter\", numberAttribute],\n      strokeWidth: [2, \"strokeWidth\", \"strokeWidth\", numberAttribute]\n    },\n    exportAs: [\"matProgressSpinner\"],\n    decls: 14,\n    vars: 11,\n    consts: [[\"circle\", \"\"], [\"determinateSpinner\", \"\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__determinate-container\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__determinate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"mdc-circular-progress__determinate-circle\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__indeterminate-container\"], [1, \"mdc-circular-progress__spinner-layer\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-left\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-circular-progress__gap-patch\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-right\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__indeterminate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n    template: function MatProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatProgressSpinner_ng_template_0_Template, 2, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(2, \"div\", 2, 1);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 3);\n        i0.ɵɵelement(5, \"circle\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n        i0.ɵɵelementContainer(9, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵelementContainer(11, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelementContainer(13, 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const circle_r2 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"viewBox\", ctx._viewBox());\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"stroke-dasharray\", ctx._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx._strokeDashOffset(), \"px\")(\"stroke-width\", ctx._circleStrokeWidth(), \"%\");\n        i0.ɵɵattribute(\"r\", ctx._circleRadius());\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n        '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n        '[attr.aria-valuemin]': '0',\n        '[attr.aria-valuemax]': '100',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [NgTemplateOutlet],\n      template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _determinateCircle: [{\n      type: ViewChild,\n      args: ['determinateSpinner']\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    diameter: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    strokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\nclass MatProgressSpinnerModule {\n  static ɵfac = function MatProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatProgressSpinnerModule,\n    imports: [MatProgressSpinner, MatSpinner],\n    exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressSpinner, MatSpinner],\n      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n", "import { A, Z, <PERSON>ERO, NINE, SPACE, HOME, END, ENTER, ESCAPE, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, QueryList, ElementRef, forwardRef, ContentChild, ViewChild, Output, Input, Inject, Optional, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { FormControl, ReactiveFormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/core';\nimport { MatOption } from '@angular/material/core';\nimport * as i5 from '@angular/material/form-field';\nimport { MatFormField } from '@angular/material/form-field';\nimport * as i3 from '@angular/material/select';\nimport { MatSelect } from '@angular/material/select';\nimport { BehaviorSubject, of, combineLatest, Subject } from 'rxjs';\nimport { switchMap, map, startWith, delay, takeUntil, take, tap, filter } from 'rxjs/operators';\nimport { NgClass, NgIf, AsyncPipe } from '@angular/common';\nimport { MatCheckbox } from '@angular/material/checkbox';\nimport { MatTooltip } from '@angular/material/tooltip';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatProgressSpinner } from '@angular/material/progress-spinner';\nimport { MatIconButton } from '@angular/material/button';\nimport { MatInput } from '@angular/material/input';\nimport { MatDivider } from '@angular/material/divider';\nimport * as i1 from '@angular/cdk/scrolling';\n\n/**\n * Directive for providing a custom clear-icon.\n * e.g.\n * <ngx-mat-select-search [formControl]=\"bankFilterCtrl\">\n *   <mat-icon matSelectSearchClear>delete</mat-icon>\n * </ngx-mat-select-search>\n */\nconst _c0 = [\"searchSelectInput\"];\nconst _c1 = [\"innerSelectSearch\"];\nconst _c2 = [[[\"\", 8, \"mat-select-search-custom-header-content\"]], [[\"\", \"matSelectSearchClear\", \"\"]], [[\"\", \"matSelectNoEntriesFound\", \"\"]]];\nconst _c3 = [\".mat-select-search-custom-header-content\", \"[matSelectSearchClear]\", \"[matSelectNoEntriesFound]\"];\nconst _c4 = (a0, a1) => ({\n  \"mat-select-search-inner-multiple\": a0,\n  \"mat-select-search-inner-toggle-all\": a1\n});\nfunction MatSelectSearchComponent_mat_checkbox_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 12);\n    i0.ɵɵlistener(\"change\", function MatSelectSearchComponent_mat_checkbox_3_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._emitSelectAllBooleanToParent($event.checked));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"color\", ctx_r2.matFormField.color)(\"checked\", ctx_r2.toggleAllCheckboxChecked)(\"indeterminate\", ctx_r2.toggleAllCheckboxIndeterminate)(\"matTooltip\", ctx_r2.toggleAllCheckboxTooltipMessage)(\"matTooltipPosition\", ctx_r2.toggleAllCheckboxTooltipPosition);\n  }\n}\nfunction MatSelectSearchComponent_mat_icon_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 13);\n    i0.ɵɵtext(1, \"search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatSelectSearchComponent_mat_spinner_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 14);\n  }\n}\nfunction MatSelectSearchComponent_button_8_ng_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"clearIcon; else defaultIcon\"]);\n  }\n}\nfunction MatSelectSearchComponent_button_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"svgIcon\", ctx_r2.closeSvgIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r2.closeSvgIcon ? ctx_r2.closeIcon : null, \" \");\n  }\n}\nfunction MatSelectSearchComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function MatSelectSearchComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._reset(true));\n    });\n    i0.ɵɵtemplate(1, MatSelectSearchComponent_button_8_ng_content_1_Template, 1, 0, \"ng-content\", 16)(2, MatSelectSearchComponent_button_8_ng_template_2_Template, 2, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultIcon_r5 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.clearIcon)(\"ngIfElse\", defaultIcon_r5);\n  }\n}\nfunction MatSelectSearchComponent_div_11_ng_content_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2, [\"*ngIf\", \"noEntriesFound; else defaultNoEntriesFound\"]);\n  }\n}\nfunction MatSelectSearchComponent_div_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate(ctx_r2.noEntriesFoundLabel);\n  }\n}\nfunction MatSelectSearchComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, MatSelectSearchComponent_div_11_ng_content_1_Template, 1, 0, \"ng-content\", 16)(2, MatSelectSearchComponent_div_11_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultNoEntriesFound_r6 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.noEntriesFound)(\"ngIfElse\", defaultNoEntriesFound_r6);\n  }\n}\nclass MatSelectSearchClearDirective {\n  static {\n    this.ɵfac = function MatSelectSearchClearDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectSearchClearDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSelectSearchClearDirective,\n      selectors: [[\"\", \"matSelectSearchClear\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchClearDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[matSelectSearchClear]',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/** List of inputs of NgxMatSelectSearchComponent that can be configured with a global default. */\nconst configurableDefaultOptions = ['ariaLabel', 'clearSearchInput', 'closeIcon', 'closeSvgIcon', 'disableInitialFocus', 'disableScrollToActiveOnOptionsChanged', 'enableClearOnEscapePressed', 'hideClearSearchButton', 'noEntriesFoundLabel', 'placeholderLabel', 'preventHomeEndKeyPropagation', 'searching'];\n/**\n * InjectionToken that can be used to specify global options. e.g.\n *\n * ```typescript\n * providers: [\n *   {\n *     provide: MAT_SELECTSEARCH_DEFAULT_OPTIONS,\n *     useValue: <MatSelectSearchOptions>{\n *       closeIcon: 'delete',\n *       noEntriesFoundLabel: 'No options found'\n *     }\n *   }\n * ]\n * ```\n *\n * See the corresponding inputs of `MatSelectSearchComponent` for documentation.\n */\nconst MAT_SELECTSEARCH_DEFAULT_OPTIONS = new InjectionToken('mat-selectsearch-default-options');\n\n/**\n * Directive for providing a custom no entries found element.\n * e.g.\n * <ngx-mat-select-search [formControl]=\"bankFilterCtrl\">\n *   <span matSelectNoEntriesFound>\n *     No entries found <button>Add</button>\n *   </span>\n * </ngx-mat-select-search>\n */\nclass MatSelectNoEntriesFoundDirective {\n  static {\n    this.ɵfac = function MatSelectNoEntriesFoundDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectNoEntriesFoundDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSelectNoEntriesFoundDirective,\n      selectors: [[\"\", \"matSelectNoEntriesFound\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectNoEntriesFoundDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[matSelectNoEntriesFound]',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Copyright (c) 2018 Bithost GmbH All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSelectSearchComponent {\n  /** Current search value */\n  get value() {\n    return this._formControl.value;\n  }\n  /** Reference to the MatSelect options */\n  set _options(_options) {\n    this._options$.next(_options);\n  }\n  get _options() {\n    return this._options$.getValue();\n  }\n  constructor(matSelect, changeDetectorRef, _viewportRuler, matOption, matFormField, defaultOptions) {\n    this.matSelect = matSelect;\n    this.changeDetectorRef = changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this.matOption = matOption;\n    this.matFormField = matFormField;\n    /** Label of the search placeholder */\n    this.placeholderLabel = 'Search';\n    /** Type of the search input field */\n    this.type = 'text';\n    /** Font-based icon used for displaying Close-Icon */\n    this.closeIcon = 'close';\n    /** Svg-based icon used for displaying Close-Icon. If set, closeIcon is overridden */\n    this.closeSvgIcon = '';\n    /** Label to be shown when no entries are found. Set to null if no message should be shown. */\n    this.noEntriesFoundLabel = 'Nothing found';\n    /**\n     * Whether or not the search field should be cleared after the dropdown menu is closed.\n     * Useful for server-side filtering. See [#3](https://github.com/bithost-gmbh/ngx-mat-select-search/issues/3)\n     */\n    this.clearSearchInput = true;\n    /** Whether to show the search-in-progress indicator */\n    this.searching = false;\n    /** Disables initial focusing of the input field */\n    this.disableInitialFocus = false;\n    /** Enable clear input on escape pressed */\n    this.enableClearOnEscapePressed = false;\n    /**\n     * Prevents home / end key being propagated to mat-select,\n     * allowing to move the cursor within the search input instead of navigating the options\n     */\n    this.preventHomeEndKeyPropagation = false;\n    /** Disables scrolling to active options when option list changes. Useful for server-side search */\n    this.disableScrollToActiveOnOptionsChanged = false;\n    /** Adds 508 screen reader support for search box */\n    this.ariaLabel = 'dropdown search';\n    /** Whether to show Select All Checkbox (for mat-select[multi=true]) */\n    this.showToggleAllCheckbox = false;\n    /** select all checkbox checked state */\n    this.toggleAllCheckboxChecked = false;\n    /** select all checkbox indeterminate state */\n    this.toggleAllCheckboxIndeterminate = false;\n    /** Display a message in a tooltip on the toggle-all checkbox */\n    this.toggleAllCheckboxTooltipMessage = '';\n    /** Define the position of the tooltip on the toggle-all checkbox. */\n    this.toggleAllCheckboxTooltipPosition = 'below';\n    /** Show/Hide the search clear button of the search input */\n    this.hideClearSearchButton = false;\n    /**\n     * Always restore selected options on selectionChange for mode multi (e.g. for lazy loading/infinity scrolling).\n     * Defaults to false, so selected options are only restored while filtering is active.\n     */\n    this.alwaysRestoreSelectedOptionsMulti = false;\n    /** Output emitter to send to parent component with the toggle all boolean */\n    this.toggleAll = new EventEmitter();\n    this.onTouched = _ => {\n      //\n    };\n    this._options$ = new BehaviorSubject(new QueryList());\n    this.optionsList$ = this._options$.pipe(switchMap(_options => _options ? _options.changes.pipe(map(options => options.toArray()), startWith(_options.toArray())) : of([])));\n    this.optionsLength$ = this.optionsList$.pipe(map(options => options ? options.length : 0));\n    this._formControl = new FormControl('');\n    /** whether to show the no entries found message */\n    this._showNoEntriesFound$ = combineLatest([this._formControl.valueChanges, this.optionsLength$]).pipe(map(([_, optionsLength]) => {\n      return this.noEntriesFoundLabel !== '' && optionsLength === this.getOptionsLengthOffset();\n    }));\n    /** Subject that emits when the component has been destroyed. */\n    this._onDestroy = new Subject();\n    this.applyDefaultOptions(defaultOptions);\n  }\n  applyDefaultOptions(defaultOptions) {\n    if (!defaultOptions) {\n      return;\n    }\n    for (const key of configurableDefaultOptions) {\n      // eslint-disable-next-line\n      if (defaultOptions.hasOwnProperty(key)) {\n        this[key] = defaultOptions[key];\n      }\n    }\n  }\n  ngOnInit() {\n    // set custom mat-option class if the component was placed inside a mat-option\n    if (this.matOption) {\n      this.matOption.disabled = true;\n      this.matOption._getHostElement().classList.add('contains-mat-select-search');\n      //this.matOption._getHostElement().setAttribute('aria-hidden', 'true'); // this provoke an error\n    } else {\n      console.error('<ngx-mat-select-search> must be placed inside a <mat-option> element');\n    }\n    // when the select dropdown panel is opened or closed\n    this.matSelect.openedChange.pipe(delay(1), takeUntil(this._onDestroy)).subscribe(opened => {\n      if (opened) {\n        this.updateInputWidth();\n        // focus the search field when opening\n        if (!this.disableInitialFocus) {\n          this._focus();\n        }\n      } else {\n        // clear it when closing\n        if (this.clearSearchInput) {\n          this._reset();\n        }\n      }\n    });\n    // set the first item active after the options changed\n    this.matSelect.openedChange.pipe(take(1), switchMap(_ => {\n      this._options = this.matSelect.options;\n      // Closure variable for tracking the most recent first option.\n      // In order to avoid avoid causing the list to\n      // scroll to the top when options are added to the bottom of\n      // the list (eg: infinite scroll), we compare only\n      // the changes to the first options to determine if we\n      // should set the first item as active.\n      // This prevents unnecessary scrolling to the top of the list\n      // when options are appended, but allows the first item\n      // in the list to be set as active by default when there\n      // is no active selection\n      let previousFirstOption = this._options.toArray()[this.getOptionsLengthOffset()];\n      return this._options.changes.pipe(tap(() => {\n        // avoid \"expression has been changed\" error\n        setTimeout(() => {\n          // Convert the QueryList to an array\n          const options = this._options.toArray();\n          // The true first item is offset by 1\n          const currentFirstOption = options[this.getOptionsLengthOffset()];\n          const keyManager = this.matSelect._keyManager;\n          if (keyManager && this.matSelect.panelOpen) {\n            // set first item active and input width\n            // Check to see if the first option in these changes is different from the previous.\n            const firstOptionIsChanged = !this.matSelect.compareWith(previousFirstOption, currentFirstOption);\n            // CASE: The first option is different now.\n            // Indiciates we should set it as active and scroll to the top.\n            if (firstOptionIsChanged || !keyManager.activeItem || !options.find(option => this.matSelect.compareWith(option, keyManager.activeItem))) {\n              keyManager.setFirstItemActive();\n            }\n            // wait for panel width changes\n            setTimeout(() => {\n              this.updateInputWidth();\n            });\n          }\n          // Update our reference\n          previousFirstOption = currentFirstOption;\n        });\n      }));\n    })).subscribe();\n    // add or remove css class depending on whether to show the no entries found message\n    // note: this is hacky\n    this._showNoEntriesFound$.pipe(takeUntil(this._onDestroy)).subscribe(showNoEntriesFound => {\n      // set no entries found class on mat option\n      if (this.matOption) {\n        if (showNoEntriesFound) {\n          this.matOption._getHostElement().classList.add('mat-select-search-no-entries-found');\n        } else {\n          this.matOption._getHostElement().classList.remove('mat-select-search-no-entries-found');\n        }\n      }\n    });\n    // resize the input width when the viewport is resized, i.e. the trigger width could potentially be resized\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      if (this.matSelect.panelOpen) {\n        this.updateInputWidth();\n      }\n    });\n    this.initMultipleHandling();\n    this.optionsList$.pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      // update view when available options change\n      this.changeDetectorRef.markForCheck();\n    });\n  }\n  _emitSelectAllBooleanToParent(state) {\n    this.toggleAll.emit(state);\n  }\n  ngOnDestroy() {\n    this._onDestroy.next();\n    this._onDestroy.complete();\n  }\n  _isToggleAllCheckboxVisible() {\n    return this.matSelect.multiple && this.showToggleAllCheckbox;\n  }\n  /**\n   * Handles the key down event with MatSelect.\n   * Allows e.g. selecting with enter key, navigation with arrow keys, etc.\n   * @param event\n   */\n  _handleKeydown(event) {\n    // Prevent propagation for all alphanumeric characters in order to avoid selection issues\n    if (event.key && event.key.length === 1 || event.keyCode >= A && event.keyCode <= Z || event.keyCode >= ZERO && event.keyCode <= NINE || event.keyCode === SPACE || this.preventHomeEndKeyPropagation && (event.keyCode === HOME || event.keyCode === END)) {\n      event.stopPropagation();\n    }\n    if (this.matSelect.multiple && event.key && event.keyCode === ENTER) {\n      // Regain focus after multiselect, so we can further type\n      setTimeout(() => this._focus());\n    }\n    // Special case if click Escape, if input is empty, close the dropdown, if not, empty out the search field\n    if (this.enableClearOnEscapePressed === true && event.keyCode === ESCAPE && this.value) {\n      this._reset(true);\n      event.stopPropagation();\n    }\n  }\n  /**\n   * Handles the key up event with MatSelect.\n   * Allows e.g. the announcing of the currently activeDescendant by screen readers.\n   */\n  _handleKeyup(event) {\n    if (event.keyCode === UP_ARROW || event.keyCode === DOWN_ARROW) {\n      const ariaActiveDescendantId = this.matSelect._getAriaActiveDescendant();\n      const index = this._options.toArray().findIndex(item => item.id === ariaActiveDescendantId);\n      if (index !== -1) {\n        this.unselectActiveDescendant();\n        const temp = this._options.toArray();\n        this.activeDescendant = temp[index]._getHostElement();\n        this.activeDescendant.setAttribute('aria-selected', 'true');\n        this.searchSelectInput?.nativeElement.setAttribute('aria-activedescendant', ariaActiveDescendantId);\n      }\n    }\n  }\n  writeValue(value) {\n    this._lastExternalInputValue = value;\n    this._formControl.setValue(value);\n    this.changeDetectorRef.markForCheck();\n  }\n  onBlur() {\n    this.unselectActiveDescendant();\n  }\n  registerOnChange(fn) {\n    this._formControl.valueChanges.pipe(filter(value => value !== this._lastExternalInputValue), tap(() => this._lastExternalInputValue = undefined), takeUntil(this._onDestroy)).subscribe(fn);\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Focuses the search input field\n   */\n  _focus() {\n    if (!this.searchSelectInput || !this.matSelect.panel) {\n      return;\n    }\n    // save and restore scrollTop of panel, since it will be reset by focus()\n    // note: this is hacky\n    const panel = this.matSelect.panel.nativeElement;\n    const scrollTop = panel.scrollTop;\n    // focus\n    this.searchSelectInput.nativeElement.focus();\n    panel.scrollTop = scrollTop;\n  }\n  /**\n   * Resets the current search value\n   * @param focus whether to focus after resetting\n   */\n  _reset(focus) {\n    this._formControl.setValue('');\n    if (focus) {\n      this._focus();\n    }\n  }\n  /**\n   * Initializes handling <mat-select [multiple]=\"true\">\n   * Note: to improve this code, mat-select should be extended to allow disabling resetting the selection while filtering.\n   */\n  initMultipleHandling() {\n    if (!this.matSelect.ngControl) {\n      if (this.matSelect.multiple) {\n        // note: the access to matSelect.ngControl (instead of matSelect.value / matSelect.valueChanges)\n        // is necessary to properly work in multi-selection mode.\n        console.error('the mat-select containing ngx-mat-select-search must have a ngModel or formControl directive when multiple=true');\n      }\n      return;\n    }\n    // if <mat-select [multiple]=\"true\">\n    // store previously selected values and restore them when they are deselected\n    // because the option is not available while we are currently filtering\n    this.previousSelectedValues = this.matSelect.ngControl.value;\n    this.matSelect.ngControl.valueChanges?.pipe(takeUntil(this._onDestroy)).subscribe(values => {\n      let restoreSelectedValues = false;\n      if (this.matSelect.multiple) {\n        if ((this.alwaysRestoreSelectedOptionsMulti || this._formControl.value && this._formControl.value.length) && this.previousSelectedValues && Array.isArray(this.previousSelectedValues)) {\n          if (!values || !Array.isArray(values)) {\n            values = [];\n          }\n          const optionValues = this.matSelect.options.map(option => option.value);\n          this.previousSelectedValues.forEach(previousValue => {\n            if (!values.some(v => this.matSelect.compareWith(v, previousValue)) && !optionValues.some(v => this.matSelect.compareWith(v, previousValue))) {\n              // if a value that was selected before is deselected and not found in the options, it was deselected\n              // due to the filtering, so we restore it.\n              values.push(previousValue);\n              restoreSelectedValues = true;\n            }\n          });\n        }\n      }\n      this.previousSelectedValues = values;\n      if (restoreSelectedValues) {\n        this.matSelect._onChange(values);\n      }\n    });\n  }\n  /**\n   *  Set the width of the innerSelectSearch to fit even custom scrollbars\n   *  And support all Operation Systems\n   */\n  updateInputWidth() {\n    if (!this.innerSelectSearch || !this.innerSelectSearch.nativeElement) {\n      return;\n    }\n    let element = this.innerSelectSearch.nativeElement;\n    if (element.parentElement !== null) {\n      let panelElement = null;\n      while (element = element.parentElement) {\n        if (element.classList.contains('mat-select-panel')) {\n          panelElement = element;\n          break;\n        }\n      }\n      if (panelElement !== null) {\n        this.innerSelectSearch.nativeElement.style.width = panelElement.clientWidth + 'px';\n      }\n    }\n  }\n  /**\n   * Determine the offset to length that can be caused by the optional matOption used as a search input.\n   */\n  getOptionsLengthOffset() {\n    if (this.matOption) {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n  unselectActiveDescendant() {\n    this.activeDescendant?.removeAttribute('aria-selected');\n    this.searchSelectInput?.nativeElement.removeAttribute('aria-activedescendant');\n  }\n  static {\n    this.ɵfac = function MatSelectSearchComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectSearchComponent)(i0.ɵɵdirectiveInject(MatSelect), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MatOption, 8), i0.ɵɵdirectiveInject(MatFormField, 8), i0.ɵɵdirectiveInject(MAT_SELECTSEARCH_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSelectSearchComponent,\n      selectors: [[\"mat-select-search\"]],\n      contentQueries: function MatSelectSearchComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatSelectSearchClearDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatSelectNoEntriesFoundDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearIcon = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noEntriesFound = _t.first);\n        }\n      },\n      viewQuery: function MatSelectSearchComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7, ElementRef);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchSelectInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.innerSelectSearch = _t.first);\n        }\n      },\n      inputs: {\n        placeholderLabel: \"placeholderLabel\",\n        type: \"type\",\n        closeIcon: \"closeIcon\",\n        closeSvgIcon: \"closeSvgIcon\",\n        noEntriesFoundLabel: \"noEntriesFoundLabel\",\n        clearSearchInput: \"clearSearchInput\",\n        searching: \"searching\",\n        disableInitialFocus: \"disableInitialFocus\",\n        enableClearOnEscapePressed: \"enableClearOnEscapePressed\",\n        preventHomeEndKeyPropagation: \"preventHomeEndKeyPropagation\",\n        disableScrollToActiveOnOptionsChanged: \"disableScrollToActiveOnOptionsChanged\",\n        ariaLabel: \"ariaLabel\",\n        showToggleAllCheckbox: \"showToggleAllCheckbox\",\n        toggleAllCheckboxChecked: \"toggleAllCheckboxChecked\",\n        toggleAllCheckboxIndeterminate: \"toggleAllCheckboxIndeterminate\",\n        toggleAllCheckboxTooltipMessage: \"toggleAllCheckboxTooltipMessage\",\n        toggleAllCheckboxTooltipPosition: \"toggleAllCheckboxTooltipPosition\",\n        hideClearSearchButton: \"hideClearSearchButton\",\n        alwaysRestoreSelectedOptionsMulti: \"alwaysRestoreSelectedOptionsMulti\"\n      },\n      outputs: {\n        toggleAll: \"toggleAll\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => MatSelectSearchComponent),\n        multi: true\n      }])],\n      ngContentSelectors: _c3,\n      decls: 13,\n      vars: 15,\n      consts: [[\"innerSelectSearch\", \"\"], [\"searchSelectInput\", \"\"], [\"defaultIcon\", \"\"], [\"defaultNoEntriesFound\", \"\"], [\"matInput\", \"\", 1, \"mat-select-search-input\", \"mat-select-search-hidden\"], [1, \"mat-select-search-inner\", \"mat-typography\", \"mat-datepicker-content\", \"mat-tab-header\", 3, \"ngClass\"], [\"class\", \"mat-select-search-toggle-all-checkbox\", \"matTooltipClass\", \"ngx-mat-select-search-toggle-all-tooltip\", 3, \"color\", \"checked\", \"indeterminate\", \"matTooltip\", \"matTooltipPosition\", \"change\", 4, \"ngIf\"], [\"autocomplete\", \"off\", 1, \"mat-select-search-input\", 3, \"keydown\", \"keyup\", \"blur\", \"type\", \"formControl\", \"placeholder\"], [\"class\", \"suffix\", 4, \"ngIf\"], [\"class\", \"mat-select-search-spinner\", \"diameter\", \"16\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Clear\", \"class\", \"mat-select-search-clear\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mat-select-search-no-entries-found\", 4, \"ngIf\"], [\"matTooltipClass\", \"ngx-mat-select-search-toggle-all-tooltip\", 1, \"mat-select-search-toggle-all-checkbox\", 3, \"change\", \"color\", \"checked\", \"indeterminate\", \"matTooltip\", \"matTooltipPosition\"], [1, \"suffix\"], [\"diameter\", \"16\", 1, \"mat-select-search-spinner\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Clear\", 1, \"mat-select-search-clear\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"svgIcon\"], [1, \"mat-select-search-no-entries-found\"]],\n      template: function MatSelectSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelement(0, \"input\", 4);\n          i0.ɵɵelementStart(1, \"div\", 5, 0);\n          i0.ɵɵtemplate(3, MatSelectSearchComponent_mat_checkbox_3_Template, 1, 5, \"mat-checkbox\", 6);\n          i0.ɵɵelementStart(4, \"input\", 7, 1);\n          i0.ɵɵlistener(\"keydown\", function MatSelectSearchComponent_Template_input_keydown_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleKeydown($event));\n          })(\"keyup\", function MatSelectSearchComponent_Template_input_keyup_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleKeyup($event));\n          })(\"blur\", function MatSelectSearchComponent_Template_input_blur_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatSelectSearchComponent_mat_icon_6_Template, 2, 0, \"mat-icon\", 8)(7, MatSelectSearchComponent_mat_spinner_7_Template, 1, 0, \"mat-spinner\", 9)(8, MatSelectSearchComponent_button_8_Template, 4, 2, \"button\", 10);\n          i0.ɵɵprojection(9);\n          i0.ɵɵelement(10, \"mat-divider\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, MatSelectSearchComponent_div_11_Template, 4, 2, \"div\", 11);\n          i0.ɵɵpipe(12, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c4, ctx.matSelect.multiple, ctx._isToggleAllCheckboxVisible()));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx._isToggleAllCheckboxVisible());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"type\", ctx.type)(\"formControl\", ctx._formControl)(\"placeholder\", ctx.placeholderLabel);\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.value === \"\" && !ctx.searching);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.searching);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hideClearSearchButton && ctx.value && !ctx.searching);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 10, ctx._showNoEntriesFound$));\n        }\n      },\n      dependencies: [NgClass, MatCheckbox, MatTooltip, ReactiveFormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlDirective, MatIcon, MatProgressSpinner, NgIf, MatIconButton, MatInput, MatDivider, AsyncPipe],\n      styles: [\".mat-select-search-hidden[_ngcontent-%COMP%]{visibility:hidden}.mat-select-search-inner[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;z-index:100;font-size:inherit;box-shadow:none}.mat-select-search-inner.mat-select-search-inner-multiple.mat-select-search-inner-toggle-all[_ngcontent-%COMP%]{display:flex;align-items:center}.suffix[_ngcontent-%COMP%]{position:absolute;top:12px;right:14px}.mat-select-search-input[_ngcontent-%COMP%]{box-sizing:border-box;width:100%;border:none;font-family:inherit;font-size:inherit;color:currentColor;outline:none;background:#fff;padding:0 44px 0 16px;height:calc(3em - 1px);line-height:calc(3em - 1px)}[dir=rtl][_nghost-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%]{padding-right:16px;padding-left:44px}.mat-select-search-inner-toggle-all[_ngcontent-%COMP%]   .mat-select-search-input[_ngcontent-%COMP%]{padding-left:5px}.mat-select-search-no-entries-found[_ngcontent-%COMP%]{padding-top:8px;padding-bottom:8px}.mat-select-search-clear[_ngcontent-%COMP%]{position:absolute;right:4px;top:0}[dir=rtl][_nghost-%COMP%]   .mat-select-search-clear[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-clear[_ngcontent-%COMP%]{right:auto;left:4px}.mat-select-search-spinner[_ngcontent-%COMP%]{position:absolute;right:16px;top:calc(50% - 8px)}[dir=rtl][_nghost-%COMP%]   .mat-select-search-spinner[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-spinner[_ngcontent-%COMP%]{right:auto;left:16px}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search{position:sticky;top:0;z-index:1;opacity:1;margin-top:-8px;pointer-events:all}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mat-icon{margin-right:0;margin-left:0}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search mat-pseudo-checkbox{display:none}  .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mdc-list-item__primary-text{opacity:1}.mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%]{padding-left:5px}[dir=rtl][_nghost-%COMP%]   .mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%], [dir=rtl]   [_nghost-%COMP%]   .mat-select-search-toggle-all-checkbox[_ngcontent-%COMP%]{padding-left:0;padding-right:5px}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select-search',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => MatSelectSearchComponent),\n        multi: true\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [NgClass, MatCheckbox, MatTooltip, ReactiveFormsModule, MatIcon, MatProgressSpinner, NgIf, MatIconButton, MatInput, MatDivider, AsyncPipe],\n      template: \"<!--\\nCopyright (c) 2018 Bithost GmbH All Rights Reserved.\\n\\nUse of this source code is governed by an MIT-style license that can be\\nfound in the LICENSE file at https://angular.io/license\\n-->\\n<!-- Placeholder to adjust vertical offset of the mat-option elements -->\\n<input matInput class=\\\"mat-select-search-input mat-select-search-hidden\\\" />\\n\\n<!-- Note: the  mat-datepicker-content mat-tab-header are needed to inherit the material theme colors, see PR #22 -->\\n<div\\n  #innerSelectSearch\\n  class=\\\"mat-select-search-inner mat-typography mat-datepicker-content mat-tab-header\\\"\\n  [ngClass]=\\\"{\\n    'mat-select-search-inner-multiple': matSelect.multiple,\\n    'mat-select-search-inner-toggle-all': _isToggleAllCheckboxVisible(),\\n  }\\\"\\n>\\n  <mat-checkbox\\n    *ngIf=\\\"_isToggleAllCheckboxVisible()\\\"\\n    [color]=\\\"matFormField.color\\\"\\n    class=\\\"mat-select-search-toggle-all-checkbox\\\"\\n    [checked]=\\\"toggleAllCheckboxChecked\\\"\\n    [indeterminate]=\\\"toggleAllCheckboxIndeterminate\\\"\\n    [matTooltip]=\\\"toggleAllCheckboxTooltipMessage\\\"\\n    matTooltipClass=\\\"ngx-mat-select-search-toggle-all-tooltip\\\"\\n    [matTooltipPosition]=\\\"toggleAllCheckboxTooltipPosition\\\"\\n    (change)=\\\"_emitSelectAllBooleanToParent($event.checked)\\\"\\n  ></mat-checkbox>\\n\\n  <input\\n    class=\\\"mat-select-search-input\\\"\\n    autocomplete=\\\"off\\\"\\n    [type]=\\\"type\\\"\\n    [formControl]=\\\"_formControl\\\"\\n    #searchSelectInput\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (keyup)=\\\"_handleKeyup($event)\\\"\\n    (blur)=\\\"onBlur()\\\"\\n    [placeholder]=\\\"placeholderLabel\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n  />\\n  <mat-icon *ngIf=\\\"value === '' && !searching\\\" class=\\\"suffix\\\">search</mat-icon>\\n  <mat-spinner\\n    *ngIf=\\\"searching\\\"\\n    class=\\\"mat-select-search-spinner\\\"\\n    diameter=\\\"16\\\"\\n  ></mat-spinner>\\n\\n  <button\\n    *ngIf=\\\"!hideClearSearchButton && value && !searching\\\"\\n    mat-icon-button\\n    aria-label=\\\"Clear\\\"\\n    (click)=\\\"_reset(true)\\\"\\n    class=\\\"mat-select-search-clear\\\"\\n  >\\n    <ng-content\\n      *ngIf=\\\"clearIcon; else defaultIcon\\\"\\n      select=\\\"[matSelectSearchClear]\\\"\\n    ></ng-content>\\n    <ng-template #defaultIcon>\\n      <mat-icon [svgIcon]=\\\"closeSvgIcon\\\">\\n        {{ !closeSvgIcon ? closeIcon : null }}\\n      </mat-icon>\\n    </ng-template>\\n  </button>\\n\\n  <ng-content select=\\\".mat-select-search-custom-header-content\\\"></ng-content>\\n\\n  <mat-divider></mat-divider>\\n</div>\\n\\n<div\\n  *ngIf=\\\"_showNoEntriesFound$ | async\\\"\\n  class=\\\"mat-select-search-no-entries-found\\\"\\n>\\n  <ng-content\\n    *ngIf=\\\"noEntriesFound; else defaultNoEntriesFound\\\"\\n    select=\\\"[matSelectNoEntriesFound]\\\"\\n  ></ng-content>\\n  <ng-template #defaultNoEntriesFound>{{ noEntriesFoundLabel }}</ng-template>\\n</div>\\n\",\n      styles: [\".mat-select-search-hidden{visibility:hidden}.mat-select-search-inner{position:absolute;top:0;left:0;width:100%;z-index:100;font-size:inherit;box-shadow:none}.mat-select-search-inner.mat-select-search-inner-multiple.mat-select-search-inner-toggle-all{display:flex;align-items:center}.suffix{position:absolute;top:12px;right:14px}.mat-select-search-input{box-sizing:border-box;width:100%;border:none;font-family:inherit;font-size:inherit;color:currentColor;outline:none;background:#fff;padding:0 44px 0 16px;height:calc(3em - 1px);line-height:calc(3em - 1px)}:host-context([dir=rtl]) .mat-select-search-input{padding-right:16px;padding-left:44px}.mat-select-search-inner-toggle-all .mat-select-search-input{padding-left:5px}.mat-select-search-no-entries-found{padding-top:8px;padding-bottom:8px}.mat-select-search-clear{position:absolute;right:4px;top:0}:host-context([dir=rtl]) .mat-select-search-clear{right:auto;left:4px}.mat-select-search-spinner{position:absolute;right:16px;top:calc(50% - 8px)}:host-context([dir=rtl]) .mat-select-search-spinner{right:auto;left:16px}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search{position:sticky;top:0;z-index:1;opacity:1;margin-top:-8px;pointer-events:all}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mat-icon{margin-right:0;margin-left:0}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search mat-pseudo-checkbox{display:none}::ng-deep .mat-mdc-option[aria-disabled=true].contains-mat-select-search .mdc-list-item__primary-text{opacity:1}.mat-select-search-toggle-all-checkbox{padding-left:5px}:host-context([dir=rtl]) .mat-select-search-toggle-all-checkbox{padding-left:0;padding-right:5px}\\n\"]\n    }]\n  }], () => [{\n    type: i3.MatSelect,\n    decorators: [{\n      type: Inject,\n      args: [MatSelect]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i4.MatOption,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MatOption]\n    }]\n  }, {\n    type: i5.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MatFormField]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SELECTSEARCH_DEFAULT_OPTIONS]\n    }]\n  }], {\n    placeholderLabel: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    closeIcon: [{\n      type: Input\n    }],\n    closeSvgIcon: [{\n      type: Input\n    }],\n    noEntriesFoundLabel: [{\n      type: Input\n    }],\n    clearSearchInput: [{\n      type: Input\n    }],\n    searching: [{\n      type: Input\n    }],\n    disableInitialFocus: [{\n      type: Input\n    }],\n    enableClearOnEscapePressed: [{\n      type: Input\n    }],\n    preventHomeEndKeyPropagation: [{\n      type: Input\n    }],\n    disableScrollToActiveOnOptionsChanged: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    showToggleAllCheckbox: [{\n      type: Input\n    }],\n    toggleAllCheckboxChecked: [{\n      type: Input\n    }],\n    toggleAllCheckboxIndeterminate: [{\n      type: Input\n    }],\n    toggleAllCheckboxTooltipMessage: [{\n      type: Input\n    }],\n    toggleAllCheckboxTooltipPosition: [{\n      type: Input\n    }],\n    hideClearSearchButton: [{\n      type: Input\n    }],\n    alwaysRestoreSelectedOptionsMulti: [{\n      type: Input\n    }],\n    toggleAll: [{\n      type: Output\n    }],\n    searchSelectInput: [{\n      type: ViewChild,\n      args: ['searchSelectInput', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    innerSelectSearch: [{\n      type: ViewChild,\n      args: ['innerSelectSearch', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    clearIcon: [{\n      type: ContentChild,\n      args: [MatSelectSearchClearDirective]\n    }],\n    noEntriesFound: [{\n      type: ContentChild,\n      args: [MatSelectNoEntriesFoundDirective]\n    }]\n  });\n})();\n\n/**\n * Copyright (c) 2018 Bithost GmbH All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSelectSearchModule {\n  static {\n    this.ɵfac = function MatSelectSearchModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectSearchModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSelectSearchModule,\n      imports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective],\n      exports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatSelectSearchComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectSearchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective],\n      exports: [MatSelectSearchComponent, MatSelectSearchClearDirective, MatSelectNoEntriesFoundDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECTSEARCH_DEFAULT_OPTIONS, MatSelectNoEntriesFoundDirective, MatSelectSearchClearDirective, MatSelectSearchComponent, MatSelectSearchModule, configurableDefaultOptions };\n", "import * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Input, Output, ViewChild, Optional, Inject, Self, ViewEncapsulation, Component } from '@angular/core';\nimport * as i1 from '@angular/forms';\nimport { FormControl, Validators, ReactiveFormsModule } from '@angular/forms';\nimport * as i8 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldModule, MatFormFieldControl } from '@angular/material/form-field';\nimport * as i4 from '@angular/material/input';\nimport { MatInputModule } from '@angular/material/input';\nimport * as i5 from '@angular/material/select';\nimport { MatSelectModule } from '@angular/material/select';\nimport * as i7 from '@ngx-translate/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { of, Subject, startWith, takeUntil, pairwise, distinctUntilChanged, combineLatestWith } from 'rxjs';\nimport * as i6 from '@onemrvapublic/design-system/mat-select-search';\nimport { MatSelectSearchModule } from '@onemrvapublic/design-system/mat-select-search';\nimport { IBAN_SUPPORTED_COUNTRIES, SEPA_ONLY_SUPPORTED_COUNTRIES } from '@onemrvapublic/design-system/shared';\nimport { map } from 'rxjs/operators';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { extractIBAN, isValidIBAN, friendlyFormatIBAN, countrySpecs } from 'ibantools';\nimport { MatDivider } from '@angular/material/divider';\nimport * as i2 from '@angular/cdk/a11y';\nconst _c0 = [\"countryCode\"];\nconst _c1 = [\"bban\"];\nfunction OnemrvaMatInputIbanComponent_ng_container_8_mat_divider_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-divider\");\n  }\n}\nfunction OnemrvaMatInputIbanComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-option\", 9)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, OnemrvaMatInputIbanComponent_ng_container_8_mat_divider_4_Template, 1, 0, \"mat-divider\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const country_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", country_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isLastPrioritized(i_r3));\n  }\n}\nclass OnemrvaMatInputIbanComponent {\n  static {\n    this.nextId = 0;\n  }\n  get bbanField() {\n    return this.parts.get('bban');\n  }\n  get countryCodeField() {\n    return this.parts.get('countryCode');\n  }\n  constructor(formBuilder, _focusMonitor, _elementRef, _cd, _formField, ngControl) {\n    this._focusMonitor = _focusMonitor;\n    this._elementRef = _elementRef;\n    this._cd = _cd;\n    this._formField = _formField;\n    this.ngControl = ngControl;\n    this.noEntriesFoundLabel = 'Not found';\n    this.placeholderLabel = 'Search';\n    this.searchAriaLabel = 'Search';\n    this.label = 'IBAN';\n    this.prioritizedCountries = [];\n    this.onlySEPA = false;\n    this.getCountry = new EventEmitter();\n    this.filteredCountries$ = of([]);\n    this.filterCtrl = new FormControl('');\n    this.destroyNotifier$ = new Subject();\n    this.placeholderIban = '';\n    this.controlType = 'bank-account-input';\n    this.stateChanges = new Subject();\n    this.focused = false;\n    this.id = `bank-account-input-${OnemrvaMatInputIbanComponent.nextId++}`;\n    this.touched = false;\n    this.onChange = _ => {\n      //\n    };\n    this.onTouched = () => {\n      //\n    };\n    this.readonly = false;\n    this._disabled = false;\n    this._placeholder = '';\n    this._required = false;\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this; // ✅ Only assign if ngControl exists\n    }\n    this.parts = formBuilder.group({\n      countryCode: ['', [Validators.maxLength(2)]],\n      bban: ['', []]\n    });\n  }\n  errors() {\n    return this.parts.errors;\n  }\n  get errorState() {\n    const control = this.ngControl?.control;\n    return !!(control && control.invalid && (control.dirty || !control.untouched));\n  }\n  get shouldLabelFloat() {\n    return this.focused || !this.empty;\n  }\n  get empty() {\n    const {\n      value: {\n        countryCode,\n        bban\n      }\n    } = this.parts;\n    return !countryCode && !bban;\n  }\n  get value() {\n    const {\n      value: {\n        countryCode,\n        bban\n      }\n    } = this.parts;\n    return `${countryCode ?? ''}${bban ?? ''}`;\n  }\n  set value(iban) {\n    const {\n      countryCode\n    } = extractIBAN(iban || '');\n    const _iban = iban?.replace(countryCode || '', '');\n    this.parts.setValue({\n      countryCode: countryCode || null,\n      bban: _iban || null\n    });\n    this.stateChanges.next();\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n    this._disabled ? this.parts.disable() : this.parts.enable();\n    this.stateChanges.next();\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  updateNumber(number) {\n    let shownValue = number.replace(/[^a-zA-Z0-9]/g, '');\n    // ****************\n    const fullNumber = this.countryCodeField.value + number;\n    if (isValidIBAN(fullNumber)) {\n      const formattedValue = friendlyFormatIBAN(fullNumber);\n      if (formattedValue !== null) {\n        shownValue = formattedValue.substring(2);\n      }\n    }\n    this.bbanField.patchValue(shownValue, {\n      emitEvent: false\n    });\n  }\n  ngOnInit() {\n    this.filteredCountries$ = this.getCountryCodes(this.prioritizedCountries);\n    this.bbanField.valueChanges.pipe(startWith(this.bbanField.getRawValue()), takeUntil(this.destroyNotifier$)).subscribe(number => {\n      if (number !== null && number !== '') {\n        if (this.firstTwoCharsAreLetters(number)) {\n          const code = number?.substring(0, 2).toUpperCase();\n          const country = IBAN_SUPPORTED_COUNTRIES.find(country => country == code);\n          this.countryCodeField.setValue(country);\n          number = number.substring(2);\n        }\n        this.updateNumber(number);\n      }\n    });\n    this.countryCodeField.valueChanges.pipe(startWith(''), pairwise(), distinctUntilChanged(), takeUntil(this.destroyNotifier$)).subscribe(([_prevIban, countryCode]) => {\n      this.placeholderIban = '';\n      this.touched = true;\n      if (!countryCode || countryCode.length < 2) {\n        return;\n      }\n      if (this.firstTwoCharsAreLetters(countryCode)) {\n        const r = countrySpecs[countryCode]?.chars;\n        if (r !== undefined) this.placeholderIban = this.generatePlaceholder(r);\n      }\n      if (this.bbanField.value !== null) {\n        this.updateNumber(this.bbanField.value);\n      }\n    });\n  }\n  handleClick(e) {\n    e.stopPropagation();\n  }\n  ngOnDestroy() {\n    this.destroyNotifier$.next();\n    this.destroyNotifier$.complete();\n  }\n  getCountryCodes(prioritizedCountries) {\n    const countryList = this.onlySEPA ? SEPA_ONLY_SUPPORTED_COUNTRIES : IBAN_SUPPORTED_COUNTRIES;\n    return this.filterCtrl.valueChanges.pipe(startWith(''), combineLatestWith(of(countryList)), map(([filterVal, countryCodes]) => {\n      return countryCodes.filter(code => !filterVal || code.toLowerCase().includes(filterVal.toLowerCase())).map(code =>\n      // Prefix prioritized countries with a space to sort them first\n      !filterVal && prioritizedCountries.includes(code) ? ` ${code}` : code).sort() // Default string sort will prioritize prefixed spaces\n      .map(code => code.trim()); // Remove the added prefix\n    }));\n  }\n  isLastPrioritized(i) {\n    return !this.filterCtrl.value && i === this.prioritizedCountries.length - 1;\n  }\n  autoFocusNext(control, nextElement) {\n    if (!control.errors && nextElement) {\n      this._focusMonitor.focusVia(nextElement, 'program');\n    }\n  }\n  onContainerClick(event) {\n    if (event.target.tagName.toLowerCase() != 'input') {\n      this._elementRef.nativeElement.querySelector('input')?.focus();\n    }\n  }\n  setDescribedByIds(ids) {\n    const controlElement = this._elementRef.nativeElement.querySelector('.iban-fields');\n    controlElement.setAttribute('aria-describedby', ids.join(' '));\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  writeValue(iban) {\n    this.value = iban;\n  }\n  _handleInput(control, nextElement) {\n    this.autoFocusNext(control, nextElement);\n    this.onChange(this.value);\n  }\n  firstTwoCharsAreLetters(input) {\n    const regex = /^[a-zA-Z]{2}/;\n    return regex.test(input);\n  }\n  generatePlaceholder(chars) {\n    let placeholder = '';\n    for (let i = 0; i < chars; i++) {\n      placeholder += '0';\n    }\n    const ph = friendlyFormatIBAN(placeholder)?.substring(2);\n    if (ph) return ph;\n    return placeholder.substring(2);\n  }\n  onKeyup(event) {\n    // Clear country when backspace on empty bban\n    if (this.bbanField.value === '' && event.key === 'Backspace') {\n      this.countryCodeField.setValue(null);\n      this.parts.clearValidators();\n    }\n  }\n  static {\n    this.ɵfac = function OnemrvaMatInputIbanComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatInputIbanComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i1.NgControl, 10));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OnemrvaMatInputIbanComponent,\n      selectors: [[\"onemrva-mat-input-iban\"]],\n      viewQuery: function OnemrvaMatInputIbanComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.countryCode = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bban = _t.first);\n        }\n      },\n      inputs: {\n        noEntriesFoundLabel: \"noEntriesFoundLabel\",\n        placeholderLabel: \"placeholderLabel\",\n        searchAriaLabel: \"searchAriaLabel\",\n        label: \"label\",\n        prioritizedCountries: \"prioritizedCountries\",\n        onlySEPA: \"onlySEPA\",\n        value: \"value\",\n        readonly: \"readonly\",\n        disabled: \"disabled\",\n        placeholder: \"placeholder\",\n        required: \"required\"\n      },\n      outputs: {\n        getCountry: \"getCountry\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: OnemrvaMatInputIbanComponent\n      }])],\n      decls: 12,\n      vars: 14,\n      consts: [[\"countryCode\", \"\"], [\"bban\", \"\"], [3, \"formGroup\"], [1, \"iban-fields\"], [1, \"iban-prefix\"], [\"formControlName\", \"countryCode\", 1, \"onemrva-iban-select\", 3, \"selectionChange\"], [3, \"formControl\", \"ariaLabel\", \"noEntriesFoundLabel\", \"placeholderLabel\"], [4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"bban\", 1, \"onemrva-text-number\", 3, \"click\", \"input\", \"keyup\", \"placeholder\", \"readOnly\"], [3, \"value\"], [4, \"ngIf\"]],\n      template: function OnemrvaMatInputIbanComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementContainerStart(0, 2);\n          i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 4)(3, \"mat-select\", 5, 0);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵlistener(\"selectionChange\", function OnemrvaMatInputIbanComponent_Template_mat_select_selectionChange_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleInput(ctx.parts.controls.countryCode));\n          });\n          i0.ɵɵelementStart(6, \"mat-option\");\n          i0.ɵɵelement(7, \"mat-select-search\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, OnemrvaMatInputIbanComponent_ng_container_8_Template, 5, 3, \"ng-container\", 7);\n          i0.ɵɵpipe(9, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"input\", 8, 1);\n          i0.ɵɵlistener(\"click\", function OnemrvaMatInputIbanComponent_Template_input_click_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleClick($event));\n          })(\"input\", function OnemrvaMatInputIbanComponent_Template_input_input_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleInput(ctx.parts.controls.bban));\n          })(\"keyup\", function OnemrvaMatInputIbanComponent_Template_input_keyup_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyup($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementContainerEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.parts);\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind1(5, 10, \"Country\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formControl\", ctx.filterCtrl)(\"ariaLabel\", ctx.searchAriaLabel)(\"noEntriesFoundLabel\", ctx.noEntriesFoundLabel)(\"placeholderLabel\", ctx.placeholderLabel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(9, 12, ctx.filteredCountries$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeholderIban)(\"readOnly\", ctx.readonly);\n          i0.ɵɵattribute(\"aria-label\", ctx.label);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.AsyncPipe, MatInputModule, i4.MatInput, MatFormFieldModule, MatSelectModule, i5.MatSelect, i5.MatOption, MatSelectSearchModule, i6.MatSelectSearchComponent, ReactiveFormsModule, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, TranslateModule, i7.TranslatePipe, MatDivider],\n      styles: [\"onemrva-mat-input-iban{width:100%}onemrva-mat-input-iban .iban-fields{display:flex}onemrva-mat-input-iban .iban-fields .iban-prefix{width:40px;margin-right:8px}\\n\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatInputIbanComponent, [{\n    type: Component,\n    args: [{\n      selector: 'onemrva-mat-input-iban',\n      standalone: true,\n      imports: [CommonModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatSelectSearchModule, ReactiveFormsModule, TranslateModule, MatDivider],\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: OnemrvaMatInputIbanComponent\n      }],\n      template: \"<ng-container [formGroup]=\\\"parts\\\">\\n  <!--  <mat-form-field class=\\\"onemrva-input-iban\\\">-->\\n  <!--    <mat-label>{{ label }}</mat-label>-->\\n  <div class=\\\"iban-fields\\\">\\n    <div class=\\\"iban-prefix\\\">\\n      <mat-select\\n        class=\\\"onemrva-iban-select\\\"\\n        formControlName=\\\"countryCode\\\"\\n        [attr.aria-label]=\\\"'Country' | translate\\\"\\n        (selectionChange)=\\\"_handleInput(parts.controls.countryCode)\\\"\\n        #countryCode\\n      >\\n        <mat-option>\\n          <mat-select-search\\n            [formControl]=\\\"filterCtrl\\\"\\n            [ariaLabel]=\\\"searchAriaLabel\\\"\\n            [noEntriesFoundLabel]=\\\"noEntriesFoundLabel\\\"\\n            [placeholderLabel]=\\\"placeholderLabel\\\"\\n          ></mat-select-search>\\n        </mat-option>\\n\\n        <ng-container\\n          *ngFor=\\\"let country of filteredCountries$ | async; let i = index\\\"\\n        >\\n          <mat-option [value]=\\\"country\\\">\\n            <span>{{ country }}</span>\\n          </mat-option>\\n\\n          <mat-divider *ngIf=\\\"isLastPrioritized(i)\\\"></mat-divider>\\n        </ng-container>\\n      </mat-select>\\n    </div>\\n    <input\\n      [attr.aria-label]=\\\"label\\\"\\n      (click)=\\\"handleClick($event)\\\"\\n      matInput\\n      [placeholder]=\\\"placeholderIban\\\"\\n      formControlName=\\\"bban\\\"\\n      class=\\\"onemrva-text-number\\\"\\n      [readOnly]=\\\"readonly\\\"\\n      (input)=\\\"_handleInput(parts.controls.bban)\\\"\\n      (keyup)=\\\"onKeyup($event)\\\"\\n      #bban\\n    />\\n  </div>\\n  <!--    <mat-hint *ngIf=\\\"hint !== ''\\\">{{ hint }}</mat-hint>-->\\n  <!--    <mat-error >{{ 'error' | translate }}</mat-error>-->\\n  <!--  </mat-form-field>-->\\n\\n  <!--  {{ parts.errors |json }}-->\\n</ng-container>\\n\",\n      styles: [\"onemrva-mat-input-iban{width:100%}onemrva-mat-input-iban .iban-fields{display:flex}onemrva-mat-input-iban .iban-fields .iban-prefix{width:40px;margin-right:8px}\\n\"]\n    }]\n  }], () => [{\n    type: i1.FormBuilder\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i8.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_FORM_FIELD]\n    }]\n  }, {\n    type: i1.NgControl,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }], {\n    countryCode: [{\n      type: ViewChild,\n      args: ['countryCode']\n    }],\n    bban: [{\n      type: ViewChild,\n      args: ['bban']\n    }],\n    noEntriesFoundLabel: [{\n      type: Input\n    }],\n    placeholderLabel: [{\n      type: Input\n    }],\n    searchAriaLabel: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    prioritizedCountries: [{\n      type: Input\n    }],\n    onlySEPA: [{\n      type: Input\n    }],\n    getCountry: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }]\n  });\n})();\n\n/*\n * Public API Surface of mat-bank-account-input\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaMatInputIbanComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,+BAA+B,IAAI,eAAe,gCAAgC;AAAA,EACtF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,uCAAuC;AAC9C,SAAO;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,IACb,qBAAqB;AAAA,EACvB;AACF;AAMA,IAAI;AAAA,CACH,SAAUA,uBAAsB;AAE/B,EAAAA,sBAAqBA,sBAAqB,MAAM,IAAI,CAAC,IAAI;AAEzD,EAAAA,sBAAqBA,sBAAqB,SAAS,IAAI,CAAC,IAAI;AAE5D,EAAAA,sBAAqBA,sBAAqB,WAAW,IAAI,CAAC,IAAI;AAE9D,EAAAA,sBAAqBA,sBAAqB,eAAe,IAAI,CAAC,IAAI;AACpE,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAKtD,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,WAAW;AAAA,EACzC,OAAO;AACT;AAEA,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA;AACF;AAEA,IAAM,WAAW,qCAAqC;AACtD,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,UAAU,OAAO,MAAM;AAAA,EACvB,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,OAAO,8BAA8B;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,QAAQ;AACN,SAAK,cAAc,cAAc,MAAM;AAAA,EACzC;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,UAAM,QAAQ,IAAI,kBAAkB;AACpC,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,6BAA6B;AAC3B,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAClB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,GAAG,KAAK,MAAM,KAAK,SAAS;AAAA,EACrC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,gBAAgB;AAAA;AAAA,EAEhB,OAAO;AAAA;AAAA,EAEP,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,yBAAyB;AAAA,EACzB,qBAAqB,qBAAqB;AAAA,EAC1C,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,qBAAqB,MAAM;AAAA,EAAC;AAAA,EAC5B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAC7C,SAAK,WAAW,YAAY,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC7D,SAAK,KAAK,KAAK,YAAY,OAAO,YAAY,EAAE,MAAM,mBAAmB;AACzE,SAAK,sBAAsB,KAAK,UAAU,uBAAuB;AAAA,EACnE;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,GAAG;AACvB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB,KAAK,cAAc;AAAA,EAC7C;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,SAAS,KAAK,SAAS;AACzB,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,UAAU;AAC3B,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,UAAM,UAAU,SAAS,KAAK;AAC9B,SAAK,iBAAiB;AACtB,QAAI,SAAS;AACX,UAAI,KAAK,gBAAgB;AACvB,aAAK,sBAAsB,qBAAqB,aAAa;AAAA,MAC/D,OAAO;AACL,aAAK,sBAAsB,KAAK,UAAU,qBAAqB,UAAU,qBAAqB,SAAS;AAAA,MACzG;AACA,WAAK,oBAAoB,KAAK,KAAK,cAAc;AAAA,IACnD;AACA,SAAK,mBAAmB,KAAK,cAAc;AAAA,EAC7C;AAAA,EACA,iBAAiB;AAAA,EACjB,oBAAoB;AAClB,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,qBAAqB;AAMnB,SAAK,mBAAmB,cAAc;AAAA,EACxC;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA;AAAA,EAEA,iBAAiB,IAAI;AACnB,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS,SAAS;AAChB,WAAO,KAAK,YAAY,QAAQ,UAAU,OAAO;AAAA,MAC/C,YAAY;AAAA,IACd,IAAI;AAAA,EACN;AAAA;AAAA,EAEA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,sBAAsB,UAAU;AAC9B,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU,KAAK,2BAA2B;AAC9C,QAAI,aAAa,YAAY,CAAC,SAAS;AACrC;AAAA,IACF;AACA,QAAI,KAAK,wBAAwB;AAC/B,cAAQ,UAAU,OAAO,KAAK,sBAAsB;AAAA,IACtD;AACA,SAAK,yBAAyB,KAAK,0CAA0C,UAAU,QAAQ;AAC/F,SAAK,qBAAqB;AAC1B,QAAI,KAAK,uBAAuB,SAAS,GAAG;AAC1C,cAAQ,UAAU,IAAI,KAAK,sBAAsB;AAEjD,YAAM,iBAAiB,KAAK;AAC5B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,mBAAW,MAAM;AACf,kBAAQ,UAAU,OAAO,cAAc;AAAA,QACzC,GAAG,GAAI;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,SAAK,8BAA8B,KAAK,OAAO;AAC/C,SAAK,OAAO,KAAK,KAAK,mBAAmB,KAAK,OAAO,CAAC;AAGtD,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,cAAc,UAAU,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,UAAU,CAAC,KAAK;AACrB,SAAK,8BAA8B,KAAK,OAAO;AAAA,EACjD;AAAA,EACA,oBAAoB;AAClB,UAAM,cAAc,KAAK,UAAU;AAEnC,QAAI,CAAC,KAAK,YAAY,gBAAgB,QAAQ;AAE5C,UAAI,KAAK,iBAAiB,gBAAgB,SAAS;AACjD,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,iBAAiB;AACtB,eAAK,oBAAoB,KAAK,KAAK,cAAc;AAAA,QACnD,CAAC;AAAA,MACH;AACA,WAAK,WAAW,CAAC,KAAK;AACtB,WAAK,sBAAsB,KAAK,WAAW,qBAAqB,UAAU,qBAAqB,SAAS;AAIxG,WAAK,iBAAiB;AAAA,IACxB,WAAW,KAAK,YAAY,KAAK,uBAAuB,CAAC,KAAK,YAAY,gBAAgB,QAAQ;AAGhG,WAAK,cAAc,cAAc,UAAU,KAAK;AAChD,WAAK,cAAc,cAAc,gBAAgB,KAAK;AAAA,IACxD;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AAIzB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,UAAU;AAMR,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,0CAA0C,UAAU,UAAU;AAE5D,QAAI,KAAK,mBAAmB,kBAAkB;AAC5C,aAAO;AAAA,IACT;AACA,YAAQ,UAAU;AAAA,MAChB,KAAK,qBAAqB;AAGxB,YAAI,aAAa,qBAAqB,SAAS;AAC7C,iBAAO,KAAK,kBAAkB;AAAA,QAChC,WAAW,YAAY,qBAAqB,eAAe;AACzD,iBAAO,KAAK,WAAW,KAAK,kBAAkB,yBAAyB,KAAK,kBAAkB;AAAA,QAChG;AACA;AAAA,MACF,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,UAAU,KAAK,kBAAkB,qBAAqB,KAAK,kBAAkB;AAAA,MACxH,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,YAAY,KAAK,kBAAkB,qBAAqB,KAAK,kBAAkB;AAAA,MAC1H,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,UAAU,KAAK,kBAAkB,yBAAyB,KAAK,kBAAkB;AAAA,IAC9H;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,OAAO;AACxB,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,qBAAe,cAAc,gBAAgB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,sBAAsB;AACpB,SAAK,kBAAkB;AACvB,QAAI,CAAC,KAAK,UAAU;AAGlB,WAAK,cAAc,cAAc,MAAM;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B,OAAO;AAC/B,QAAI,CAAC,CAAC,MAAM,UAAU,KAAK,cAAc,cAAc,SAAS,MAAM,MAAM,GAAG;AAC7E,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,YAAY,IAAI,EAAE,cAAc,IAAI,EAAE,mBAAmB,IAAI;AAC5E,QAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,YAAY;AAC3D,QAAG,YAAY,2BAA2B,IAAI,mBAAmB,gBAAgB,EAAE,0BAA0B,IAAI,QAAQ,EAAE,6BAA6B,IAAI,QAAQ,EAAE,4BAA4B,IAAI,OAAO,EAAE,yCAAyC,IAAI,mBAAmB;AAAA,MACjR;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,MAC1D,cAAc,CAAC,GAAG,iBAAiB,gBAAgB,gBAAgB;AAAA,MACnE,cAAc,CAAC,GAAG,iBAAiB,cAAc;AAAA,MACjD,UAAU,CAAC,GAAG,aAAa,UAAU;AAAA,MACrC,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,SAAY,gBAAgB,KAAK,CAAC;AAAA,MACjG,OAAO;AAAA,MACP,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,MACvF,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,IACvE;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC,qCAAqC;AAAA,MACrE,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,2BAA2B,IAAI,GAAG,SAAS,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iCAAiC,GAAG,OAAO,GAAG,CAAC,QAAQ,YAAY,GAAG,gCAAgC,GAAG,QAAQ,SAAS,UAAU,WAAW,iBAAiB,YAAY,MAAM,YAAY,UAAU,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,aAAa,SAAS,WAAW,aAAa,eAAe,QAAQ,GAAG,yBAAyB,GAAG,CAAC,QAAQ,QAAQ,KAAK,oCAAoC,GAAG,8BAA8B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,cAAc,IAAI,GAAG,2BAA2B,uBAAuB,GAAG,oBAAoB,qBAAqB,mBAAmB,GAAG,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;AAAA,IAChyB,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,SAAS,SAAS,0CAA0C,QAAQ;AAChF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,0BAA0B,MAAM,CAAC;AAAA,QAC7D,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7C,QAAG,WAAW,SAAS,SAAS,4CAA4C;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,CAAC;AAAA,QACjD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,QAAQ,SAAS,6CAA6C;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,8CAA8C;AACjE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC,EAAE,UAAU,SAAS,6CAA6C,QAAQ;AACzE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,QACvD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,gBAAgB;AACnB,QAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,SAAS,IAAI,CAAC;AACpC,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,cAAiB,YAAY,CAAC;AACpC,QAAG,WAAW,iBAAiB,IAAI,aAAa;AAChD,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,0BAA0B,IAAI,OAAO;AACpD,QAAG,WAAW,WAAW,IAAI,OAAO,EAAE,iBAAiB,IAAI,aAAa,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,EAAE,MAAM,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AACrP,QAAG,YAAY,cAAc,IAAI,aAAa,IAAI,EAAE,mBAAmB,IAAI,cAAc,EAAE,oBAAoB,IAAI,eAAe,EAAE,gBAAgB,IAAI,gBAAgB,UAAU,IAAI,EAAE,iBAAiB,IAAI,YAAY,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,OAAO,IAAI,EAAE,iBAAiB,IAAI,YAAY,EAAE,aAAa,IAAI,QAAQ,EAAE,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,KAAK;AACrY,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,WAAW,EAAE,qBAAqB,IAAI,iBAAiB,IAAI,QAAQ,EAAE,qBAAqB,IAAI;AAChI,QAAG,UAAU;AACb,QAAG,WAAW,OAAO,IAAI,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,qBAAqB;AAAA,IAC/C,QAAQ,CAAC,q6fAAu6f;AAAA,IACh7f,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,QAAQ;AAAA;AAAA,QAER,qCAAqC;AAAA,QACrC,oCAAoC;AAAA,QACpC,iDAAiD;AAAA,QACjD,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC,qCAAqC;AAAA,QAC/C,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA,MACD,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,WAAW,qBAAqB;AAAA,MAC1C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,q6fAAu6f;AAAA,IACl7f,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,SAAY,gBAAgB,KAAK;AAAA,MACvE,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kCAAkC;AAAA,EACtC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,4BAA4B;AAAA,EAC1D,OAAO;AACT;AASA,IAAM,+BAAN,MAAM,sCAAqC,0BAA0B;AAAA,EACnE,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qCAAqC,mBAAmB;AACtE,cAAQ,8CAA8C,4CAA+C,sBAAsB,6BAA4B,IAAI,qBAAqB,6BAA4B;AAAA,IAC9M;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,YAAY,IAAI,mBAAmB,EAAE,GAAG,CAAC,gBAAgB,YAAY,IAAI,eAAe,EAAE,GAAG,CAAC,gBAAgB,YAAY,IAAI,WAAW,EAAE,CAAC;AAAA,IACzK,UAAU,CAAI,mBAAmB,CAAC,+BAA+B,CAAC,GAAM,0BAA0B;AAAA,EACpG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,WAAW,CAAC,+BAA+B;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sCAAN,MAAM,qCAAoC;AAAA,EACxC,OAAO,OAAO,SAAS,4CAA4C,mBAAmB;AACpF,WAAO,KAAK,qBAAqB,sCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,4BAA4B;AAAA,IACtC,SAAS,CAAC,4BAA4B;AAAA,EACxC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,4BAA4B;AAAA,MACtC,SAAS,CAAC,4BAA4B;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,eAAe;AAAA,IACtC,SAAS,CAAC,aAAa,eAAe;AAAA,EACxC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,aAAa,iBAAiB,eAAe;AAAA,EACzD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,eAAe;AAAA,MACtC,SAAS,CAAC,aAAa,eAAe;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC/tBH,IAAMC,OAAM,CAAC,oBAAoB;AACjC,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,UAAU,EAAE;AAC5B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,WAAW,OAAO,SAAS,CAAC;AAC3C,IAAG,UAAU;AACb,IAAG,YAAY,oBAAoB,OAAO,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,OAAO,qBAAqB,IAAI,GAAG,IAAI,EAAE,gBAAgB,OAAO,mBAAmB,GAAG,GAAG;AACtL,IAAG,YAAY,KAAK,OAAO,cAAc,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,uCAAuC,IAAI,eAAe,wCAAwC;AAAA,EACtG,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,+CAA+C;AACtD,SAAO;AAAA,IACL,UAAU;AAAA,EACZ;AACF;AAIA,IAAM,YAAY;AAIlB,IAAM,oBAAoB;AAC1B,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB;AAAA,EACA,cAAc;AACZ,UAAM,gBAAgB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,UAAMC,YAAW,OAAO,oCAAoC;AAC5D,SAAK,kBAAkB,kBAAkB,oBAAoB,CAAC,CAACA,aAAY,CAACA,UAAS;AACrF,SAAK,OAAO,KAAK,YAAY,cAAc,SAAS,YAAY,MAAM,gBAAgB,kBAAkB;AACxG,QAAIA,WAAU;AACZ,UAAIA,UAAS,OAAO;AAClB,aAAK,QAAQ,KAAK,gBAAgBA,UAAS;AAAA,MAC7C;AACA,UAAIA,UAAS,UAAU;AACrB,aAAK,WAAWA,UAAS;AAAA,MAC3B;AACA,UAAIA,UAAS,aAAa;AACxB,aAAK,cAAcA,UAAS;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,gBAAgB,KAAK,SAAS;AAAA,EACrD;AAAA,EACA,IAAI,MAAM,GAAG;AACX,SAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,SAAS;AAAA;AAAA,EAET,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,MAAM;AACjB,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,WAAW;AAAA,EAC9C;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,SAAS;AAAA,EAC/B;AAAA,EACA;AAAA;AAAA,EAEA,gBAAgB;AACd,YAAQ,KAAK,WAAW,qBAAqB;AAAA,EAC/C;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,cAAc,IAAI,IAAI,KAAK;AAChD,WAAO,OAAO,OAAO,IAAI,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,IAAI,KAAK,KAAK,KAAK,cAAc;AAAA,EAC1C;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,SAAS,eAAe;AAC/B,aAAO,KAAK,qBAAqB,KAAK,MAAM,KAAK,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,cAAc,KAAK,WAAW;AAAA,EAC5C;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,GAAG,CAAC,aAAa,CAAC;AAAA,IACrD,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,YAAYD,MAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,eAAe,YAAY,MAAM,GAAG,4BAA4B,uBAAuB;AAAA,IAC3G,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,EAAE,iBAAiB,IAAI,SAAS,gBAAgB,IAAI,QAAQ,IAAI,EAAE,QAAQ,IAAI,IAAI;AACzI,QAAG,WAAW,SAAS,IAAI,KAAK;AAChC,QAAG,YAAY,SAAS,IAAI,UAAU,IAAI,EAAE,UAAU,IAAI,UAAU,IAAI,EAAE,gCAAgC,IAAI,WAAW,IAAI,EAAE,kDAAkD,IAAI,WAAW,IAAI;AACpM,QAAG,YAAY,2BAA2B,IAAI,eAAe,EAAE,wCAAwC,IAAI,SAAS,eAAe;AAAA,MACrI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,IAChE;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,8CAA8C,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,mDAAmD,GAAG,CAAC,MAAM,OAAO,MAAM,OAAO,GAAG,2CAA2C,GAAG,CAAC,eAAe,QAAQ,GAAG,gDAAgD,GAAG,CAAC,GAAG,sCAAsC,GAAG,CAAC,GAAG,yCAAyC,oCAAoC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,yCAAyC,qCAAqC,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,qDAAqD,GAAG,CAAC,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,IAC7zB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACnH,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,aAAa,EAAE;AAClB,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,QAAG,mBAAmB,GAAG,CAAC;AAC1B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,QAAG,mBAAmB,IAAI,CAAC;AAC3B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,QAAG,mBAAmB,IAAI,CAAC;AAC3B,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,YAAY,CAAC;AAClC,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,WAAW,IAAI,SAAS,CAAC;AACxC,QAAG,UAAU;AACb,QAAG,YAAY,oBAAoB,IAAI,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,IAAI,kBAAkB,GAAG,IAAI,EAAE,gBAAgB,IAAI,mBAAmB,GAAG,GAAG;AACtK,QAAG,YAAY,KAAK,IAAI,cAAc,CAAC;AACvC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAC3C,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,SAAS;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,QAAQ,CAAC,irIAAirI;AAAA,IAC1rI,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA;AAAA,QAGT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,mCAAmC;AAAA,QACnC,gDAAgD;AAAA,QAChD,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,wCAAwC;AAAA,QACxC,0DAA0D;AAAA,QAC1D,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,eAAe;AAAA,MACjB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ,CAAC,irIAAirI;AAAA,IAC5rI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAa;AACnB,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,oBAAoB,UAAU;AAAA,IACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,EAC3D,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,UAAU;AAAA,MACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AChRH,IAAME,OAAM,CAAC,mBAAmB;AAChC,IAAMC,OAAM,CAAC,mBAAmB;AAChC,IAAMC,OAAM,CAAC,CAAC,CAAC,IAAI,GAAG,yCAAyC,CAAC,GAAG,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC,CAAC;AAC5I,IAAM,MAAM,CAAC,4CAA4C,0BAA0B,2BAA2B;AAC9G,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,oCAAoC;AAAA,EACpC,sCAAsC;AACxC;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,gBAAgB,EAAE;AACvC,IAAG,WAAW,UAAU,SAAS,gFAAgF,QAAQ;AACvH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,8BAA8B,OAAO,OAAO,CAAC;AAAA,IAC5E,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,aAAa,KAAK,EAAE,WAAW,OAAO,wBAAwB,EAAE,iBAAiB,OAAO,8BAA8B,EAAE,cAAc,OAAO,+BAA+B,EAAE,sBAAsB,OAAO,gCAAgC;AAAA,EAC3Q;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,QAAQ;AACrB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,EAAE;AAAA,EACnC;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,SAAS,6BAA6B,CAAC;AAAA,EAChE;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,CAAC,OAAO,eAAe,OAAO,YAAY,MAAM,GAAG;AAAA,EAChF;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,qEAAqE;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,IAAI,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,cAAc,EAAE,EAAE,GAAG,0DAA0D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACtN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAoB,YAAY,CAAC;AACvC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,EAAE,YAAY,cAAc;AAAA,EACpE;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,GAAG,CAAC,SAAS,4CAA4C,CAAC;AAAA,EAC/E;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,kBAAkB,OAAO,mBAAmB;AAAA,EACjD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,cAAc,EAAE,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAClN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,2BAA8B,YAAY,CAAC;AACjD,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc,EAAE,YAAY,wBAAwB;AAAA,EACnF;AACF;AACA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC;AAAA,IAC9C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,6BAA6B,CAAC,aAAa,oBAAoB,aAAa,gBAAgB,uBAAuB,yCAAyC,8BAA8B,yBAAyB,uBAAuB,oBAAoB,gCAAgC,WAAW;AAkB/S,IAAM,mCAAmC,IAAI,eAAe,kCAAkC;AAW9F,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,aAAO,KAAK,qBAAqB,mCAAkC;AAAA,IACrE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,IACjD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,2BAAN,MAAM,0BAAyB;AAAA;AAAA,EAE7B,IAAI,QAAQ;AACV,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,SAAS,UAAU;AACrB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAAA,EACA,YAAY,WAAW,mBAAmB,gBAAgB,WAAW,cAAc,gBAAgB;AACjG,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,eAAe;AAEpB,SAAK,mBAAmB;AAExB,SAAK,OAAO;AAEZ,SAAK,YAAY;AAEjB,SAAK,eAAe;AAEpB,SAAK,sBAAsB;AAK3B,SAAK,mBAAmB;AAExB,SAAK,YAAY;AAEjB,SAAK,sBAAsB;AAE3B,SAAK,6BAA6B;AAKlC,SAAK,+BAA+B;AAEpC,SAAK,wCAAwC;AAE7C,SAAK,YAAY;AAEjB,SAAK,wBAAwB;AAE7B,SAAK,2BAA2B;AAEhC,SAAK,iCAAiC;AAEtC,SAAK,kCAAkC;AAEvC,SAAK,mCAAmC;AAExC,SAAK,wBAAwB;AAK7B,SAAK,oCAAoC;AAEzC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,YAAY,OAAK;AAAA,IAEtB;AACA,SAAK,YAAY,IAAI,gBAAgB,IAAI,UAAU,CAAC;AACpD,SAAK,eAAe,KAAK,UAAU,KAAK,UAAU,cAAY,WAAW,SAAS,QAAQ,KAAK,IAAI,aAAW,QAAQ,QAAQ,CAAC,GAAG,UAAU,SAAS,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1K,SAAK,iBAAiB,KAAK,aAAa,KAAK,IAAI,aAAW,UAAU,QAAQ,SAAS,CAAC,CAAC;AACzF,SAAK,eAAe,IAAI,YAAY,EAAE;AAEtC,SAAK,uBAAuB,cAAc,CAAC,KAAK,aAAa,cAAc,KAAK,cAAc,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,aAAa,MAAM;AAChI,aAAO,KAAK,wBAAwB,MAAM,kBAAkB,KAAK,uBAAuB;AAAA,IAC1F,CAAC,CAAC;AAEF,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,oBAAoB,cAAc;AAAA,EACzC;AAAA,EACA,oBAAoB,gBAAgB;AAClC,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,eAAW,OAAO,4BAA4B;AAE5C,UAAI,eAAe,eAAe,GAAG,GAAG;AACtC,aAAK,GAAG,IAAI,eAAe,GAAG;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAET,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,WAAW;AAC1B,WAAK,UAAU,gBAAgB,EAAE,UAAU,IAAI,4BAA4B;AAAA,IAE7E,OAAO;AACL,cAAQ,MAAM,sEAAsE;AAAA,IACtF;AAEA,SAAK,UAAU,aAAa,KAAK,MAAM,CAAC,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AACzF,UAAI,QAAQ;AACV,aAAK,iBAAiB;AAEtB,YAAI,CAAC,KAAK,qBAAqB;AAC7B,eAAK,OAAO;AAAA,QACd;AAAA,MACF,OAAO;AAEL,YAAI,KAAK,kBAAkB;AACzB,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAAA,IACF,CAAC;AAED,SAAK,UAAU,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,OAAK;AACvD,WAAK,WAAW,KAAK,UAAU;AAW/B,UAAI,sBAAsB,KAAK,SAAS,QAAQ,EAAE,KAAK,uBAAuB,CAAC;AAC/E,aAAO,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM;AAE1C,mBAAW,MAAM;AAEf,gBAAM,UAAU,KAAK,SAAS,QAAQ;AAEtC,gBAAM,qBAAqB,QAAQ,KAAK,uBAAuB,CAAC;AAChE,gBAAM,aAAa,KAAK,UAAU;AAClC,cAAI,cAAc,KAAK,UAAU,WAAW;AAG1C,kBAAM,uBAAuB,CAAC,KAAK,UAAU,YAAY,qBAAqB,kBAAkB;AAGhG,gBAAI,wBAAwB,CAAC,WAAW,cAAc,CAAC,QAAQ,KAAK,YAAU,KAAK,UAAU,YAAY,QAAQ,WAAW,UAAU,CAAC,GAAG;AACxI,yBAAW,mBAAmB;AAAA,YAChC;AAEA,uBAAW,MAAM;AACf,mBAAK,iBAAiB;AAAA,YACxB,CAAC;AAAA,UACH;AAEA,gCAAsB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC,EAAE,UAAU;AAGd,SAAK,qBAAqB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,wBAAsB;AAEzF,UAAI,KAAK,WAAW;AAClB,YAAI,oBAAoB;AACtB,eAAK,UAAU,gBAAgB,EAAE,UAAU,IAAI,oCAAoC;AAAA,QACrF,OAAO;AACL,eAAK,UAAU,gBAAgB,EAAE,UAAU,OAAO,oCAAoC;AAAA,QACxF;AAAA,MACF;AAAA,IACF,CAAC;AAED,SAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC5E,UAAI,KAAK,UAAU,WAAW;AAC5B,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,qBAAqB;AAC1B,SAAK,aAAa,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAEjE,WAAK,kBAAkB,aAAa;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,8BAA8B,OAAO;AACnC,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,8BAA8B;AAC5B,WAAO,KAAK,UAAU,YAAY,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AAEpB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,WAAW,QAAQ,MAAM,WAAW,QAAQ,MAAM,YAAY,SAAS,KAAK,iCAAiC,MAAM,YAAY,QAAQ,MAAM,YAAY,MAAM;AAC1P,YAAM,gBAAgB;AAAA,IACxB;AACA,QAAI,KAAK,UAAU,YAAY,MAAM,OAAO,MAAM,YAAY,OAAO;AAEnE,iBAAW,MAAM,KAAK,OAAO,CAAC;AAAA,IAChC;AAEA,QAAI,KAAK,+BAA+B,QAAQ,MAAM,YAAY,UAAU,KAAK,OAAO;AACtF,WAAK,OAAO,IAAI;AAChB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAClB,QAAI,MAAM,YAAY,YAAY,MAAM,YAAY,YAAY;AAC9D,YAAM,yBAAyB,KAAK,UAAU,yBAAyB;AACvE,YAAM,QAAQ,KAAK,SAAS,QAAQ,EAAE,UAAU,UAAQ,KAAK,OAAO,sBAAsB;AAC1F,UAAI,UAAU,IAAI;AAChB,aAAK,yBAAyB;AAC9B,cAAM,OAAO,KAAK,SAAS,QAAQ;AACnC,aAAK,mBAAmB,KAAK,KAAK,EAAE,gBAAgB;AACpD,aAAK,iBAAiB,aAAa,iBAAiB,MAAM;AAC1D,aAAK,mBAAmB,cAAc,aAAa,yBAAyB,sBAAsB;AAAA,MACpG;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,0BAA0B;AAC/B,SAAK,aAAa,SAAS,KAAK;AAChC,SAAK,kBAAkB,aAAa;AAAA,EACtC;AAAA,EACA,SAAS;AACP,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,aAAa,aAAa,KAAK,OAAO,WAAS,UAAU,KAAK,uBAAuB,GAAG,IAAI,MAAM,KAAK,0BAA0B,MAAS,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,EAAE;AAAA,EAC5L;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,UAAU,OAAO;AACpD;AAAA,IACF;AAGA,UAAM,QAAQ,KAAK,UAAU,MAAM;AACnC,UAAM,YAAY,MAAM;AAExB,SAAK,kBAAkB,cAAc,MAAM;AAC3C,UAAM,YAAY;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACZ,SAAK,aAAa,SAAS,EAAE;AAC7B,QAAI,OAAO;AACT,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,UAAI,KAAK,UAAU,UAAU;AAG3B,gBAAQ,MAAM,iHAAiH;AAAA,MACjI;AACA;AAAA,IACF;AAIA,SAAK,yBAAyB,KAAK,UAAU,UAAU;AACvD,SAAK,UAAU,UAAU,cAAc,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAC1F,UAAI,wBAAwB;AAC5B,UAAI,KAAK,UAAU,UAAU;AAC3B,aAAK,KAAK,qCAAqC,KAAK,aAAa,SAAS,KAAK,aAAa,MAAM,WAAW,KAAK,0BAA0B,MAAM,QAAQ,KAAK,sBAAsB,GAAG;AACtL,cAAI,CAAC,UAAU,CAAC,MAAM,QAAQ,MAAM,GAAG;AACrC,qBAAS,CAAC;AAAA,UACZ;AACA,gBAAM,eAAe,KAAK,UAAU,QAAQ,IAAI,YAAU,OAAO,KAAK;AACtE,eAAK,uBAAuB,QAAQ,mBAAiB;AACnD,gBAAI,CAAC,OAAO,KAAK,OAAK,KAAK,UAAU,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,aAAa,KAAK,OAAK,KAAK,UAAU,YAAY,GAAG,aAAa,CAAC,GAAG;AAG5I,qBAAO,KAAK,aAAa;AACzB,sCAAwB;AAAA,YAC1B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,yBAAyB;AAC9B,UAAI,uBAAuB;AACzB,aAAK,UAAU,UAAU,MAAM;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,qBAAqB,CAAC,KAAK,kBAAkB,eAAe;AACpE;AAAA,IACF;AACA,QAAI,UAAU,KAAK,kBAAkB;AACrC,QAAI,QAAQ,kBAAkB,MAAM;AAClC,UAAI,eAAe;AACnB,aAAO,UAAU,QAAQ,eAAe;AACtC,YAAI,QAAQ,UAAU,SAAS,kBAAkB,GAAG;AAClD,yBAAe;AACf;AAAA,QACF;AAAA,MACF;AACA,UAAI,iBAAiB,MAAM;AACzB,aAAK,kBAAkB,cAAc,MAAM,QAAQ,aAAa,cAAc;AAAA,MAChF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACvB,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,SAAK,kBAAkB,gBAAgB,eAAe;AACtD,SAAK,mBAAmB,cAAc,gBAAgB,uBAAuB;AAAA,EAC/E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAkB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,aAAa,GAAM,kBAAkB,WAAW,CAAC,GAAM,kBAAkB,cAAc,CAAC,GAAM,kBAAkB,kCAAkC,CAAC,CAAC;AAAA,IACtT;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,gBAAgB,SAAS,wCAAwC,IAAI,KAAK,UAAU;AAClF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,+BAA+B,CAAC;AAC5D,UAAG,eAAe,UAAU,kCAAkC,CAAC;AAAA,QACjE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAChE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,QACvE;AAAA,MACF;AAAA,MACA,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAYF,MAAK,GAAG,UAAU;AACjC,UAAG,YAAYC,MAAK,GAAG,UAAU;AAAA,QACnC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AAAA,QAC1E;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,kBAAkB;AAAA,QAClB,MAAM;AAAA,QACN,WAAW;AAAA,QACX,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,4BAA4B;AAAA,QAC5B,8BAA8B;AAAA,QAC9B,uCAAuC;AAAA,QACvC,WAAW;AAAA,QACX,uBAAuB;AAAA,QACvB,0BAA0B;AAAA,QAC1B,gCAAgC;AAAA,QAChC,iCAAiC;AAAA,QACjC,kCAAkC;AAAA,QAClC,uBAAuB;AAAA,QACvB,mCAAmC;AAAA,MACrC;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC,CAAC,CAAC;AAAA,MACH,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,YAAY,IAAI,GAAG,2BAA2B,0BAA0B,GAAG,CAAC,GAAG,2BAA2B,kBAAkB,0BAA0B,kBAAkB,GAAG,SAAS,GAAG,CAAC,SAAS,yCAAyC,mBAAmB,4CAA4C,GAAG,SAAS,WAAW,iBAAiB,cAAc,sBAAsB,UAAU,GAAG,MAAM,GAAG,CAAC,gBAAgB,OAAO,GAAG,2BAA2B,GAAG,WAAW,SAAS,QAAQ,QAAQ,eAAe,aAAa,GAAG,CAAC,SAAS,UAAU,GAAG,MAAM,GAAG,CAAC,SAAS,6BAA6B,YAAY,MAAM,GAAG,MAAM,GAAG,CAAC,mBAAmB,IAAI,cAAc,SAAS,SAAS,2BAA2B,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,sCAAsC,GAAG,MAAM,GAAG,CAAC,mBAAmB,4CAA4C,GAAG,yCAAyC,GAAG,UAAU,SAAS,WAAW,iBAAiB,cAAc,oBAAoB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,YAAY,MAAM,GAAG,2BAA2B,GAAG,CAAC,mBAAmB,IAAI,cAAc,SAAS,GAAG,2BAA2B,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,oCAAoC,CAAC;AAAA,MACzzC,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgBC,IAAG;AACtB,UAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,gBAAgB,CAAC;AAC1F,UAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,UAAG,WAAW,WAAW,SAAS,2DAA2D,QAAQ;AACnG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,UAClD,CAAC,EAAE,SAAS,SAAS,yDAAyD,QAAQ;AACpF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,UAChD,CAAC,EAAE,QAAQ,SAAS,0DAA0D;AAC5E,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,OAAO,CAAC;AAAA,UACpC,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,4CAA4C,GAAG,GAAG,UAAU,EAAE;AACjO,UAAG,aAAa,CAAC;AACjB,UAAG,UAAU,IAAI,aAAa;AAC9B,UAAG,aAAa;AAChB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,OAAO,EAAE;AAC3E,UAAG,OAAO,IAAI,OAAO;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,IAAI,UAAU,UAAU,IAAI,4BAA4B,CAAC,CAAC;AAC/G,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,4BAA4B,CAAC;AACvD,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,IAAI,EAAE,eAAe,IAAI,YAAY,EAAE,eAAe,IAAI,gBAAgB;AACpG,UAAG,YAAY,cAAc,IAAI,SAAS;AAC1C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,UAAU,MAAM,CAAC,IAAI,SAAS;AACxD,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,SAAS;AACnC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,yBAAyB,IAAI,SAAS,CAAC,IAAI,SAAS;AAC/E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,oBAAoB,CAAC;AAAA,QACxE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,aAAa,YAAY,qBAAwB,sBAAyB,iBAAoB,sBAAsB,SAAS,oBAAoB,MAAM,eAAe,UAAU,YAAY,SAAS;AAAA,MAC7N,QAAQ,CAAC,qvEAAqvE;AAAA,MAC9vE,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,aAAa,YAAY,qBAAqB,SAAS,oBAAoB,MAAM,eAAe,UAAU,YAAY,SAAS;AAAA,MAClJ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,4qDAA4qD;AAAA,IACvrD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uCAAuC,CAAC;AAAA,MACtC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gCAAgC,CAAC;AAAA,MAC/B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iCAAiC,CAAC;AAAA,MAChC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mCAAmC,CAAC;AAAA,MAClC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,MACnG,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,IACrG,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,wBAAwB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,MACnG,SAAS,CAAC,0BAA0B,+BAA+B,gCAAgC;AAAA,IACrG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACjzBH,IAAMC,OAAM,CAAC,aAAa;AAC1B,IAAMC,OAAM,CAAC,MAAM;AACnB,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa;AAAA,EAC/B;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,cAAc,CAAC,EAAE,GAAG,MAAM;AAC/C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,eAAe,EAAE;AAC5G,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,UAAU;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,UAAU;AAC/B,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,kBAAkB,IAAI,CAAC;AAAA,EACtD;AACF;AACA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,OAAO;AACL,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,MAAM,IAAI,MAAM;AAAA,EAC9B;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,MAAM,IAAI,aAAa;AAAA,EACrC;AAAA,EACA,YAAY,aAAa,eAAe,aAAa,KAAK,YAAY,WAAW;AAC/E,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AACb,SAAK,uBAAuB,CAAC;AAC7B,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,qBAAqB,GAAG,CAAC,CAAC;AAC/B,SAAK,aAAa,IAAI,YAAY,EAAE;AACpC,SAAK,mBAAmB,IAAI,QAAQ;AACpC,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,UAAU;AACf,SAAK,KAAK,sBAAsB,8BAA6B,QAAQ;AACrE,SAAK,UAAU;AACf,SAAK,WAAW,OAAK;AAAA,IAErB;AACA,SAAK,YAAY,MAAM;AAAA,IAEvB;AACA,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,gBAAgB;AAAA,IACjC;AACA,SAAK,QAAQ,YAAY,MAAM;AAAA,MAC7B,aAAa,CAAC,IAAI,CAAC,WAAW,UAAU,CAAC,CAAC,CAAC;AAAA,MAC3C,MAAM,CAAC,IAAI,CAAC,CAAC;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,aAAa;AACf,UAAM,UAAU,KAAK,WAAW;AAChC,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,SAAS,CAAC,QAAQ;AAAA,EACrE;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,WAAW,CAAC,KAAK;AAAA,EAC/B;AAAA,EACA,IAAI,QAAQ;AACV,UAAM;AAAA,MACJ,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,WAAO,CAAC,eAAe,CAAC;AAAA,EAC1B;AAAA,EACA,IAAI,QAAQ;AACV,UAAM;AAAA,MACJ,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,WAAO,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE;AAAA,EAC1C;AAAA,EACA,IAAI,MAAM,MAAM;AACd,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,YAAY,QAAQ,EAAE;AAC1B,UAAM,QAAQ,MAAM,QAAQ,eAAe,IAAI,EAAE;AACjD,SAAK,MAAM,SAAS;AAAA,MAClB,aAAa,eAAe;AAAA,MAC5B,MAAM,SAAS;AAAA,IACjB,CAAC;AACD,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAE5C,SAAK,YAAY,KAAK,MAAM,QAAQ,IAAI,KAAK,MAAM,OAAO;AAC1D,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAC5C,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI,aAAa,OAAO,QAAQ,iBAAiB,EAAE;AAEnD,UAAM,aAAa,KAAK,iBAAiB,QAAQ;AACjD,QAAI,YAAY,UAAU,GAAG;AAC3B,YAAM,iBAAiB,mBAAmB,UAAU;AACpD,UAAI,mBAAmB,MAAM;AAC3B,qBAAa,eAAe,UAAU,CAAC;AAAA,MACzC;AAAA,IACF;AACA,SAAK,UAAU,WAAW,YAAY;AAAA,MACpC,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,qBAAqB,KAAK,gBAAgB,KAAK,oBAAoB;AACxE,SAAK,UAAU,aAAa,KAAK,UAAU,KAAK,UAAU,YAAY,CAAC,GAAG,UAAU,KAAK,gBAAgB,CAAC,EAAE,UAAU,YAAU;AAC9H,UAAI,WAAW,QAAQ,WAAW,IAAI;AACpC,YAAI,KAAK,wBAAwB,MAAM,GAAG;AACxC,gBAAM,OAAO,QAAQ,UAAU,GAAG,CAAC,EAAE,YAAY;AACjD,gBAAM,UAAU,yBAAyB,KAAK,CAAAC,aAAWA,YAAW,IAAI;AACxE,eAAK,iBAAiB,SAAS,OAAO;AACtC,mBAAS,OAAO,UAAU,CAAC;AAAA,QAC7B;AACA,aAAK,aAAa,MAAM;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB,aAAa,KAAK,UAAU,EAAE,GAAG,SAAS,GAAG,qBAAqB,GAAG,UAAU,KAAK,gBAAgB,CAAC,EAAE,UAAU,CAAC,CAAC,WAAW,WAAW,MAAM;AACnK,WAAK,kBAAkB;AACvB,WAAK,UAAU;AACf,UAAI,CAAC,eAAe,YAAY,SAAS,GAAG;AAC1C;AAAA,MACF;AACA,UAAI,KAAK,wBAAwB,WAAW,GAAG;AAC7C,cAAM,IAAI,aAAa,WAAW,GAAG;AACrC,YAAI,MAAM,OAAW,MAAK,kBAAkB,KAAK,oBAAoB,CAAC;AAAA,MACxE;AACA,UAAI,KAAK,UAAU,UAAU,MAAM;AACjC,aAAK,aAAa,KAAK,UAAU,KAAK;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,GAAG;AACb,MAAE,gBAAgB;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,KAAK;AAC3B,SAAK,iBAAiB,SAAS;AAAA,EACjC;AAAA,EACA,gBAAgB,sBAAsB;AACpC,UAAM,cAAc,KAAK,WAAW,gCAAgC;AACpE,WAAO,KAAK,WAAW,aAAa,KAAK,UAAU,EAAE,GAAG,kBAAkB,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,YAAY,MAAM;AAC7H,aAAO,aAAa,OAAO,UAAQ,CAAC,aAAa,KAAK,YAAY,EAAE,SAAS,UAAU,YAAY,CAAC,CAAC,EAAE,IAAI;AAAA;AAAA,QAE3G,CAAC,aAAa,qBAAqB,SAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAAA,OAAI,EAAE,KAAK,EAC3E,IAAI,UAAQ,KAAK,KAAK,CAAC;AAAA,IAC1B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,kBAAkB,GAAG;AACnB,WAAO,CAAC,KAAK,WAAW,SAAS,MAAM,KAAK,qBAAqB,SAAS;AAAA,EAC5E;AAAA,EACA,cAAc,SAAS,aAAa;AAClC,QAAI,CAAC,QAAQ,UAAU,aAAa;AAClC,WAAK,cAAc,SAAS,aAAa,SAAS;AAAA,IACpD;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,MAAM,OAAO,QAAQ,YAAY,KAAK,SAAS;AACjD,WAAK,YAAY,cAAc,cAAc,OAAO,GAAG,MAAM;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,kBAAkB,KAAK;AACrB,UAAM,iBAAiB,KAAK,YAAY,cAAc,cAAc,cAAc;AAClF,mBAAe,aAAa,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,EAC/D;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,WAAW,MAAM;AACf,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa,SAAS,aAAa;AACjC,SAAK,cAAc,SAAS,WAAW;AACvC,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,wBAAwB,OAAO;AAC7B,UAAM,QAAQ;AACd,WAAO,MAAM,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,qBAAe;AAAA,IACjB;AACA,UAAM,KAAK,mBAAmB,WAAW,GAAG,UAAU,CAAC;AACvD,QAAI,GAAI,QAAO;AACf,WAAO,YAAY,UAAU,CAAC;AAAA,EAChC;AAAA,EACA,QAAQ,OAAO;AAEb,QAAI,KAAK,UAAU,UAAU,MAAM,MAAM,QAAQ,aAAa;AAC5D,WAAK,iBAAiB,SAAS,IAAI;AACnC,WAAK,MAAM,gBAAgB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,kBAAqB,WAAW,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,gBAAgB,CAAC,GAAM,kBAAqB,WAAW,EAAE,CAAC;AAAA,IAC9S;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,MACtC,WAAW,SAAS,mCAAmC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAYF,MAAK,CAAC;AACrB,UAAG,YAAYC,MAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,OAAO,GAAG;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,qBAAqB;AAAA,QACrB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,OAAO;AAAA,QACP,sBAAsB;AAAA,QACtB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,MACH,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,mBAAmB,eAAe,GAAG,uBAAuB,GAAG,iBAAiB,GAAG,CAAC,GAAG,eAAe,aAAa,uBAAuB,kBAAkB,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,YAAY,IAAI,mBAAmB,QAAQ,GAAG,uBAAuB,GAAG,SAAS,SAAS,SAAS,eAAe,UAAU,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC;AAAA,MACxb,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,wBAAwB,GAAG,CAAC;AAC/B,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,cAAc,GAAG,CAAC;AACjE,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,mBAAmB,SAAS,8EAA8E;AACtH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,IAAI,MAAM,SAAS,WAAW,CAAC;AAAA,UACxE,CAAC;AACD,UAAG,eAAe,GAAG,YAAY;AACjC,UAAG,UAAU,GAAG,qBAAqB,CAAC;AACtC,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,gBAAgB,CAAC;AAC9F,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,SAAS,GAAG,CAAC;AACnC,UAAG,WAAW,SAAS,SAAS,8DAA8D,QAAQ;AACpG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,UAC/C,CAAC,EAAE,SAAS,SAAS,gEAAgE;AACnF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,IAAI,MAAM,SAAS,IAAI,CAAC;AAAA,UACjE,CAAC,EAAE,SAAS,SAAS,8DAA8D,QAAQ;AACzF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,QAAQ,MAAM,CAAC;AAAA,UAC3C,CAAC;AACD,UAAG,aAAa,EAAE;AAClB,UAAG,sBAAsB;AAAA,QAC3B;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,aAAa,IAAI,KAAK;AACpC,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,cAAiB,YAAY,GAAG,IAAI,SAAS,CAAC;AAC7D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,eAAe,IAAI,UAAU,EAAE,aAAa,IAAI,eAAe,EAAE,uBAAuB,IAAI,mBAAmB,EAAE,oBAAoB,IAAI,gBAAgB;AACvK,UAAG,UAAU;AACb,UAAG,WAAW,WAAc,YAAY,GAAG,IAAI,IAAI,kBAAkB,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,eAAe,IAAI,eAAe,EAAE,YAAY,IAAI,QAAQ;AAC1E,UAAG,YAAY,cAAc,IAAI,KAAK;AAAA,QACxC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,SAAY,MAAS,WAAW,gBAAmB,UAAU,oBAAoB,iBAAoB,WAAc,WAAW,uBAA0B,0BAA0B,qBAAwB,sBAAyB,iBAAoB,sBAAyB,sBAAyB,oBAAuB,iBAAiB,iBAAoB,eAAe,UAAU;AAAA,MAC9Z,QAAQ,CAAC,oKAAoK;AAAA,MAC7K,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,gBAAgB,oBAAoB,iBAAiB,uBAAuB,qBAAqB,iBAAiB,UAAU;AAAA,MACpJ,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,oKAAoK;AAAA,IAC/K,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": ["TransitionCheckState", "_c0", "defaults", "_c0", "_c1", "_c2", "_c0", "_c1", "country"]}