package be.fgov.onerva.cu.backend.adapter.in.queue;

import be.fgov.onerva.cu.backend.application.port.in.ChangePersonalDataRequestUseCase;
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef;
import be.fgov.onerva.unemployment.c9.rest.model.C9;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class C9MessageQueueConsumerTest {

    @Mock
    private ChangePersonalDataRequestUseCase receivedChangePersonalDataRequestUseCase;

    @InjectMocks
    private C9MessageQueueConsumer consumer;

    @Test
    @DisplayName("Should receive message from the queue - 410")
    void shouldReceiveChangePersonalData410() {
        // Given
        AttestRef attestRef = new AttestRef().id(4321).type("EC1");

        C9 message = new C9();
        message.setId(12345L);
        message.setType("410");
        message.setUnemploymentOffice(123456);
        message.setPaymentInstitution(4321);
        message.setOpKey("sdfs");
        message.setSectOp("sdfsdf");
        message.setRequestDate(LocalDate.of(2024, 12, 16));
        message.setSsin("12345678901");
        message.setEntityCode("ent-code");
        message.setTreatmentStatus("READY_TO_BE_TREATED");
        message.setIntroductionDate(LocalDate.of(2024, 12, 16));
        message.getAttestRefs().add(attestRef);

        // When
        consumer.onC9EventReceivedFromQueue(message, null);

        // Then
        // Nothing to verify yet
        verify(receivedChangePersonalDataRequestUseCase, times(1)).receivedChangePersonalData(any());
    }

    @Test
    @DisplayName("Should receive message from the queue - 410 - no EC1")
    void shouldReceiveChangePersonalData410_no_EC1() {
        // Given
        C9 message = new C9();
        message.setId(12345L);
        message.setType("410");
        message.setUnemploymentOffice(123456);
        message.setPaymentInstitution(4321);
        message.setOpKey("sdfs");
        message.setSectOp("sdfsdf");
        message.setRequestDate(LocalDate.of(2024, 12, 16));
        message.setSsin("12345678901");
        message.setEntityCode("ent-code");
        message.setTreatmentStatus("READY_TO_BE_TREATED");

        // When
        consumer.onC9EventReceivedFromQueue(message, null);

        // Then
        // Nothing to verify yet
        verifyNoInteractions(receivedChangePersonalDataRequestUseCase);
    }

    @Test
    @DisplayName("Should receive message from the queue - 410 - not treated")
    void shouldReceiveChangePersonalData410_not_treated() {
        // Given
        C9 message = new C9();
        message.setId(12345L);
        message.setType("410");
        message.setUnemploymentOffice(123456);
        message.setPaymentInstitution(4321);
        message.setOpKey("sdfs");
        message.setSectOp("sdfsdf");
        message.setRequestDate(LocalDate.of(2024, 12, 16));
        message.setSsin("12345678901");
        message.setEntityCode("ent-code");
        message.setTreatmentStatus("OTHER");

        // When
        consumer.onC9EventReceivedFromQueue(message, null);

        // Then
        // Nothing to verify yet
        verifyNoInteractions(receivedChangePersonalDataRequestUseCase);
    }

    @Test
    @DisplayName("Should receive message from the queue - other than 410")
    void shouldReceiveChangePersonalData() {
        // Given
        C9 message = new C9();
        message.setId(12345L);
        message.setType("other");
        message.setUnemploymentOffice(123456);
        message.setPaymentInstitution(4321);
        message.setOpKey("sdfs");
        message.setSectOp("sdfsdf");
        message.setRequestDate(LocalDate.of(2024, 12, 16));
        message.setSsin("12345678901");
        message.setEntityCode("ent-code");
        message.setTreatmentStatus("READY_TO_BE_TREATED");

        // When
        consumer.onC9EventReceivedFromQueue(message, null);

        // Then
        // Nothing to verify yet
        verifyNoInteractions(receivedChangePersonalDataRequestUseCase);
    }

    @Test
    @DisplayName("Should process treated message from the queue")
    void shouldProcessTreatedChangePersonalData() {
        // Given
        AttestRef attestRef = new AttestRef().id(4321).type("EC1");

        C9 message = new C9();
        message.setId(12345L);
        message.setType("410");
        message.setUnemploymentOffice(123456);
        message.setPaymentInstitution(4321);
        message.setOpKey("sdfs");
        message.setSectOp("sdfsdf");
        message.setRequestDate(LocalDate.of(2024, 12, 16));
        message.setSsin("12345678901");
        message.setEntityCode("ent-code");
        message.setTreatmentStatus("TREATED");  // Status is TREATED
        message.getAttestRefs().add(attestRef);
        message.setDecisionDate(LocalDate.of(2024, 12, 16));
        message.setDecisionType("C2");
        message.setDecisionWindowsUser("me");

        // When
        consumer.onC9EventReceivedFromQueue(message, null);

        // Then
        verify(receivedChangePersonalDataRequestUseCase, times(1)).processTreatedChangePersonalData(any());
        verify(receivedChangePersonalDataRequestUseCase, times(0)).receivedChangePersonalData(any());
    }

    @Test
    @DisplayName("Should handle exception when processing message from the queue")
    void shouldHandleExceptionWhenProcessingMessage() {
        // Given
        AttestRef attestRef = new AttestRef().id(4321).type("EC1");

        C9 message = new C9();
        message.setId(12345L);
        message.setType("410");
        message.setUnemploymentOffice(123456);
        message.setPaymentInstitution(4321);
        message.setOpKey("sdfs");
        message.setSectOp("sdfsdf");
        message.setRequestDate(LocalDate.of(2024, 12, 16));
        message.setSsin("12345678901");
        message.setEntityCode("ent-code");
        message.setTreatmentStatus("READY_TO_BE_TREATED");
        message.setIntroductionDate(LocalDate.of(2024, 12, 16));
        message.getAttestRefs().add(attestRef);

        when(receivedChangePersonalDataRequestUseCase.receivedChangePersonalData(any())).thenThrow(new RuntimeException(
                "Test exception"));

        // When/Then
        assertThatThrownBy(() -> consumer.onC9EventReceivedFromQueue(message,
                null)).isInstanceOf(RuntimeException.class).hasMessage("Test exception");
    }
}