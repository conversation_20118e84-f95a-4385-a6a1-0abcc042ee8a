package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.time.LocalDate
import java.util.UUID
import org.springframework.context.annotation.Profile
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase

@RestController()
@RequestMapping("api/e2e")
@Profile("dev", "ci")
class E2ETestController(
    val changePersonalDataRequestUseCase: ChangePersonalDataRequestUseCase,
) {

    @PostMapping("/create-basic-cases")
    fun createBasicE2ECases(): List<UUID> {
        val cases = listOf(
            ChangePersonalDataRequestReceivedCommand(
                c9Id = 12345,
                c9Type = "410",
                ssin = "18031307065",
                ec1Id = 12340,
                ec1DisplayUrl = "https://localhost/api/eC1s/ref2/99999999999/344015078",
                requestDate = LocalDate.now(),
                paymentInstitution = 1,
                entityCode = "123456",
                opKey = "opKey",
                sectOp = "sectOp",
                scanUrl = "http://example.com/scan1",
                unemploymentOffice = 123,
                introductionDate = LocalDate.now(),
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12),
            ),
            ChangePersonalDataRequestReceivedCommand(
                c9Id = 123456,
                c9Type = "410",
                ssin = "18031307065",
                ec1Id = null,
                ec1DisplayUrl = null,
                requestDate = LocalDate.now(),
                paymentInstitution = 1,
                entityCode = "123456",
                opKey = "opKey",
                sectOp = "sectOp",
                scanUrl = "http://example.com/scan1",
                unemploymentOffice = 123,
                introductionDate = LocalDate.now(),
                dateValid = LocalDate.now().plusDays(20),
                scanNumber = 12345L,
                operatorCode = 123,
                introductionType = IntroductionType.INTRO_FIRST_DEMAND,
                dueDate = LocalDate.now().plusDays(12)
            )
        )
        val uuids = cases.map { case ->
            changePersonalDataRequestUseCase.receivedChangePersonalData(case)!!
        }
        return uuids
    }
}