{{- if .Values.wiremock.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: barema-stubs-mappings
  labels:
    app: wiremock
    {{- include "cu.labels" . | nindent 4 }}
data:
  {{- $mappingsPath := "files/wiremock/mappings/" }}
  {{- range $path, $_ := .Files.Glob (printf "%s*.json" $mappingsPath) }}
  {{- $filename := base $path }}
  {{ $filename }}: |-
    {{- $.Files.Get $path | nindent 4 }}
  {{- end }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: barema-stubs-files
  labels:
    app: wiremock
    {{- include "cu.labels" . | nindent 4 }}
data:
  {{- $filesPath := "files/wiremock/__files/" }}
  {{- range $path, $_ := .Files.Glob (printf "%s*.json" $filesPath) }}
  {{- $filename := base $path }}
  {{ $filename }}: |-
    {{- $.Files.Get $path | nindent 4 }}
  {{- end }}
{{- end }}