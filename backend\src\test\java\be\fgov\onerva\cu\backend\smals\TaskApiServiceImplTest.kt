package be.fgov.onerva.cu.backend.smals

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.rest.client.wo.smals.api.TaskApi
import be.fgov.onerva.cu.backend.rest.client.wo.smals.rest.model.TaskDTO
import be.fgov.onerva.cu.backend.rest.client.wo.smals.rest.model.TechnicalInformationDTO
import be.fgov.onerva.cu.backend.taskapi.TaskApiServiceImpl
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class TaskApiServiceImplTest {

    @MockK
    private lateinit var taskApi: TaskApi

    @InjectMockKs
    private lateinit var taskApiService: TaskApiServiceImpl

    @Test
    fun `softDeleteTask should return TaskDTO when task is successfully soft deleted`() {
        val taskId = 123L
        val reason = "Soft deletion"
        val expectedTaskDTO = TaskDTO()

        val technicalInformation = TaskDTO().technicalInformation(
            TechnicalInformationDTO().technicalStatus(TechnicalInformationDTO.TechnicalStatusEnum.DELETED)
        )

        every { taskApi.patchTask(taskId, null, technicalInformation) } returns expectedTaskDTO

        val result = taskApiService.softDeleteTask(taskId, reason)

        assertThat(result).isNotNull
        assertThat(result).isEqualTo(expectedTaskDTO)
        verify(exactly = 1) { taskApi.patchTask(taskId, null, technicalInformation) }
    }

    @Test
    fun `softDeleteTask should return null when RuntimeException is thrown`() {
        val taskId = 456L
        val reason = "Error case"
        val exception = RuntimeException("API call failed")

        val technicalInformation = TaskDTO().technicalInformation(
            TechnicalInformationDTO().technicalStatus(TechnicalInformationDTO.TechnicalStatusEnum.DELETED)
        )

        every { taskApi.patchTask(taskId, null, technicalInformation) } throws exception

        val result = taskApiService.softDeleteTask(taskId, reason)

        assertThat(result).isNull()
        verify(exactly = 1) { taskApi.patchTask(taskId, null, technicalInformation) }
    }

    @Test
    fun `softDeleteTask should handle null reason parameter`() {
        val taskId = 789L
        val reason: String? = null
        val expectedTaskDTO = TaskDTO()

        val technicalInformation = TaskDTO().technicalInformation(
            TechnicalInformationDTO().technicalStatus(TechnicalInformationDTO.TechnicalStatusEnum.DELETED)
        )

        every { taskApi.patchTask(taskId, null, technicalInformation) } returns expectedTaskDTO

        val result = taskApiService.softDeleteTask(taskId, reason)

        assertThat(result).isNotNull
        assertThat(result).isEqualTo(expectedTaskDTO)
        verify(exactly = 1) { taskApi.patchTask(taskId, null, technicalInformation) }
    }

    @Test
    fun `softDeleteTask should call patchTask with correct technical information`() {
        // Given
        val taskId = 999L
        val reason = "Test reason"
        val expectedTaskDTO = TaskDTO()

        every { taskApi.patchTask(any(), any(), any()) } returns expectedTaskDTO

        // When
        taskApiService.softDeleteTask(taskId, reason)

        // Then
        verify(exactly = 1) {
            taskApi.patchTask(
                eq(taskId),
                any<String>(),
                match { taskDto ->
                    taskDto.technicalInformation?.technicalStatus == TechnicalInformationDTO.TechnicalStatusEnum.DELETED
                }
            )
        }
    }


    @Test
    fun `softDeleteTask should log error when exception occurs`() {
        val taskId = 555L
        val reason = "Test"
        val exception = RuntimeException("Database error")

        val technicalInformation = TaskDTO().technicalInformation(
            TechnicalInformationDTO().technicalStatus(TechnicalInformationDTO.TechnicalStatusEnum.DELETED)
        )

        every { taskApi.patchTask(taskId, null, technicalInformation) } throws exception

        val result = taskApiService.softDeleteTask(taskId, reason)

        assertThat(result).isNull()
        verify(exactly = 1) { taskApi.patchTask(taskId, null, technicalInformation) }
    }
}