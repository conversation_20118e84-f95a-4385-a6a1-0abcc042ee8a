package be.fgov.onerva.cu.backend.cucumber.steps

import org.assertj.core.api.Assertions.assertThat
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.person.api.CitizenInfoApi
import com.fasterxml.jackson.databind.ObjectMapper
import io.cucumber.datatable.DataTable
import io.cucumber.java.en.And
import io.cucumber.java.en.When
import io.cucumber.spring.ScenarioScope

@ScenarioScope
class HistorySteps : BaseSteps() {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var jdbcHelper: Jdbc<PERSON><PERSON><PERSON>

    @Autowired
    private lateinit var citizenInfoApi: CitizenInfoApi

    @When("I get the ONEM historical information for the citizen")
    fun getOnemHistoricalInformation() {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        testContext.result = mockMvc.perform(
            get("/api/requests/${requestId}/historical/citizen-information/ONEM")
                .withAuth()
        )
    }

    @And("the response should contain the following information:")
    fun verifyResponseContainsInformation(dataTable: DataTable) {
        val expectedValues = dataTable.asMap()
        val response = testContext.result!!.andReturn().response
        val actualValues = objectMapper.readValue(response.contentAsString, Map::class.java) as Map<*, *>

        expectedValues.forEach { (key, value) ->
            if (key.endsWith("_date")) {
                // Handle date conversion
                val dateValue = value as String
                if (dateValue == "null")
                    assertThat(actualValues[key]).isNull()
                else
                    assertThat(actualValues[key]).isEqualTo(dateValue)
            } else {
                when (value) {
                    "null" -> assertThat(actualValues[key]).isNull()
                    else -> when (key) {
                        "nationalityCode", "countryCode" -> assertThat(actualValues[key]).isEqualTo(value.toInt())
                        else -> assertThat(actualValues[key]).isEqualTo(value)
                    }
                }
            }
        }
    }

    @And("the address in the response should contain the following values:")
    fun verifyResponseContainsAddressValueDate(dataTable: DataTable) {
        val expectedValues = dataTable.asMap()
        val response = testContext.result!!.andReturn().response
        val actualValues = objectMapper.readValue(response.contentAsString, Map::class.java) as Map<*, *>

        val address = actualValues["address"] as Map<*, *>
        expectedValues.forEach { (key, value) ->
            when (value) {
                "null" -> assertThat(address[key]).isNull()
                else -> when (key) {
                    "nationalityCode", "countryCode" -> assertThat(address[key]).isEqualTo(value.toInt())
                    else -> assertThat(address[key]).isEqualTo(value)
                }
            }
        }
    }
}
