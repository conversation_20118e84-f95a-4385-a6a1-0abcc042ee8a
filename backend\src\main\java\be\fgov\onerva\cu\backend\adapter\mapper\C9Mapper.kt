package be.fgov.onerva.cu.backend.adapter.mapper

import java.time.LocalDate
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestTreatedCommand
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IntroductionType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.exception.InvalidC9Exception
import be.fgov.onerva.cu.common.utils.DateUtils
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef
import be.fgov.onerva.unemployment.c9.rest.model.C9
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment
import be.fgov.onerva.unemployment.c9.rest.model.EC1TradeUnionContribution

/**
 * Maps an EC1Identity to the domain Identity model
 */
fun EC1Identity.toDomainCitizenInformation(validFrom: LocalDate): CitizenInformation =
    CitizenInformation(
        firstName = this.firstName,
        lastName = this.lastName,
        birthDate = this.dateOfBirth?.let {
            DateUtils.parseDate(this.dateOfBirth)
        } ?: throw InvalidC9Exception("The C9 is invalid - Invalid date of birth"),
        nationalityCode = this.nationality?.let { this.nationality.code.toInt() } ?: 150,
        address = Address(
            street = this.street,
            houseNumber = this.houseNumber,
            boxNumber = this.boxNumber,
            zipCode = this.zipCode.zipCode,
            city = this.city,
            countryCode = this.country.code.toInt(),
            validFrom = validFrom,
        )
    )

/**
 * Maps an EC1ModeOfPayment to the domain ModeOfPayment model
 */
fun EC1ModeOfPayment.toDomainModeOfPayment(fromDate: LocalDate): ModeOfPayment =
    ModeOfPayment(
        otherPersonName = if (this.isMyBankAccount != null && !this.isMyBankAccount) this.bankAccountForOtherPersonName else null,
        iban = if (this.foreignBankAccountIBAN != null) this.foreignBankAccountIBAN else this.belgianSEPABankAccount,
        bic = this.foreignBankAccountBIC,
        validFrom = fromDate,
    )

fun EC1TradeUnionContribution.toDomainUnionContribution(): UnionContribution =
    UnionContribution(
        authorized = this.contributionDeductionFromTheMonth != null,
        effectiveDate = this.contributionDeductionFromTheMonth ?: this.stopContributionDeductionFromTheMonth,
    )

/**
 * Extension function to convert a C9 object to ChangePersonalDataReceived
 * Assumes the first EC1 attestRef is the relevant one for processing
 */
fun C9.toChangePersonalDataRequestCommand(): ChangePersonalDataRequestReceivedCommand {
    val eC1 = this.getEC1()
    return ChangePersonalDataRequestReceivedCommand(
        c9Id = this.id,
        c9Type = this.type,
        ssin = this.ssin,
        ec1Id = eC1?.id,
        ec1DisplayUrl = eC1?.displayUrl,
        requestDate = this.requestDate,
        introductionDate = this.introductionDate,
        paymentInstitution = this.paymentInstitution,
        entityCode = this.entityCode,
        opKey = this.opKey,
        sectOp = this.sectOp,
        scanUrl = this.scanUrl,
        unemploymentOffice = this.unemploymentOffice,
        dateValid = this.dateValid,
        scanNumber = this.scanNumber,
        operatorCode = this.operatorCode,
        introductionType = this.introductionType?.let { IntroductionType.valueOf(it) },
        dueDate = this.dueDate,
    )
}

fun C9.toChangePersonalDataRequestTreatedCommand(): ChangePersonalDataRequestTreatedCommand =
    ChangePersonalDataRequestTreatedCommand(
        c9Id = this.id,
        type = this.type,
        ssin = this.ssin,
        decisionDate = this.decisionDate,
        decisionType = DecisionType.valueOf(this.decisionType),
        user = this.decisionWindowsUser,
    )

fun C9.hasEC1(): Boolean = this.attestRefs.any { it.type == "EC1" }

fun C9.getEC1(): AttestRef? = this.attestRefs.firstOrNull { it.type == "EC1" }

fun C9.isChangedOfPersonalDataReady(): Boolean =
    (this.type == "400" || this.type == "410") && this.treatmentStatus == "READY_TO_BE_TREATED" && (this.hasEC1() || this.scanUrl != null)

fun C9.isChangedOfPersonalDataTreated(): Boolean =
    (this.type == "400" || this.type == "410") && this.treatmentStatus == "TREATED"
