---
global:
  routes:
    host: 'cu.prod.paas.onemrva.priv'
backend:
  replicaCount: 2
  secrets:
    spring_security_oauth2_client_registration_keycloak_clientsecret: '==INJECTED VIA BAMBOOSPEC SECRETS=='
  image:
    registry: 'docker-release.onemrva.priv'
  springConfiguration:
    client:
      security:
        enabled: true
    spring:
      rabbitmq:
        host: rabbitmq.rabbitmq.svc.cluster.local
      profiles:
        active: prod
      security:
        oauth2:
          client:
            registration:
              keycloak:
                client-secret: '==INJECTED VIA BAMBOOSPEC SECRETS=='
          resourceserver:
            jwt:
              issuer-uri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents
      datasource:
        url: *********************************************************************************************************************************************
        username: CU_java_MSSQL
        password: '==INJECTED VIA BAMBOOSPEC SECRETS=='
      liquibase:
        contexts: ddl,dml,ddl-prod,dml-prod
    keycloak:
      auth-server-url: https://keycloak.prod.paas.onemrva.priv
      checktoken: true
      redirect: https://cu.prod.paas.onemrva.priv/*
      realm: onemrva-agents
    rabbitmq:
      oauth:
        enabled: true
    app:
      allowMultipleC9s: false

    woThirdPartyApi:
      url: https://wo-thirdparty-api.prod.paas.onemrva.priv/thirdParties/v1
    woOrganizationalChartApi:
      url: https://wo-organizational-chart-api.prod.paas.onemrva.priv/rest/organizationalChart/nsso/v2/services
    werkomgeving:
      enabled: true
      mock: false
      woFacadeApi:
        url: https://wo-configurator.prod.paas.onemrva.priv/api
      gateway:
        url: https://wo-gateway.neocloud.be/REST/nssoWorkEnvironmentGateway/v1
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: https://person.prod.paas.onemrva.priv/api
    barema:
      url: https://bareme.prod.paas.onemrva.priv/api
    c9Api:
      url: 'https://c9.prod.paas.onemrva.priv/api'
    registry:
      url: https://registerproxyservice.prod.paas.onemrva.priv/registerproxyservice/rest
    flagsmith:
      api:
        key: WwcY32fDWGr2sGPgsJQ9pV
        url: https://flagsmith.prod.paas.onemrva.priv/api/v1/
bff:
  replicaCount: 2
  secrets:
    spring_security_oauth2_client_registration_keycloak_clientsecret: '==INJECTED VIA BAMBOOSPEC SECRETS=='
  image:
    registry: 'docker-release.onemrva.priv'
  springConfiguration:
    client:
      security:
        enabled: true
    spring:
      profiles:
        active: prod
      security:
        oauth2:
          client:
            registration:
              keycloak:
                client-secret: '==INJECTED VIA BAMBOOSPEC SECRETS=='
            provider:
              keycloak:
                issuer-uri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents
                token-uri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token
          resourceserver:
            jwt:
              issuer-uri: https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents
    keycloak:
      auth-server-url: https://keycloak.prod.paas.onemrva.priv
      checktoken: true
      redirect: https://cu.prod.paas.onemrva.priv/*
      realm: onemrva-agents
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: https://person.prod.paas.onemrva.priv/api
    backend:
      base-url: https://cu.prod.paas.onemrva.priv
    woUserFacadeApi:
      url: https://wo-configurator.prod.paas.onemrva.priv/api
    c51:
      url: https://proc51.prod.paas.onemrva.priv/proc51/home.jsf
    c9Api:
      url: 'https://c9.prod.paas.onemrva.priv/api'
    regis:
      url: https://regis.prod.paas.onemrva.priv/regis/regis/rew.seam
cu:
  image:
    registry: 'docker-release.onemrva.priv'
  route:
    path: '/elements'
  readinessProbe:
    httpGet:
      path: '/elements/elements.js'
  livenessProbe:
    httpGet:
      path: '/elements/elements.js'
kcconfig:
  keycloak:
    url: 'https://keycloak.prod.paas.onemrva.priv'
  realm:
    clients:
      cu-frontend:
        redirectUris:
          - 'https://cu.prod.paas.onemrva.priv/*'
    users:
      cu_admin:
        enabled: false
      cu_user:
        enabled: false

woconfig:
  configClient:
    baseUrl: https://wo-configurator.prod.paas.onemrva.priv
    oauth:
      issuerUrl: 'https://keycloak.prod.paas.onemrva.priv/realms/onemrva-agents'
