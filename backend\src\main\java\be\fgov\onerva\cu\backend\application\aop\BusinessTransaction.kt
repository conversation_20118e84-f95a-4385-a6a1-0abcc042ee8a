package be.fgov.onerva.cu.backend.application.aop

import org.springframework.transaction.annotation.Isolation
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import kotlin.reflect.KClass

/**
 * Meta-annotation for marking business transaction boundaries in the application layer.
 * 
 * This annotation abstracts Spring's @Transactional to avoid direct dependencies 
 * on Spring-specific annotations in the application layer, maintaining clean architecture.
 * 
 * Use this annotation on service methods that combine persistence operations with 
 * external service calls to ensure data consistency. If external services fail,
 * persistence operations will be rolled back.
 * 
 * @param value Qualifier value for the transaction manager
 * @param propagation Transaction propagation behavior (default: REQUIRED)
 * @param isolation Transaction isolation level (default: DEFAULT)
 * @param timeout Transaction timeout in seconds (default: -1, uses system default)
 * @param readOnly Whether this transaction is read-only (default: false)
 * @param rollbackFor Additional exceptions that should trigger rollback
 * @param noRollbackFor Exceptions that should NOT trigger rollback
 */
@Target(AnnotationTarget.FUNCTION, AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Transactional
annotation class BusinessTransaction(
    val value: String = "",
    val propagation: Propagation = Propagation.REQUIRED,
    val isolation: Isolation = Isolation.DEFAULT,
    val timeout: Int = -1,
    val readOnly: Boolean = false,
    val rollbackFor: Array<KClass<out Throwable>> = [],
    val noRollbackFor: Array<KClass<out Throwable>> = []
)
