package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.aop.BusinessTransaction
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.port.`in`.FieldSourceUseCase
import be.fgov.onerva.cu.backend.application.port.out.FieldSourcePort
import be.fgov.onerva.cu.common.aop.LogMethodCall

/**
 * Service implementation for managing field-level source information.
 *
 * This service implements the [FieldSourceUseCase] and handles the business logic for
 * retrieving and updating source information at a field level through the persistence adapter.
 */
@Service
@BusinessTransaction
class FieldSourceService(
    private val fieldSourcePort: FieldSourcePort,
) : FieldSourceUseCase {

    @LogMethodCall
    override fun getAllFieldSources(entityType: String, entityId: UUID): List<FieldSource> {
        return fieldSourcePort.getAllFieldSources(entityType, entityId)
    }
}