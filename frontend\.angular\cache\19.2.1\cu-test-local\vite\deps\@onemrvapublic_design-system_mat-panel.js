import {
  OnemrvaMatColor
} from "./chunk-X7GZ3B26.js";
import {
  NgClass,
  NgIf,
  NgTemplateOutlet
} from "./chunk-VK67YYVV.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  HostBinding,
  Input,
  NgModule,
  TemplateRef,
  ViewChild,
  setClassMetadata,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementContainer,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-IY4PCAU4.js";
import "./chunk-6ONIGQ3T.js";
import "./chunk-VOL5AYIH.js";
import "./chunk-NFXQRNMD.js";
import "./chunk-3F3XMFWP.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-panel.mjs
var _c0 = ["*"];
function OnemrvaMatPanelTitleActionComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 0);
    ɵɵprojection(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngClass", ctx_r0.customNgClass);
  }
}
function OnemrvaMatPanelTitleComponent_ng_template_0_ng_container_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function OnemrvaMatPanelTitleComponent_ng_template_0_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, OnemrvaMatPanelTitleComponent_ng_template_0_ng_container_2_ng_container_1_Template, 1, 0, "ng-container", 2);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const tpl_r1 = ctx.ngIf;
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", tpl_r1);
  }
}
function OnemrvaMatPanelTitleComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 0);
    ɵɵprojection(1);
    ɵɵtemplate(2, OnemrvaMatPanelTitleComponent_ng_template_0_ng_container_2_Template, 2, 1, "ng-container", 1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("ngClass", ctx_r1.customNgClass);
    ɵɵadvance(2);
    ɵɵproperty("ngIf", ctx_r1.content == null ? null : ctx_r1.content.template);
  }
}
function OnemrvaMatPanelContentComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 0);
    ɵɵprojection(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngClass", ctx_r0.customNgClass);
  }
}
function OnemrvaMatPanelIconComponent_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 0);
    ɵɵprojection(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngClass", ctx_r0.customNgClass);
  }
}
function OnemrvaMatPanelComponent_ng_container_0_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function OnemrvaMatPanelComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, OnemrvaMatPanelComponent_ng_container_0_ng_container_1_Template, 1, 0, "ng-container", 2);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const tpl_r1 = ctx.ngIf;
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", tpl_r1);
  }
}
function OnemrvaMatPanelComponent_h1_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "h1", 3);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵtextInterpolate(ctx_r1.error);
  }
}
function OnemrvaMatPanelComponent_ng_container_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function OnemrvaMatPanelComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, OnemrvaMatPanelComponent_ng_container_2_ng_container_1_Template, 1, 0, "ng-container", 2);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const tpl_r3 = ctx.ngIf;
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", tpl_r3);
  }
}
function OnemrvaMatPanelComponent_ng_container_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function OnemrvaMatPanelComponent_ng_container_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, OnemrvaMatPanelComponent_ng_container_3_ng_container_1_Template, 1, 0, "ng-container", 2);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const tpl_r4 = ctx.ngIf;
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", tpl_r4);
  }
}
var OnemrvaMatPanelTitleActionComponent = class _OnemrvaMatPanelTitleActionComponent {
  static {
    this.ɵfac = function OnemrvaMatPanelTitleActionComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatPanelTitleActionComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatPanelTitleActionComponent,
      selectors: [["onemrva-mat-panel-title-action"]],
      viewQuery: function OnemrvaMatPanelTitleActionComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TemplateRef, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.template = _t.first);
        }
      },
      inputs: {
        customNgClass: "customNgClass"
      },
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      consts: [[1, "onemrva-mat-panel-title-action", 3, "ngClass"]],
      template: function OnemrvaMatPanelTitleActionComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, OnemrvaMatPanelTitleActionComponent_ng_template_0_Template, 2, 1, "ng-template");
        }
      },
      dependencies: [NgClass],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatPanelTitleActionComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-panel-title-action",
      standalone: true,
      imports: [NgClass],
      template: '<ng-template>\n  <div class="onemrva-mat-panel-title-action" [ngClass]="customNgClass">\n    <ng-content></ng-content>\n  </div>\n</ng-template>\n'
    }]
  }], null, {
    customNgClass: [{
      type: Input
    }],
    template: [{
      type: ViewChild,
      args: [TemplateRef]
    }]
  });
})();
var OnemrvaMatPanelTitleComponent = class _OnemrvaMatPanelTitleComponent {
  constructor(cd) {
    this.cd = cd;
  }
  ngAfterViewInit() {
    this.cd.detectChanges();
  }
  static {
    this.ɵfac = function OnemrvaMatPanelTitleComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatPanelTitleComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatPanelTitleComponent,
      selectors: [["onemrva-mat-panel-title"]],
      contentQueries: function OnemrvaMatPanelTitleComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, OnemrvaMatPanelTitleActionComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.content = _t.first);
        }
      },
      viewQuery: function OnemrvaMatPanelTitleComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TemplateRef, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.template = _t.first);
        }
      },
      inputs: {
        customNgClass: "customNgClass"
      },
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      consts: [[1, "onemrva-mat-panel-title", 3, "ngClass"], [4, "ngIf"], [4, "ngTemplateOutlet"]],
      template: function OnemrvaMatPanelTitleComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, OnemrvaMatPanelTitleComponent_ng_template_0_Template, 3, 2, "ng-template");
        }
      },
      dependencies: [NgClass, NgTemplateOutlet, NgIf],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatPanelTitleComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-panel-title",
      standalone: true,
      imports: [NgClass, NgTemplateOutlet, NgIf],
      template: '<ng-template>\n  <div class="onemrva-mat-panel-title" [ngClass]="customNgClass">\n    <ng-content></ng-content>\n    <ng-container *ngIf="content?.template as tpl">\n      <ng-container *ngTemplateOutlet="tpl"></ng-container>\n    </ng-container>\n  </div>\n</ng-template>\n'
    }]
  }], () => [{
    type: ChangeDetectorRef
  }], {
    customNgClass: [{
      type: Input
    }],
    template: [{
      type: ViewChild,
      args: [TemplateRef]
    }],
    content: [{
      type: ContentChild,
      args: [OnemrvaMatPanelTitleActionComponent]
    }]
  });
})();
var OnemrvaMatPanelContentComponent = class _OnemrvaMatPanelContentComponent {
  static {
    this.ɵfac = function OnemrvaMatPanelContentComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatPanelContentComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatPanelContentComponent,
      selectors: [["onemrva-mat-panel-content"]],
      viewQuery: function OnemrvaMatPanelContentComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TemplateRef, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.template = _t.first);
        }
      },
      inputs: {
        customNgClass: "customNgClass"
      },
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      consts: [[1, "onemrva-mat-panel-content", 3, "ngClass"]],
      template: function OnemrvaMatPanelContentComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, OnemrvaMatPanelContentComponent_ng_template_0_Template, 2, 1, "ng-template");
        }
      },
      dependencies: [NgClass],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatPanelContentComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-panel-content",
      standalone: true,
      imports: [NgClass],
      template: '<ng-template>\n  <div class="onemrva-mat-panel-content" [ngClass]="customNgClass">\n    <ng-content></ng-content>\n  </div>\n</ng-template>\n'
    }]
  }], null, {
    customNgClass: [{
      type: Input
    }],
    template: [{
      type: ViewChild,
      args: [TemplateRef]
    }]
  });
})();
var OnemrvaMatPanelIconComponent = class _OnemrvaMatPanelIconComponent {
  static {
    this.ɵfac = function OnemrvaMatPanelIconComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatPanelIconComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatPanelIconComponent,
      selectors: [["onemrva-mat-panel-icon"]],
      viewQuery: function OnemrvaMatPanelIconComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TemplateRef, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.template = _t.first);
        }
      },
      inputs: {
        customNgClass: "customNgClass"
      },
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      consts: [[1, "onemrva-mat-panel-icon", 3, "ngClass"]],
      template: function OnemrvaMatPanelIconComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, OnemrvaMatPanelIconComponent_ng_template_0_Template, 2, 1, "ng-template");
        }
      },
      dependencies: [NgClass],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatPanelIconComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-panel-icon",
      standalone: true,
      imports: [NgClass],
      template: '<ng-template>\n  <div class="onemrva-mat-panel-icon" [ngClass]="customNgClass">\n    <ng-content></ng-content>\n  </div>\n</ng-template>\n'
    }]
  }], null, {
    customNgClass: [{
      type: Input
    }],
    template: [{
      type: ViewChild,
      args: [TemplateRef]
    }]
  });
})();
var NEXT_ID = 0;
var OnemrvaMatPanelComponent = class _OnemrvaMatPanelComponent {
  constructor(cd) {
    this.cd = cd;
    this.id = `onemrva-mat-panel-${NEXT_ID++}`;
    this.data_cy = "onemrva-mat-panel";
    this.disabled = false;
    this._color = OnemrvaMatColor.NONE;
  }
  ngAfterContentInit() {
    if (!this.title) {
      this.error = "Missing title in mat-panel: When using onemrva-mat-panel, you're supposed to use onemrva-mat-panel-title to display the title";
    }
  }
  ngAfterViewInit() {
    this.cd.detectChanges();
  }
  /**
   *
   * @param value
   */
  set color(value) {
    this._color = value;
  }
  get color() {
    return this._color;
  }
  /** @hidden @internal */
  get _colorAccent() {
    return this.color === OnemrvaMatColor.ACCENT;
  }
  /** @hidden @internal */
  get _isPrimary() {
    return this.color === OnemrvaMatColor.PRIMARY;
  }
  /** @hidden @internal */
  get _isAccent() {
    return this.color === OnemrvaMatColor.ACCENT;
  }
  /** @hidden @internal */
  get _isError() {
    return this.color === OnemrvaMatColor.ERROR;
  }
  /** @hidden @internal */
  get _isWarn() {
    return this.color === OnemrvaMatColor.WARN;
  }
  /** @hidden @internal */
  get _isSuccess() {
    return this.color === OnemrvaMatColor.SUCCESS;
  }
  /** @hidden @internal */
  get _isInfo() {
    return this.color === OnemrvaMatColor.INFO;
  }
  /** @hidden @internal */
  get _isGrayscale() {
    return this.color === OnemrvaMatColor.GRAYSCALE;
  }
  static {
    this.ɵfac = function OnemrvaMatPanelComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatPanelComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatPanelComponent,
      selectors: [["onemrva-mat-panel"]],
      contentQueries: function OnemrvaMatPanelComponent_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, OnemrvaMatPanelTitleComponent, 5);
          ɵɵcontentQuery(dirIndex, OnemrvaMatPanelContentComponent, 5);
          ɵɵcontentQuery(dirIndex, OnemrvaMatPanelIconComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.title = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.content = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.icon = _t.first);
        }
      },
      hostVars: 20,
      hostBindings: function OnemrvaMatPanelComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵattribute("id", ctx.id)("data-cy", ctx.data_cy);
          ɵɵclassProp("mat-disabled", ctx.disabled)("has-icon", ctx.icon)("mat-accent", ctx._isAccent)("mat-primary", ctx._isPrimary)("mat-error", ctx._isError)("mat-warn", ctx._isWarn)("mat-success", ctx._isSuccess)("mat-info", ctx._isInfo)("mat-grayscale", ctx._isGrayscale);
        }
      },
      inputs: {
        id: "id",
        data_cy: "data_cy",
        disabled: "disabled",
        color: "color"
      },
      decls: 4,
      vars: 4,
      consts: [[4, "ngIf"], ["style", "color: red", 4, "ngIf"], [4, "ngTemplateOutlet"], [2, "color", "red"]],
      template: function OnemrvaMatPanelComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, OnemrvaMatPanelComponent_ng_container_0_Template, 2, 1, "ng-container", 0)(1, OnemrvaMatPanelComponent_h1_1_Template, 2, 1, "h1", 1)(2, OnemrvaMatPanelComponent_ng_container_2_Template, 2, 1, "ng-container", 0)(3, OnemrvaMatPanelComponent_ng_container_3_Template, 2, 1, "ng-container", 0);
        }
        if (rf & 2) {
          ɵɵproperty("ngIf", ctx.icon == null ? null : ctx.icon.template);
          ɵɵadvance();
          ɵɵproperty("ngIf", ctx.error);
          ɵɵadvance();
          ɵɵproperty("ngIf", ctx.title == null ? null : ctx.title.template);
          ɵɵadvance();
          ɵɵproperty("ngIf", ctx.content == null ? null : ctx.content.template);
        }
      },
      dependencies: [NgIf, NgTemplateOutlet],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatPanelComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-panel",
      changeDetection: ChangeDetectionStrategy.OnPush,
      imports: [NgIf, NgTemplateOutlet],
      standalone: true,
      template: '<ng-container *ngIf="icon?.template as tpl">\n  <ng-container *ngTemplateOutlet="tpl"></ng-container>\n</ng-container>\n<h1 *ngIf="error" style="color: red">{{ error }}</h1>\n<ng-container *ngIf="title?.template as tpl">\n  <ng-container *ngTemplateOutlet="tpl"></ng-container>\n</ng-container>\n<ng-container *ngIf="content?.template as tpl">\n  <ng-container *ngTemplateOutlet="tpl"></ng-container>\n</ng-container>\n'
    }]
  }], () => [{
    type: ChangeDetectorRef
  }], {
    id: [{
      type: HostBinding,
      args: ["attr.id"]
    }, {
      type: Input
    }],
    data_cy: [{
      type: HostBinding,
      args: ["attr.data-cy"]
    }, {
      type: Input
    }],
    disabled: [{
      type: HostBinding,
      args: ["class.mat-disabled"]
    }, {
      type: Input
    }],
    title: [{
      type: ContentChild,
      args: [OnemrvaMatPanelTitleComponent]
    }],
    content: [{
      type: ContentChild,
      args: [OnemrvaMatPanelContentComponent]
    }],
    icon: [{
      type: HostBinding,
      args: ["class.has-icon"]
    }, {
      type: ContentChild,
      args: [OnemrvaMatPanelIconComponent]
    }],
    color: [{
      type: Input
    }],
    _colorAccent: [{
      type: HostBinding,
      args: ["class.mat-accent"]
    }],
    _isPrimary: [{
      type: HostBinding,
      args: ["class.mat-primary"]
    }],
    _isAccent: [{
      type: HostBinding,
      args: ["class.mat-accent"]
    }],
    _isError: [{
      type: HostBinding,
      args: ["class.mat-error"]
    }],
    _isWarn: [{
      type: HostBinding,
      args: ["class.mat-warn"]
    }],
    _isSuccess: [{
      type: HostBinding,
      args: ["class.mat-success"]
    }],
    _isInfo: [{
      type: HostBinding,
      args: ["class.mat-info"]
    }],
    _isGrayscale: [{
      type: HostBinding,
      args: ["class.mat-grayscale"]
    }]
  });
})();
var OnemrvaMatPanelModule = class _OnemrvaMatPanelModule {
  static {
    this.ɵfac = function OnemrvaMatPanelModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatPanelModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaMatPanelModule,
      imports: [OnemrvaMatPanelComponent, OnemrvaMatPanelTitleComponent, OnemrvaMatPanelContentComponent, OnemrvaMatPanelTitleActionComponent, OnemrvaMatPanelIconComponent],
      exports: [OnemrvaMatPanelComponent, OnemrvaMatPanelTitleComponent, OnemrvaMatPanelContentComponent, OnemrvaMatPanelTitleActionComponent, OnemrvaMatPanelIconComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatPanelModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [OnemrvaMatPanelComponent, OnemrvaMatPanelTitleComponent, OnemrvaMatPanelContentComponent, OnemrvaMatPanelTitleActionComponent, OnemrvaMatPanelIconComponent],
      exports: [OnemrvaMatPanelComponent, OnemrvaMatPanelTitleComponent, OnemrvaMatPanelContentComponent, OnemrvaMatPanelTitleActionComponent, OnemrvaMatPanelIconComponent]
    }]
  }], null, null);
})();
export {
  OnemrvaMatPanelComponent,
  OnemrvaMatPanelContentComponent,
  OnemrvaMatPanelIconComponent,
  OnemrvaMatPanelModule,
  OnemrvaMatPanelTitleActionComponent,
  OnemrvaMatPanelTitleComponent
};
//# sourceMappingURL=@onemrvapublic_design-system_mat-panel.js.map
