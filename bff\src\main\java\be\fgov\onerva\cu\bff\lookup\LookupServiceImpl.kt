package be.fgov.onerva.cu.bff.lookup

import java.time.LocalDate
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import jakarta.annotation.PostConstruct
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.api.LookupApi
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.LookupSimpleItemizedDTO
import be.fgov.onerva.cu.common.utils.logger

@Service
class LookupServiceImpl(val lookupApi: LookupApi) : LookupService {
    private val log = logger

    val COUNTRY_NATIONALITY_BCSS_CLASS_NAME: String =
        "be.fgov.onerva.lookup.wppt.persistence.model.common.NationalityBCSS"
    val COUNTRY_ONEM_CLASS_NAME: String = "be.fgov.onerva.lookup.wppt.persistence.model.common.Country"
    val INS_POST_CLASS_NAME: String = "be.fgov.onerva.lookup.wppt.persistence.model.common.InsPost"

    private val updateLock = ReentrantLock()
    private var countryCache: Map<String, CountryDTO> = HashMap()
    private var nationalityCache: Map<String, NationalityDTO> = HashMap()
    private var cityCache: Map<String, CityDTO> = HashMap()

    @PostConstruct
    fun init() {
        updateFromLookupApi()
    }

    @Scheduled(cron = "\${app.jobs.lookup.cron}")
    fun updateFromLookupApi() {
        log.info("Starting update of lookup caches")
        try {
            //NOPMD
            if (updateLock.tryLock()) {
                try {
                    updateCountry()
                    updateNationality()
                    updateCity()
                    log.info("Successfully lookup caches")
                } finally {
                    updateLock.unlock()
                }
            } else {
                //NOPMD
                log.info("Update already in progress, skipping this iteration")
            }
        } catch (e: Exception) {
            log.error("Failed to update lookup caches", e)
        }
    }

    private fun ensureCachePopulated() {
        //NOPMD
        if (countryCache.isEmpty() || nationalityCache.isEmpty()) {
            log.info("Cache is empty, attempting to populate it")
            updateFromLookupApi()
        }
    }

    private fun updateCountry() {
        val countryLookupDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(COUNTRY_ONEM_CLASS_NAME).map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<String, CountryDTO> = ConcurrentHashMap<String, CountryDTO>()

        countryLookupDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.getEndDate() == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                val entity: CountryDTO =
                    CountryDTO(dto.id, dto.code, dto.descFr, dto.descNl)
                temporaryMap[entity.code] = entity
            }

        countryCache = temporaryMap
        log.info(
            "Country cache updated, {} active countries found", temporaryMap.size
        )
    }

    /**
     * Update the city cache. The logic followed is that we use the nis code as the key and find the last entry for
     * a specific nis code.
     */
    private fun updateCity() {
        val cityLookupDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(INS_POST_CLASS_NAME)
                .map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<String, CityDTO> = ConcurrentHashMap<String, CityDTO>()

        cityLookupDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.getEndDate() == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                @Suppress("UNCHECKED_CAST")
                val codeMultiple = if (dto.codeMultiple is Map<*, *>) {
                    dto.codeMultiple as? Map<String, String>
                } else throw InvalidLookupException("Invalid lookup in city with ${dto.id}")
                val entity =
                    CityDTO(
                        LocalDate.parse(dto.beginDate.toString()),
                        codeMultiple?.get("code.nisCode")
                            ?: throw InvalidLookupException("Invalid lookup in city with ${dto.id}"),
                        dto.id,
                        codeMultiple?.get("code.code")
                            ?: throw InvalidLookupException("Invalid lookup in city with ${dto.id}"),
                        dto.descFr,
                        dto.descNl,
                    )
                if (temporaryMap.containsKey(entity.nisCode)) {
                    if (temporaryMap.get(entity.nisCode)?.beginDate?.isBefore(entity.beginDate) == true) {
                        temporaryMap[entity.nisCode] = entity
                    }
                } else {
                    temporaryMap[entity.nisCode] = entity
                }
            }

        cityCache = temporaryMap
        log.info(
            "City cache updated, {} active cities found", temporaryMap.size
        )
    }

    private fun updateNationality() {
        val nationalityLookupDTOs: List<LookupSimpleItemizedDTO> =
            lookupApi.lookupGetAllLookupsGet(COUNTRY_NATIONALITY_BCSS_CLASS_NAME).map { it as LookupSimpleItemizedDTO }
        val temporaryMap: MutableMap<String, NationalityDTO> = ConcurrentHashMap<String, NationalityDTO>()

        nationalityLookupDTOs.stream().filter { dto: LookupSimpleItemizedDTO -> dto.getEndDate() == null }
            .forEach { dto: LookupSimpleItemizedDTO ->
                @Suppress("UNCHECKED_CAST", "kotlin:S6530")
                val singleProperties = dto.singlePropertiesMap as Map<String, String>
                val onemCountryCode = singleProperties["countryCode"] ?: ""

                val entity: NationalityDTO =
                    NationalityDTO(dto.id, dto.code, dto.descFr, dto.descNl, onemCountryCode)
                temporaryMap[entity.code] = entity
            }

        nationalityCache = temporaryMap
        log.info(
            "Nationality cache updated, {} active nationalities found", temporaryMap.size
        )
    }

    override fun lookupCountry(searchQuery: String?): List<CountryDTO> {
        ensureCachePopulated()
        return countryCache.values.asSequence()
            .filter {
                searchQuery == null ||
                        it.descFr.contains(
                            searchQuery,
                            true
                        ) ||
                        it.descNl.contains(searchQuery, true) ||
                        it.code == searchQuery
            }.toList()
    }

    override fun lookupNationality(searchQuery: String?): List<NationalityDTO> {
        ensureCachePopulated()
        return nationalityCache.values.filter {
            searchQuery == null || it.descFr.contains(
                searchQuery,
                true
            ) ||
                    it.descNl.contains(searchQuery, true) ||
                    it.code == searchQuery
        }
            .toList()
    }

    override fun lookupCity(searchQuery: String?): List<CityDTO> {
        ensureCachePopulated()
        return cityCache.values.filter {
            searchQuery == null || it.descFr.contains(
                searchQuery,
                true
            ) ||
                    it.descNl.contains(searchQuery, true) ||
                    it.code == searchQuery
        }
            .toList()
    }

    override fun getNationalityCodeFromOnemCountryCode(onemCountryCode: String): Int? {
        return nationalityCache.values.filter { it.onemCountryCode == onemCountryCode }
            .minByOrNull { it.code.toInt() }?.code?.toInt()
    }
}